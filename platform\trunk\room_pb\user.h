
#ifndef __USER_H__
#define __USER_H__

#pragma warning(disable:4244)

#include "typedef.h"
#include "comm_def.h"
#include "user_interface.h"
#include <vector>
#include <time.h>
using namespace svrlib;

#define MAX_CHECK_TABLE 10

class CUser : public IUser
{
public:
	CUser();
	~CUser();

	// set function
	void reset_data();

	void set_head_image(const char *my_img_url);
	void set_user_version(const char *my_version);
	void set_nick_name(const char *my_nick_name);
	void set_ip(char  ip[64]);
	void set_sign_info(const char *sign);

	_uint64 get_gold() { return m_gold; }
	void set_gold(_uint64 gold);
	void update_gold(int result, bool need_flesh = true, bool is_add =true, bool is_tax=false, bool is_back=false);
	
	//���½�Ҳ��Ҹ��²ʽ�
	void update_gold_and_clore(int result, bool need_flesh = true, bool is_add =true, bool is_tax=false);

	void set_user_id(_uint64 my_uid)
	{
		uid = my_uid;
	}
	void set_client(_uint32 my_client_id)
	{
		client_id = my_client_id;
	}

	void set_table_id(_uint32 my_table_id);

	void set_chair_id(_uint8 my_chair_id)
	{
		chair_id = my_chair_id;
	}
	void set_score(int my_score)
	{
		score = my_score;
	}
	void set_socket_id(socket_id my_sid)
	{
		sid = my_sid;
	}
	void set_sex(_uint8 my_sex)
	{
		sex = my_sex;
	}
	void set_room_card(_uint64 count)
	{
		card_count = count;
	}

	// get function
	_uint32 get_user_id() { return uid; }
	_uint32 get_client_id() { return client_id; }
	_uint32 get_ver_int() { return clt_ver; }

	socket_id get_socket_id() { return sid; }

	int get_table_id() { return table_id; }
	int get_score() { return score; }
	_uint64 get_room_card() { return card_count; }
	int cmp_user_ver(_uint32 ver);

	_uint8 get_chair_id() { return chair_id; }
	_uint8 get_sex() { return sex; }

	bool is_offline() { return offline == 1; }
	void not_offline() { offline = 0; }

	bool is_temp_leave() { return temp_leave == 1; }
	void not_temp_leave() { temp_leave = 0; }

	const char *get_nick_name() { return nick_name; }
	const char *get_head_img() { return head_img; }
	const char *get_ip() { return ip_addr; }
	const char *get_version() { return version; }
	const char *get_sign() { return sign_info; }

	void   set_user_status(_uint8 my_status);
	_uint8 get_user_status();
	_uint8 get_real_status();

	void set_user_type(int type) { m_user_type = type; }
	int get_user_type() { return m_user_type; }

	void set_sys_type(int type) { m_sys_type = type; }
	int get_sys_type() { return m_sys_type; }

	void set_end_clear_status(bool status) { m_end_clear = status; }
	bool get_end_clear_status() { return m_end_clear; }

	int get_standup_time() { return m_standup_time; }

	void set_node_id(int id) 
	{ 
		node_id = id; 
	}
	int get_node_id() { return node_id; }

	void    set_gps(int longitude, int latitude);
	int     get_longitude();
	int     get_latitude();
	void    set_site(const char* site, int len);
	char*   get_site();

	void set_control_time(int tt)
	{
		m_control_time = tt;
	}
	int get_control_time() { return m_control_time; }

	int get_add_num() { return m_add_num; }
	int get_user_result() { return m_user_result; }

	void set_position_info(const char* position_info, int len);
	char* get_position_info();

	void set_vir_uid(int uid) { vir_uid = uid; };
	int get_vir_uid() { return vir_uid; }

	void set_plat_type(int type) { m_plat_type = type; }
	int get_plat_type() { return m_plat_type; }
	void set_vir_level();

	void set_last_gold(_uint64 gold) { last_gold = gold; }
	_uint64 get_last_gold() { return last_gold; }
	void set_changegold(_uint64 gold) { change_gold += gold; }

	void set_have_virinfo(bool is_have) { is_have_vir = is_have; }
	bool get_ishave_virinfo() { return is_have_vir; }

	_uint64 get_enter_time() { return enter_time; }
	void set_enter_time(_uint64 entime) { enter_time = entime; }

	void update_play_count(int count) { m_play_count += count; }
	int get_play_count() { return m_play_count; }

	int get_last_table() { return last_table_id; }

	void set_chat_time() { chat_time = time(0); }
	int get_chat_time() { return chat_time; }

	int get_add_bill() { return m_add_bill; }
	void update_add_bill(int bet);

	void set_user_country(int country) { country_code = country; }
	int get_user_country() { return country_code; }

	void set_single_status(int status);
	int get_single_status() { return is_single_post; }

    void set_playing_status(UserPlayingStatus status);
    UserPlayingStatus get_playing_status();
    bool is_running();
    bool is_idle();
	bool is_broken();

	virtual int get_api_type();
    virtual void set_api_type(int api_mode_type);

    virtual void set_currency(const std::string &currency) { this->currency = currency; }
    virtual std::string get_currency(){ return currency; }

private:
    UserPlayingStatus game_playing_status;
    socket_id sid;
    _uint64   uid;
	_uint32   vir_uid;
    _uint32   client_id;
    _uint8    sex;
	int		  level;
	bool	  is_have_vir;
	int		  chat_time;
	int       country_code;

    char      head_img[512];
    char      nick_name[64];    
    char      ip_addr[64];
	char	  sign_info[512];
	char	  positioninfo[128];
    
    char      version[32]; 
    _uint32   clt_ver;
    
    _uint8    chair_id;
    _uint8    status;
    _uint8    offline;
	_uint8	  temp_leave;

	int	      node_id;
    int       score;
    int       table_id; 
	int		  last_table_id;
    _uint64   card_count;
	_uint64   m_gold;
	_uint64   m_add_bill;

	int       m_plat_type;
	int		  m_user_type;
	int		  m_sys_type;
	int		  m_standup_time;
	bool	  m_end_clear;

	int       m_user_result;
	int		  m_add_num;

	int		  m_longitude;
	int       m_latitude;
	char      m_site[512];
	int		  m_control_time;

	_uint64   change_gold;
	_uint64	  last_gold;
	_uint64	  last_bank_gold;
	_uint64	  enter_time;

	int       is_single_post;
	int		  m_play_count;

	int       api_type;  // 0是单一钱包，1是转账钱包

    std::string currency = "PHP";
};

#endif

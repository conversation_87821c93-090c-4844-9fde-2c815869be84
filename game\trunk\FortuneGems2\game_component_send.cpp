#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "server.pb.h"
#include "fgp.pb.h"
#include "google/protobuf/text_format.h"
#include "google/protobuf/util/json_util.h"
#include "google/protobuf/util/message_differencer.h"
#include "google/protobuf/message.h"
#include "google/protobuf/descriptor.h"

#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <fstream>
#include <cmath>
#include <array>

#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/buffer.h>
// AES 密钥长度（256-bit = 32 字节）
constexpr size_t AES_KEY_SIZE = 32;
constexpr size_t AES_BLOCK_SIZE = 16;

std::string base64Encode(const std::vector<unsigned char>& data) 
{
    BIO* bio, * b64;
    BUF_MEM* bufferPtr;
    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);
    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);  // 不加换行符
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);
    std::string encoded(bufferPtr->data, bufferPtr->length);
    BIO_free_all(bio);
    return encoded;
}

// AES-CBC 加密函数
std::vector<unsigned char> encryptAESCBC(const std::string& plaintext, const std::string& token) 
{
    std::vector<unsigned char> iv(AES_BLOCK_SIZE);
    RAND_bytes(iv.data(), AES_BLOCK_SIZE);  // 生成随机 IV

    // 截取 token 前 32 字节作为 key
    std::string keyString = token.substr(0, AES_KEY_SIZE);
    std::vector<unsigned char> key(keyString.begin(), keyString.end());

    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) throw std::runtime_error("Failed to create context");

    if (1 != EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key.data(), iv.data()))
        throw std::runtime_error("EncryptInit failed");

    std::vector<unsigned char> ciphertext(plaintext.size() + AES_BLOCK_SIZE);
    int len = 0;
    int ciphertext_len = 0;

    if (1 != EVP_EncryptUpdate(ctx, ciphertext.data(), &len,
                               reinterpret_cast<const unsigned char*>(plaintext.data()),
                               plaintext.size()))
        throw std::runtime_error("EncryptUpdate failed");

    ciphertext_len += len;

    if (1 != EVP_EncryptFinal_ex(ctx, ciphertext.data() + ciphertext_len, &len))
        throw std::runtime_error("EncryptFinal failed");

    ciphertext_len += len;
    ciphertext.resize(ciphertext_len);  // 截取实际密文长度

    EVP_CIPHER_CTX_free(ctx);

    // 合并 IV + 密文
    std::vector<unsigned char> result;
    result.insert(result.end(), iv.begin(), iv.end());
    result.insert(result.end(), ciphertext.begin(), ciphertext.end());

    return result;
}

std::string base64_decode(const std::string &encoded_string)
{
	// Base64字符集
	static const std::string base64_chars =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		"abcdefghijklmnopqrstuvwxyz"
		"0123456789+/";

	auto is_base64 = [](unsigned char c) -> bool
	{
		return (isalnum(c) || (c == '+') || (c == '/'));
	};

	// 清除空格等非法字符
	std::string clean_input;
	for (auto c : encoded_string)
	{
		if (is_base64(c) || c == '=')
		{
			clean_input += c;
		}
	}

	int in_len = clean_input.size();
	int i = 0;
	int j = 0;
	int in_ = 0;
	unsigned char char_array_4[4], char_array_3[3];
	std::string ret;

	while (in_len-- && (clean_input[in_] != '=') && is_base64(clean_input[in_]))
	{
		char_array_4[i++] = clean_input[in_];
		in_++;
		if (i == 4)
		{
			for (i = 0; i < 4; i++)
			{
				char_array_4[i] = base64_chars.find(char_array_4[i]);
			}

			char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
			char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
			char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

			for (i = 0; i < 3; i++)
			{
				ret += char_array_3[i];
			}
			i = 0;
		}
	}

	if (i)
	{
		for (j = i; j < 4; j++)
		{
			char_array_4[j] = 0;
		}

		for (j = 0; j < 4; j++)
		{
			char_array_4[j] = base64_chars.find(char_array_4[j]);
		}

		char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
		char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
		char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

		for (j = 0; j < i - 1; j++)
		{
			ret += char_array_3[j];
		}
	}

	return ret;
}

bool compare_graph(const fgpProto::SpinAck &g1, const fgpProto::SpinAck &g2, std::string &diff)
{
	bool equal = true;

	google::protobuf::util::MessageDifferencer differencer;
	differencer.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
	// differencer.set_treat_nan_as_equal();

	const google::protobuf::FieldDescriptor *field = fgpProto::SpinAck::descriptor()->FindFieldByName("NextRow");
	if (field != nullptr)
	{
		differencer.IgnoreField(field);
	}

	std::string d = "";
	differencer.ReportDifferencesToString(&diff);
	if (!differencer.Compare(g1, g2))
	{
		equal = false;
		return false;
	}

	diff = "equal";
	return equal;
}

void CGameComponent::send_data(IUser *pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *pData)
{

	if (pUser)
		m_table->send(pUser, mcmd, scmd, pData);
	else
		m_table->send_user_batch(mcmd, scmd, pData);
}

void CGameComponent::send_bet_roll_err_result(IUser *user, serverProto::AckType type, serverProto::Error err,
											  const std::string &token, const std::string &msg, void *data, size_t size)
{
	serverProto::GaiaResponse response;
	response.set_type(type);
	response.set_ret(err);
	response.set_errormsg(msg);
	response.set_token(token);
	auto encrypted = encryptAESCBC("", token);
	response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());

	std::string content = response.SerializeAsString();

	svrlib::socket_id sid = user->get_socket_id();
	if (m_table)
	{
		m_table->send_http(sid, content);
	}
	else
	{
		MY_LOG_ERROR("user[%d] invalid table", user->get_user_id());
	}
}

void encodeACKProto(fgpProto::SpinAck &ack, const user_game_info &game_info)
{
	ack.set_totalwin(std::round(game_info.result) / 100.0);
	
	for(int i = 0; i < 3; i++) 
	{
		fgpProto::Column *col = ack.add_platesymbol();
		col->add_col(game_info.plate_data[0+i]);
		col->add_col(game_info.plate_data[3+i]);
		col->add_col(game_info.plate_data[6+i]);
	}

	int bs = game_info.plate_data[9];	
	fgpProto::Column *col = ack.add_platesymbol();

	vector<int> vec_multer;
	if(game_info.cur_mode == 1)
	{
		for (int i = BOX_X2; i <= BOX_WHEEL; i++)
		{
			if (bs != i)
			{
				vec_multer.push_back(i);
			}
		}
	}
	else
	{
		for (int i = BOX_X1; i <= BOX_WHEEL; i++)
		{

			if (bs != i)
			{
				vec_multer.push_back(i);
			}
		}
	}
	
	int r = getRand(0,vec_multer.size()-1);
	col->add_col(vec_multer[r]);
	col->add_col(bs);
	int bs1 = vec_multer[r];
	vec_multer.clear();
	if(game_info.cur_mode == 1)
	{
		for (int i = BOX_X2; i <= BOX_WHEEL; i++)
		{
			if (bs != i && bs1 !=i)
			{
				vec_multer.push_back(i);
			}
		}
	}
	else
	{
		for (int i = BOX_X1; i <= BOX_WHEEL; i++)
		{
			if (bs != i && bs1 !=i)
			{
				vec_multer.push_back(i);
			}
		}
	}
	r =getRand(0,vec_multer.size()-1);
	col->add_col(vec_multer[r]);

	int line_win = 0;
	for(const auto& aw:game_info.vec_award)
	{
		fgpProto::AwardData *awarddata = ack.add_awarddatavec();
		awarddata->set_symbol(aw.symbol);
		awarddata->set_line(aw.line);
		awarddata->set_win(std::round(aw.win)/10000.0);
		line_win += aw.win;
	}

	//MY_LOG_PRINT("line_win = %d game_info.bs = %d", line_win, game_info.bs);
	ack.set_linemult(game_info.bs);
	ack.set_linewin(std::round(line_win)/10000.0);
	
	if(bs == BOX_WHEEL)
	{
		int odd = game_info.plate_data[10]/100.0;
		int wm = 1;
		if(game_info.cur_mode == 1)
		{
			wm = game_info.plate_data[11];
		}
		ack.set_wheelodd(odd);
		ack.set_wheelwin(odd*wm*game_info.bet/100.0);
		ack.set_awardtypeflag(2);
		ack.set_isbonus(true);
		if(game_info.cur_mode == 1)
			ack.set_wheelmult(wm);
		else
			ack.set_wheelmult(1);
		if (line_win > 0)
		{
			ack.set_awardtypeflag(3);
		}
	}
	else
	{
		if (game_info.result > 0)
		{
			ack.set_awardtypeflag(1);
		}
		else
		{
			ack.set_awardtypeflag(0);
		}
	}
}

void encodeACKProto_test(fgpProto::SpinAck &ack, const user_game_info &game_info)
{
	// ack.set_platewin(100);
	// ack.set_rtp(0.96);

	// int test_data[5];
	// test_data[0] = 4;
	// test_data[1] = 3;
	// test_data[2] = 1;
	// test_data[3] = BOX_SCATTER;
	// test_data[4] = 500;
	// //normal
	// mcProto::PlateInfoDetail *info = ack.add_plateinfo();
	// for(int i=0; i<4;i++)
	// 	info->add_platesymbol(test_data[i]);
	// info->add_order(0);
	// info->add_order(1);
	// if(game_info.cur_mode == 0)
	// 	info->add_order(-1);
	// else
	// 	info->add_order(2);
	// info->set_platewin(100);

	// //respin
	// if(0)//game_info.hasRespin)
	// {
	// 	mcProto::PlateInfoDetail *info = ack.add_plateinfo();
	// 	for (int i = 0; i < 4; i++)
	// 		info->add_platesymbol(game_info.respin_data[i]);
	// 	info->add_order(0);
	// 	info->add_order(1);
	// 	if (game_info.cur_mode == 0)
	// 		info->add_order(-1);
	// 	else
	// 		info->add_order(2);
	// 	info->set_platewin(game_info.respin_platewin);
	// }
	// ack.set_totalwin(600);
	// int award_type = 1;
	// int tmp = test_data[3];
	// if (tmp == BOX_ALL)
	// {
	// 	award_type = 9;
	// }
	// else if (tmp == BOX_X2)
	// {
	// 	award_type = 2;
	// }
	// else if (tmp == BOX_X5)
	// {
	// 	award_type = 3;
	// }
	// else if (tmp == BOX_X10)
	// {
	// 	award_type = 4;
	// }
	// else if (tmp == BOX_SCATTER)
	// {
	// 	award_type = 5;
	// }
	// ack.set_wheelwin(500);
	// ack.set_awardtypeflag(award_type);
	
}

void CGameComponent::send_bet_roll_result(IUser *user, std::string msg, bool is_suc)
{
	MY_LOG_DEBUG("send_bet_roll_result");	
	serverProto::SpinResponse spin;

	serverProto::ServiceData *service = spin.mutable_service();
	serverProto::freeSpinList *freeSpinList= service->mutable_freeremainv2();
	auto uid = user->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		MY_LOG_ERROR("user[%d] game info is empty", user->get_user_id());
		return;
	}

	auto gold = user->get_gold();
	spin.set_roundindexv2(iter->second.round_index_v2);
	spin.set_postmoney(std::round(gold) / 100.0);
	spin.set_totalwin(std::round(iter->second.result)/ 100.0);
	//spin.set_totalwin(std::round(iter->second.result)/ 100.0);
	spin.set_hasspin(true);
	spin.set_basebet(std::round(iter->second.bet) / 100.0);
	spin.set_realbet(std::round(iter->second.bet) / 100.0);
	serverProto::SpinReq *req = spin.mutable_spinreq();
	req->set_bet(std::round(iter->second.bet) / 100.0);
	req->mutable_special();
	fgpProto::SpinAck ack;
	encodeACKProto(ack, iter->second);
	//encodeACKProto_test(ack, iter->second);

	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = true;
	//google::protobuf::util::MessageToJsonString(ack, &json_string, options);

	fgpProto::SpinAck demo;

	if(0)
	{
		static int i = 36;
		MY_LOG_PRINT("第%d个图形", i + 1);
		string dd;
		myredis::GetInstance()->get_graph_from_redis_by_index("fgp_reply", dd, i, 3);
		// i++;
		// if (i > 1000)
		// {
		// 	i = 0;
		// }
		std::string decoded = base64_decode(dd);

		demo.ParseFromString(decoded);
	}

	std::string *d = spin.mutable_data();
	if(0)
		demo.SerializeToString(d);
	else
		ack.SerializeToString(d);
	//
	json_string.clear();
	// google::protobuf::util::MessageToJsonString(spin, &json_string, options);
	// MY_LOG_PRINT("spin = %s", json_string.c_str());

	string data;
	spin.SerializeToString(&data);

	serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::spin);
	if(iter->second.token.length() >= AES_KEY_SIZE)
	{
		std::vector<unsigned char> encrypted = encryptAESCBC(data, iter->second.token);
		// string str = base64Encode(encrypted);
		// MY_LOG_PRINT("str = %s", str.c_str());

		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
		json_string.clear();

		response.set_token(iter->second.token);
		//google::protobuf::util::MessageToJsonString(response, &json_string, options);
		//MY_LOG_PRINT("response = %s", json_string.c_str());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content = "";
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
	MY_LOG_DEBUG("user[%d] send spin response ok with content length: %d total win: %.3f post_money: %.3f",
				 uid, content.length(), spin.totalwin(), spin.postmoney());
}

void CGameComponent::test_bet_roll_result(const user_game_info &game_info, int i)
{
	fgpProto::SpinAck ack;
	encodeACKProto(ack, game_info);

	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = false;

	google::protobuf::util::MessageToJsonString(ack, &json_string, options);

	std::remove("test.txt");
	std::ofstream ofs;
	ofs.open("test.txt", ios::out | ios::app);
	ofs << json_string;

	string dd;
	//myredis::GetInstance()->get_graph_from_redis_by_index("ge_reply", dd, i, 3);
	std::string decoded = base64_decode(dd);
	fgpProto::SpinAck demo;
	demo.ParseFromString(decoded);

	string diff;
	if (!compare_graph(ack, demo, diff))
	{
		MY_LOG_PRINT("graph%d diff = %s", i + 1, diff.c_str());
	}
	else
	{
		MY_LOG_PRINT("graph%d equal", i + 1);
	}

	ofs.close();
}

void CGameComponent::send_room_info(IUser *pUser)
{
}

void CGameComponent::send_my_user_info(IUser *pUser)
{
}

void CGameComponent::send_game_info_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::GameInfoAck ack;
	serverProto::WalletInfo *wallet = ack.add_walletinfo();
	int64_t gold = user->get_gold();
	wallet->set_coin(std::round(gold) / 100);

    for (int i = 0; i < MAX_BET_CONFIG_COUNT; i++)
	{
		wallet->add_bet(bet_config[i]);
	}

	wallet->set_currencynumber(33);
	//wallet->set_currencyname("PKR");
	//wallet->set_currencysymbol("Rs");
	wallet->set_unit(1);
	wallet->set_ratio(10);
	wallet->set_rate(0.2);
	wallet->set_decimal(4);

	ack.set_maxodd(375);
	ack.set_freespintype(-1);
	serverProto::freeSpinList *freespin = ack.mutable_freespin();

	fgpProto::GameInfoData gameinfo;
	gameinfo.add_mul(1);
	gameinfo.add_mul(1.5);
	for (int i = 0; i < WHEEL_ODDS_CONFIG_COUNT; i++)
	{
		gameinfo.add_wheelodds(wheel_odds_config[i]);
	}

	std::string *d = ack.mutable_extrainfo();
    gameinfo.SerializeToString(d);


    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::info);	
	string data;
	ack.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}


void CGameComponent::send_notice_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::NoticeInfo info;
	
	int64_t gold = user->get_gold();
	info.set_type(0);
	info.set_value(0);
	info.set_currencynumber(33);

    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::notice);	
	string data;
	info.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}

void CGameComponent::send_setting_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::JiliJpSetting setting;
	
    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::jilijpSetting);	
	string data;
	setting.SerializeToString(&data);
	MY_LOG_DEBUG("data = %s", data.c_str());

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}
﻿
#include "game_frame.h"
#include "log_manager.h"
#include "user_manager.h"

#include "table_Hundred.h"
#include "table_manager.h"
#include "robot_manager.h"

#include "room_route_challenge.h"
#include "cmd_net.h"
#include "room_route.h"
#include "kw.h"
#include "client_cmd.h"
#include "data_pool.h"
#include "redis_manager.h"
#include "my_redis.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "room_route_lobby.h"
#include "room_bill_fun.h"
#include "web_request.h"

#ifdef USE_GAME_DLL
#include "i_game_config.h"
#else
#include "game_component.h"
#endif

#include "dbproxy_data.h"
#include "common.h"
#include "server.pb.h"


static UserManager *m_user_mgr = UserManager::GetInstance();

CTableHundred::CTableHundred()
{   
	m_match_sn = 0;
    m_game_com = 0;
	m_table_id = 0;
	m_node_id  = -1;
	m_max_player = 10000;
	m_room_result = 0;
	m_room_result_time = time(0);
	m_now_user_count = 0;
}

CTableHundred::~CTableHundred()
{
    if (m_game_com)
    {
#ifdef USE_GAME_DLL
        m_game_dll.DeleteInstance();
#else
        delete m_game_com;
#endif
        m_game_com = 0;
    }
}


int CTableHundred::on_recv(_uint8 scmd, const void* pData, int size, IUser* pUser)
{
    if (!pUser)
    {
        MY_LOG_ERROR("Table recv param is error.");
        return -1;
    }

    m_recv_data = pData;
    m_recv_len = size;
    m_puser = pUser;

	//聊天和发表情要限制间隔
	if (scmd == cmd_net::SUB_LOOK_AVATAR_REQ || scmd == cmd_net::SUB_CHAT_MSG_REQ)
	{
		if (time(0) - pUser->get_chat_time() < config_manager::GetInstance()->get_chat_distance())
		{
			//const char* tips = "请不要频繁发送聊天信息";
			const char* tips = get_error_msg(MSG_ROOM_DO_NOT_SEND_MSG_FREQUENTLY, pUser->get_user_country());

			send_common_msg(pUser->get_chair_id(), tips, strlen(tips), cmd_room::ROOM_NOMAL_TIPS);
			return 0;
		}
		pUser->set_chat_time();
	}

	switch (scmd)
	{
	case cmd_net::SUB_LOOK_AVATAR_REQ:
		on_look_avatar(pUser, pData, size);
		break;
	case cmd_net::SUB_CHAT_MSG_REQ:
		on_chat(pUser, pData, size);
		break;
	case cmd_net::SUB_USER_GPS_UPLOAD:
		//on_upload_gps(pData, size);
		break;
	case cmd_net::SUB_USER_USE_PROPS_CARD_REQ:
		on_user_use_props_card(pUser, pData, size);
		break;
	case cmd_net::SUB_USER_PROPS_SEARCH_REQ:
		on_user_props_search(pUser, pData, size);
		break;
	case cmd_net::SUB_USER_BAG_MESSAGE_SEARCH_REQ:
		on_user_bag_message_search(pUser, pData, size);
		break;
	case cmd_net::SUB_USER_BAG_STATUS_SEARCH_REQ:
		on_user_bag_red_point_status_search(pUser, pData, size);
		break;
	case cmd_net::SUB_USER_BAG_CHANGE_PAGE_REQ:
		on_user_bag_change_tab_page(pUser, pData, size);
		break;
	default:
		break;
	}
    return 0;
}

int CTableHundred::on_upload_gps(const void *pdata, int size)
{
	_uint8 chair_id = m_puser->get_chair_id();
	if (chair_id == INVALID_CHAIR)
	{
		MY_LOG_ERROR("User is not on table chair. uid=%u", m_puser->get_user_id());
		return -1;
	}

	cmd_room::GPSUpload gpsUpload;    

	if (gpsUpload.unpack(pdata, size))
	{
		cmd_room::GPSPush   gpsPush;

		MY_LOG_DEBUG("User upload gps position.uid=%u longitude=%d latitude=%d"
			, m_puser->get_user_id(), gpsUpload.get_Longitude(), gpsUpload.get_Latitude());

		m_puser->set_gps(gpsUpload.get_Longitude(), gpsUpload.get_Latitude());
		m_puser->set_site(gpsUpload.get_site(), gpsUpload.site_length());

		gpsPush.set_ChairID(chair_id);
		gpsPush.set_Longitude(m_puser->get_longitude());
		gpsPush.set_Latitude(m_puser->get_latitude());
		gpsPush.set_site(gpsUpload.get_site(), 0);
		gpsPush.set_Uid(m_puser->get_user_id());
		//send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_USER_GPS_PUSH, &gpsPush);

		return 0;
	}

	return -99;
}

int CTableHundred::on_game_recv(_uint8 scmd, const void* pData, int size, IUser* pUser)
{
    if (m_game_com)
    {
        return m_game_com->on_recv(cmd_net::CMD_GAME, scmd, pData, size, pUser);
    }
    return 0;
}

int CTableHundred::init(const void * rule, int rule_len/* = 0*/)
{
    int ret = 0;

    if (m_game_com == 0)
    {
#ifdef USE_GAME_DLL
        MY_LOG_DEBUG("Use game dll. name=[%s]", g_pstGameConfig->get_dll_name());

        DLL_MOBULE_INIT(m_game_dll, g_pstGameConfig->get_dll_name(), "_create_component_module", "_free_component_module");        

        m_game_com = m_game_dll.GetInterface();        
#else
        m_game_com = new CGameComponent();
#endif

        if (!m_game_com)
        {
            MY_LOG_ERROR("new game component fail.");
            return -10;
        }       
    }

    do 
    {
		if (0 != m_game_com->init(this, data_pool::GetInstance(), NULL, rule, rule_len))
		{
			MY_LOG_ERROR("init game com fail");
			ret = -12;
			break;
		}          

    } while (0);

    if (ret)
    {
#ifdef USE_GAME_DLL
        m_game_dll.DeleteInstance();            
#else
        delete m_game_com;
#endif
        m_game_com = 0;
        return ret;
    }  

	m_game_props = game_props::GetInstance();	

    //初始化redis房间输赢
    //char op_type[64] = { 0 };
	//sprintf(op_type, CUR_ROOM_GOLD_STATISTIC, config_manager::GetInstance()->get_game_id(), m_node_id);
    //myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA,     op_type, 0);
	
	//读取房间输赢
	//query_room_gold_statistic(config_manager::GetInstance()->get_game_id(), m_node_id);
    return 0;
}

void CTableHundred::reset_game_rule(string rule)
{
	if (m_game_com != NULL)
		m_game_com->reset_game_rule(rule.c_str(), rule.length());
}

void CTableHundred::check_robot_gold(int uid, int min_gold, int max_gold)
{
	IUser *user = m_user_mgr->get_user(uid);
	if (user == NULL || user->get_user_type() != ROBOT_TYPE)
	{
		return;
	}

	robot_manager::GetInstance()->check_robot_gold(uid, min_gold, max_gold);
}

void CTableHundred::on_user_game_info(IUser *puser)
{
	if (m_game_com)
	{
		m_game_com->on_user_game_info(puser);
	}
}

void CTableHundred::on_look_avatar(IUser *pUser, const void *pdata, int size)
{
	cmd_room::LookAvatarReq stReq;
	cmd_room::LookAvatarResp stRsp;

	if (stReq.unpack(pdata, size))
	{
		stRsp.set_Code(stReq.get_Code());
		stRsp.set_ToChairId(stReq.get_ToChairId());
		stRsp.set_SendChairId(pUser->get_chair_id());
		stRsp.set_Uid(pUser->get_user_id());
		stRsp.set_ToUid(stReq.get_ToUid());
		send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_LOOK_AVATAR_RESP, &stRsp);
	}
}

int CTableHundred::on_chat(IUser *pUser, const void *pdata, int size)
{
	cmd_room::TableChatReq stReq;
	cmd_room::TableChatRsp stRsp;


	if (stReq.unpack(pdata, size) && stReq.Msg_length() > 0)
	{
		stRsp.set_ChairID(pUser->get_chair_id());
		stRsp.set_Type(stReq.get_Type());
		stRsp.set_uid(pUser->get_user_id());
		stRsp.set_head(pUser->get_head_img(), 0);

		if (stReq.get_Type() == cmd_room::TCHAT_MSG)
		{
			/* 文字聊天屏蔽敏感字、词 */
			int  iGbk = 0;
			char szGBK[1024] = {0};           

			_utf8_to_gbk(stReq.get_Msg(), stReq.Msg_length(), szGBK, sizeof(szGBK) - 1);

			if (-1 == kw_hexie(szGBK, &iGbk))
			{
				char szUTF8[512 + 1] = {0};
				_gbk_to_utf8(szGBK, iGbk, szUTF8, sizeof(szUTF8) - 1);
				stRsp.set_Msg(szUTF8, 0);
			}
			else
			{
				stRsp.set_Msg(stReq.get_Msg(), stReq.Msg_length());
			}
		}
		else
		{            
			stRsp.set_Msg(stReq.get_Msg(), stReq.Msg_length());
		}

		send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_CHAT_MSG_RSP, &stRsp);

		return 0;
	}

	return -1;
}

void CTableHundred::on_user_use_props_card(IUser* pUser, const void* pdata, int size)
{
	MY_LOG_DEBUG(">>> start on_user_use_props_card.");

	cmd_room::UserUsePropsCardReq stReq;
	cmd_room::UserUsePropsCardRsp stRsp;

	if (pUser == NULL)
	{
		MY_LOG_DEBUG("pUser null.");
		return;
	}

	int uid = pUser->get_user_id();

	stReq.unpack(pdata, size);
	
	int props_card_numbers = stReq.get_props_card_numbers();	
	int props_card_count = stReq.get_props_card_count();

	int per_game_id = config_manager::GetInstance()->get_game_id();
	int game_id = m_game_props->get_props_game_id(props_card_numbers);

	//int consume = m_game_props->get_user_game_props_data(uid, props_card_numbers, CUR_PROPS_CONSUME);

	int cur_props_card_count = m_game_props->get_user_game_props_data(uid, props_card_numbers, CUR_PROPS_NUMBER);

	int result = 0;

	stGameProps tmpGamePropsConfig;
	if (!m_game_props->get_game_props_config_data(props_card_numbers, tmpGamePropsConfig))
	{
		stRsp.set_Msg(get_error_msg(MSG_PROPS_CARD_NO_EXIST, pUser->get_user_country()), 0);
		result = 1;
	}

	// 道具数量不足
	if (cur_props_card_count < tmpGamePropsConfig.props_card_config.consume_number)
	{
		MY_LOG_DEBUG(">>> spin card is no enough.uid:%d cur_props_card_count:%d consume_number:%d props_card_count:%d", uid, cur_props_card_count, tmpGamePropsConfig.props_card_config.consume_number, props_card_count);
		stRsp.set_Msg(get_error_msg(MSG_PROPS_CARD_NOT_ENOUGH, pUser->get_user_country()), 0);
		result = 2;
	}

	// 道具停止状态
	if (tmpGamePropsConfig.props_base_config.props_status == PROPS_STOPPED)
	{
		stRsp.set_Msg(get_error_msg(MSG_CONFIG_ERROR, pUser->get_user_country()), 0);
		result = 3;
	}

	int cur_freegame_times = m_game_props->get_user_free_data(uid, game_id, CUR_SLOT_FREE_TIMES);

	int cur_have_use_props = m_game_props->get_user_free_data(uid, game_id, CUR_SLOT_FREE_IF_USE_PROPS);

	MY_LOG_DEBUG("on_user_use_props_card props_card_numbers:%d uid:%d game_id:%d cur_have_use_props:%d cur_freegame_times:%d props_status:%d", props_card_numbers, uid, game_id, cur_have_use_props, cur_freegame_times, tmpGamePropsConfig.props_base_config.props_status);

	// 如果普通freegame未完成，则不能使用游戏卡道具
	if (cur_freegame_times > 0 || cur_have_use_props!=0)
	{
		stRsp.set_Msg(get_error_msg(MSG_PROPS_NOT_USE_BECAUSE_NORMAL_FREEGAME, pUser->get_user_country()), 0);
		result = 4;
	}

	int total_free_game = 0;

	if (result == 0)
	{
		// 能组合几次的游戏局数增长
		int multi_game = props_card_count / tmpGamePropsConfig.props_card_config.consume_number;
		multi_game = multi_game > 0 ? multi_game : 1;
		// 转化的免费游戏的总次数
		total_free_game = multi_game * tmpGamePropsConfig.props_card_config.game_number;
		// 真实消耗的免费卡片数量
		int total_consume_card = multi_game * tmpGamePropsConfig.props_card_config.consume_number;

		// 道具使用，则道具卡片和消息页有更新小红点
		m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_PROPS_CARD, 0);
		m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_MESSAGE, 0);

		cur_props_card_count = m_game_props->update_user_game_props_data(uid, props_card_numbers, CUR_PROPS_NUMBER, -total_consume_card);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_TIMES, total_free_game, true);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_BET, tmpGamePropsConfig.props_card_config.bet_level, false);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_IF_USE_PROPS, 1, false);
		m_game_props->update_user_game_props_data(uid, props_card_numbers, CUR_PROPS_CONSUME, total_consume_card);
		// 清理免费游戏数据
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_REWARD, 0, false);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_JEW, 0, false);
	}	

	MY_LOG_DEBUG("use props userID:%d  result:%d props_card_numbers:%d cur_props_card_count:%d free_jew:%d.", uid, result, props_card_numbers, cur_props_card_count, m_game_props->get_user_free_data(uid, game_id, CUR_SLOT_FREE_JEW));
	
	stRsp.set_result(result);
	stRsp.set_props_card_numbers(props_card_numbers);
	stRsp.set_props_card_count(cur_props_card_count);
	stRsp.set_game_id(game_id);
	stRsp.set_free_spin_count(total_free_game);
	stRsp.set_bet_money(tmpGamePropsConfig.props_card_config.bet_level);

	send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_USER_USE_PROPS_CARD_RSP, &stRsp);	
}

void CTableHundred::on_user_props_search(IUser* pUser, const void* pdata, int size)
{
	MY_LOG_DEBUG(">>> start on_user_props_search.");

	cmd_room::UserPropsSearchReq stReq;
	cmd_room::UserPropsSearchRsp stRsp;

	if (pUser == NULL)
	{
		MY_LOG_DEBUG("pUser null.");
		return;
	}

	int uid = pUser->get_user_id();

	stReq.unpack(pdata, size);

	int game_id = stReq.get_game_id();

	int per_game_id = config_manager::GetInstance()->get_game_id();

	vec_props props_type_members;

	// 查询道具，首页的道具碎片tab小红点就设置成已读
	m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_PROPS_SECTION, 1);

	m_game_props->get_user_props_list(uid, props_type_members);

	int total_num = props_type_members.size();	

	int count = 0;

	MY_LOG_DEBUG("玩家[%d]获取的道具[%d]:\n", uid, total_num);

	for (int i = 0; i < total_num; i++)
	{
		cmd_room::PropsDetailsInfo* temp = stRsp.props_details_list_item(count);

		int &props_number = props_type_members[i];

		int user_props_count = m_game_props->get_user_game_props_data(uid, props_number, CUR_PROPS_NUMBER);
		
		temp->set_props_numbers(props_number);
		temp->set_props_count(user_props_count);

		stGameProps props_data;	
		
		if (m_game_props->get_game_props_config_data(props_number, props_data))
		{		
			int props_game_id = props_data.props_base_config.game_id;
			// 如果不是查全部，则返回当前游戏的道具列表
			if (game_id > 0 && props_game_id != per_game_id)
			{
				MY_LOG_DEBUG("on_user_props_search per_game_id:%d props_game_id:%d", per_game_id, props_game_id);
				continue;
			}

			temp->set_props_type(props_data.props_base_config.props_type);			
			temp->set_game_id(props_game_id);
			temp->set_consume_count(props_data.props_card_section_config.consume_props_number);
			temp->set_props_color(props_data.props_base_config.color, 0);
			temp->set_props_avatar(props_data.props_base_config.avatar, 0);
			temp->set_props_subject(props_data.props_base_config.subject, 0);
			temp->set_props_description(props_data.props_base_config.description, 0);

			MY_LOG_DEBUG("count:%d id:%d game_id:%d type:%d color:[%s] avatar:[%s] subject:[%s] description:[%s]\n", count, temp->get_props_numbers(), temp->get_game_id(), temp->get_props_type(), temp->get_props_color(), temp->get_props_avatar(), temp->get_props_subject(), temp->get_props_description());
		}			
		
		++count;
		stRsp.set_props_details_list_item_count(count);
	}	

	stRsp.set_total_num(count);

	send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_USER_PROPS_SEARCH_RSP, &stRsp);
}

// 查询玩家背包消息
void CTableHundred::on_user_bag_message_search(IUser* pUser, const void* pdata, int size)
{
	MY_LOG_DEBUG(">>> start on_user_bag_message_search.");

	if (pUser == NULL)
	{
		MY_LOG_DEBUG("pUser null.");
		return;
	}

	int uid = pUser->get_user_id();
	int sid = pUser->get_socket_id();
	int lau = pUser->get_user_country();

	MY_LOG_DEBUG("on_user_bag_message_search uid(%d) sid(%d).", uid, sid);

	cmd_room::UserBagMessageSearchReq req;
	req.unpack(pdata, size);

	cmd_dbproxy::CDBProxyExceSection db_section;
	db_section.set_SID(sid);
	db_section.set_UserID(uid);
	db_section.set_wParamInt(lau);

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	json_req_value["i_uid"] = Json::Value(uid);
	json_req_value["i_gameid"] = Json::Value(req.get_game_id());
	json_req_value["i_page"] = Json::Value(req.get_Page());
	json_req_value["i_page_size"] = Json::Value(req.get_Page_size());
	json_req_value["i_max_count"] = Json::Value(100);

	MY_LOG_DEBUG("on_user_bag_message_search uid(%d) sid(%d)  gameid:%d,page:%d,pagesize:%d ", uid, sid, req.get_game_id(), req.get_Page(), req.get_Page_size());

	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(this, on_bag_message_record_startpage_dbproxy, uid, "bag_message_record_startpage_req", json_req_data.c_str(), db_section);
}

// 查询玩家背包红点状态信息
void CTableHundred::on_user_bag_red_point_status_search(IUser* pUser, const void* pdata, int size)
{
	MY_LOG_DEBUG(">>> start on_user_bag_red_point_status_search.");

	if (pUser == NULL)
	{
		MY_LOG_DEBUG("pUser null.");
		return;
	}

	int uid = pUser->get_user_id();

	cmd_room::UserBagRedpointStatusRsp stRsp;

	int section_status = m_game_props->get_user_red_point_data(uid, CUR_REDPOINT_PROPS_SECTION);
	int card_status = m_game_props->get_user_red_point_data(uid, CUR_REDPOINT_PROPS_CARD);
	int message_status = m_game_props->get_user_red_point_data(uid, CUR_REDPOINT_MESSAGE);

	MY_LOG_DEBUG("on_user_bag_red_point_status_search uid:%d section_status:%d card_status:%d message_status:%d", uid, section_status, card_status, message_status);

	stRsp.set_props_section_status(section_status);
	stRsp.set_props_card_status(card_status);
	stRsp.set_props_message_status(message_status);

	send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_STATUS_SEARCH_RSP, &stRsp);
}

// 玩家背包切换tab页
void CTableHundred::on_user_bag_change_tab_page(IUser* pUser, const void* pdata, int size)
{
	MY_LOG_DEBUG(">>> start on_user_bag_change_tab_page.");

	if (pUser == NULL)
	{
		MY_LOG_DEBUG("pUser null.");
		return;
	}

	cmd_room::UserBagChangeTabPageReq stReq;
	cmd_room::UserBagChangeTabPageRsp stRsp;

	stReq.unpack(pdata, size);

	int uid = pUser->get_user_id();

	int section_status = m_game_props->get_user_red_point_data(uid, CUR_REDPOINT_PROPS_SECTION);
	int card_status = m_game_props->get_user_red_point_data(uid, CUR_REDPOINT_PROPS_CARD);
	int message_status = m_game_props->get_user_red_point_data(uid, CUR_REDPOINT_MESSAGE);

	int page = stReq.get_tab_id();

	switch (page)
	{
	case TAB_SECTION:
	{
		section_status = 1;
		m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_PROPS_SECTION, section_status);
	}
	break;
	case TAB_CARD:
	{
		card_status = 1;
		m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_PROPS_CARD, card_status);
	}
	break;
	case TAB_MESSAGE:
	{
		message_status = 1;
		m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_MESSAGE, message_status);
	}
	break;
	default:
		break;
	}

	stRsp.set_tab_id(page);
	stRsp.set_props_section_status(section_status);
	stRsp.set_props_card_status(card_status);
	stRsp.set_props_message_status(message_status);

	send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_CHANGE_PAGE_RSP, &stRsp);
}

void CTableHundred::on_force_end(_uint64 uid)
{
	IUser *user = m_user_mgr->get_user(uid);
	if (user == NULL)
	{
		return;
	}

	m_game_com->on_forced_end(uid);

	//const char* tips = "您的账户异常, 强制退出房间";
	const char* tips = get_error_msg(MSG_ROOM_USER_ERROR, user->get_user_country());

	send_common_msg(user->get_chair_id(), tips, strlen(tips), cmd_room::ROOM_HUND_TICK);

	user->not_offline();
	user->set_user_status(cmd_room::USER_STATUS_NULL);
	CRoomRouteChallenge::send_web_user_leave(user->get_user_id());

	MY_LOG_DEBUG("CTableHundred release on_force_end....chairid:%d  uid:%d", user->get_chair_id(), m_game_users[user->get_chair_id()]);
	m_game_users[user->get_chair_id()] = 0;
	m_now_user_count--;
	m_user_mgr->delete_user(user->get_user_id());
}

int CTableHundred::get_vir_uid()
{
	return UserManager::GetInstance()->get_vir_uid();
}


IUser * CTableHundred::get_user_from_uid(_tint64 userID)
{
    return m_user_mgr->get_user(userID);
}

IUser * CTableHundred::get_user_from_chair(int chairID)
{
    if (chairID < 0 || chairID >= (int)m_game_users.size())
    {
        return NULL;
    }

    return m_user_mgr->get_user(m_game_users[chairID]);
}

_uint64 CTableHundred::get_uid_from_chair(int chairID)
{
    if (chairID < 0 || chairID >= (int)m_game_users.size())
        return 0;

    return m_game_users[chairID];
}


int CTableHundred::get_room_id()
{
	return CGameFrame::GetInstance()->get_room_id();
}

bool CTableHundred::on_start_game()
{
	MY_LOG_DEBUG("CTableHundred on_start_game...");

    if (m_playing)
        return false;

    m_playing = true;
	/*for (_uint32 i = 0; i < m_game_users.size(); i++)
	{
		if (m_game_users[i] == 0)
			continue;

		IUser* user = UserManager::GetInstance()->get_user(m_game_users[i]);
		if (user == NULL)
			continue;

		user->set_user_status(cmd_room::USER_STATUS_PLAY);
	}*/

	on_send_game_info();
	return true;
}

bool CTableHundred::on_end_game()
{
	MY_LOG_DEBUG("CTableHundred on_end_game...");
	m_playing = false;

	//清除断线用户
	for (_uint32 i = 0; i < m_game_users.size(); i++)
	{
		if (m_game_users[i] == 0)
			continue;

		IUser* user = UserManager::GetInstance()->get_user(m_game_users[i]);
		if (user == NULL)
			continue;
        if (!m_game_com->skip_set_user_status()) {
		    user->set_user_status(cmd_room::USER_STATUS_SIT_DOWN);
		}
		if (user->is_offline())
		{
		    if (m_game_com->is_can_clear_user(user->get_chair_id()))
		    {
				CClientCmd::do_update_user_vcoin(user->get_user_id(), user->get_gold(), 0,
					cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

				m_game_com->on_user_action(user, comm_action_leave);
				CRoomRouteChallenge::send_web_user_leave(m_game_users[i]);

				room_bill_fun::GetInstance()->write_hund_room_bill(user, RBT_LOGON_OUT, m_node_id);
				m_user_mgr->delete_user(m_game_users[i]);

				MY_LOG_DEBUG("CTableHundred release delete offline....chairid:%d  uid:%d", i, m_game_users[i]);
				m_game_users[i] = 0;
				m_now_user_count--;
		    }
		}
		else	
		if (user->get_user_type() == ROBOT_TYPE)
		{  
			int nLeave_min = 0;
			int nMaxGold = 0;
			stGoldNodeInfo* node_info = config_manager::GetInstance()->get_node_info(m_node_id);
			if (node_info && node_info->get_hundred_leave_min() > 0 && node_info->get_hundred_max_gold() > 0)
			{
				nLeave_min = node_info->get_hundred_leave_min();
				nMaxGold = node_info->get_hundred_max_gold();
			}
			else
			{
				nLeave_min = config_manager::GetInstance()->get_hundred_leave_min();
				nMaxGold = config_manager::GetInstance()->get_hundred_max_gold();
			}
			if (user->get_gold() <= nLeave_min ||
				user->get_gold() >= nLeave_min + nMaxGold)
			{
				if (m_game_com->need_check_gold(user))
				{
					robot_manager::GetInstance()->check_robot_gold(m_game_users[i], nLeave_min,
						nLeave_min + nMaxGold, config_manager::GetInstance()->get_game_id());
				}

				//校验机器人货币，先离开
				/*robot_manager::GetInstance()->push_robot(m_game_users[i]);
				m_game_com->on_user_action(user, comm_action_leave);
				CRoomRouteChallenge::send_web_user_leave(m_game_users[i]);
				m_game_users[i] = 0;
				m_now_user_count--;
				user->set_chair_id(INVALID_CHAIR);
				user->set_table_id(INVALID_TABLE);*/
			}

			//机器人刷新虚拟信息
			bool is_honor = m_game_com->check_is_Honor(m_game_users[i]);
			if (!is_honor)
			{
				int vir_uid = UserManager::GetInstance()->get_vir_uid();
				user->set_vir_uid(vir_uid);

				char str[64];
				sprintf(str, "%d", vir_uid);
				user->set_nick_name(str);
			}
			
		}
		else //防挂机
		if (user->get_user_type() != SUPER_USER_TYPE && 
			time(0) - user->get_control_time() >= config_manager::GetInstance()->get_hundred_tick_time() && 
			m_game_com->is_user_in_game(user->get_user_id()))
		{
			CClientCmd::do_update_user_vcoin(user->get_user_id(), user->get_gold(), 0,
				cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

			//const char tips[256] = { "您长时间未操作，自动离开房间" };
			const char *tips = get_error_msg(MSG_ROOM_NOT_OPERATE_TOO_LONG, user->get_user_country());

			send_common_msg(i, tips, strlen(tips), cmd_room::ROOM_HUND_TICK);
			tick_user(m_game_users[i]);
		}
	}

	/*HundredRobotItem* item = config_manager::GetInstance()->get_hundred_config();
	if (item != NULL)
	{
		int need = rand() % (item->end_num - item->begin_num) + item->begin_num;
		check_robot(need);
	}*/

	//flesh_robot();
	on_send_game_info();
    return 0;
}

void CTableHundred::on_send_game_info()
{
	if (config_manager::GetInstance()->m_room_status == ROOM_STATUS_PAUSE)
	{
		return;
	}

	//获取游戏内数据
	int status_time = 0;
	char data_buff[1024] = { 0 };
	int buff_len = m_game_com->get_game_info(data_buff, 1024, status_time);
	if (buff_len == 0)
		return;
	
	room_route::TableGameInfo info;
	info.set_gameid(config_manager::GetInstance()->get_game_id());
	info.set_nodeid(m_node_id);
	info.set_endtime(status_time);
	stGoldNodeInfo* node_info = config_manager::GetInstance()->get_node_info(m_node_id);
	if (node_info)
	{
		info.set_roomname(node_info->m_node_name.c_str(), 0);
	}
	info.set_data(data_buff, buff_len);

	map<int, int> node_usere_count;
	node_usere_count.clear();
	UserManager::GetInstance()->get_node_count(node_usere_count);
	info.set_online_num(get_user_count());
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_TABLE_GAME_INFO, &info);
}

void CTableHundred::flesh_all_user_gold()
{
	for (_uint32 i = 0; i < m_game_users.size(); i++)
	{
		if (m_game_users[i] == 0)
			continue;

		IUser* user = UserManager::GetInstance()->get_user(m_game_users[i]);
		if (user == NULL || user->get_user_type() == ROBOT_TYPE)
			continue;

		int gold = user->get_gold();
		CClientCmd::do_update_user_vcoin(m_game_users[i], gold, 0, cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);
	}
}

//刷新机器人
void CTableHundred::flesh_robot()
{
	//计算要更换的机器人
	int begin_rate  = config_manager::GetInstance()->get_robot_rate_begin();
	int end_rate    = config_manager::GetInstance()->get_robot_rate_end();
	int play_time   = config_manager::GetInstance()->get_limit_time();
	if (end_rate <= begin_rate)
		return;

	int rand_rate = rand() % (end_rate - begin_rate) + begin_rate;

	int uids[100] = { 0 };
	int uid_count = 0;
	m_game_com->get_change_robot_uid(uids, uid_count);
	if (uid_count == 0)
		return;
	
	int leave_count = uid_count * rand_rate / 100;
	if (leave_count <= 0)
		return;

	//只要换掉贵宾席的机器人,将数组打乱,将排前面的机器换掉即可
	int temp_uids[100] = { 0 };
	_uint8 cbRandCount = 0, cbPosition = 0;
	do
	{
		cbPosition = rand() % (uid_count - cbRandCount);
		temp_uids[cbRandCount++] = uids[cbPosition];
		uids[cbPosition] = uids[uid_count - cbRandCount];
		uids[uid_count - cbRandCount] = temp_uids[cbRandCount - 1];
	} while (cbRandCount < uid_count);
	
	for (int i = 0; i < leave_count; i++)
	{
		if (temp_uids[i] == 0)
			continue;

		IUser* user = UserManager::GetInstance()->get_user(temp_uids[i]);
		if (user == NULL)
			continue;

		if (user->get_user_type() == ROBOT_TYPE)
		{
			if (!m_game_com->is_can_clear_user(user->get_chair_id()))
				continue;
			
			workRobotInfo* info = robot_manager::GetInstance()->get_work_info(temp_uids[i]);
			if (info && time(0) - info->enter_time >= play_time)
			{
				//检查机器人是否在桌子上面呆的够久了，是的话，换一个放进去
				//先从空闲队列里取一个机器人
				//将当前机器人放回队列
				int robot_uid = robot_manager::GetInstance()->pop_robot(m_node_id);
				if (robot_uid != 0)
				{
					IUser* robot_user = UserManager::GetInstance()->get_user(robot_uid);
					if (robot_user)
					{
						robot_manager::GetInstance()->check_robot_gold(robot_uid, config_manager::GetInstance()->get_hundred_leave_min(),
							config_manager::GetInstance()->get_hundred_leave_min() + config_manager::GetInstance()->get_hundred_max_gold()
							, config_manager::GetInstance()->get_game_id());

						on_user_enter(robot_user);
					}
				}

				char last_pos[128] = { 0 };
				get_rand_postion(last_pos);
				user->set_position_info(last_pos, strlen(last_pos));
				MY_LOG_DEBUG("flesh_robot... uid:%u  rand_pos:%s", user->get_user_id(), last_pos);
				user->set_gold(0);
				robot_manager::GetInstance()->push_robot(temp_uids[i]);
				CClientCmd::do_update_user_vcoin(user->get_user_id(), user->get_gold(), 0,
					cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

				//用户离开桌子
				m_game_com->on_user_action(user, comm_action_leave);
				CRoomRouteChallenge::send_web_user_leave(temp_uids[i]);
				int chairid = get_chair_from_uid(temp_uids[i]);
				if (chairid != INVALID_CHAIR)
				{
					MY_LOG_DEBUG("CTableHundred release flesh_robot ....chairid:%d  uid:%d", chairid, m_game_users[chairid]);
					user->set_chair_id(INVALID_CHAIR);
					user->set_table_id(INVALID_TABLE);
					m_game_users[chairid] = 0;
					m_now_user_count--;
				}
			}
		}
	}
}

int CTableHundred::get_chair_from_uid(int uid)
{
	for (int i = 0; i < m_game_users.size(); i++)
	{
		if (m_game_users[i] == uid)
		{
			return i;
		}
	}
	return INVALID_CHAIR;
}

void CTableHundred::check_robot(int need_robot)
{
	//统计当前机器人数目
	int robot_count = 0;
	for (int i = 0; i < m_game_users.size(); i++)
	{
		IUser* user = UserManager::GetInstance()->get_user(m_game_users[i]);
		if (user == NULL)
			continue;

		if (user->get_user_type() == ROBOT_TYPE)
			robot_count++;
	}
	
	MY_LOG_DEBUG("CTableHundred delete check_robot need_robot:%d now_robot:%d", need_robot, robot_count);
	if (robot_count < need_robot)
	{
		//机器人不够，补
		for(int i = 0; i < need_robot - robot_count; i++)
		{
			int uid = robot_manager::GetInstance()->pop_robot();
			IUser* user = UserManager::GetInstance()->get_user(uid);
			if (user == NULL)
				continue;

			on_user_enter(user);
		}
	}
	else
	if (robot_count > need_robot)
	{
		//机器人太多，退
		int leave_count = robot_count - need_robot;
		for (int i = 0; i < m_game_users.size(); i++)
		{
			IUser* user = UserManager::GetInstance()->get_user(m_game_users[i]);
			if (user == NULL || user->get_user_type() != ROBOT_TYPE)
				continue;

			if (!m_game_com->is_can_clear_user(i))
				continue;

			robot_manager::GetInstance()->push_robot(m_game_users[i]);
			m_game_com->on_user_action(user, comm_action_leave);
			CRoomRouteChallenge::send_web_user_leave(m_game_users[i]);

			MY_LOG_DEBUG("CTableHundred release check_robot ....chairid:%d  uid:%d", i, m_game_users[i]);
			m_game_users[i] = 0;
			m_now_user_count--;
			user->set_chair_id(INVALID_CHAIR);
			user->set_table_id(INVALID_TABLE);
			MY_LOG_DEBUG("check_robot delete robot user uid:%lld chairid:%d", m_game_users[i], i);
			leave_count--;
			if (leave_count <= 0)
				break;
		}
	}
}

void CTableHundred::write_gold_bill(IUser * pUser, int gold, int type_id,int lose_rate)
{
	if (!pUser)
		return;

	bool bstatistics = pUser->get_user_type() != ROBOT_TYPE ? true : false;
	int last_gold = pUser->get_gold() - gold;
	if (type_id == GOLD_HANDRED_BET) //如果是百人场下注流水
	{
		last_gold = pUser->get_gold();
	}
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		pUser->get_client_id(),
		m_table_id,
		pUser->get_version(),
		pUser->get_user_id(),
		pUser->get_gold(),
		gold,
		last_gold,
		(GOLD_BILL_TYPE)type_id,
		m_node_id,
		pUser->get_user_type(),
		bstatistics,
		TYPE_HUNDRED,
		lose_rate);

}

void CTableHundred::write_gold_bill(int uid, int gold, int type_id, int lose_rate)
{
	IUser* user = UserManager::GetInstance()->get_user(uid);
	if (user == NULL)
	{   
		_tint64 now_gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	    write_gold_bill(uid, gold, now_gold, 0, "1.0.0", type_id, lose_rate);
	}
	else
	{
		write_gold_bill(uid, gold, user->get_gold(), user->get_client_id(), user->get_version(), type_id, lose_rate);
	}
}
//写用户金币流水
void CTableHundred::write_gold_bill(int uid, int gold, _uint64 now_gold, int client_id, const char version[16], int type_id, int lose_rate)
{   
	bool bstatistics = false;
	IUser* user = UserManager::GetInstance()->get_user(uid);
	int user_type = 0;
	if (user)
	{
		user_type = user->get_user_type();
		bstatistics = user->get_user_type() != ROBOT_TYPE ? true : false;
	}
	int last_gold = now_gold - gold;
	//if (type_id == GOLD_HANDRED_BET) //如果是百人场下注流水
	//{
	//	last_gold = now_gold;
	//	now_gold  = now_gold - abs(gold);
	//}
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		client_id, 
		0, 
		version, 
		uid, 
		now_gold, 
		gold, 
		last_gold,
		(GOLD_BILL_TYPE)type_id,
		m_node_id,
		user_type,
		bstatistics,
		TYPE_HUNDRED,
		lose_rate);
}

// 游戏写道具记录
void CTableHundred::write_prop_bill(int uid, int prop_id, int now_prop, int incre_prop, int client_id, const char version[16], const char param[64], int type, int sub_type, int props_type, int reward_props_id, int reward_number)
{
	IUser* user = UserManager::GetInstance()->get_user(uid);	
	if (!user)
	{
		return;
	}
	int last_prop = now_prop - incre_prop;
	BILL->write_prop_bill(
		config_manager::GetInstance()->get_game_id(),
		client_id,
		0,
		version,
		param,
		uid,
		now_prop,
		incre_prop,
		last_prop,
		prop_id,
		type,
		props_type,
		reward_props_id,
		reward_number,
		sub_type);
}

void CTableHundred::write_versus_bill(const char * data, int len)
{
	if(!data || len == 0) 
		return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, m_table_id, "mjq_write_versus_bill", data, db_section, false);

}

void CTableHundred::write_game_result(const char * data, int len, const char *issue, int issue_len)
{
	if (!data || len == 0)
		return;

	int game_id = config_manager::GetInstance()->get_game_id();

	Json::Value json_req_value;
	json_req_value["game_id"] = Json::Value(game_id);
	json_req_value["nodeid"] = Json::Value(m_node_id);
	json_req_value["issue_time"] = Json::Value(issue);
	json_req_value["str"] = Json::Value(data);

	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	MY_LOG_DEBUG("write_game_result data:%s", json_req_data.c_str());

	g_dbproxy_namager->post_exec(0, 0, m_table_id, "mjq_write_game_result", json_req_data.c_str(), db_section, false);
}


void CTableHundred::write_gamedata_bill(const char * data, int len)
{
	if(!data || len == 0) return;

	cmd_dbproxy::CDBProxyExceSection db_section;

	g_dbproxy_namager->post_exec(0, 0, m_table_id, "mjq_write_gamedata_bill", data, db_section, false);

}

void CTableHundred::write_game_record(const char * data, int len, const char* cmd) 
{
	if(!data || len == 0) return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	if (cmd == NULL)
	{
		g_dbproxy_namager->post_exec(0, 0, m_table_id, "mjq_write_game_record", data, db_section, false);
	}
	else
	{
		g_dbproxy_namager->post_exec(0, 0, m_table_id, cmd, data, db_section, false);
	}
}

int CTableHundred::add_user(_tint64 userID, int &chChairID)
{
	for (int i = 0; i < (int)m_game_users.size(); i++)
	{
		if (m_game_users[i] == userID)
		{
			chChairID = i;
			return 0;
		}
	}

	for (int i = 0; i < (int)m_game_users.size(); i++)
	{
		if (m_game_users[i] == 0)
		{
			m_game_users[i] = userID;
			chChairID  = i;
			break;
		}
	}

	if (chChairID == -1)
	{
		chChairID = m_game_users.size();
		m_game_users.push_back(userID);
	}
	MY_LOG_DEBUG("CTableHundred add_user uid:%lld  chChairID:%d", userID, chChairID);
	m_now_user_count++;
    return 0;
}

bool CTableHundred::set_timer(_uint8 timer_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat/* = 1*/)
{
	bool bRet = g_timer_mgr->set_timer(this
		, on_timer
		, TABLE_TIMER_ID(m_table_id, timer_id)
		, timeout_msec
		, param
		, repeat);

	if (!bRet)
	{
		MY_LOG_WARN("设置定时器 %d 时间 %u 毫秒 失败");
	}

	return bRet;
}

bool CTableHundred::kill_timer(_uint8 timer_id)
{
	return g_timer_mgr->kill_timer(TABLE_TIMER_ID(m_table_id, timer_id));
}

void CTableHundred::on_time_out(_uint8 timer_id, _uint64 param)
{    
	if (timer_id <= MAX_GAME_COMM_TIMERID && m_game_com)
		m_game_com->on_time(timer_id, param);
}

FTYPE(void) CTableHundred::on_timer(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay) {

	int table_id = TABLE_ID_FROM_TIMER_ID(timerID);
	_uint8 timer_id = LOGIC_ID_FROM_TIMER_ID(timerID);
	CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(table_id);
	if (ptable)
	{
		ptable->on_time_out(timer_id, param);
	}
}

int CTableHundred::on_user_enter(IUser *pUser)
{
	if (pUser == NULL)
		return -1;
	MY_LOG_DEBUG("CTableHundred on_user_enter uid:%u  ", pUser->get_user_id());
	if (m_now_user_count >= m_max_player)
		return -1;

	room_bill_fun::GetInstance()->write_hund_room_bill(pUser, RBT_LOGON_IN, m_node_id);

	int chairid = -1;
	add_user(pUser->get_user_id(), chairid);
	pUser->set_node_id(m_node_id);
		
	pUser->set_chair_id(chairid);
	pUser->set_table_id(m_table_id);
	pUser->set_control_time(time(0));
	myredis::GetInstance()->set_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_ROOM_NODE, m_node_id);

	if (pUser->get_user_type() != ROBOT_TYPE)
	{
		_uint32 temp_uid = pUser->get_user_id();
		if (pUser->get_good_uid() != 0)
			temp_uid = pUser->get_good_uid();

		char str[64];
		sprintf(str, "%d", temp_uid);
		pUser->set_nick_name(str);
	}

	if (pUser->get_user_type() == ROBOT_TYPE)
	{
		//为机器人添加虚拟UID，头像，昵称
		int vir_uid = UserManager::GetInstance()->get_vir_uid();
		pUser->set_vir_uid(vir_uid);

		char str[64];
		sprintf(str, "%d", vir_uid);
		pUser->set_nick_name(str);
		pUser->set_vir_level();
		pUser->set_have_virinfo(true);

		stRobotVirInfo info = { 0 };
		config_manager::GetInstance()->rand_robot_vir_info(info);
		pUser->set_head_image(info.face);
		MY_LOG_DEBUG("CTableHundred on_user_enter get vir info uid:%d face:%s nick_name:%s matchsn:%d", vir_uid, info.face, pUser->get_nick_name(),m_match_sn);

		/*stRobotVirInfo info;
		if (config_manager::GetInstance()->pop_robot_vir_info(info))
		{
			MY_LOG_DEBUG("pop_robot_vir_info. face=%s name=%s", info.face, info.name);
			pUser->set_head_image(info.face);
			pUser->set_nick_name(info.name);
			pUser->set_have_virinfo(true);
		}*/
		//将机器人放入工作队列
		robot_manager::GetInstance()->add_robot_work(pUser->get_user_id(), m_table_id);
	}

	m_game_com->on_user_action(pUser, comm_action_sit_down);
	return 0;
}

int CTableHundred::on_user_reconn(IUser *pUser)
{
	if (!pUser) {
		MY_LOG_DEBUG("invalid user");   
		return -1;
	}
    MY_LOG_DEBUG("User reconn. uid=%u ChairID=%d", pUser->get_user_id(), pUser->get_chair_id());   
    if (!pUser->is_offline())
    {
        MY_LOG_WARN("User multi reconn. uid=%u status=%d", pUser->get_user_id(), pUser->get_user_status());        
    }    

    pUser->not_offline();
    m_game_com->on_user_action(pUser, comm_action_online);
    return 0;
}

int CTableHundred::on_user_leave(IUser *pUser)
{
	MY_LOG_DEBUG("CTableHundred on_user_leave uid:%lld  ", pUser->get_user_id());
    if (!pUser)
        return -1;

	if (!m_game_com->is_can_clear_user(pUser->get_chair_id()))
	{
		if (!m_game_com->skip_set_user_status()) {
	        pUser->set_user_status(cmd_room::USER_STATUS_TEMP_LEAVE);
		}
		m_game_com->on_user_action(pUser, comm_action_leave);
		return -2;
	}

	m_game_com->on_user_action(pUser, comm_action_leave);
	room_bill_fun::GetInstance()->write_hund_room_bill(pUser, RBT_LOGON_OUT, m_node_id);

	CRoomRouteChallenge::send_web_user_leave(pUser->get_user_id());
	CClientCmd::do_update_user_vcoin(pUser->get_user_id(), pUser->get_gold(), 0, 
		cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

	int charid = pUser->get_chair_id();
	if (charid >= 0 && charid < (int)m_game_users.size())
	{
		MY_LOG_DEBUG("CTableHundred release on_user_leave ....chairid:%d  uid:%d", charid, m_game_users[charid]);
		m_game_users[charid] = 0;
		m_now_user_count--;
	}

	MY_LOG_DEBUG("CTableHundred on_user_leave  delete user  uid:%lld  charid:%d", pUser->get_user_id(), charid);
	m_user_mgr->delete_user(pUser);
    return 0;
}

int CTableHundred::on_user_offline(IUser *pUser)
{
	MY_LOG_DEBUG("CTableHundred on_user_offline uid:%lld  ", pUser->get_user_id());
    if (!pUser)
        return -1;

    if (!m_game_com->skip_set_user_status()) {
	    pUser->set_user_status(cmd_room::USER_STATUS_OFFLINE);
	}
	m_game_com->on_user_action(pUser, comm_action_offline);

	//游戏没开始，直接清除
	if (m_game_com->is_can_clear_user(pUser->get_chair_id()))
		on_user_leave(pUser);
	
    return 0;
}

void CTableHundred::update_user_gold_result(IUser* user) {

    MY_LOG_DEBUG("CTableHundred update_user_gold_result uid:%lld  ", user->get_user_id());
	m_game_com->on_user_action(user, comm_action_update_gold);
}

//1、流水和局数是两个条件，可以单独的进行配置 。
//流水数量：达到对应的流水数，有几率掉落碎片；例如设置成100，用户的投注流水总额达到100的时候，有多少几率掉落碎片；
//游戏局数：用户游戏回合数（不包含freegame）到达设定的局数，有几率掉落碎片；例如设置每10局有几率掉落碎片，判定条件则是10局为1个周期，无论碎片是否掉落；进入下一个周期的局数统计；
//2、也可以进行组合配置，在进行组和配置的时候同时满足两个条件，才会掉落碎片；

void CTableHundred::update_user_prop(_uint64 uid, int total_bet, int game_nums, bool free_game /*= false*/, int type_id /*= GOLD_HANDRED_BET*/)
{
	MY_LOG_DEBUG("update_user_prop start.uid(%d) total_bet:(%d) game_nums:(%d) free_game:(%d) type_id:(%d)", uid, total_bet, game_nums, free_game, type_id);
	IUser* user = UserManager::GetInstance()->get_user(uid);
	if (user == NULL)
	{
		MY_LOG_ERROR("update_user_prop user is null.uid(%d)", uid);
		return;
	}

	// 免费游戏则过滤
	if (free_game)
	{
		MY_LOG_ERROR("update_user_prop free_game.");
		return;
	}

	_tint64 nUserGold = user->get_gold();
	if (total_bet > nUserGold)
	{		
		MY_LOG_ERROR("update_user_prop uid=%d have no enough money.", uid);
		return;
	}

	int game_id = config_manager::GetInstance()->get_game_id();

	// 通过游戏id获取本游戏有哪些道具碎片和卡片
	int props_section_number = m_game_props->get_game_props_section_id(game_id);
	int props_card_number = m_game_props->get_game_props_card_id(game_id);

	// 通过碎片编号获取道具配置，得到碎片产出条件以及合成道具的条件
	stGameProps tmpGamePropsConfig;
	if (!m_game_props->get_game_props_config_data(props_section_number, tmpGamePropsConfig))
	{
		MY_LOG_DEBUG("get_game_props_config_data fail.props_section_number=%d", props_section_number);
		return;
	}

	int props_status = tmpGamePropsConfig.props_base_config.props_status;
	int props_type = tmpGamePropsConfig.props_base_config.props_type;

	// 产出需要的流水
	int amount = tmpGamePropsConfig.props_card_section_config.amount * 100;
	// 产出需要的游戏局数
	int game_number = tmpGamePropsConfig.props_card_section_config.game_number;
	// 产出道具数量
	int game_produce_props_number = m_game_props->get_rand_number(tmpGamePropsConfig.props_card_section_config.production_number, tmpGamePropsConfig.props_card_section_config.max_production_number);
	// 游戏产出的概率，最大为100
	int production_prob = tmpGamePropsConfig.props_card_section_config.production_prob;
	// 合成旋转卡时消耗此道具碎片数量
	int consume_props_number = tmpGamePropsConfig.props_card_section_config.consume_props_number;
	// 合成的道具ID
	int compound_props_id = tmpGamePropsConfig.props_card_section_config.compound_props_id;
	// 合成的道具数量
	int compound_props_number = m_game_props->get_rand_number(tmpGamePropsConfig.props_card_section_config.compound_props_number, tmpGamePropsConfig.props_card_section_config.max_compound_props_number);

	// 本碎片关闭或者不是碎片
	if (props_status == PROPS_STOPPED || props_type != cmd_room::PROPS_SECTION)
	{
		MY_LOG_ERROR("update_user_prop props_status:[%d] props_type:[%d].", props_status, props_type);
		return;
	}

	int total_bet_count = m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_BET_COUNT, total_bet);

	int total_game_runcount = m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_GAME_RUN_COUNT, 1);

	if (amount > 0 && total_bet_count < amount)
	{
		MY_LOG_ERROR("update_user_prop bet glod is not enough amount:[%d] total_bet_count:[%d].", amount, total_bet_count);
		return;
	}

	if (game_number > 0 && total_game_runcount < game_number)
	{
		MY_LOG_ERROR("update_user_prop total_game_runcount is not enough game_number:[%d] total_game_runcount:[%d].", game_number, total_game_runcount);
		return;
	}

	int rand_rate = rand() % 100;

	if (production_prob <= 0 || rand_rate > production_prob)
	{
		MY_LOG_ERROR("update_user_prop production_prob:[%d] rand_rate:[%d].", production_prob, rand_rate);
		if (total_game_runcount >= game_number)
		{
			// 已经达到掉落条件的游戏局数，临时数据清零，重新统计
			m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_GAME_RUN_COUNT, -total_game_runcount);
		}
		return;
	}

	int cur_game_props_count = m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_NUMBER, game_produce_props_number);

	MY_LOG_ERROR("update_user_prop drop props section cur_game_props_count:[%d] consume_props_number:[%d].", cur_game_props_count, consume_props_number);

	// 掉落了，有效下注金额会重新计算
	m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_BET_COUNT, -total_bet_count);
	// 已经达到掉落条件的游戏局数，临时数据清零，重新统计
	m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_GAME_RUN_COUNT, -total_game_runcount);

	int num = amount > 0 ? (amount / 100) : game_number;
	char props_param[64] = { 0 };
	sprintf(props_param, "%d,%d", num, game_produce_props_number);

	int sub_type = 0;
	if (amount > 0 && game_number > 0)
	{
		sub_type = cmd_room::BAG_SECTION_DROP_ALL;
		memset(props_param, 0, sizeof(props_param));
		sprintf(props_param, "%d,%d,%d", num, game_number, game_produce_props_number);
	}
	else if (amount > 0)
	{
		sub_type = cmd_room::BAG_SECTION_DROP;
	}
	else if (game_number > 0)
	{
		sub_type = cmd_room::BAG_SECTION_DROP_GAME;
	}

	// 记录碎片增加
	write_prop_bill(uid, props_section_number, cur_game_props_count, game_produce_props_number, user->get_client_id(), user->get_version(), props_param, type_id, sub_type, cmd_room::PROPS_SECTION);

	// 当前碎片达到可以合成的数量
	if (cur_game_props_count >= consume_props_number)
	{
		// 可合成的道具数量等分
		int canProduceCardRatio = cur_game_props_count / consume_props_number;
		// 合成道具消耗的碎片数量
		int consumeSectionCount = canProduceCardRatio * consume_props_number;
		//  可合成的道具数量
		int canProduceCardCount = canProduceCardRatio * compound_props_number;

		// 碎片减少，卡片增加
		int cur_props_section_remain_count = m_game_props->update_user_game_props_data(uid, props_section_number, CUR_PROPS_NUMBER, -consumeSectionCount);
		int cur_props_card_count = m_game_props->update_user_game_props_data(uid, props_card_number, CUR_PROPS_NUMBER, canProduceCardCount);

		char props_param[64] = { 0 };
		sprintf(props_param, "%d,%d", consumeSectionCount, canProduceCardCount);

		// 记录碎片减少
		write_prop_bill(uid, props_section_number, cur_props_section_remain_count, -consumeSectionCount, user->get_client_id(), user->get_version(), props_param, type_id, cmd_room::BAG_SECTION_COMPOUND, cmd_room::PROPS_SECTION, props_card_number, canProduceCardCount);
		// 记录卡片增加
		write_prop_bill(uid, props_card_number, cur_props_card_count, canProduceCardCount, user->get_client_id(), user->get_version(), props_param, type_id, cmd_room::BAG_SECTION_COMPOUND, cmd_room::PROPS_CARD);
		// 碎片合成，则道具碎片、道具卡片和消息页有更新小红点
		m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_PROPS_CARD, 0);
	} 

	// 碎片掉落，则道具碎片和消息页有更新小红点
	m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_PROPS_SECTION, 0);
	m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_MESSAGE, 0);
	// 通知获取到碎片
	cmd_room::PropsSectionDropOutMessage propsSectionDropOutMsg;
	propsSectionDropOutMsg.set_game_id(game_id);
	propsSectionDropOutMsg.set_props_section_numbers(props_section_number);
	propsSectionDropOutMsg.set_consume_props_count(consume_props_number);
	propsSectionDropOutMsg.set_props_count(cur_game_props_count);
	propsSectionDropOutMsg.set_produce_props_number(game_produce_props_number);
	propsSectionDropOutMsg.set_props_card_numbers(props_card_number);
	propsSectionDropOutMsg.set_compound_card_count(compound_props_number);
	char tips[1024] = { 0 };
	sprintf(tips, get_error_msg(MSG_PROPS_SECTION_DROP_OUT, user->get_user_country()), game_produce_props_number);
	propsSectionDropOutMsg.set_Msg(tips, sizeof(tips));
	send(user, cmd_net::CMD_ROOM, cmd_net::SUB_PROPS_SECTION_DROP_OUT_MESSAGE, &propsSectionDropOutMsg);	
}

void CTableHundred::update_user_props_Rewards(_uint64 uid, int gold, int type_id)
{
	IUser* user = UserManager::GetInstance()->get_user(uid);
	if (user == NULL)
	{
		MY_LOG_ERROR("update_user_props_Rewards user is null.uid(%d)", uid);
		return;
	}

	int game_id = config_manager::GetInstance()->get_game_id();

	int props_card_number = m_game_props->get_game_props_card_id(game_id);
	
	// 获取玩家消耗道具临时数据，>0表示当前使用了道具
	int consume = m_game_props->get_user_game_props_data(uid, props_card_number, CUR_PROPS_CONSUME);

	if (consume <= 0)
	{
		MY_LOG_DEBUG("update_user_props_Rewards user is not use props card.uid(%d) consume(%d)", uid, consume);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_IF_USE_PROPS, 0, false);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_REWARD, 0, false);
		m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_JEW, 0, false);
		return;
	}

	MY_LOG_DEBUG("update_user_props_Rewards gold:%d consume:%d", gold, consume);

	// 玩家消耗道具临时数据清零
	m_game_props->update_user_game_props_data(uid, props_card_number, CUR_PROPS_CONSUME, -consume);	
	// 清理免费游戏数据
	m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_IF_USE_PROPS, 0, false);
	m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_REWARD, 0, false);
	m_game_props->set_user_free_data(uid, game_id, CUR_SLOT_FREE_JEW, 0, false);

	// 道具使用结果，则消息页有更新小红点
	m_game_props->set_user_red_point_data(uid, CUR_REDPOINT_MESSAGE, 0);	

	char props_param[64] = { 0 };
	sprintf(props_param, "%d", gold);

	int cur_props_card_count = m_game_props->get_user_game_props_data(uid, props_card_number, CUR_PROPS_NUMBER);

	// 记录道具获得的总奖励分,旋转卡片减少
	write_prop_bill(uid, props_card_number, cur_props_card_count, -consume, user->get_client_id(), user->get_version(), props_param, type_id, cmd_room::BAG_PROPS_CARD_USE, cmd_room::PROPS_CARD, PROPS_REWARDS_GOLD, gold);
}

void CTableHundred::update_user_gold(_uint64 uid, int num, bool need_flesh, int type_id, int lose_rate)
{
	IUser* user = UserManager::GetInstance()->get_user(uid);
	if (user == NULL)
	{
		_tint64 now_gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
		if (now_gold + num < 0)
		{
			now_gold  = 0;
			myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, -now_gold);
		}
		else
		{
			now_gold += num;
			myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, num);
		}
		CClientCmd::do_update_user_to_server(uid, now_gold, cmd_sys::GAME_ROOM_GOLD);
		CClientCmd::do_update_user_vcoin(uid, now_gold, num, cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

		//写流水
		write_gold_bill(uid, num, now_gold, 0, "1.0.0", type_id, lose_rate);
	}	
	else
	{
		user->update_gold(num, need_flesh, true);
		write_gold_bill(uid, num, user->get_gold(), user->get_client_id(), user->get_version(), type_id, lose_rate);
	}
}

void CTableHundred::update_gold_and_clore(_uint64 uid, int num, bool need_flesh, int type_id, int lose_rate)
{
	MY_LOG_DEBUG("update_gold_and_clore num(%d) uid(%llu) type_id:%d", num, uid, type_id);
	if (num == 0)
		return;
 
 	IUser* user = UserManager::GetInstance()->get_user(uid);
 	if (user == NULL)
 	{
 		_tint64 now_gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
 		if (now_gold + num < 0)
 		{
 			MY_LOG_DEBUG("update_gold_and_clore now_gold(%d) + num(%d) < 0 uid(%llu)", now_gold, num, uid);
 
 			now_gold  = 0;
 			myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, -now_gold);
 		}
 		else
 		{
 			now_gold += num;
 			myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, num);
 		}
 		CClientCmd::do_update_user_to_server(uid, now_gold, cmd_sys::GAME_ROOM_GOLD);
 		CClientCmd::do_update_user_vcoin(uid, now_gold, num, cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);
 
 		//写流水
 		write_gold_bill(uid, num, now_gold, 0, "1.0.0", type_id, lose_rate);
 	}	
 	else
 	{
		bool is_tax = type_id == GOLD_DEL_TAX_REVENUE;
 		user->update_gold(num, need_flesh, true, is_tax);
 		write_gold_bill(uid, num, user->get_gold(), user->get_client_id(), user->get_version(), type_id, lose_rate);
 	}
}

void CTableHundred::update_gold_and_clore_single(_uint64 uid, int num, int type_id, int lose_rate) {
	
	MY_LOG_INFO("update user gold num(%d) uid(%llu) type_id:%d", num, uid, type_id);
	if (num == 0) {
		return;
	}

 	IUser* user = UserManager::GetInstance()->get_user(uid);
 	if (user == NULL) {
 		_tint64 now_gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
 		if (now_gold + num < 0) {
 			MY_LOG_DEBUG("update_gold_and_clore now_gold(%d) + num(%d) < 0 uid(%llu)", now_gold, num, uid);
 			now_gold  = 0;
 			myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, -now_gold);
 		} else {
 			now_gold += num;
 			myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, num);
 		}

 		//写流水
 		write_gold_bill(uid, num, now_gold, 0, "1.0.0", type_id, lose_rate);
 	} else {
		bool is_tax = type_id == GOLD_DEL_TAX_REVENUE;
 		user->update_gold(num, false, true, is_tax);
 		write_gold_bill(uid, num, user->get_gold(), user->get_client_id(), user->get_version(), type_id, lose_rate);
 	}
}

int CTableHundred::get_leave_min()
{
	return config_manager::GetInstance()->get_hundred_min_gold(m_node_id);
}

int CTableHundred::get_leave_max()
{
	return config_manager::GetInstance()->get_hundred_max_gold(m_node_id);
}

void CTableHundred::tick_user(_uint64 uid)
{
	IUser* pUser = UserManager::GetInstance()->get_user(uid);
	if (pUser == NULL || pUser->get_user_type() == ROBOT_TYPE)
		return;
	
	room_bill_fun::GetInstance()->write_hund_room_bill(pUser, RBT_LOGON_OUT, m_node_id);
	CRoomRouteChallenge::send_web_user_leave(uid);

	int charid = pUser->get_chair_id();
	if (charid >= 0 && charid < (int)m_game_users.size())
	{
		MY_LOG_DEBUG("CTableHundred release tick_user ....chairid:%d  uid:%d", charid, m_game_users[charid]);
		m_game_users[charid] = 0;
		m_now_user_count--;
	}
	m_game_com->on_user_action(pUser, comm_action_leave);
	MY_LOG_DEBUG("CTableHundred tick_user  delete user  uid:%lld  charid:%d", pUser->get_user_id(), charid);
	m_user_mgr->delete_user(pUser);
}

bool CTableHundred::send(IUser * pUser, _uint8 mcmd, _uint8 scmd, const void * pData, int size)
{       
	if (pUser && !pUser->is_offline())
	{
		socket_id sid = pUser->get_socket_id();
		return g_server->send(sid, mcmd, scmd, pData, size);
	}
	return false;
}

bool CTableHundred::send(IUser * pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData)
{   
	if (pUser && !pUser->is_offline())
	{
		socket_id sid = pUser->get_socket_id();
		return g_server->send(sid, mcmd, scmd, pData);
	}  

	return false;
}

bool CTableHundred::send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, const void * pData, int size)
{   
	IUser * pUser = get_user_from_chair(chairID);
	if (pUser && !pUser->is_offline())
	{
		socket_id sid = pUser->get_socket_id();
		return g_server->send(sid, mcmd, scmd, pData, size);
	}    

	return false;
}

bool CTableHundred::send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData)
{   
	IUser * pUser = get_user_from_chair(chairID);
	if (pUser && !pUser->is_offline())
	{
		socket_id sid = pUser->get_socket_id();
		return g_server->send(sid, mcmd, scmd, pData);
	}    

	return false;
}

bool CTableHundred::send_user_batch(_uint8 mcmd, _uint8 scmd, const void * pdata, int size)
{   
	for (_uint32 i = 0; i < m_game_users.size(); ++i)
	{
		IUser * pUser = m_user_mgr->get_user(m_game_users[i]);
		if (pUser && !pUser->is_offline())
		{
			g_server->send(pUser->get_socket_id(), mcmd, scmd, pdata, size);
		}
	}

	return true;
}

bool CTableHundred::send_user_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData)
{   
	for (_uint32 i = 0; i < m_game_users.size(); ++i)
	{
		IUser * pUser = m_user_mgr->get_user(m_game_users[i]);
		if (pUser && !pUser->is_offline())
		{
			g_server->send(pUser->get_socket_id(), mcmd, scmd, pData);
		}
	}

	return true;
}

void CTableHundred::send_common_msg(_uint8 chair_id, const char* msg, int len, int type)
{
	CGameFrame::GetInstance()->send_comm_message(m_game_users[chair_id], type, msg);
}

void CTableHundred::write_user_roominfo(_uint64 user_id, int type)
{
	IUser* puser = UserManager::GetInstance()->get_user(user_id);
	if (!puser || puser->get_user_type() == ROBOT_TYPE)
		return;

	Json::Value json_req_value;
	if (type == RBT_LOGON_IN)
	{
		json_req_value["uid"] = Json::Value(user_id);
		json_req_value["room_id"] = Json::Value(get_room_id());
		json_req_value["room_name"] = Json::Value(config_manager::GetInstance()->get_hundred_node_name(m_node_id));
		json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
	}
	else
	{
		Json::Value json_req_value;
		json_req_value["uid"] = Json::Value(user_id);
		json_req_value["room_id"] = Json::Value(0);
		json_req_value["room_name"] = Json::Value("");
		json_req_value["game_id"] = Json::Value(0);
	}

	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, user_id, "update_room_info", json_req_data.c_str(), db_section, false);

	MY_LOG_DEBUG("write_user_roominfo user_id:%d, TableHundred json_data: %s", user_id, json_req_data.c_str());
}

void CTableHundred::game_db_push_data(const char* type, const char *data, int len)
{
	if(!data || len == 0) return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, m_node_id, type, data, db_section, false);
}

void CTableHundred::game_db_get_data(const char* type, const char *data, int len, void* postdata, int postlen)
{
	cmd_dbproxy::CDBProxyExceSection db_section;
	db_section.set_wParamBuffer((char*)postdata, postlen);
	db_section.set_lParamInt(m_node_id);
	g_dbproxy_namager->post_exec(0, on_get_db_data, m_node_id, type, data, db_section);
}

void CTableHundred::comm_get_db_data(void *obj, const char *data, int len)
{
	m_game_com->db_get_data(obj, data, len);

}

FTYPE(void) CTableHundred::on_get_db_data(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section)
{
	int node_id = section.get_lParamInt();
	CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
	if (ptable)
	{
		ptable->comm_get_db_data((void*)section.get_wParamBuffer(), data_json, size);
	}
}

void CTableHundred::write_room_gold_statistic(int gold, int type_id)
{   
	int node_id = m_node_id;
	if (node_id == 0)
	{
		return;
	}
	BILL->room_gold_statistics(
		0
		, 0
		, config_manager::GetInstance()->get_game_id()
		, node_id
		, gold
		, type_id
		);
}

int CTableHundred::get_user_gold_by_uid(_uint64 uid)
{
	return myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
}

void CTableHundred::update_enter_time(int uid)
{
	workRobotInfo* info = robot_manager::GetInstance()->get_work_info(uid);
	if (info)
		info->enter_time = time(0);
}

int CTableHundred::gm_control(const char* buff, int len, char* tips, int &tips_len, string ip, int account_id)
{
	return m_game_com->gm_control(buff, len, tips, tips_len, ip, account_id);
}

//GM超控记录
void CTableHundred::update_super_control_info(const char* key, const char * data, int len)
{
    if(!key || !data || len == 0) return;

    MY_LOG_DEBUG("CTable::update_super_control_info key:%s, len:%d .", key, len);

	myredis::GetInstance()->set_super_control_info_for_string(key, data);
}

void CTableHundred::query_room_gold_statistic(int gameid, int nodeid)
{
    cmd_dbproxy::CDBProxyExceSection section;
	Json::FastWriter json_writer;
	Json::Value json_req_value;

	json_req_value["i_game_id"] = Json::Value(gameid);
	json_req_value["i_node_id"] = Json::Value(nodeid);
	std::string json_req_data = json_writer.write(json_req_value);
	//g_dbproxy_namager->post_exec(this, on_query_room_gold_statistic_dbproxy, 0, "comm_cmd_query_room_gold_statistic", json_req_data.c_str(), section);

	MY_LOG_DEBUG("CTableHundred::query_room_gold_statistic gameid:%d, nodeid:%d .", gameid, nodeid);
}

FTYPE(void) CTableHundred::on_query_room_gold_statistic_dbproxy(void*obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection &section)
{
    Json::Reader json_reader;
	Json::Value json_value;
	if (!get_dbproxy_resp_value(data_json, size, json_value, NULL))
	{
		MY_LOG_ERROR("CTableHundred::on_update_regist_time_dbproxy is error！");
		return;
	}

	CTableHundred *ptable = (CTableHundred*)obj;
	if (ptable == NULL)
		return;
	
	int game_id = json_value["recordset0"][0]["game_id"].asUInt();
	int node_id = json_value["recordset0"][0]["node_id"].asUInt();
	if (game_id != 0)
	    ptable->m_room_result_time = time(0);
	//ptable->m_room_result = json_value["recordset0"][0]["gold"].asInt64();

	char op_type[64] = { 0 };
	sprintf(op_type, CUR_ROOM_GOLD_STATISTIC, game_id, node_id);
	_uint64 room_result = json_value["recordset0"][0]["gold"].asInt64();
	if (game_id != 0)
	    myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA,     op_type, room_result);
	
	MY_LOG_DEBUG("CTableHundred::on_query_room_gold_statistic_dbproxy game_id:%d, node_id:%d, room_golds:%lld, m_room_result_time:%d .", game_id, node_id, room_result, ptable->m_room_result_time);
}

void CTableHundred::update_room_gold_statistic(_tint64 gold)
{
	char op_type[64] = { 0 };
	char op_type_time[64] = { 0 };
	int gameid = config_manager::GetInstance()->get_game_id();
	sprintf(op_type, CUR_ROOM_GOLD_STATISTIC, gameid, m_node_id);
	sprintf(op_type_time, CUR_ROOM_GOLD_STATISTIC_TIME, gameid, m_node_id);
    _uint32	game_result_time = myredis::GetInstance()->get_user_comm_data(DB_SAVE_DATA, op_type_time);
	
	if (!is_in_today(game_result_time))
	{
	    _tint64 old_time = game_result_time;
		_tint64 new_time = time(0);
		
        MY_LOG_DEBUG("CTableHundred::update_room_gold_statistic not in today gameid:%d, m_node_id:%d, game_result_time:%d, old_time:%d, new_time:%d,result_time:%d .  ", gameid, m_node_id, game_result_time, old_time, new_time, new_time-old_time);

		if (new_time - old_time < 0 || new_time - old_time >= 22*60*60)
		{
		    myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type_time, time(0));
		    myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type, gold);
		}
		else
		{
		    myredis::GetInstance()->update_data_by_comm(DB_SAVE_DATA, op_type, gold);
		}
	}
	else 
	{
		myredis::GetInstance()->update_data_by_comm(DB_SAVE_DATA, op_type, gold);
	}

	MY_LOG_DEBUG("CTableHundred::update_room_gold_statistic end gold:%d, gameid:%d, nodeid:%d, game_result_time:%d .", gold, config_manager::GetInstance()->get_game_id(), m_node_id, game_result_time);
}

_tint64 CTableHundred::get_room_gold_statistic()
{
    char op_type[64] = { 0 };
	char op_type_time[64] = { 0 };
	int gameid = config_manager::GetInstance()->get_game_id();
	sprintf(op_type, CUR_ROOM_GOLD_STATISTIC, gameid, m_node_id);
	sprintf(op_type_time, CUR_ROOM_GOLD_STATISTIC_TIME, gameid, m_node_id);
    _uint32	game_result_time = myredis::GetInstance()->get_user_comm_data(DB_SAVE_DATA, op_type_time);

	MY_LOG_DEBUG("CTableHundred::get_room_gold_statistic gameid:%d, m_node_id:%d, game_result_time:%d .  ", gameid, m_node_id, game_result_time);
	
	if (!is_in_today(game_result_time))
	{
	    _tint64 old_time = game_result_time;
		_tint64 new_time = time(0);
		
        MY_LOG_DEBUG("CTableHundred::get_room_gold_statistic not in today gameid:%d, m_node_id:%d, game_result_time:%d, old_time:%d, new_time:%d,result_time:%d .  ", gameid, m_node_id, game_result_time, old_time, new_time, new_time-old_time);

		if (new_time - old_time < 0 || new_time - old_time >= 22*60*60)
		{
		    myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type_time, time(0));
			myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type, 0);
			return 0;
		}
	}

	return myredis::GetInstance()->get_user_comm_data(DB_SAVE_DATA, op_type);
}

int CTableHundred::get_curr_room_status()
{
	MY_LOG_WARN("CTableHundred::get_curr_room_status room[%d] 状态:%d", CGameFrame::GetInstance()->get_room_id(), config_manager::GetInstance()->m_room_status);
	return config_manager::GetInstance()->m_room_status;
}

void CTableHundred::update_date_number(int num)
{
	char op_type[64] = { 0 };
	char op_type_time[64] = { 0 };
	int gameid = config_manager::GetInstance()->get_game_id();
	sprintf(op_type, CUR_GAME_DATE_NUMBER, gameid, m_node_id);
	sprintf(op_type_time, CUR_GAME_DATE_NUMBER_TIME, gameid, m_node_id);
	_uint32	game_result_time = myredis::GetInstance()->get_user_comm_data(DB_SAVE_DATA, op_type_time);

	MY_LOG_DEBUG("CTableHundred::update_date_number start game_result_time:%d, num:%d, gameid:%d, nodeid:%d .", game_result_time, num, gameid, m_node_id);
	
	if (!is_in_today(game_result_time))
	{
		myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type_time, time(0));
		myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type, num);
	}
	else 
	{
		myredis::GetInstance()->update_data_by_comm(DB_SAVE_DATA, op_type, num);
	}

	MY_LOG_DEBUG("CTableHundred::update_date_number end game_result_time:%d, num:%d, gameid:%d, nodeid:%d .", game_result_time, num, gameid, m_node_id);
}

_tint64 CTableHundred::get_date_number()
{
    char op_type[64] = { 0 };
	char op_type_time[64] = { 0 };
	int gameid = config_manager::GetInstance()->get_game_id();
	sprintf(op_type, CUR_GAME_DATE_NUMBER, gameid, m_node_id);
	sprintf(op_type_time, CUR_GAME_DATE_NUMBER_TIME, gameid, m_node_id);
    _uint32	game_result_time = myredis::GetInstance()->get_user_comm_data(DB_SAVE_DATA, op_type_time);
	
	MY_LOG_DEBUG("CTableHundred::get_date_number gameid:%d, m_node_id:%d, game_result_time:%d .  ", gameid, m_node_id, game_result_time);
	
	if (!is_in_today(game_result_time))
	{
		myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type, 1);
		myredis::GetInstance()->set_user_comm_data(DB_SAVE_DATA, op_type_time, time(0));
		return 1;
	}

	return myredis::GetInstance()->get_user_comm_data(DB_SAVE_DATA, op_type);
}

void CTableHundred::stop_game()
{
	m_game_com->stop_game();
	m_game_users.clear();
}

void CTableHundred::start_game()
{
	m_game_com->start_game();
}

void CTableHundred::write_big_card_versus_bill(const char * data, int len)
{
	if(!data || len == 0) return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, m_table_id, "mjq_write_big_card_versus_bill", data, db_section, false);

}

//投递投付结果
bool CTableHundred::on_post_transfer_inout(transfer_inout_result_info inout_result)
{
	return m_game_com->transfer_inout_result(inout_result);
}

//写三方投付
bool CTableHundred::write_transfer_inout_ex(hundred_transfer_inout_one inout)
{
	inout.tableid = m_table_id;
	return room_bill_fun::GetInstance()->hund_write_transfer_inout_ex(inout);
}

//写三方投付
bool CTableHundred::write_transfer_inout(hundred_transfer_inout_one inout)
{
	inout.tableid = m_table_id;
	return room_bill_fun::GetInstance()->hund_write_transfer_inout(inout);
}

//请求刷新三方用户金币
void CTableHundred::get_flush_user_gold(int uid, int gameid)
{
	web_request::GetInstance()->get_flush_user_gold(uid, gameid, m_table_id);	
}

void CTableHundred::batch_write_transfer_inout(hundred_transfer_inout inout)
{
	char data_item[256] = { 0 };
	std::map<int, vector<string> > map_sql_data; //安渠道统计数据
	map_sql_data.clear();
	//获取UUID
	int gameid = config_manager::GetInstance()->get_game_id();
	Json::Value json_req_value;
	int nSingleModeCount = 0;
	for (int i = 0; i < inout.user_count; i++)
	{    
		IUser*pUser = m_user_mgr->get_user(inout.uid[i]);
		int user_type = 0;
		int client_id = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_CHANNEL_ID);
		int sub_client_id = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_SUB_CHANNEL_ID);
		int now_gold = 0;
		if (pUser)
		{
			user_type = pUser->get_user_type();
			now_gold = pUser->get_gold();
		}
		else
		{
			user_type = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_USER_TYPE);
			now_gold = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_GOLD);
		}
		if (user_type == ROBOT_TYPE)
			continue;

		char* token = myredis::GetInstance()->get_token_by_uid(inout.uid[i]);
		//int now_gold = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_GOLD);
		//int client_id = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_CHANNEL_ID);
		int api_type = myredis::GetInstance()->get_data_by_uid(inout.uid[i], DB_COMM, CUR_API_MODE);

		char gguid[GUID_LEN] = { 0 };
		if (strlen(inout.gguid) != 0)
		{
			memcpy(gguid, inout.gguid, GUID_LEN);
			sprintf(gguid, "%s_%d", inout.gguid, inout.uid[i]);
		}
		else
		{
			get_guid(gguid);
		}

		char k[GUID_LEN] = { 0 };
		get_guid(k);

		//交易标识 {BetId}-{ParentBetId}-{transactionType}-{balanceId}
		char transaction_id[128] = { 0 };
		sprintf(transaction_id, "{%s}-{%s}-{%d}-{%s}", "0", gguid, inout.bill_type, k);
		if (api_type == MODE_SINGLE)
		{
			json_req_value[nSingleModeCount]["player_uid"] = Json::Value(inout.uid[i]);
			json_req_value[nSingleModeCount]["game_id"] = Json::Value(gameid);
			json_req_value[nSingleModeCount]["token"] = Json::Value(token == NULL ? "" : token);
			json_req_value[nSingleModeCount]["bill_type"] = Json::Value(inout.bill_type);
			json_req_value[nSingleModeCount]["is_end"] = Json::Value(inout.is_end ? 1 : 0);
			json_req_value[nSingleModeCount]["bet_amount"] = Json::Value(inout.bet[i]);
			json_req_value[nSingleModeCount]["transfer_amount"] = Json::Value(inout.result[i]);
			json_req_value[nSingleModeCount]["pay_out"] = Json::Value(inout.pay_out[i]);

			//转账模式不需要投付
			json_req_value[nSingleModeCount]["api_mode"] = Json::Value(api_type);
			json_req_value[nSingleModeCount]["status"] = Json::Value(api_type == MODE_SINGLE ? 0 : 1);

			json_req_value[nSingleModeCount]["client_id"] = Json::Value(client_id);
			json_req_value[nSingleModeCount]["last_gold"] = Json::Value(now_gold - inout.result[i]);
			json_req_value[nSingleModeCount]["now_gold"] = Json::Value(now_gold);

			json_req_value[nSingleModeCount]["parent_bet_id"] = Json::Value(gguid);	//母单
			json_req_value[nSingleModeCount]["bet_id"] = Json::Value("0");	//子单

			json_req_value[nSingleModeCount]["transaction_id"] = Json::Value(transaction_id);

			nSingleModeCount++;
		}

		/*(`game_id`,
		`player_uid`,
		`token`,
		`parent_bet_id`,
		`bet_id`,
		`bet_amount`,
		`transfer_amount`,
		`transaction_id`,
		`bill_type`,
		`is_end`,
		`create_time`,
		`client_id`,
		`balanceBefore`,
		`balanceAfter`,
		`status`,
		`api_mode`)*/
		memset(data_item, 0, sizeof(data_item));
		sprintf(data_item, "(%d,%d,\'%s\',\'%s\',%d,%d,%d,\'%s\',%d,%d,%s,%d,%d,%d,%d,%d)",
			gameid,
			inout.uid[i],
			token == NULL ? "" : token,
			gguid,
			0,
			inout.bet[i],
			inout.pay_out[i],
			transaction_id,
			inout.bill_type,
			inout.is_end ? 1 : 0,
			"NOW()",
			sub_client_id,
			now_gold - inout.pay_out[i],
			now_gold,
			api_type == MODE_SINGLE ? 0 : 1,
			api_type);

		string tem_str;
		tem_str=data_item;
		map_sql_data[client_id].push_back(tem_str);
		//data.append(data_item);
		//if (i != inout.user_count - 1)
		//{
		//	data.append(",");
		//}
	}
	if (nSingleModeCount > 0)
	{
		//推送WEB
		Json::FastWriter json_writer;
		std::string json_web_req_data = json_writer.write(json_req_value);
		web_request::GetInstance()->post_transfer_inout_batch(json_web_req_data);

		MY_LOG_DEBUG("batch_write_transfer_inout table json_data: %s", json_web_req_data.c_str());
	}


	string data;
	Json::Value va;
	Json::Value json_req_data_value;
	int nIndex = 0;
	char szClientlist[32] = { 0 };
	std::map<int, vector<string> >::iterator itr = map_sql_data.begin();
	for (; itr != map_sql_data.end(); itr++)
	{
		vector<string> vec_data = itr->second;
		int nVecSize = vec_data.size();
		data.clear();
		MY_LOG_DEBUG("batch_write_transfer_inout map_data map_size:%d client:%d: vec_size:%d", 
			map_sql_data.size(),itr->first, nVecSize);
		for (int i = 0; i < nVecSize; i++)
		{  
			data.append(vec_data[i]);
			if (i != nVecSize - 1)
			{
				data.append(",");
			}
		}
		if (data.length() > 0)
		{
			va["client_id"] = Json::Value(itr->first);  
			va["data_list"] = Json::Value(data);

			mysprintf(szClientlist, sizeof(szClientlist), "client_%d", nIndex);
			json_req_data_value[szClientlist] = Json::Value(va);
			nIndex++;
		}

	}
	
	json_req_data_value["client_num"] = Json::Value(nIndex); //渠道个数
	if (inout.is_writedb)
	{
		//落地
		cmd_dbproxy::CDBProxyExceSection db_section;
		Json::FastWriter json_data_writer;
		std::string json_data_req = json_data_writer.write(json_req_data_value);
		g_dbproxy_namager->post_exec(0, 0, m_table_id, "cmd_transfer_inout_bill_batch", json_data_req.c_str(),
			db_section, false);
		MY_LOG_DEBUG("batch_write_transfer_inout table sql data:%s", json_data_req.c_str());
	}
}

//写游戏详情
void CTableHundred::write_game_detail(char gguid[GUID_LEN], int bet_id, int uid, string detail)
{
	int gameid = config_manager::GetInstance()->get_game_id();
	string name = "";
	myredis::GetInstance()->get_data_by_uid_for_s_string(uid, DB_COMM, CUR_PLAER_NAME, name);
	int sub_client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_SUB_CHANNEL_ID);
	int client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);

	cmd_dbproxy::CDBProxyExceSection db_section;

	Json::Value json_req_value;
	json_req_value["uid"] = Json::Value(uid);
	json_req_value["game_id"] = Json::Value(gameid);
	json_req_value["parent_bet_id"] = Json::Value(gguid);	//母单
	json_req_value["bet_id"] = Json::Value("0");	//子单
	json_req_value["detail"] = Json::Value(detail.c_str());
	json_req_value["player_name"] = Json::Value(name.c_str());
	json_req_value["client_id"] = Json::Value(client_id);
	json_req_value["sub_client_id"] = Json::Value(sub_client_id);

	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, m_table_id, "cmd_game_detail", json_req_data.c_str(),
		db_section, false);
}

FTYPE(void) CTableHundred::on_bag_message_record_startpage_dbproxy(void* obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection& section)
{
	if (!data_json || size == 0)
	{
		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy data_json null.");
		return;
	}

	_tint64 uid = section.get_UserID();
	_uint32 sid = section.get_SID();
	_uint32 lau = section.get_wParamInt();

	cmd_room::UserBagMessageSearchRsp resp;

	MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy data_json:%s uid(%d) sid(%d) lau(%d)", data_json, uid, sid, lau);
	Json::Value json_value;
	if (!get_dbproxy_resp_value(data_json, size, json_value, NULL))
	{
		MY_LOG_ERROR("on_bag_message_record_startpage_dbproxy data_json(%s) uid(%d)", data_json, uid);
		g_server->send((socket_id)section.get_SID(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_SEARCH_RSP, &resp);
		return;
	}

	int recordsetcount = json_value["recordsetcount"].asInt();
	int nTotalNum = 0;
	if (!json_value["recordset0"].isNull())
	{
		nTotalNum = json_value["recordset0"][0]["total_num"].asInt();
	}
	resp.set_total_num(nTotalNum);
	if (nTotalNum > 0)
	{
		int nRecorCount = json_value["recordset1"].size();
		for (int i = 0; i < nRecorCount; i++)
		{
	//		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy fill start.");
			string message_param = json_value["recordset1"][i]["param"].asString();
			_uint64 id = json_value["recordset1"][i]["id"].asInt();
			int message_id = json_value["recordset1"][i]["message_id"].asInt();
			int game_id = json_value["recordset1"][i]["game_id"].asInt();
			int props_id = json_value["recordset1"][i]["props_id"].asInt();
			int post_time = json_value["recordset1"][i]["post_time"].asInt();

			vector<string> vecParam;
			split_string_str(message_param, vecParam, ",");

	//		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy split_string_str message_param:[%s] size:%d.", message_param.c_str(), vecParam.size());

			const char* game_name = get_error_msg(game_id, lau);
			const char* message_str = get_error_msg(message_id, lau);

			string format_str = message_str;

	//		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy clear_side_space message_param:[%s] size:%d.", message_param.c_str(), vecParam.size());

			clear_side_space(format_str);

			char msg[2048] = "test";

			MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy start message_id:%d msg:[%s] format_str:[%s] vecParam:%d", message_id, msg, format_str.c_str(), vecParam.size());

			if ((message_id == MSG_PROPS_MESSAGE_REWARD || message_id == MSG_PROPS_MESSAGE_REWARD_GAME) && vecParam.size() > 1)
			{
				sprintf(msg, format_str.c_str(), game_name, atoi(vecParam[0].c_str()), atoi(vecParam[1].c_str()));
			}
			else if (message_id == MSG_PROPS_MESSAGE_SCORE_BY_USE && vecParam.size() > 0)
			{
				sprintf(msg, format_str.c_str(), game_name, atoll(vecParam[0].c_str()) / 100.0);
			}
			else if (message_id == MSG_PROPS_MESSAGE_COMPOUND_CARD && vecParam.size() > 1)
			{
				if (lau == 4)
				{
					sprintf(msg, format_str.c_str(), game_name, atoi(vecParam[1].c_str()), atoi(vecParam[0].c_str()));
				} 
				else
				{
					sprintf(msg, format_str.c_str(), atoi(vecParam[0].c_str()), game_name, atoi(vecParam[1].c_str()));
				}				
			}
			else if (message_id == MSG_PROPS_MESSAGE_REWARD_ALL && vecParam.size() > 2)
			{
				sprintf(msg, format_str.c_str(), game_name, atoi(vecParam[0].c_str()), atoi(vecParam[1].c_str()), atoi(vecParam[2].c_str()));
			}

		//	MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy msg:[%s] format_str:[%s]", msg, format_str.c_str());

			cmd_room::BagMessageDetailsInfo* temp = resp.bag_message_details_list_item(i);
			temp->set_id(id);
			temp->set_msg(msg, 0);
			temp->set_create_time(post_time);
			resp.set_bag_message_details_list_item_count(i + 1);

	//		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy fill end.");
		}

		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy end nTotalNum:%d nRecorCount:%d", nTotalNum, nRecorCount);
	}

	g_server->send((socket_id)section.get_SID(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_SEARCH_RSP, &resp);
}


int CTableHundred::on_http_recv(socket_id sid, int uid, int opid, const string& strParam)
{
    if (m_game_com)
    {
        m_game_com->on_http_recv(sid, uid, opid, strParam);
		return 0;
    }
    return 1;
}

bool CTableHundred::send_http(socket_id sid, const string &str)
{   
    return g_server->send(sid, str); 
}

void CTableHundred::send_http_failed(socket_id sid, int ret, const string& msg) {

	MY_LOG_ERROR("send_http_room_failed ret = %d, msg=%s", ret, msg.c_str());

	Json::Value json_rsp_value;
	Json::Value json_err_value;

	json_err_value["cd"] = Json::Value(ret);
	json_err_value["msg"] = Json::Value(msg);
	json_err_value["tid"] = Json::Value(0);
	json_rsp_value["dt"] = Json::nullValue;
	json_rsp_value["err"] = json_err_value;

	Json::FastWriter json_writer;
	string json_rsp_data = json_writer.write(json_rsp_value);

	g_server->send(sid, json_rsp_data);
}

bool CTableHundred::send(socket_id sid, const string &str)
{   
    return g_server->send(sid, str); 
}

IUser* CTableHundred::create_user(_tint64 uid)
{
	return m_user_mgr->create_user(uid, 1, 1);
}

void CTableHundred::delete_user(IUser* user) {
	m_user_mgr->delete_user(user);
}

bool CTableHundred::write_spin_round_history(SpinRoundHistory& h) {

    std::string history = h.serialize();
	cmd_dbproxy::CDBProxyExceSection db_section;
	return g_dbproxy_namager->post_exec(0, 0, m_table_id, "cmd_spin_round_history", history.c_str(), db_section, false);
}

void CTableHundred::transfer_use_item_result(IUser* user, char *buffer, size_t len, int64_t record_id, int32_t bet, int32_t balance_require) 
{
	return;
    // MY_LOG_DEBUG("user[%d] CTableHundred transfer_use_item_result record_id: %lld", user->get_user_id(), record_id);
    // if (m_game_com) {
    //     if (record_id > 0) {
    //         serverProto::Request pb_request;
    //         serverProto::Itemdata sreq;
    //         sreq.set_bet(bet / 100.0);
    //         sreq.set_coinlimit((float)balance_require / 100.0);
    //         sreq.set_currencynumber(record_id);
    //         pb_request.set_ack(serverProto::AckType::itemSpin);
    //         std::string *d = pb_request.mutable_req();
    //         sreq.SerializeToString(d);
    //         std::string content = "";
    //         pb_request.SerializeToString(&content);
    //         m_game_com->on_http_recv(user->get_socket_id(), user->get_user_id(), serverProto::AckType::itemSpin, content);
    //     } else {
    //         m_game_com->on_http_recv(user->get_socket_id(), user->get_user_id(), serverProto::AckType::spin, std::string(buffer, len));
    //     }
    // } else {
    //     MY_LOG_ERROR("user[%d] m_game_com is invalid", user->get_user_id());
    // }
}

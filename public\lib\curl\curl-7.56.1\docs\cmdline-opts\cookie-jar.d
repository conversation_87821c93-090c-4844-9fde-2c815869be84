Short: c
Long: cookie-jar
Arg: <filename>
Protocols: HTTP
Help: Write cookies to <filename> after operation
---
Specify to which file you want curl to write all cookies after a completed
operation. <PERSON><PERSON><PERSON> writes all cookies from its in-memory cookie storage to the
given file at the end of operations. If no cookies are known, no data will be
written. The file will be written using the Netscape cookie file format. If
you set the file name to a single dash, "-", the cookies will be written to
stdout.

This command line option will activate the cookie engine that makes curl
record and use cookies. Another way to activate it is to use the --cookie
option.

If the cookie jar can't be created or written to, the whole curl operation
won't fail or even report an error clearly. Using --verbose will get a warning
displayed, but that is the only visible feedback you get about this possibly
lethal situation.

If this option is used several times, the last specified file name will be
used.

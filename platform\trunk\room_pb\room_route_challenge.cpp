﻿#include "room_route_challenge.h"
#include "table_manager.h"
#include "game_frame.h"
#include "client_cmd.h"
#include "bill_fun.h"
#include "user_manager.h"
#include "table_Hundred.h"

#define SEND_ROOM_INFO_TIME         10 * 1000               //发送房间信息时间
#define SEND_TIME_ID				12345678

static CRoomRouteChallenge _room_route_challenge;
_uint32 CRoomRouteChallenge::m_timer_id = 0;
int CRoomRouteChallenge::g_send_room_info_time = 0;

CRoomRouteChallenge::CRoomRouteChallenge()
{
    __reg_action_sink(ACTION_ROOM_CONNECT_GATE_SUCCEED, connect_gate);
}

CRoomRouteChallenge::~CRoomRouteChallenge()
{

}

FTYPE(void) CRoomRouteChallenge::set_info_time()
{
	g_log->write_log(LOG_TYPE_DEBUG, "CRoomRouteChallenge... set_info_time room_id:%d room_type:%d", 
		CGameFrame::GetInstance()->get_room_id(), config_manager::GetInstance()->get_room_type());

	send_room_info();
	CRoomRouteChallenge::g_send_room_info_time = time(0);
	g_timer_mgr->set_timer(NULL, timer_check, SEND_TIME_ID, SEND_ROOM_INFO_TIME, 0, -1);
}

FTYPE(void) CRoomRouteChallenge::connect_gate(void * obj, enAction action, const void * pdata, int size)
{
	send_room_info();
	CRoomRouteChallenge::g_send_room_info_time = time(0);
	g_timer_mgr->set_timer(NULL, timer_check, SEND_TIME_ID, SEND_ROOM_INFO_TIME, 0, -1);
}

FTYPE(void) CRoomRouteChallenge::timer_check(void * obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{
    send_room_info();
	CRoomRouteChallenge::g_send_room_info_time = time(0);
}

FTYPE(void) CRoomRouteChallenge::send_room_info()
{
    room_route::CRoomInfo roomInfo;
	roomInfo.clear();
	roomInfo.set_GameID(config_manager::GetInstance()->get_game_id());
	roomInfo.set_Status(CGameFrame::GetInstance()->get_room_status());
	roomInfo.set_RoomID(CGameFrame::GetInstance()->get_room_id());
	roomInfo.set_FreeTableCount(TableManager::GetInstance()->get_free_count());
	roomInfo.set_UsedTableCount(TableManager::GetInstance()->get_table_count());

	vector<int> node_list;
	config_manager::GetInstance()->get_nodeid(node_list);

	MY_LOG_INFO("send_room_info node_list size:%d", node_list.size());
	for (int i = 0; i < node_list.size(); i++)
	{
		roomInfo.add_NodeID(node_list[i]);
	}

	int onlineCount = 0;
	int offlineCount = 0;
	UserManager::GetInstance()->user_count(onlineCount,offlineCount);
	roomInfo.set_offline(offlineCount);
	roomInfo.set_online(onlineCount);
	roomInfo.set_RoomType(config_manager::GetInstance()->get_room_type());
	roomInfo.set_RoomLimitUser(0);
	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		roomInfo.set_RoomLimitUser(TableManager::GetInstance()->get_hundred_limit_user());
	}

    int ret = g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_ROOM_SYN, &roomInfo);
	g_log->write_log(LOG_TYPE_DEBUG, "send_room_info... ret:%d room_id:%d room_type:%d, node count:%d",
		ret,CGameFrame::GetInstance()->get_room_id(),config_manager::GetInstance()->get_room_type(),
		roomInfo.NodeID_item_count());
}

FTYPE(void) CRoomRouteChallenge::send_challenge_game_info(_uint64 owner_id,_tint64 room_num,_uint8 total_round,_uint8 cur_round,_uint8 max_player)
{
	room_route::CRoomChallengeGameInfo stGameInfo;
	stGameInfo.clear();
	stGameInfo.set_OwnerID(owner_id);
	stGameInfo.set_Code(room_num);
	stGameInfo.set_CurRound(cur_round);
	stGameInfo.set_TotalRound(total_round);
	stGameInfo.set_PlayerCount(max_player);

	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_UPDATE_CHALLENGE_INFO, &stGameInfo);
}

/* 一局结束, 同步约战桌信息 */
FTYPE(void) CRoomRouteChallenge::send_challenge_play_game_end(_uint64 owner_id,
															  _tint64 room_num,
															  _uint8 total_round,
															  _uint8 cur_round,
															  _uint8 max_player,
															  MAPSCORE &inMapScore)
{
	room_route::CRoomChallengeGameInfo stGameInfo;
	stGameInfo.clear();
	stGameInfo.set_OwnerID(owner_id);
	stGameInfo.set_Code(room_num);
	stGameInfo.set_CurRound(cur_round);
	stGameInfo.set_TotalRound(total_round);
	stGameInfo.set_PlayerCount(max_player);

	MAPSCORE::iterator iter = inMapScore.begin();
	for ( int i = 0; iter != inMapScore.end() && i < stGameInfo.UserList_max_count(); ++i,++iter )
	{
		room_route::CRoomChallengeUserInfo* userList = stGameInfo.UserList_item(i);
		userList->set_UserID(iter->first);
		userList->set_Score(iter->second.first);
		userList->set_UserName(iter->second.second.c_str(), iter->second.second.length());
	}
	int nItemCount = inMapScore.size();
	nItemCount = nItemCount <= stGameInfo.UserList_max_count() ? nItemCount : stGameInfo.UserList_max_count();
	stGameInfo.set_UserList_item_count(nItemCount);

	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_UPDATE_CHALLENGE_INFO, &stGameInfo);
}

FTYPE(void) CRoomRouteChallenge::send_challenge_game_over(_uint64 owner_id,_tint64 room_num,int cost_card,_uint8 reason)
{
    room_route::CRoomChallengeGameOver stGameOver;
	stGameOver.clear();
	stGameOver.set_OwnerID(owner_id);
    stGameOver.set_Code(room_num);
	stGameOver.set_Reason(reason);

	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_GAME_OVER, &stGameOver);
}

FTYPE(void) CRoomRouteChallenge::send_challenge_create_result(_uint64 owner_id,_tint64 room_num,int ret,CTable* p_table,
															  int is_web, int web_sid)
{
	room_route::CRoomChallengeCreateGameResult stCreateResult;
	stCreateResult.clear();
	stCreateResult.set_OwnerID(owner_id);
	stCreateResult.set_Code(room_num);
	stCreateResult.set_Ret(ret);
	stCreateResult.set_gameid(config_manager::GetInstance()->get_game_id());
	stCreateResult.set_roomid(CGameFrame::GetInstance()->get_room_id());
	stCreateResult.set_is_web(is_web);
	stCreateResult.set_web_sid(web_sid);

	IUser* puser = UserManager::GetInstance()->get_user(owner_id);
	char nick_name[64] = { 0 };
	if (puser)
	{
		sprintf(nick_name, "%s", puser->get_nick_name());
		stCreateResult.set_face(puser->get_head_img(), 0);
	}
	else
	{
		sprintf(nick_name, "%lld", owner_id);
		stCreateResult.set_face("", 0);
	}
	stCreateResult.set_nick_name(nick_name, 0);
	
	if(p_table)
	{
		stCreateResult.set_PayType(p_table->get_pay_mode());
		stCreateResult.set_TableRule(p_table->get_table_rule(),0);
		stCreateResult.set_PlayerCount(p_table->get_max_player());
		stCreateResult.set_TotalRound(p_table->get_max_count());

		stCreateResult.set_PlayingMethod(p_table->get_playing_method());
		stCreateResult.set_DefaultScore(p_table->get_default_score());

		stCreateResult.set_CreateTime(p_table->get_create_time());
		stCreateResult.set_CostCard(p_table->get_cost_card());
	}

	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_CREATE_RESULT,&stCreateResult);
}


FTYPE(void) CRoomRouteChallenge::send_challenge_user_enter(_uint64 uid,const char* uname,_tint64 room_num, int clientid, const char* face)
{
	room_route::CRoomChallengeEnterLeave enter_info;
	enter_info.set_UserID(uid);
	enter_info.set_UserName(uname,0);
	enter_info.set_Code(room_num);
	enter_info.set_client(clientid);
	enter_info.set_roomid(g_server_id);
	enter_info.set_face(face, 0);
	enter_info.set_gameid(config_manager::GetInstance()->get_game_id());
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_USER_ENTER,&enter_info);
}

FTYPE(void) CRoomRouteChallenge::send_challenge_user_leave(_uint64 uid,_tint64 room_num, _uint64 cost_card, int isfaile)
{
	// add by hsl on 03/14/2017 AA支付，用户离开，非房主，返钻
	if ( cost_card > 0 )
	{
		CClientCmd::do_add_room_card_dbproxy(uid, room_num, cost_card, AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
	}
	
	g_log->write_log(LOG_TYPE_DEBUG, "send_challenge_user_leave:roomnum %lld", room_num);
	room_route::CRoomChallengeEnterLeave leave_info;
	leave_info.set_UserID(uid);
	leave_info.set_Code(room_num);
	leave_info.set_isfaile(isfaile);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_USER_LEAVE,&leave_info);
}

// add by hsl on 03/14/2017 AA支付，未开局解散，非房主，返钻
FTYPE(void) CRoomRouteChallenge::add_room_card_on_noplay_dismissed(_tint64 userID,int room_num, int cost_card, int bill_type)
{
	if (config_manager::GetInstance()->get_create_pay_type() == PAY_TYPE_DIAMOND)
	{
		CClientCmd::do_add_room_card_dbproxy(userID, room_num, cost_card, bill_type);
	}
	else
	if (config_manager::GetInstance()->get_create_pay_type() == PAY_TYPE_GOLD)
	{
		CClientCmd::do_add_room_gold_redis(userID, room_num, cost_card, bill_type);
	}
}

/* 玩家进入普通房通知 */
FTYPE(void) CRoomRouteChallenge::send_nomal_user_enter(_uint64 uid)
{
	room_route::ChanllengeRoomUserNomalEnter enter_info;
	enter_info.set_UserID(uid);
	enter_info.set_RoomID(g_server_id);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_NOMAL_USER_ENTER, &enter_info);
}

/* 玩家离开普通房通知 */
FTYPE(void) CRoomRouteChallenge::send_nomal_user_leave(_uint64 uid)
{
	room_route::ChanllengeRoomUserNomalLeave enter_info;
	enter_info.set_UserID(uid);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_NOMAL_USER_LEAVE, &enter_info);
}

/* 玩家进入WEB房通知 */
FTYPE(void) CRoomRouteChallenge::send_web_user_enter(_uint64 uid)
{
	room_route::ChanllengeRoomUserWebEnter enter_info;
	enter_info.set_UserID(uid);
	enter_info.set_RoomID(g_server_id);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_WEB_USER_ENTER, &enter_info);
}

/* 玩家离开WEB房通知 */
FTYPE(void) CRoomRouteChallenge::send_web_user_leave(_uint64 uid)
{
	room_route::ChanllengeRoomUserWebLeave enter_info;
	enter_info.set_UserID(uid);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_WEB_USER_LEAVE, &enter_info);
}

/* 玩家进入切磋房通知 */
FTYPE(void) CRoomRouteChallenge::send_qiecuo_user_enter(_uint64 uid)
{
	room_route::ChanllengeRoomUserQieCuoEnter enter_info;
	enter_info.set_UserID(uid);
	enter_info.set_RoomID(g_server_id);
	enter_info.set_GameID(config_manager::GetInstance()->get_game_id());
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_QIECUO_USER_ENTER, &enter_info);
}

/* 玩家离开切磋房通知 */
FTYPE(void) CRoomRouteChallenge::send_qiecuo_user_leave(_uint64 uid)
{
	room_route::ChanllengeRoomUserQieCuoLeave enter_info;
	enter_info.set_UserID(uid);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_QIECUO_USER_LEAVE, &enter_info);
}

/* 切磋房牌局结束 */
FTYPE(void) CRoomRouteChallenge::send_qiecuo_game_over(_uint64 user_id,_uint8 reason)
{
	room_route::CRoomQieCuoGameOver stGameOver;
	stGameOver.clear();
	stGameOver.set_UserID(user_id);
	stGameOver.set_Reason(reason);
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_QIECUO_GAME_OVER, &stGameOver);
}
@echo off
echo GoldenBank 简化比较测试
echo ========================

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请确保已安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译简化比较测试...
g++ -std=c++11 -Wall -g -O0 -I. -I../../../public -I../../../public/comm simple_compare_test.cpp game_logic.cpp game_config.cpp -o simple_compare.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 检查是否存在 test.txt 文件
if not exist "test.txt" (
    echo 警告: 未找到 test.txt 文件
    echo 正在创建示例 test.txt 文件...
    echo {"totalwin":15.5,"award":1} > test.txt
    echo 已创建示例文件
    echo.
)

echo 正在运行简化比较测试...
echo.

simple_compare.exe

echo.
echo 测试完成！
echo.
echo 生成的文件:
if exist "current_simple_result.txt" (
    echo - current_simple_result.txt: 当前计算结果
)

pause

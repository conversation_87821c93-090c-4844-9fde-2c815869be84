﻿  vtls.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  openssl.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  darwinssl.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  version.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  oauth2.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  ntlm.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  digest.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  cram.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  cleartext.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  url.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  transfer.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  tftp.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  telnet.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  strerror.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  ssh.c
..\..\..\..\lib\ssh.c(33): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  speedcheck.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  socks.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  smtp.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  smb.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  share.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  正在生成代码...
  正在编译...
  sendf.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  select.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  rtsp.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  rand.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  progress.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  pop3.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  pipeline.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  pingpong.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  multi.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  mime.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  md5.c
..\..\..\..\lib\md5.c(88): fatal error C1083: 无法打开包括文件: “openssl/md5.h”: No such file or directory
  ldap.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  imap.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  http_proxy.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  http_ntlm.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  http_digest.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  http_chunks.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  http.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  hostsyn.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  hostip6.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  正在生成代码...
  正在编译...
  hostip4.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  hostip.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  hostasyn.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  gopher.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  getinfo.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  ftplistparser.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  ftp.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  formdata.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  file.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  escape.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  easy.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  dict.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  curl_sasl.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  curl_ntlm_core.c
..\..\..\..\lib\curl_ntlm_core.c(58): fatal error C1083: 无法打开包括文件: “openssl/des.h”: No such file or directory
  cookie.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  connect.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  conncache.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  base64.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  asyn-thread.c
d:\woker\bc\code\server\svn_svr\public\lib\curl\curl-7.56.1\lib\ssh.h(28): fatal error C1083: 无法打开包括文件: “libssh2.h”: No such file or directory
  正在生成代码...

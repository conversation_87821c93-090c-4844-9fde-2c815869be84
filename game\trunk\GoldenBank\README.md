# GoldenBank 比较测试工具

## 功能说明

这个工具用于比较两个游戏结果数据：
1. **当前计算结果**：根据代码中固定的 icons 数组计算得出的结果
2. **参考数据**：从 test.txt 文件中读取的参考结果

## 文件说明

- `test_bet_roll_unit.cpp` - 核心比较测试程序
- `run_compare_test.bat` - Windows 一键运行脚本
- `game_logic.cpp` / `game_logic.h` - 游戏逻辑文件
- `game_config.cpp` / `game_config.h` - 游戏配置文件

## 使用方法

### Windows 用户（推荐）
```bash
# 双击运行或命令行执行
run_compare_test.bat
```

### 手动编译运行
```bash
# 编译
g++ -std=c++11 -Wall -g -O0 -I. -I../../../public -I../../../public/comm test_bet_roll_unit.cpp game_logic.cpp game_config.cpp -o compare_test.exe

# 运行
./compare_test.exe
```

## 测试数据

程序使用固定的 icons 数组进行测试：
```cpp
int icons[] = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};
```

## 输出说明

程序会输出：
1. **当前计算结果**：
   - totalwin（总赢分）
   - award（奖励类型）
   - 3x3 图标矩阵

2. **参考数据**：从 test.txt 文件读取的内容

3. **生成文件**：
   - `current_result.txt`：当前计算结果的详细信息

## 比较方法

1. 程序会自动显示当前计算结果
2. 读取并显示 test.txt 中的参考数据
3. 将当前结果保存到 current_result.txt
4. 用户可以手动比较两个文件的差异

## 故障排除

### 编译错误
- 确保安装了 C++ 编译器（如 MinGW）
- 检查 game_logic.cpp 和 game_config.cpp 文件是否存在
- 确保头文件路径正确

### 运行错误
- 如果没有 test.txt 文件，程序会创建一个示例文件
- 检查当前目录的读写权限

## 修改测试数据

如需测试其他数据，请修改 `test_bet_roll_unit.cpp` 中的 icons 数组：
```cpp
int icons[] = {你的测试数据};
```

## 注意事项

1. 程序会在当前目录生成 current_result.txt 文件
2. 确保有足够的磁盘空间
3. test.txt 文件应包含有效的参考数据

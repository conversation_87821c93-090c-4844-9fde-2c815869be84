# GoldenBank Protobuf 比较测试工具

## 功能说明

这个工具用于比较两个 Protobuf 对象：
1. **当前 Protobuf**：根据代码中固定的 icons 数组计算并生成的 cbtProto::SpinAck 对象
2. **参考 Protobuf**：从 test.txt 文件中读取的 JSON 数据解析成的 cbtProto::SpinAck 对象

## 文件说明

- `test_bet_roll_unit.cpp` - 核心比较测试程序
- `run_compare_test.bat` - Windows 一键运行脚本
- `game_logic.cpp` / `game_logic.h` - 游戏逻辑文件
- `game_config.cpp` / `game_config.h` - 游戏配置文件

## 使用方法

### Windows 用户（推荐）
```bash
# 双击运行或命令行执行
run_compare_test.bat
```

### 手动编译运行
```bash
# 编译（需要 protobuf 库）
g++ -std=c++11 -Wall -g -O0 -I. -I../../../public -I../../../public/comm test_bet_roll_unit.cpp game_logic.cpp game_config.cpp -lprotobuf -o compare_test.exe

# 运行
./compare_test.exe
```

## 依赖要求

- **C++ 编译器**：支持 C++11 标准
- **Protobuf 库**：Google Protocol Buffers 库
  - Windows: 可通过 vcpkg 安装 `vcpkg install protobuf`
  - Linux: `sudo apt-get install libprotobuf-dev protobuf-compiler`
  - macOS: `brew install protobuf`

## 测试数据

程序使用固定的 icons 数组进行测试：
```cpp
int icons[] = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};
```

## 输出说明

程序会输出：
1. **当前 Protobuf 对象**：
   - 从固定 icons 数组计算生成
   - 包含 totalwin、award、ackqueue 等字段
   - 保存为 JSON 格式到 `current_protobuf.json`

2. **参考 Protobuf 对象**：
   - 从 test.txt 读取 JSON 数据解析而来
   - 与当前对象进行逐字段比较

3. **比较结果**：
   - 使用 `compare_graph` 函数进行详细比较
   - 输出具体的差异信息
   - 显示 protobuf 文本格式用于调试

## 比较方法

1. **自动比较**：程序使用 Google Protobuf 的 MessageDifferencer 进行比较
2. **差异分析**：详细分析 totalwin、award、ackqueue 等字段的差异
3. **调试输出**：显示两个 protobuf 对象的文本格式便于人工检查

## 故障排除

### 编译错误
- 确保安装了 C++ 编译器（如 MinGW）
- 检查 game_logic.cpp 和 game_config.cpp 文件是否存在
- 确保头文件路径正确

### 运行错误
- 如果没有 test.txt 文件，程序会创建一个示例文件
- 检查当前目录的读写权限

## 修改测试数据

如需测试其他数据，请修改 `test_bet_roll_unit.cpp` 中的 icons 数组：
```cpp
int icons[] = {你的测试数据};
```

## 注意事项

1. 程序会在当前目录生成 current_result.txt 文件
2. 确保有足够的磁盘空间
3. test.txt 文件应包含有效的参考数据

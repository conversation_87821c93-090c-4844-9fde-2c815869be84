#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
using json = nlohmann::json;
user_game_info::user_game_info()
{
    uid = 0;
    time = 0;
    result = 0;
    pay_out = 0;
    _last_kick_user_offline_tm_sec = 0;
    user_type = 2;
    client_id = 0;
	sub_client_id = 0;
    web_token = "";
    token = "";
    cur_mode = 0;
    base_bet = 0;
    is_free = false;
    scatter_count = 0;
    reset();
}

game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    init_weights();
}


card game_logic::get_rand_card()
{
    int *weight = card_weights;
    int total = all_weights;
    int size = ALL_CARD_LEN;

    card c;
    int r = getRand(0, total - 1);
    for (int i = 0; i < size; i++)
    {
        if (r < weight[i])
        {
            c.value = i;
            //MY_LOG_PRINT("c.value = %d", c.value);
            break;
        }
        else
        {
            r -= weight[i];
        }
    }

    return c;
}

card game_logic::get_rand_card_diff(const map<int, int>& map_card_count)
{
    int card_weights_tmp[ALL_CARD_LEN];
    for(int i=0;i<ALL_CARD_LEN;i++)
        card_weights_tmp[i] = card_weights[i];
    int *weight = card_weights_tmp;
    int total = all_weights;
    int size = ALL_CARD_LEN;

    for(auto& m:map_card_count)
    {
        total-=weight[m.first];
        weight[m.first] = 0;
    }

    card c;
    int r = getRand(0, total - 1);
    for (int i = 0; i < size; i++)
    {
        if (r < weight[i])
        {
            c.value = i;
            //MY_LOG_PRINT("c.value = %d", c.value);
            break;
        }
        else
        {
            r -= weight[i];
        }
    }

    return c;
}

void game_logic::gen_list(user_game_info &game_info, int l, int num, int wild_rate)
{
    int r = getRand(1,100);
    int c1 = 1;
    int c2 = 4;
    if(game_info.is_free)
    {
        c1 = 0;
        c2 = 5;
    }
    if((r < 15 && l>c1&&l<c2))
    {
        int r1 = getRand(1,100);
        card c;
        c.value = BOX_WILD;
        int count = getRand(1,num);

        for(int i=0; i<count;i++)
        {
            game_info.vec_now_box[l].push_back(c);
        }
        
        map<int, int> map_card_count;
        c = get_rand_card();
        map_card_count[c.value]++;
        
        if(count+2 <= num)
        {
            game_info.vec_now_box[l].push_back(c);
            game_info.vec_now_box[l].push_back(c);
            count+=2;
            c = get_rand_card_diff(map_card_count);
            if(count+2 <= num)
            {                
                game_info.vec_now_box[l].push_back(c);
                game_info.vec_now_box[l].push_back(c);
            }
            else
            {
                if(count+1 <= num)
                    game_info.vec_now_box[l].push_back(c);
            }
        }
        else
        {
            if(count+1 <= num)
                game_info.vec_now_box[l].push_back(c);
        }
    }
    else
    {
        int count = 0;
        if (getRand(1, 100) < 50) // 2,2,1
        {
            map<int, int> map_card_count;

            card c = get_rand_card();
            map_card_count[c.value]++;

            if (count + 2 <= num)
            {
                game_info.vec_now_box[l].push_back(c);
                game_info.vec_now_box[l].push_back(c);
                count += 2;
                c = get_rand_card_diff(map_card_count);
                map_card_count[c.value]++;
                if (count + 2 <= num)
                {
                    game_info.vec_now_box[l].push_back(c);
                    game_info.vec_now_box[l].push_back(c);
                    count += 2;
                    c = get_rand_card_diff(map_card_count);
                    if (count + 1 <= num)
                        game_info.vec_now_box[l].push_back(c);
                }
                else
                {
                    if (count + 1 <= num)
                        game_info.vec_now_box[l].push_back(c);
                }
            }
            else
            {
                if (count + 1 <= num)
                    game_info.vec_now_box[l].push_back(c);
            }
        }
        else // 1,2,2
        {
            map<int, int> map_card_count;

            card c = get_rand_card();
            map_card_count[c.value]++;
            if (count + 1 <= num)
            {
                count++;
                game_info.vec_now_box[l].push_back(c);
            }

            c = get_rand_card_diff(map_card_count);
            map_card_count[c.value]++;
            if (count + 2 <= num)
            {
                game_info.vec_now_box[l].push_back(c);
                game_info.vec_now_box[l].push_back(c);
                count += 2;
                c = get_rand_card_diff(map_card_count);
                map_card_count[c.value]++;
                if (count + 2 <= num)
                {
                    game_info.vec_now_box[l].push_back(c);
                    game_info.vec_now_box[l].push_back(c);
                }
                else
                {
                    if (count + 1 <= num)
                        game_info.vec_now_box[l].push_back(c);
                }
            }
            else
            {
                if (count + 1 <= num)
                    game_info.vec_now_box[l].push_back(c);
            }
        }
    }
}

void game_logic::gen_list_ex(user_game_info &game_info, int l, map<int, int>& map_card_count)
{
    if (getRand(1, 100) < 50) // 2,2,1
    {
        card c = get_rand_card_diff(map_card_count);
        map_card_count[c.value]++;
        game_info.vec_now_box[l].push_back(c);
        game_info.vec_now_box[l].push_back(c);

        c = get_rand_card_diff(map_card_count);
        map_card_count[c.value]++;
        game_info.vec_now_box[l].push_back(c);
        game_info.vec_now_box[l].push_back(c);
        c = get_rand_card_diff(map_card_count);
        game_info.vec_now_box[l].push_back(c);
    }
    else // 1,2,2
    {
        card c = get_rand_card_diff(map_card_count);
        map_card_count[c.value]++;
        game_info.vec_now_box[l].push_back(c);

        c = get_rand_card_diff(map_card_count);
        map_card_count[c.value]++;
        game_info.vec_now_box[l].push_back(c);
        game_info.vec_now_box[l].push_back(c);

        c = get_rand_card_diff(map_card_count);
        map_card_count[c.value]++;
        game_info.vec_now_box[l].push_back(c);
        game_info.vec_now_box[l].push_back(c);
    }
}

void game_logic::gen_no_reward_game_data(user_game_info &game_info)
{
    map<int, int> map_card_count;
    //第一列
    gen_list(game_info,0,MAX_ROW);
    for(auto&c:game_info.vec_now_box[0])
    {
        map_card_count[c.value]++;
    }
   
    //第二列
    gen_list_ex(game_info,1,map_card_count);


    for (int i = 2; i < MAX_LIST; i++)
    {
        gen_list(game_info,i, MAX_ROW, 20);
    }

    check_card(game_info);
}

bool game_logic::gen_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();

    for(auto& d:data[0])
    {
        game_info.graph_data.push_back(d);
    }

    for (int i = 0; i < MAX_LIST; i++)
    {
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c; 
            int v = game_info.graph_data[i*MAX_ROW+j];
            c.value = v;

            game_info.vec_now_box[i].push_back(c);
            game_info.g_index++;
        }       
    }

    check_card_ex(game_info);
    // MY_LOG_PRINT("game_info.normal_platewin = %d", game_info.normal_platewin);
    // MY_LOG_PRINT("game_info.respin_platewin = %d", game_info.respin_platewin);
    // MY_LOG_PRINT("game_info.wheel_win = %d", game_info.wheel_win);

    return true;
}

bool game_logic::gen_freegame_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();
    if(data.size()< 8)
    {
        MY_LOG_ERROR("gen_freegame_data_from_graph_data failed");
        return false;
    }

    int plusCount = data.size() - 8;

    std::random_device rd;
    std::mt19937 g(rd());
    std::shuffle(data.begin(), data.end()-1, g);
    //vec_pos[0] = 1;
    int count = 8;
    int id = 0;
    int ip = 0;
    for(auto& d:data)
    {
        bool hasPlus = false;
        game_info.reset();
		game_info.comboBonus = 1;
		game_info.is_free = true;
        
        for(auto&g:d)
        {
            game_info.graph_data.push_back(g);
            if(g == BOX_PLUS)
            {
                //MY_LOG_PRINT("hasPlus id=%d", id);
                hasPlus = true;
            }
        }    
        for (int i = 0; i < MAX_LIST; i++)
        {
            for (int j = 0; j < MAX_ROW; j++)
            {
                card c;
                int v = game_info.graph_data[i * MAX_ROW + j];
                c.value = v;

                game_info.vec_now_box[i].push_back(c);
                game_info.g_index++;
            }
        }

        check_card_ex(game_info);

        
        if(hasPlus)
        {
            //MY_LOG_PRINT("id = %d", id);
            game_info.has_plus.push_back(1);
            count++;
            plusCount--;            
        }
        else
            game_info.has_plus.push_back(0);

        //MY_LOG_PRINT("game_info.all_total_multer = %d", game_info.all_total_multer);
        game_info.game_multer.push_back(game_info.total_multer);
        game_info.free_total_round.push_back(count);
        game_info.all_total_multer += game_info.total_multer;
        game_info.game_vec_remove.push_back(game_info.vec_remove);
        id++;
    }

    return true;
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

// 消除扑克
bool game_logic::remove_card(remove_info &reInfo, user_game_info &game_info)
{
    bool is_remove = false;
    bool flag1 = true;
    for (int i = 0; i < MAX_WAYS; i++)
    {
        int v1 = -1;
        bool flag = false;
        int len = 0;
        for (int j = 0; j < MAX_LIST; j++)
        {
            pos p = pay_lines[i][j];
            //MY_LOG_PRINT("p.x = %d, p.y = %d", p.x,p.y);
            if (game_info.vec_now_box[p.x][p.y].value != BOX_WILD)
            {
                if (v1 < 0)
                {
                    v1 = game_info.vec_now_box[p.x][p.y].value;
                }
                else
                {
                    if (v1 != game_info.vec_now_box[p.x][p.y].value)
                    {
                        len = j;
                        flag = true;
                        break;
                    }
                }
            }
        }
        if(!flag)
        {
            len = MAX_LIST;
        }

        if (len >=3)
        {
            for (int j = 0; j < len; j++)
            {
                pos p = pay_lines[i][j];
                game_info.vec_now_box[p.x][p.y].status = 1;
            }
            int index = len - 3;
            if (index > 2)
            {
                index = 2;
            }            
            int multer = card_multer[v1][index];            
            //MY_LOG_PRINT("i=%d, v1 = %d, len = %d, multer = %d", i, v1, len, multer);
            reward rw(v1, len, i, multer);
            reInfo.vec_reward.push_back(rw);
            reInfo.multer += multer;
            is_remove = true;
        }
    }

    return is_remove;
}

// 打印数据
void game_logic::print_data(vector<card> vec_now_box[MAX_LIST])
{
    std::ofstream ofs;

    std::remove("test.txt");
    ofs.open("test.txt", ios::out | ios::app);
    ofs << "===============" << std::endl;

    card now_box[MAX_ROW][MAX_LIST];
    // for (int i = 0; i < MAX_ROW; i++)
    // {
    //     for (int j = 0; j < MAX_LIST; j++)
    //     {
    //         now_box[i][j].value = -1;
    //     }
    // }
    for (int i = 0; i < MAX_LIST; i++)
    {
        int j = 0;
        for (auto c : vec_now_box[i])
        {
            {
                now_box[4-j][i] = c;
            }
            j++;
        }
    }

    for (int i = 0; i < MAX_ROW; i++)
    {
        for (int j = 0; j < MAX_LIST; j++)
        {
            if (now_box[i][j].status == 0)
            {
                int value = now_box[i][j].value;

                ofs << value << "             ";
            }
            else
            {
                ofs << "X" << now_box[i][j].value << "             ";
            }
        }
        ofs << std::endl;
    }
    ofs.close();
}

void game_logic::check_card(user_game_info &game_info)
{
    do
    {
        remove_info remove_data;        
        bool is_remove = remove_card(remove_data, game_info);
        remove_data.comboBonus = game_info.comboBonus;
        
        if (is_remove)
        {
            remove_data.multer *= remove_data.comboBonus;
            game_info.total_multer += remove_data.multer;
            remove_data.comboBonus = game_info.comboBonus;
            init_weights();
        }
        //print_data(game_info.vec_now_box);
        remove_data.save_box_info(game_info.vec_now_box);
        
        if (!is_remove)
        {
            game_info.vec_remove.push_back(remove_data);                
            break;
        }

        fill_remove_box(remove_data, game_info);

        game_info.vec_remove.push_back(remove_data);
        int i = game_info.vec_remove.size();
        if(i > 5)
        {
            i = 5;
        }

        if (game_info.is_free)
            game_info.comboBonus = combo_bonus[i];
        else
            game_info.comboBonus = 1;
    } while (true);
}

void game_logic::check_card_ex(user_game_info &game_info)
{
    do
    {
        remove_info remove_data;        
        bool is_remove = remove_card(remove_data, game_info);
        remove_data.comboBonus = game_info.comboBonus;
        
        if (is_remove)
        {
            remove_data.multer *= remove_data.comboBonus;
            game_info.total_multer += remove_data.multer;
            remove_data.comboBonus = game_info.comboBonus;
        }
        
        remove_data.save_box_info(game_info.vec_now_box);
        //print_data(game_info.vec_now_box);
        if (!is_remove)
        {
            game_info.vec_remove.push_back(remove_data);  
                          
            break;
        }

        fill_remove_box_ex(remove_data, game_info);

        game_info.vec_remove.push_back(remove_data);
        int i = game_info.vec_remove.size();
        if(i > 5)
        {
            i = 5;
        }

        if (game_info.is_free)
            game_info.comboBonus = combo_bonus[i];
        else
            game_info.comboBonus = 1;
    } while (true);
}

void game_logic::insert_scatter(vec_remove_info& vec_remove, int num, bool isPlus)
{    
    if (num <= 0 || vec_remove.empty())
    {
        return;
    }
    //MY_LOG_PRINT("insert_scatter num = %d", num);
    int graph_value = BOX_SCATTER;
    if(isPlus)
    {
        graph_value = BOX_PLUS;
    }
    std::random_device rd;
    std::mt19937 g(rd());
    vector<int> vec_col;
    for (int i = 0; i < MAX_LIST; i++)
    {
        vec_col.push_back(i);
    }
    std::shuffle(vec_col.begin(), vec_col.end(), g);
    int len = vec_remove.size();

    if (len <= 1)
    {
        auto &rm = vec_remove[0];
        int i = 0;
        while (num > 0)
        {
            int r = getRand(1, 100);
            for(int k= 0; k< 100; k++)
            {
                int j = r % rm.vec_now_box[vec_col[i]].size();

                if (rm.vec_now_box[vec_col[i]][j].value != BOX_WILD)
                {
                    rm.vec_now_box[vec_col[i]][j].value = graph_value;                    
                    num--;
                    break;
                }               
            }
            i++;
        }
    }
    else // if (len <= 2)
    {
        int i = 0;
        
        while (num > 0)
        {
            int col = vec_col[i]; //
            int r = getRand(1,100);
            vector<int> vec_pos;
            bool find = false;
            if(r < 50)
            {
                for(int i=0; i<MAX_ROW;i++)
                {
                    bool flag = false;
                    for(auto &rm:vec_remove)
                    {
                        for (auto &c:rm.vec_now_box[col])
                        {
                            if(c.status == 1)
                            {
                                flag = true;
                                break;
                            }
                        }
                        if(flag)
                            break;
                    }
                    if(!flag)
                    {
                        vec_pos.push_back(i);                        
                    }
                }
                if(vec_pos.size()>0)
                {
                    int p = getRand(1, 100) % vec_pos.size();
                    int x = vec_pos[p];
                    for(auto &rm:vec_remove)
                        rm.vec_now_box[col][x].value = graph_value;
                    num--;
                    find = true;
                }
            }

            if(!find)
            {
                int count = 0;
                for (int j = 0; j < MAX_ROW; j++)
                {
                    card c = vec_remove[len - 2].vec_now_box[col][j];

                    if (c.status == 1)
                    {
                        count++;
                    }
                }
                if (count > 0)
                {
                    int p = getRand(1, 100) % count+1;
                    vec_remove[len - 1].vec_now_box[col][MAX_ROW-p].value = graph_value;
                    vec_remove[len - 2].vec_fill_box[col][count-p].value = graph_value;
                    num--;
                    //MY_LOG_PRINT("col =%d, x = %d", col, x);
                    // for (auto &c : vec_remove[len - 1].vec_now_box[col])
                    // {
                    //     c.value = BOX_SCATTER;
                    //     num--;
                    // }
                }
            }
           
            i++;
            if(i >= MAX_LIST)
                break;
        }
    }
}

// 填充被消除格子
void game_logic::fill_remove_box(remove_info &reInfo, user_game_info &game_info)
{    
    for (int i = 0; i < MAX_LIST; i++)
    {
        reInfo.vec_fill_box[i].clear();
        vector<card>::iterator c = game_info.vec_now_box[i].begin();
        int len = 0;
        for (auto &rc : reInfo.vec_now_box[i])
        {
            if (rc.status == 1)
            {               
                c = game_info.vec_now_box[i].erase(c);                 
                len++;
            }
            else
            {
                c++;
            }
        }
        if(len > 0)
        {            
            gen_list(game_info,i, len, 50);
            if(i == 0 && getRand(1,100)<30)
            {       
                map<int, int> map_card_count;         
                for (auto &c : game_info.vec_now_box[0])
                {
                    map_card_count[c.value]++;
                }
                int type[5]={0};
                int k = 0;
                for (auto &v : map_card_count)
                {
                    type[k++] = v.first;
                    // MY_LOG_PRINT("v.first = %d",v.first);
                }

                for (int i = 0; i < ALL_CARD_LEN; i++)
                {
                    card_weights[i] = 0;
                }
                for(int i=0; i<5;i++)
                {
                    if(type[i] > 0)
                        card_weights[type[i]] = 1;
                }
                all_weights = 0;

                for (int i = 0; i < ALL_CARD_LEN; i++)
                {
                    all_weights += card_weights[i];
                }                
            }

            for(int k=MAX_ROW-len; k<MAX_ROW;k++)
            {
                fillInfo f;
                f.value = game_info.vec_now_box[i][k].value;
                reInfo.vec_fill_box[i].push_back(f);
            }
        }    
    }
}

// 填充被消除格子
void game_logic::fill_remove_box_ex(remove_info &reInfo, user_game_info &game_info)
{
    for (int i = 0; i < MAX_LIST; i++)
    {
        reInfo.vec_fill_box[i].clear();
        vector<card>::iterator c = game_info.vec_now_box[i].begin();
        int len = 0;
        for (auto &rc : reInfo.vec_now_box[i])
        {
            if (rc.status == 1)
            {
                c = game_info.vec_now_box[i].erase(c);
                len++;
            }
            else
            {
                c++;
            }
        }
        for(int k=0;k<len;k++)
        {
            card c;

            int v = game_info.graph_data[game_info.g_index++];
            c.value = v;
            game_info.vec_now_box[i].push_back(c);
            fillInfo f;
            f.value = v;
            reInfo.vec_fill_box[i].push_back(f);
        }
    }
}

void game_logic::rand_card(user_game_info &game_info)
{
    for (int i = 0; i < MAX_LIST; i++)
    {
        gen_list(game_info,i,MAX_ROW, 50);       
    }
    if(getRand(1,100) < 30)
    {
        map<int, int> map_card_count;
        for (auto &c : game_info.vec_now_box[0])
        {
            map_card_count[c.value]++;
        }
        int type[3];
        int k = 0;
        for (auto &v : map_card_count)
        {
            type[k++] = v.first;
            // MY_LOG_PRINT("v.first = %d",v.first);
        }
        int col = 1;
        if (getRand(1, 100) < 50)
        {
            game_info.vec_now_box[col][0].value = type[0];
            game_info.vec_now_box[col][1].value = type[0];
            game_info.vec_now_box[col][2].value = type[1];
            game_info.vec_now_box[col][3].value = type[1];
            game_info.vec_now_box[col][4].value = type[2];
        }
        else
        {
            game_info.vec_now_box[col][0].value = type[0];
            game_info.vec_now_box[col][1].value = type[1];
            game_info.vec_now_box[col][2].value = type[1];
            game_info.vec_now_box[col][3].value = type[2];
            game_info.vec_now_box[col][4].value = type[2];
        }
    }
    // col = 2;
    // if(getRand(1,100)< 50)
    // {
    //     game_info.vec_now_box[col][0].value = type[0];
    //     game_info.vec_now_box[col][1].value = type[0];
    //     game_info.vec_now_box[col][2].value = type[1];
    //     game_info.vec_now_box[col][3].value = type[1];
    //     game_info.vec_now_box[col][4].value = type[2];
    // }
    // else
    // {
    //     game_info.vec_now_box[col][0].value = type[0];
    //     game_info.vec_now_box[col][1].value = type[1];
    //     game_info.vec_now_box[col][2].value = type[1];
    //     game_info.vec_now_box[col][3].value = type[2];
    //     game_info.vec_now_box[col][4].value = type[2];
    // }
    check_card(game_info);
}

void game_logic::init_weights()
{
    // std::random_device rd;
    // std::mt19937 g(rd());
    vector<int> vec_col;
    int weight[ALL_CARD_LEN] = {10,10,10,10,5,5,3,3,2,1};
    //int weight[ALL_CARD_LEN] = {2,2,2,2,2,2,2,2,2,2};
    for (int i = 0; i < ALL_CARD_LEN; i++)
    {
        vec_col.push_back(weight[i]);
    }
    //std::shuffle(vec_col.begin(), vec_col.end(), g);
	
	for (int i=0;i< ALL_CARD_LEN;i++)
	{
		card_weights[i] = vec_col[i];
	}	

	all_weights = 0;

	for (int i = 0; i < ALL_CARD_LEN; i++)
	{
		all_weights += card_weights[i];
	}
}
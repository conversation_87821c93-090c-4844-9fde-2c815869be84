#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <cstdlib>
#include <ctime>

// 简化的日志宏
#define MY_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

// 包含游戏逻辑头文件
#include "game_logic.h"

// 简化的测试函数
void simple_test_bet_roll_result()
{
    std::cout << "=== 简化版 test_bet_roll_result 测试 ===" << std::endl;
    
    try {
        // 创建游戏信息
        user_game_info game_info;
        game_info.cur_mode = 0;
        game_info.bet = 1000;
        
        // 设置测试图标数据
        int icons[9] = {0,11,0,1,0,2,0,1,0};
        for (int i = 0; i < 9; i++) {
            game_info.graph_data.push_back(icons[i]);
        }
        
        std::cout << "输入图标数据: ";
        for (int icon : game_info.graph_data) {
            std::cout << icon << " ";
        }
        std::cout << std::endl;
        
        // 调用游戏逻辑计算
        game_logic::GetInstance()->CalcResultTimes(game_info);
        
        // 输出结果
        std::cout << "计算结果:" << std::endl;
        std::cout << "  - 游戏结果: " << game_info.result << std::endl;
        std::cout << "  - 奖励类型: " << game_info.award_type << std::endl;
        std::cout << "  - 下注金额: " << game_info.bet << std::endl;
        std::cout << "  - 当前模式: " << game_info.cur_mode << std::endl;
        
        // 输出图标数组（如果有的话）
        std::cout << "  - 图标矩阵:" << std::endl;
        for (int i = 0; i < 3; i++) {
            std::cout << "    ";
            for (int j = 0; j < 3; j++) {
                std::cout << game_info.Icons[i][j] << " ";
            }
            std::cout << std::endl;
        }
        
        std::cout << "测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "测试过程中发生异常: " << e.what() << std::endl;
    }
}

// 测试多种图标组合
void test_multiple_combinations()
{
    std::cout << "\n=== 多种图标组合测试 ===" << std::endl;
    
    // 定义测试用例
    std::vector<std::vector<int>> test_cases = {
        {0,11,0,1,0,2,0,1,0},    // 原始测试用例
        {1,1,1,2,2,2,3,3,3},     // 三种不同的三连
        {4,4,4,0,0,0,0,0,0},     // ICON_7 三连
        {5,5,5,0,0,0,0,0,0},     // ICON_BONUS 三连
        {5,5,0,0,0,0,0,0,0},     // ICON_BONUS 两个
        {5,0,0,0,0,0,0,0,0},     // ICON_BONUS 一个
        {0,0,0,0,0,0,0,0,0},     // 全空
        {11,11,11,0,0,0,0,0,0},  // WILD 三连
    };
    
    std::vector<std::string> test_names = {
        "原始测试用例",
        "三种不同三连",
        "ICON_7三连",
        "ICON_BONUS三连", 
        "ICON_BONUS两个",
        "ICON_BONUS一个",
        "全空测试",
        "WILD三连测试"
    };
    
    for (size_t test_idx = 0; test_idx < test_cases.size(); test_idx++) {
        std::cout << "\n--- 测试用例 " << (test_idx + 1) << ": " << test_names[test_idx] << " ---" << std::endl;
        
        try {
            user_game_info game_info;
            game_info.cur_mode = 0;
            game_info.bet = 1000;
            
            // 设置测试图标
            for (int icon : test_cases[test_idx]) {
                game_info.graph_data.push_back(icon);
            }
            
            std::cout << "输入: ";
            for (int icon : game_info.graph_data) {
                std::cout << icon << " ";
            }
            std::cout << std::endl;
            
            // 计算结果
            game_logic::GetInstance()->CalcResultTimes(game_info);
            
            std::cout << "结果: " << game_info.result << ", 奖励类型: " << game_info.award_type << std::endl;
            
            // 显示图标矩阵
            std::cout << "图标矩阵:" << std::endl;
            for (int i = 0; i < 3; i++) {
                std::cout << "  ";
                for (int j = 0; j < 3; j++) {
                    std::cout << game_info.Icons[i][j] << " ";
                }
                std::cout << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "测试用例 " << (test_idx + 1) << " 发生异常: " << e.what() << std::endl;
        }
    }
}

// 测试 get_odds 方法
void test_get_odds()
{
    std::cout << "\n=== 测试 get_odds 方法 ===" << std::endl;
    
    try {
        // 创建图标计数数组
        int iconCount[12] = {0}; // ICON_WILD + 1 = 12
        int nWildCount = 0;
        int curLight = 0;
        
        // 测试不同的图标组合
        std::vector<std::string> test_names = {
            "ICON_BAR_1 三连",
            "ICON_BAR_2 三连", 
            "ICON_BAR_3 三连",
            "ICON_7 三连",
            "ICON_BONUS 三个",
            "ICON_BONUS 两个",
            "ICON_BONUS 一个",
            "无匹配"
        };
        
        // 测试用例
        std::vector<std::vector<int>> icon_counts = {
            {0,3,0,0,0,0,0,0,0,0,0,0}, // ICON_BAR_1 三连
            {0,0,3,0,0,0,0,0,0,0,0,0}, // ICON_BAR_2 三连
            {0,0,0,3,0,0,0,0,0,0,0,0}, // ICON_BAR_3 三连
            {0,0,0,0,3,0,0,0,0,0,0,0}, // ICON_7 三连
            {0,0,0,0,0,3,0,0,0,0,0,0}, // ICON_BONUS 三个
            {0,0,0,0,0,2,0,0,0,0,0,0}, // ICON_BONUS 两个
            {0,0,0,0,0,1,0,0,0,0,0,0}, // ICON_BONUS 一个
            {0,0,0,0,0,0,0,0,0,0,0,0}, // 无匹配
        };
        
        for (size_t i = 0; i < test_names.size(); i++) {
            std::cout << "\n测试: " << test_names[i] << std::endl;
            
            // 复制图标计数
            for (int j = 0; j < 12; j++) {
                iconCount[j] = icon_counts[i][j];
            }
            
            curLight = 0;
            int odds = game_logic::GetInstance()->get_odds(curLight, iconCount, nWildCount);
            
            std::cout << "  赔率: " << odds << ", 灯光值: " << curLight << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "get_odds 测试发生异常: " << e.what() << std::endl;
    }
}

// 主函数
int main()
{
    std::cout << "GoldenBank 简化测试单元" << std::endl;
    std::cout << "========================" << std::endl;
    
    // 初始化随机种子
    srand(time(nullptr));
    
    try {
        // 运行基本测试
        simple_test_bet_roll_result();
        
        // 运行多组合测试
        test_multiple_combinations();
        
        // 测试 get_odds 方法
        test_get_odds();
        
    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n=== 所有测试完成 ===" << std::endl;
    std::cout << "按回车键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}

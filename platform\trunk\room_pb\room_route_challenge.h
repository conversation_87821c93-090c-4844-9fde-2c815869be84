﻿

#ifndef _ROOM_ROUTE_CHALLENGE_H_
#define _ROOM_ROUTE_CHALLENGE_H_

#include "room_common.h"
#include "table_manager.h"

class CRoomRouteChallenge
{
public:
    CRoomRouteChallenge();
    virtual ~CRoomRouteChallenge();

public:
	static int g_send_room_info_time;

public:

	static FTYPE(void) set_info_time();

    static FTYPE(void) connect_gate(void * obj, enAction action, const void * pdata, int size);

    static FTYPE(void) timer_check(void * obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);

public:

	/* 房间状态改变定时同步房间信息(状态,桌子数) */
    static FTYPE(void) send_room_info();

	/* 一局开始,结束同步约战桌信息 */
	static FTYPE(void) send_challenge_game_info(_uint64 owner_id,_tint64 room_num,_uint8 total_round,_uint8 cur_round, _uint8 max_player);

	/* 一局结束, 同步约战桌信息 */
	static FTYPE(void) send_challenge_play_game_end(_uint64 owner_id,_tint64 room_num,_uint8 total_round,_uint8 cur_round, _uint8 max_player,MAPSCORE &inMapScore);

	/* 约战牌局结束通知  */
	static FTYPE(void) send_challenge_game_over(_uint64 owner_id,_tint64 room_num,int cost_card,_uint8  reason);

	/* 创建约战牌局结果通知  */
	static FTYPE(void) send_challenge_create_result(_uint64 owner_id,_tint64 room_num,int ret,CTable* p_table, int is_web=0, int web_sid=0);

	/* 玩家进入牌局通知  */
	static FTYPE(void) send_challenge_user_enter(_uint64 uid,const char* uname,_tint64 room_num, int clientid, const char* face);

	/* 玩家离开牌局通知  */
	static FTYPE(void) send_challenge_user_leave(_uint64 uid, _tint64 room_num, _uint64 cost_card = 0, int isfaile=0);

	/* 无开局解散牌局，AA支付模式下非房主返钻*/
	static FTYPE(void) add_room_card_on_noplay_dismissed(_tint64 userID,int room_num,int room_card, int bill_type);

	/* 玩家进入普通房通知 */
	static FTYPE(void) send_nomal_user_enter(_uint64 uid);

	/* 玩家离开普通房通知 */
	static FTYPE(void) send_nomal_user_leave(_uint64 uid);

	/* 玩家进入WEB房通知 */
	static FTYPE(void) send_web_user_enter(_uint64 uid);

	/* 玩家离开WEB房通知 */
	static FTYPE(void) send_web_user_leave(_uint64 uid);

	/* 玩家进入切磋房通知 */
	static FTYPE(void) send_qiecuo_user_enter(_uint64 uid);

	/* 玩家离开切磋房通知 */
	static FTYPE(void) send_qiecuo_user_leave(_uint64 uid);

	/* 切磋房牌局结束 */
	static FTYPE(void) send_qiecuo_game_over(_uint64 user_id,_uint8 reason);

private:
    static _uint32                         m_timer_id;
};

#endif //_ROOM_ROUTE_CHALLENGE_H_

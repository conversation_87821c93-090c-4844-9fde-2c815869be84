﻿#ifndef _RAND_POSITION_
#define _RAND_POSITION_
#include "comm_msg.h"
#include "ip_location.h"

#define SCREEN_LIMIT_COUNT 6
#define FIRST_RATE_COUNT 32
#define TOTAL_FRIST_RATE 2320
const char screen_data[SCREEN_LIMIT_COUNT][128] = { "柬埔寨","局域网","菲律宾","香港","共享地址","新加坡" };
const int frist_rate[FIRST_RATE_COUNT] = {90,90,90,90,10,90,90,90,10,90,90,90,90,90,90,90,90,90,90,90,90,90,10,10,90,10,90,10,90,90,90,10};

static bool is_screen(const char pos[128])
{
	return true;
	//非中文版不随机地区
	//int l_type = get_language_type();
	//MY_LOG_DEBUG("is_screen...l_type:%d  position:%s", l_type, pos);
	//if (l_type != LANGUAGE_TYPE_ENGLISH)
	//	return true;

	for (int i = 0; i < SCREEN_LIMIT_COUNT; i++)
	{
		if (strcmp(pos, screen_data[i]) == 0)
		{
			return true;
		}
	}
	return false;
}

static void get_first_name(int id, char desc[32])
{
	char name[50][32] = {
		"河北","重庆","福建","四川",
		"新疆","山西","辽宁","吉林",
		"黑龙江","江苏","浙江","安徽",
		"江西","山东","河南","湖北",
		"湖南","广东","海南","贵州",
		"云南","陕西","甘肃","青海",
		"宁夏","西藏","广西","内蒙古",
		"天津","北京","上海","台湾"
	};
	sprintf(desc, "%s", name[id]);
}

static int get_first_index()
{
	int rand_rate = rand() % TOTAL_FRIST_RATE;
	for (int i = 0; i < FIRST_RATE_COUNT; i++)
	{
		rand_rate -= frist_rate[i];
		if (rand_rate <= 0)
			return i;
	}
	return 17;
}

static void get_second_name(int id0, int id1, char desc[32])
{
	char name[50][50][32] = {
		{ "石家庄","唐山","秦皇岛","邯郸","邢台","保定","张家口","承德","廊坊","衡水","沧州" },
		{ "渝中","江北","双桥","渝北","巴南","万盛","万州","黔江","长寿","永川","合川","江津","南岸","南川","綦江","北碚","潼南","荣昌","璧山","大足","铜梁","梁平","城口","垫江","武隆","丰都","奉节","忠县","开县","云阳","巫溪","石柱","秀山","西阳","彭水","涪陵","大渡口","沙坪坝","九龙坡" },
		{ "福州","厦门","莆田","泉州","漳州","龙岩","三明","南平","宁德" },
		{ "宜宾","成都","绵阳","攀枝花","泸州","眉山","自贡","甘牧","阿坝州","资阳","雅安","巴中","达州","德阳","南充","广安","乐山","内江","遂宁","广元" },
		{ "吐鲁番","哈密","伊犁","阿勒泰","和田","喀什","克拉玛依","巴音郭楞","克州","博尔塔拉","阿克苏","塔城","昌州","乌鲁木齐" },
		{ "大同","太原","临汾","运城","忻州","长治","晋城","吕梁","晋中","朔州","阳泉" },
		{ "本溪","沈阳","朝阳","大连","阜新","抚顺","锦州","铁岭","辽阳","盘锦","营口","丹东","葫芦岛","鞍山" },
		{ "松原","延边","白山","通化","辽源","吉林","长春","白城","四平" },
		{ "齐齐哈尔","牡丹江","哈尔滨","黑河","鸡西","伊春","大庆","绒化","七台河","佳木斯","大兴安岭","双鸭山","鹤岗" },
		{ "无锡","南京","徐州","常州","苏州","南通","扬州","镇江","泰州","盐城","宿迁","淮安","连云港" },
		{ "绍兴","温州","宁波","杭州","嘉兴","湖州","金华","舟山","台州","丽水","鄞州" },
		{ "马鞍山","铜陵","淮北","淮南","蚌埠","芜湖","合肥","安庆","宣城","六安","池州","宿州","亳州","阜阳","滁州","黄山","巢湖" },
		{ "景德镇","吉安","赣州","新余","九江","萍乡","南昌","抚州","宜春","上饶","鹰潭" },
		{ "日照","临沂","济宁","泰安","菏泽","聊城","德州","济南","滨州","莱芜","威海","烟台","滩坊","东营","枣庄","淄博","青岛" },
		{ "平顶山","周口","商丘","焦作","洛阳","开封","郑州","驻马店","三门峡","济源","信阳","南阳","漯河","许昌","濮阳","安阳","新乡","鹤壁" },
		{ "武汉","黄石","恩施","十堰","荆州","直辖","随州","威宁","襄樊","黄冈","孝感" },
		{ "张家界","怀化","益阳","岳阳","湘潭","株洲","长沙","湘西","娄底","永州","郴州","常德","邵阳","衡阳" },
		{ "广州","深圳","珠海","汕头","韶关","惠州","河源","梅州","湛江","汕尾","东莞","中山","江门","佛山","茂名","肇庆","清远","潮州","揭阳","云浮","阳江" },
		{ "三亚","海口","东方","儋州","万宁","三沙" },
		{ "岭南","贵阳","六盘水","铜仁","毕节","闽西","遵义","安顺" },
		{ "曲靖","昆明","玉溪","保山","昭通","迪庆","怒江","德宏","大理","楚雄","红河","文山","西双版纳","丽江","临沧","思茅" },
		{ "铜川","西安","宝鸡","咸阳","渭南","延安","汉中","安康","商洛","榆林" },
		{ "嘉峪关","金昌","兰州","酒泉","天水","张掖","平凉","白银","定西","临夏","甘南","武威","陇南","庆阳" },
		{ "海东","西宁","海北","玉树","海西","海南","黄南","果洛" },
		{ "石嘴山","银川","吴忠","固原","中卫" },
		{ "阿里","拉萨","昌都","日喀则","山南","那曲","林芝" },
		{ "柳州","桂林","南宁","梧州","钦州","玉林","白色","防城","贵港","河池","贺州","来宾","北海","崇左" },
		{ "通辽","呼和浩特","赤峰","包头","乌海","呼伦贝尔","兴安","巴音淖尔","鄂尔多斯","锡林郭勒","乌兰察布","阿拉善盟" },
		{ "河东","河西","南开","红桥","塘沽","汉沽","大港","东丽","西青","北辰","津南","武清","宝坻","静海","宁河","和平" },
		{ "朝阳","丰田","门头沟","石景山","海淀","房山","通州","顺义","大兴","怀柔","平谷","密云","延庆","昌平","东城","西城" },
		{ "卢湾","徐汇","徐家汇","长宁","静安","普陀","闸北","虹口","杨浦","宝山","问行","嘉定","浦东","松江","青浦","南汇","奉贤","黄埔","崇明" },
		{ "新北","高雄","嘉义","基隆","台南","台北" }
	};
	sprintf(desc, "%s", name[id0][id1]);
}


static void get_rand_postion(char desc[128])
{
	//非中文版不随机地区
	//if (get_language_type() != LANGUAGE_TYPE_ENGLISH)
	//{
		db_ip_locationinfo ipinfo;
		if (iplocation_inst->get_rand_location(ipinfo))
		{
			sprintf(desc, "%s" ,ipinfo.szcity);
		}
		return;
	//}
	
	/*int rand_id0 = get_first_index();
	int rand_id1 = rand() % 6;
	char first_name[32] = { 0 };
	char second_name[32] = { 0 };
	char last_pos[128] = { 0 };
	get_first_name(rand_id0, first_name);
	get_second_name(rand_id0, rand_id1, second_name);
	sprintf(desc, "%s%s", first_name, second_name);
	if (strlen(desc) > 15)
		sprintf(desc, "%s", second_name);*/
}


#endif

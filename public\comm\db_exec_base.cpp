﻿/*
-----------------------------------------------------------------------------

File name        :   db_exec_base.cpp       
Author           :    
Version          :   1.0            
Date             :      
Description      :   DB代理客户端实现  

-----------------------------------------------------------------------------
*/
#include "../../public/lib/tinyxml/tinyxml.h"
#include "db_exec_base.h"
#pragma warning(disable:4996)

static CDBProxyClient g_dbproxy_install;
IDBPClient*  g_dbproxy = &g_dbproxy_install;
static CDBProxyClientManager g_dbproxy_manager_install;
IDBPClientManager*  g_dbproxy_namager = &g_dbproxy_manager_install;

CDBProxyClient::CDBProxyClient(void)
{
    m_com       = 0;
    m_log       = 0;
    m_pattemper = 0;
	m_port      = 0;
	m_plat		= 0;
}

CDBProxyClient::~CDBProxyClient(void)
{

}

//初始化配置
void CDBProxyClient::init_config(const char* ip, int port)
{
	m_ip = ip;
	m_port = port;
}

bool CDBProxyClient::start(ICommon* pcom,ILog* plog,IAttemper*pattemper)
{
    m_com       = pcom;
    m_log       = plog;
    m_pattemper = pattemper;

    if(!pcom || !plog) return false;
    if(!m_client.GetInterface())
    {
        m_client.DLLInit(SVRLIB_NAME,"_tcp_client_instance","_tcp_client_free");
        if(!m_client.GetInterface())  
        {
            if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] instance dbproxy net fail()");
            return false;
        }
    }

    if(!m_bfpool.GetInterface())
    {
        m_bfpool.DLLInit(SVRLIB_NAME,"_block_poolex_instance","_block_poolex_free");
        if(!m_bfpool.GetInterface())  
        {
            if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] instance dbproxy buffer pool fail()");
            return false;
        }
    }

    tagClientInit initdata;
    initdata.psink         = this; //回调接口
    initdata.max_item      = 1000; //最大并发
    initdata.thread_count  = 1;    //工作线程
    initdata.check_func    = on_check_connect; //检测回调地址（非必须选项，建议不使用）,周期间断回调
    initdata.check_obj     = this; //检测设置对象（非必须选项，建议不使用）
    initdata.recv_block    = RCEV_BUFF_SIZE;
    initdata.send_block    = 1024*8;
    initdata.max_send_buf  = 1024*1024*10;
    m_client->init(&initdata,sizeof(initdata));

    m_client->start();
    m_client->create_connect(m_socketid,this);

    m_last_check_time = 0;
    on_check_connect(this);

    return true;
}

bool CDBProxyClient::stop()
{
    do 
    {
        CGuard guard(m_setionlist_lock);
        map<_uint32,TBlock*> ::iterator it = m_setionlist.begin();
        for ( ; it != m_setionlist.end() ; it)
        {
            m_client->kill_timer(it->first);
            m_bfpool->push(it->second);
        }
        m_setionlist.clear();
    } while (false);

    do 
    {
        CGuard guard(m_send_list_lock);
        m_send_list.clear();
    } while (false);

    m_client->free_connect(m_socketid);
    return m_client->stop();
}

bool CDBProxyClient::post_exec(void*obj,DBPSINK sink,_uint64 UserID,const char* cmd,const char* data,cmd_dbproxy::CDBProxyExceSection &section,bool b_wait_dbresult)
{
    if(0 == m_socketid) return false;
    if(!cmd)
    {
        if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] error param info!()");
        return false;
    }

    _uint32  requestid = m_client->set_timer(this,on_request_timeout,0,REQUEST_TIMEOUT,1,0);
    if(0 == requestid)
    {
        if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] create request timer fail()");
        return false;
    }

    //保存会话数据
    section.set_RequestTime(m_com->_get_tick_count());
    section.set_RequestID(requestid);
    section.set_RequestCMD(cmd,0);
    section.set_RequestData(data,0);

    int bfs = sizeof(tagSectionHead) + section.bfsize();
    TBlock* bf  = m_bfpool->pop(__max(bfs,1024));
    if(!bf)
    {
        m_client->kill_timer(requestid);
        if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] get section mem fail()");
        return false;
    }

    tagSectionHead &head = *((tagSectionHead*)bf);
    head.obj   = obj;
    head.sink  = sink;
    head.uid   = UserID;
    head.b_wait_dbresult = b_wait_dbresult;
    head.slen  = section.pack((char*)bf+sizeof(tagSectionHead),bfs - sizeof(tagSectionHead));

    do 
    {
        CGuard guard(m_setionlist_lock);
        m_setionlist[requestid] = bf;
    } while (false);
    
    __send(requestid,UserID,cmd,data,b_wait_dbresult,false);

    return true;
}

FTYPE(void) CDBProxyClient::on_log(TLOGTYPE logtype,const char* sz_msg)
{
    if(m_log) m_log->write_log(logtype,"[DBP] %s)",sz_msg);
}

bool CDBProxyClient::__get_dbproxy_ipinfo(char* szip,int ipbfs,int &port)
{
	if (port != 0)
	{
		szip = (char*)m_ip.c_str();
		port = m_port;
		return true;
	}

    TiXmlDocument xmlcfg;
    TiXmlElement* pRoot = 0; 
    TiXmlElement* pItem = 0;
    char xmlfile[1024];

    if(!m_com->_get_app_path(xmlfile,sizeof(xmlfile))) return false;
    __sprintf(xmlfile+strlen(xmlfile),sizeof(xmlfile)-strlen(xmlfile),"config.xml");

    if(!xmlcfg.LoadFile(xmlfile)) return false;
    if(!(pRoot = xmlcfg.FirstChildElement("config")))        return false;
    if(!(pItem = pRoot->FirstChildElement("dbproxy")))       return false;
    if(!pItem->Attribute("ip") || !pItem->Attribute("port")) return false;

    __strcpy_s(szip,(_uint32)ipbfs,pItem->Attribute("ip"));
    port = atoi(pItem->Attribute("port"));

    return true;
}

FTYPE(void) CDBProxyClient::on_check_connect(void* obj)
{
    CDBProxyClient* pthis = (CDBProxyClient*)obj;
    _uint32 spt = pthis->m_com->_cmp_tick_count(pthis->m_com->_get_tick_count(),pthis->m_last_check_time);
    if(spt < 1000) return;

    pthis->m_last_check_time = pthis->m_com->_get_tick_count();
    if(pthis->m_client->is_online(pthis->m_socketid))
    {
        spt = pthis->m_com->_cmp_tick_count(pthis->m_com->_get_tick_count(),pthis->m_last_sndheart_time);
        if(spt < SEND_HEART_SPACE_MSEC) return;
        pthis->m_last_sndheart_time = pthis->m_com->_get_tick_count();

        pthis-> __send(0,0,"heartbeat",0,false,false); //发送心跳
    }
    else
    {
        if(pthis->m_b_connecting) 
            return;

        int  port = 0;
        char szip[128];
        if(!pthis->__get_dbproxy_ipinfo(szip,sizeof(szip),port))
        {
            if(pthis->m_log) pthis->m_log->write_log(LOG_TYPE_ERROR,"[DBP] get dbproxy ip info fail!)");
            return ;
        }

        if(pthis->m_log) pthis->m_log->write_log(LOG_TYPE_PRINT,"[DBP] connecting (%s:%d)......",szip,port);
        if(!pthis->m_client->connect_asyn(pthis->m_socketid,szip,port,CONNECT_TIMEOUT))
        {
            if(pthis->m_log) pthis->m_log->write_log(LOG_TYPE_ERROR,"[DBP] connect (%s:%d) fail!",szip,port);
            return ;
        }

        pthis->m_b_connecting = true;
    }
}

FTYPE(void) CDBProxyClient::on_connected(_uint32 type,socket_id sid)
{
    if(m_log) m_log->write_log(LOG_TYPE_PRINT,"[DBP] dbproxy conected!)");

    m_b_connecting = false;
    m_last_sndheart_time = m_com->_get_tick_count();
    __check_sendlist();
	if (m_connect_func != NULL)
	{
		m_connect_func();
	}
}

FTYPE(void) CDBProxyClient::on_disconnected(_uint32 type,socket_id sid,int code,int ext_code)
{
    if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] dbproxy disconected!)");
    m_b_connecting = false;
}

//获取一行数据，返回值： 0- 成功  1 - 数据不完整  -1-错误数据
inline int __get_line(const char* bf,int bfs,int off,const char*& pline,int &linelen)
{
    const char* phead_begin = bf+off;
    const char* phead_end = strstr(phead_begin,"\r\n");

    if(!phead_end) //数据未接受完整
    {
        if(bfs - off >= MAX_HEAD_SIZE) //行数据过大，属于无效数据包
            return -1;
        return 1;
    }

    linelen = phead_end - phead_begin + 2;
    if(linelen > bfs - off) //数据未接收完毕
    {
        if(linelen > MAX_HEAD_SIZE) //行数据过大，属于无效数据包
            return -1;
        return 1;
    }

    pline = phead_begin;
    return 0;
}

//判断是否以某个字符串开始，返回值： 大于0- 成功  0-失败  
inline int __is_substr_line_begin(const char*str,int len,const char* sub)
{
    if(!str || !sub) return 0;
    int lensub = (int)strlen(sub);
    if(lensub > len) return 0;

    for (int i=0 ; i < lensub ; i++)
    {
        if(toupper(str[i]) != toupper(sub[i])) return 0;
    }
    return lensub;
}

//获取数据大小，返回值： 大于0- 成功  1 - 数据不完整  -1-错误数据
inline int __get_data_length(const char* bf,int bfs,int off,int &datalen)
{
    const char* pline   = 0;
    int         linelen = 0;

    if(bfs - off < sizeof(LENGTH_TAG)) //Length:
        return 1;

    if(__is_substr_line_begin(bf+off,bfs-off,LENGTH_TAG) == 0)
        return -1;

    int ret = __get_line(bf,bfs,off,pline,linelen);
    if(-1 == ret) 
        return -1;
    else if(1 == ret)
    {
        if(bfs - off > 30)
            return -1;
        return 1;
    }
    else
    {
        datalen = atoi(pline + strlen(LENGTH_TAG));
        if(datalen < 0 && datalen > RCEV_BUFF_SIZE - MAX_HEAD_SIZE)
            return -1;

        return 0;
    }
}

//分析数据
inline int __analy_data(const char* bf,int bfs,int &off,const char*&phead,int&headsize,const char*& data,int &datasize)
{
    phead    = 0;
    headsize = 0;
    data     = 0;
    datasize = 0;

    int ret  = -1;
    int hlen = 0;
    int dlen = 0;
    //获取数据大小，返回值： 大于0- 成功  1 - 数据不完整  -1-错误数据
    ret = __get_data_length(bf,bfs,off,dlen);
    if(ret < 0)
        return ERROR_PACKET;

    else if(ret > 0) //数据未接受完整
        return off;

    const char* phead_begin = bf+off;
    const char* phead_end = strstr(phead_begin,"\r\n\r\n");
    if(!phead_end) //数据未接受完整
    {
        if(bfs - off >= MAX_HEAD_SIZE) //包头过大，属于无效数据包
            return ERROR_PACKET;
        return off;
    }

    hlen = phead_end - phead_begin + 4;
    if(hlen > bfs - off) //数据未接收完毕
    {
        if(hlen > MAX_HEAD_SIZE) //包头过大，属于无效数据包
            return ERROR_PACKET;
        return off;
    }

    if(hlen + dlen > bfs - off) //数据未接收完毕
    {
        if(hlen + dlen > RCEV_BUFF_SIZE) //包过大，属于无效数据包
            return ERROR_PACKET;
        return off;
    }

    phead    = phead_begin;
    headsize = hlen;
    data     = dlen > 0 ? phead + headsize : 0;
    datasize = dlen;

    off += (hlen + dlen);

    return off;
}

FTYPE(int)  CDBProxyClient::on_recv(_uint32 type,socket_id sid,const void* p_data,int size,_uint64 bind_tag,const void* p_bind)
{
    int off = 0;

    if(size < sizeof(LENGTH_TAG)) 
        return 0;

    if(m_log) m_log->write_log(LOG_TYPE_INFO,"[DBP] recv data(len=%d):\r\n%s",size,(const char*)p_data);

    while(off < size)
    {
        int  head_len = 0;
        int  data_len = 0;
        const char* szask_head = 0;
        const char* szask_date = 0;

        int oldoff = off;
        int ret = __analy_data((const char*)p_data,size,off,szask_head,head_len,szask_date,data_len);


        if(ERROR_PACKET == ret) //错误数据包
        {
            if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] error data(len=%d):\r\n%s",size-off,(const char*)p_data + oldoff);
            return ret;
        } 

        if(oldoff == off) 
        {
            if (m_log) m_log->write_log(LOG_TYPE_DEBUG, "[DBP] recv 数据包不完整");
            return off;
        }

        if(!m_pattemper)
            on_atp_recv(szask_head,head_len,szask_date,data_len);
        else
        {
            WLPARAM param;
            param.obj = this;
            param.tag = head_len;
            if (!m_pattemper->post((FAttemperSink)on_attemper_recv,(void*)szask_head,head_len,(void*)szask_date,data_len,0,0,&param))
            {
                if (m_log)
                {
                    m_log->write_log(LOG_TYPE_ERROR, "[DBP] recv post fail! head:%s data:%s", szask_head ? szask_head : "", szask_date ? szask_date : "");
                }
            }
        }
    }

    return -1;
}

FTYPE(void) CDBProxyClient::on_event(_uint32 type,socket_id sid,SEVTCODE code,_uint32 lparam,_uint32 wparam)
{
    switch(code)
    {
    case SEVENT_CODE_SEND_COMPLETE:   //发送完毕
        __check_sendlist();
        break;

    case SEVENT_CODE_CONN_FAIL:       //连接失败
    case SEVENT_CODE_CONN_TIMEOUT:    //连接超时
        if(m_log) m_log->write_log(LOG_TYPE_ERROR,"[DBP] connect dbproxy fail! code=%d)",code);
        m_b_connecting = false;
        break;
    }
}

FTYPE(void)  CDBProxyClient::on_request_timeout(void*p_obj,socket_id requestid,_uint32 delay,_uint32 repeat,void* pdata)
{
    CDBProxyClient* pthis = (CDBProxyClient*)p_obj;
    if(!pthis->m_pattemper)
        pthis->on_atp_timeout(requestid);
    else
    {
        WLPARAM param;
        param.obj = pthis;
        param.tag = requestid;
        if (!pthis->m_pattemper->post((FAttemperSink)on_attemper_timeout,0,0,0,0,0,0,&param))
        {
            if (pthis->m_log)
            {
                pthis->m_log->write_log(LOG_TYPE_ERROR, "db_timeout post apt fail! requestid:%u", requestid);
            }
        }
    }
}

bool CDBProxyClient::__send(_uint32 requestid,_uint64 uid,const char* cmd,const char* data,bool b_wait_dbresult,bool bcheck)
{
    char head[1024]={0};
    int hlen = 0;
    int dlen = data ? strlen(data) : 0;
    hlen += __sprintf(head + hlen,sizeof(head)-hlen,"%s%d\r\n",LENGTH_TAG,dlen);
    if(0 != requestid)
    {
        hlen += __sprintf(head + hlen,sizeof(head)-hlen,"section:%d\r\n",requestid); 
    }
    hlen += __sprintf(head + hlen,sizeof(head)-hlen,"cmd:%s\r\n",cmd);
    hlen += __sprintf(head + hlen,sizeof(head)-hlen,"uid:%llu\r\n",uid);
    hlen += __sprintf(head + hlen,sizeof(head)-hlen,"wait:%s\r\n",b_wait_dbresult?"yes":"no");
    hlen += __sprintf(head + hlen,sizeof(head)-hlen,"\r\n");

    m_log->write_log(LOG_TYPE_INFO, "db_send head:%s data:%s", head, data ? data : "");

    if(m_client->send(m_socketid,(void*)head,hlen,(void*)data,dlen)) return true;

    if(0 != requestid)
    {
        CGuard guard(m_send_list_lock);
        if(bcheck) 
            m_send_list.push_front(requestid);
        else 
            m_send_list.push_back(requestid);
    }

    return false;
}

void CDBProxyClient::__check_sendlist()
{
    do 
    {
        _uint32 requestid =  0;
        do 
        {
            CGuard guard(m_send_list_lock);
            if(m_send_list.size() <= 0) return;
            list<_uint32>::iterator it = m_send_list.begin();
            requestid = *it;
            m_send_list.pop_front();
        } while (false);

        tagSectionHead sechead;
        cmd_dbproxy::CDBProxyExceSection section;
        if(!__get_section(requestid,section,sechead,false)) continue;

        if(!__send(requestid,sechead.uid,section.get_RequestCMD(),section.get_RequestData(),sechead.b_wait_dbresult,true)) return;
    } while (true);
}

bool CDBProxyClient::__get_section(_uint32 requestid,cmd_dbproxy::CDBProxyExceSection &section,tagSectionHead &sechead,bool bremove)
{
    CGuard guard(m_setionlist_lock);
    map<_uint32,TBlock*> ::iterator it = m_setionlist.find(requestid);
    if(it == m_setionlist.end()) return false;
    sechead = *((tagSectionHead*)it->second);
    section.unpack((char*)it->second + sizeof(tagSectionHead),sechead.slen);
    if(bremove) 
    {
        m_bfpool->push(it->second);
        m_setionlist.erase(it);
    }
    return true;
}

FTYPE(void) CDBProxyClient::on_attemper_timeout(void* pData,int size,const WLPARAM *pParam)
{
    ((CDBProxyClient*)pParam->obj)->on_atp_timeout((_uint32)pParam->tag);
}

FTYPE(void) CDBProxyClient::on_attemper_recv(void* pData,int size,const WLPARAM *pParam)
{
    int hlen = (int)pParam->tag;
    const char* szhead = hlen > 0 ? (char*)pData : 0;
    int dlen = size - hlen;
    const char* szdata = dlen > 0 ? (char*)pData + hlen : 0;
    ((CDBProxyClient*)pParam->obj)->on_atp_recv(szhead,hlen,szdata,dlen);
}

void CDBProxyClient::on_atp_timeout(_uint32 requestid)
{
    tagSectionHead sechead;
    cmd_dbproxy::CDBProxyExceSection section;
    if(!__get_section(requestid,section,sechead,true))
    {
        if (m_log)
        {
            m_log->write_log(LOG_TYPE_ERROR, "db_timeout get requestid:%u fail!", requestid);
        }
        return;
    }
    if(sechead.sink)
    {
        section.set_RequestTime(m_com->_cmp_tick_count(m_com->_get_tick_count(),(_uint32)section.get_RequestTime()));
        //const char* errjson="{\"result\":-1001,\"msg\":\"request timeout\"}";
        char errjson[512] = {0};
        sprintf(errjson, "{\"result\":-1001,\"msg\":\"request %u timeout\"}", requestid);
        m_log->write_log(LOG_TYPE_INFO, "db_timeout %s", errjson);
        sechead.sink(sechead.obj,errjson,strlen(errjson),section);
    }
}

void CDBProxyClient::on_atp_recv(const char* head,int hlen,const char* data,int dlen)
{
    _uint32 requestid = 0;


    if(hlen <= 0) return;
    ((char*)head)[hlen - 2] = '\0';

    m_log->write_log(LOG_TYPE_INFO, "db_recv head:%s", head ? head : "");

    //获取一行数据，返回值： 0- 成功  1 - 数据不完整  -1-错误数据
    int off = 0;
    do 
    {
        int   llen   = 0;
        const char* pline = 0;
        if(0 != __get_line(head,hlen,off,pline,llen)) return;

        if(__is_substr_line_begin(pline,llen,"section:") > 0)
        {
            sscanf(pline + strlen("section:"),"%u",&requestid);
            break;
        }
        off +=  llen;
    } while (true);
    
    if(0 == requestid) return;
    m_client->kill_timer(requestid); //请求完毕结束定时器 add hjj 2016-01-05

    tagSectionHead sechead;
    cmd_dbproxy::CDBProxyExceSection section;
    if(!__get_section(requestid,section,sechead,true))
    {
        if(m_log) m_log->write_log(LOG_TYPE_INFO,"[DBP] recv hlen=%d dlen=%d id=%u data:\r\n%s", hlen,dlen,requestid,data);
        return;
    }

    if(m_log) m_log->write_log(LOG_TYPE_INFO,"[DBP] cmd=%s recv hlen=%d dlen=%d id=%u data:\r\n%s", section.get_RequestCMD(), hlen,dlen,requestid,data);
    
    if(sechead.sink)
    {
        section.set_RequestTime(m_com->_cmp_tick_count(m_com->_get_tick_count(),(_uint32)section.get_RequestTime()));
        sechead.sink(sechead.obj,data,dlen,section);
    }
}

//***************************************************************************************************************************************************************
//***************************************************************************************************************************************************************
//***************************************************************************************************************************************************************

CDBProxyClientManager::CDBProxyClientManager()
{
	m_dbproxy_count = 0;
	m_main_plat = 0;
}

CDBProxyClientManager::~CDBProxyClientManager()
{
}

//加载库名配置
bool CDBProxyClientManager::load_db_name()
{
	TiXmlDocument xmlcfg;
	TiXmlElement* pRoot = 0;
	TiXmlElement* pItem = 0;
	char xmlfile[1024];

	if (!m_com->_get_app_path(xmlfile, sizeof(xmlfile))) return true;
	__sprintf(xmlfile + strlen(xmlfile), sizeof(xmlfile) - strlen(xmlfile), "config.xml");

	if (!xmlcfg.LoadFile(xmlfile)) return true;
	if (!(pRoot = xmlcfg.FirstChildElement("config")))
		return true;

	m_map_db_name.clear();
	pItem = pRoot->FirstChildElement("db_name");
	while (pItem)
	{
		string str_name = "";
		const char* name = pItem->Attribute("name");
		if (name != NULL && strlen(name) != 0)
			str_name = name;
		
		int plat = 0;
		if (pItem->Attribute("type") == NULL)
			return false;

		plat = atoi(pItem->Attribute("type"));
		
		m_map_db_name[plat] = str_name;
		pItem = pItem->NextSiblingElement("db_name");
	}
	return true;
}

//获取库名配置
bool CDBProxyClientManager::get_db_name(int plat, char *dbname, int buff_len)
{
	iter_db_name iter = m_map_db_name.find(plat);
	if (iter != m_map_db_name.end())
	{
		memcpy(dbname, iter->second.c_str(), buff_len);
		return true;
	}
	return false;
}

bool CDBProxyClientManager::start(ICommon* pcom, ILog* plog, IAttemper*pattemper, CONNET_EVENT event_func)
{
	m_com = pcom;
	m_log = plog;

	TiXmlDocument xmlcfg;
	TiXmlElement* pRoot = 0;
	TiXmlElement* pItem = 0;
	char xmlfile[1024];

	if (!m_com->_get_app_path(xmlfile, sizeof(xmlfile))) return false;
	__sprintf(xmlfile + strlen(xmlfile), sizeof(xmlfile) - strlen(xmlfile), "config.xml");

	if (!xmlcfg.LoadFile(xmlfile)) return false;
	if (!(pRoot = xmlcfg.FirstChildElement("config")))        
		return false;

	pItem = pRoot->FirstChildElement("dbproxy");
	while (pItem)
	{
		if (!pItem->Attribute("ip") || !pItem->Attribute("port")) 
			continue;

		char szip[32] = { 0 };
		int  port = 0;
		__strcpy_s(szip, 32, pItem->Attribute("ip"));
		port = atoi(pItem->Attribute("port"));
		int plat = 0;
		if (pItem->Attribute("type") != NULL)
		{
			plat = atoi(pItem->Attribute("type"));
		}
		m_dbproxy_client[m_dbproxy_count].set_connect_event(event_func);
		m_dbproxy_client[m_dbproxy_count].init_config(szip, port);
		m_dbproxy_client[m_dbproxy_count].set_plat(plat);
		m_dbproxy_client[m_dbproxy_count].start(pcom, plog, pattemper);

		//定义主平台，序号最小作为主库
		if (m_main_plat == 0 || plat < m_main_plat)
		{
			m_main_plat = plat;
		}

		m_dbproxy_count++;
		pItem = pItem->NextSiblingElement("dbproxy");
	}
	return (m_dbproxy_count > 0);
}

//停止
bool CDBProxyClientManager::stop()
{
	for (int i = 0; i < m_dbproxy_count; i++)
	{
		m_dbproxy_client[m_dbproxy_count].stop();
	}
	return true;
}

//投递
bool CDBProxyClientManager::post_exec(void*obj, DBPSINK sink, _uint64 UserID, const char* cmd, const char* data, 
	cmd_dbproxy::CDBProxyExceSection &section, bool b_wait_dbresult, int plat)
{
	//挑选对应平台的DB
	/*if (plat == 0)
		plat = m_main_plat;

	int post_index = 0;
	int dbproxy_len = 0;
	int dbproxy_index[MAX_DBCLIENT_COUNT] = { 0 };

	for (int i = 0; i < m_dbproxy_count; i++)
	{
		int db_plat = m_dbproxy_client[i].get_plat();
		if (db_plat != plat)
		{
			continue;
		}
		dbproxy_index[dbproxy_len] = i;
		dbproxy_len++;
	}

	if (dbproxy_len != 0)
	{
		int index  = rand() % dbproxy_len;
		post_index = dbproxy_index[index];
	}*/
	return m_dbproxy_client[0].post_exec(obj, sink, UserID, cmd, data, section, b_wait_dbresult);
}
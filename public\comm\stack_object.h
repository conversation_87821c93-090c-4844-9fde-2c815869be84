﻿/***********************************************************
 * File Name          :       stack_object.h
 * Author             :       原海洋
 * Version            :       1.0
 * Date               :       2015-11-24 11:13
 * Description        :       分配临时对象，只限于同步调用 不要做为类成员使用
***********************************************************/

#ifndef _STACK_OBJECT_H_
#define _STACK_OBJECT_H_

#define MAX_BUFFER_SIZE                     1024 * 1024 * 10                //临时缓冲总大小

template <typename OBJECT>
class CStackObject
{
public:
    CStackObject();
    virtual ~CStackObject();

public:
    /**
     *  @brief: 获得对象指针
     *  @return: 返回对象首地址
    **/
    OBJECT * get_object(int count = 1);
    /**
     *  @brief: 获得创建大小
     *  @return:返回创建缓冲区字节大小
    **/
    int get_size();
    /**
     *  @brief: 重置数据，对象实例请不要使用reset清空数据。内部使用memset重置数据
     *  @return: 无返回值
    **/
    void reset();

private:
    void _reset_data();

private:
    static char                     m_szBuffer[MAX_BUFFER_SIZE];
    static int                      m_bufferIndex;
    int                             m_bufferSize;
};

template <typename OBJECT>
char CStackObject<OBJECT>::m_szBuffer[MAX_BUFFER_SIZE] = {0};
template <typename OBJECT>
int CStackObject<OBJECT>::m_bufferIndex = 0;

template <typename OBJECT>
CStackObject<OBJECT>::CStackObject()
{
    _reset_data();
}

template <typename OBJECT>
CStackObject<OBJECT>::~CStackObject()
{
    m_bufferIndex -= m_bufferSize;
    _reset_data();
}

template <typename OBJECT>
OBJECT * CStackObject<OBJECT>::get_object(int count/* = 1*/)
{
    int bufferSize = sizeof(OBJECT) * count;
    int index = m_bufferIndex;
    m_bufferIndex += bufferSize;
    if (m_bufferIndex >= sizeof(m_szBuffer))
    {
        m_bufferIndex -= bufferSize;
        return NULL;
    }
    m_bufferSize += bufferSize;
    memset(m_szBuffer + index, 0, bufferSize);
    return new (m_szBuffer + index) OBJECT[count];
}

template <typename OBJECT>
int CStackObject<OBJECT>::get_size()
{
    return m_bufferSize;
}

template <typename OBJECT>
void CStackObject<OBJECT>::reset()
{
    memset(m_szBuffer + m_bufferIndex - m_bufferSize, 0, m_bufferSize);
}

template <typename OBJECT>
void CStackObject<OBJECT>::_reset_data()
{
    m_bufferSize = 0;
}


#endif //_STACK_OBJECT_H_

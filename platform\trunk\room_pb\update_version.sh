#!/bin/sh

dest_file="version.h"

if [ ! -e "$dest_file" ]; then 
   echo "#ifndef _VERSION_H_" >$dest_file
   echo "#define _VERSION_H_" >>$dest_file
   echo "" >>$dest_file
   echo "#define MAJOR_VERSION   1" >>$dest_file
   echo "#define MINOR_VERSION   0" >>$dest_file
   echo "#define REVISE_VERSION  0" >>$dest_file
   echo "#define BUILD_VERSION   0" >>$dest_file
   echo "" >>$dest_file
   echo "#endif //_VERSION_H_" >>$dest_file
fi 

text=`cat $dest_file | awk '{print NR,$0}'`

modifyStr=`echo "$text" | grep BUILD_VERSION | awk '{print $2,$3,$4}'`
verLine=`echo "$text" | grep BUILD_VERSION | awk '{print $1}'`
verNumber=`echo "$text" | grep BUILD_VERSION | awk '{print $4}'`
verNumber=`expr $verNumber + 1`
modifyStr="#define BUILD_VERSION   "$verNumber
sed -i "$verLine c $modifyStr" $dest_file

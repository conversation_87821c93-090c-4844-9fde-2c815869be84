#ifndef  __PUBLIC_COMMON_HTTP_URL_PARSER__
#define __PUBLIC_COMMON_HTTP_URL_PARSER__

#include <string>
#include <map>
#include <vector>

class ParseUrlParam {

public:
    ParseUrlParam();
    virtual ~ParseUrlParam();
    std::map<std::string, std::string> decode(const std::string& param);
    int get_url_param_string(const std::string& key, std::string& value);
    int get_url_param_int(const std::string& key, int *value);
    int get_url_param_float(const std::string& key, float *value);

    std::string to_string();

private:
    std::vector<std::string> split(const std::string& s, char seperator);

    std::map<std::string, std::string> _url_parsed_params;
    std::string _url_param;
};


#endif

﻿
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <string.h>

#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "user_token.h"
#include "block_timeout_socket.h"
#include <iostream>
#include <fstream>
#include <chrono>
#include "winlose.h"
#include "google/protobuf/text_format.h"
#include "mc.pb.h"
#include "server.pb.h"

CDLLHelper<ICommon> g_com(SVRLIB_NAME, "_common_instance", "_common_free");

std::string &replace_all(string &str, const std::string &old_value, const std::string &new_value, int off)
{

	std::string::size_type pos(0);
	while (true)
	{
		pos = str.find(old_value, pos);
		if (pos != string::npos)
		{
			str.replace(pos, old_value.length(), new_value);
			pos += off;
		}
		else
		{
			break;
		}
	}
	return str;
}

int char2bits(char ch)
{

	int bits = 0;
	if (ch >= 'a' && ch <= 'z')
	{
		bits = ch - 'a' + 10;
	}
	else if (ch >= 'A' && ch <= 'Z')
	{
		bits = ch - 'A' + 10;
	}
	else if (ch >= '0' && ch <= '9')
	{
		bits = ch - '0';
	}
	else
	{
		bits = -1;
	}
	return bits;
}

_uint64 _parseInt(const char *a, int len)
{

	std::vector<int> numArr;
	int radix = 16;
	_uint64 result = 0;
	for (int i = 0; i < len; i++)
	{
		int num = char2bits(a[i]);
		numArr.push_back(num);
	}
	for (int i = 0; i < numArr.size(); i++)
	{
		result += numArr[i] * pow(radix, numArr.size() - i - 1);
	}
	return result;
}
void writeGraph(const user_game_info &info)
{
	// std::remove("graph.txt");
	std::ofstream ofs;
	ofs.open("graph.txt", ios::out | ios::app);

	Json::Value graph_root;
	Json::Value item;

	for (int i = 0; i < MAX_LIST; i++)
	{
		for (int j = 0; j < MAX_ROW; j++)
		{
			card c = info.vec_remove[0].vec_now_box[i][j];

			int value = c.value + 10 * c.is_gold;
			item.append(value);
		}
	}

	for (auto &rm : info.vec_remove)
	{
		for (int i = 0; i < MAX_LIST; i++)
		{
			for (auto &f : rm.vec_fill_box[i])
			{
				int value = f.value + 10 * f.is_gold + 100*f.x + 1000*f.y;
				item.append(value);
			}
		}
	}

	//static int i = 1;
	{
		// MY_LOG_PRINT("generate %d", i++);
		graph_root["data"] = item;
		graph_root["multer"] = info.total_multer;
		int size = info.vec_remove.size();
		graph_root["combo"] = size;

		Json::FastWriter json_writer;
		string detail = json_writer.write(graph_root);
		ofs << detail << "\n";
	}
	ofs.close();
}

//#define TEST_BET

// int test_multer[] = {
// 	//5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 195, 200, 205, 210, 215, 220, 225, 230, 235, 240, 245, 250, 255, 260, 265, 270, 275, 280, 285, 290, 295, 300, 305, 310, 315, 320, 325, 330, 335, 340, 345, 350, 355, 360, 365, 370, 375, 380, 385, 390, 395, 400, 405, 410, 415, 420, 425, 430, 435, 440, 445, 450, 455, 460, 465, 470, 475, 480, 485, 490, 495, 500, 505, 510, 515, 520, 525, 530, 535, 540, 545, 550, 555, 560, 565, 570, 575, 580, 585, 590, 595, 600, 605, 610, 615, 620, 625, 630, 635, 640, 645, 650, 655, 660, 665, 670, 675, 680, 685, 690, 695, 700, 705, 710, 715, 720, 725, 730, 735, 740, 745, 750, 755, 760, 765, 770, 775, 780, 785, 790, 795, 800, 805, 810, 815, 820, 825, 830, 835, 840, 845, 850, 855, 860, 865, 870, 875, 880, 885, 890, 895, 900, 905, 910, 915, 920, 925, 930, 935, 940, 945, 950, 955, 960, 965, 970, 975, 980, 985, 990, 995, 1000, 1005, 1010, 1015, 1020, 1025, 1030, 1035, 1040, 1045, 1050, 1055, 1060, 1065, 1070, 1075, 1080, 1085, 1090, 1095, 1100, 1105, 1110, 1115, 1120, 1125, 1130, 1135, 1140, 1145, 1150, 1155, 1160, 1165, 1170, 1175, 1180, 1185, 1190, 1195, 1200, 1205, 1210, 1215, 1220, 1225, 1230, 1235, 1240, 1245, 1250, 1255, 1260, 1265, 1270, 1275, 1280, 1285, 1290, 1295, 1300, 1305, 1310, 1315, 1320, 1325, 1330, 1335, 1340, 1345, 1350, 1355, 1360, 1365, 1370, 1375, 1380, 1385, 1390, 1395, 1400, 1405, 1410, 1415, 1420, 1425, 1430, 1435, 1440, 1445, 1450, 1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1505, 1510, 1515, 1520, 1525, 1530, 1535, 1540, 1545, 1550, 1555, 1560, 1565, 1570, 1575, 1580, 1585, 1590, 1595, 1600, 1605, 1610, 1615, 1620, 1625, 1630, 1635, 1640, 1645, 1650, 1655, 1660, 1665, 1670, 1675, 1680, 1685, 1690, 1695, 1700, 1705, 1710, 1715, 1720, 1725, 1730, 1735, 1740, 1745, 1750, 1755, 1760, 1765, 1770, 1775, 1780, 1785, 1790, 1795, 1800, 1805, 1810, 1815, 1820, 1825, 1830, 1835, 1840, 1845, 1850, 1855, 1860, 1865, 1870, 1875, 1880, 1885, 1890, 1895, 1900, 1905, 1910, 1915, 1920, 1925, 1930, 1935, 1940, 1945, 1950, 1955, 1960, 1965, 1970, 1975, 1980, 1985, 1990, 1995, 2000, 2005, 2010, 2015, 2020, 2025, 2030, 2035, 2040, 2045, 2050, 2055, 2060, 2065, 2070, 2075, 2080, 2085, 2090, 2095, 2100, 2105, 2110, 2115, 2120, 2125, 2130, 2135, 2140, 2145, 2150, 2155, 2160, 2165, 2170, 2175, 2180, 2185, 2190, 2195, 2200, 2205, 2210, 2215, 2220, 2225, 2230, 2235, 2240, 2245, 2250, 2255, 2260, 2265, 2270, 2275, 2280, 2285, 2290, 2295, 2300, 2305, 2310, 2315, 2320, 2325, 2330, 2335, 2340, 2345, 2350, 2355, 2360, 2365, 2370, 2375, 2380, 2385, 2390, 2395, 2400, 2405, 2410, 2415, 2420, 2425, 2430, 2435, 2440, 2445, 2450, 2455, 2460, 2465, 2470, 2475, 2480, 2485, 2490, 2495, 2500, 2505, 2510, 2515, 2520, 2525, 2530, 2535, 2540, 2545, 2550, 2555, 2560, 2565, 2570, 2575, 2580, 2585, 2590, 2595, 2600, 2605, 2610, 2615, 2620, 2625, 2630, 2635, 2640, 2645, 2650, 2655, 2660, 2665, 2670, 2675, 2680, 2685, 2690, 2695, 2700, 2705, 2710, 2715, 2720, 2725, 2730, 2735, 2740, 2745, 2750, 2755, 2760, 2765, 2770, 2775, 2780, 2785, 2790, 2795, 2800, 2805, 2810, 2815, 2820, 2825, 2830, 2835, 2840, 2845, 2850, 2855, 2860, 2865, 2870, 2875, 2880, 2885, 2890, 2895, 2900, 2905, 2910, 2915, 2920, 2925, 2930, 2935, 2940, 2945, 2950, 2955, 2960, 2965, 2970, 2975, 2980, 2985, 2990, 2995, 3000
// 	//2965,3005, 3010, 3015, 3020, 3025, 3030, 3035, 3040, 3045, 3050, 3055, 3060, 3065, 3070, 3075, 3080, 3085, 3090, 3095, 3100, 3105, 3110, 3115, 3120, 3125, 3130, 3135, 3140, 3145, 3150, 3155, 3160, 3165, 3170, 3175, 3180, 3185, 3190, 3195, 3200, 3205, 3210, 3215, 3220, 3225, 3230, 3235, 3240, 3245, 3250, 3255, 3260, 3265, 3270, 3275, 3280, 3285, 3290, 3295, 3300, 3305, 3310, 3315, 3320, 3325, 3330, 3335, 3340, 3345, 3350, 3355, 3360, 3365, 3370, 3375, 3380, 3385, 3390, 3395, 3400, 3405, 3410, 3415, 3420, 3425, 3430, 3435, 3440, 3445, 3450, 3455, 3460, 3465, 3470, 3475, 3480, 3485, 3490, 3495, 3500, 3510, 3515, 3520, 3525, 3530, 3535, 3540, 3545, 3550, 3555, 3560, 3565, 3570, 3575, 3580, 3590, 3595, 3600, 3605, 3610, 3615, 3620, 3625, 3630, 3640, 3645, 3650, 3655, 3660, 3665, 3670, 3675, 3680, 3685, 3690, 3695, 3700, 3705, 3710, 3715, 3720, 3725, 3730, 3735, 3740, 3745, 3750, 3755, 3760, 3765, 3770, 3775, 3780, 3785, 3790, 3795, 3800, 3805, 3810, 3815, 3820, 3825, 3830, 3835, 3840, 3845, 3850, 3855, 3860, 3865, 3870, 3875, 3880, 3885, 3890, 3895, 3900, 3905, 3910, 3915, 3920, 3925, 3930, 3935, 3940, 3945, 3950, 3955, 3960, 3965, 3970, 3975, 3980, 3985, 3990, 3995, 4000, 4005, 4010, 4015, 4020, 4025, 4030, 4035, 4040, 4045, 4050, 4055, 4060, 4065, 4070, 4075, 4080, 4085, 4090, 4095, 4100, 4105, 4110, 4115, 4120, 4125, 4130, 4135, 4140, 4145, 4150, 4155, 4160, 4165, 4170, 4175, 4180, 4185, 4190, 4195, 4200, 4210, 4215, 4220, 4225, 4230, 4235, 4240, 4245, 4250, 4255, 4260, 4265, 4270, 4275, 4280, 4285, 4290, 4295, 4300, 4305, 4310, 4315, 4320, 4325, 4330, 4335, 4340, 4345, 4350, 4360, 4365, 4370, 4375, 4380, 4385, 4390, 4395, 4400, 4405, 4410, 4415, 4420, 4425, 4430, 4435, 4440, 4445, 4450, 4455, 4460, 4465, 4470, 4475, 4480, 4485, 4490, 4495, 4500, 4505, 4510, 4515, 4520, 4530, 4535, 4540, 4545, 4550, 4555, 4560, 4565, 4570, 4575, 4580, 4585, 4590, 4595, 4600, 4605, 4610, 4615, 4620, 4625, 4630, 4635, 4640, 4645, 4650, 4655, 4660, 4665, 4670, 4675, 4680, 4685, 4690, 4695, 4700, 4705, 4710, 4715, 4720, 4730, 4735, 4740, 4745, 4750, 4755, 4760, 4765, 4770, 4780, 4785, 4790, 4795, 4800, 4805, 4810, 4815, 4820, 4825, 4830, 4835, 4840, 4845, 4850, 4855, 4860, 4865, 4870, 4875, 4880, 4885, 4890, 4900, 4905, 4910, 4915, 4920, 4925, 4930, 4935, 4940, 4950, 4955, 4960, 4965, 4970, 4975, 4980, 4985, 4990, 4995, 5000, 5005, 5010, 5015, 5020, 5025, 5030, 5035, 5040, 5045, 5050, 5060, 5070, 5075, 5080, 5085, 5090, 5095, 5100, 5105, 5110, 5115, 5120, 5125, 5130, 5140, 5145, 5150, 5155, 5160, 5170, 5175, 5180, 5190, 5200, 5210, 5215, 5220, 5225, 5230, 5235, 5240, 5245, 5250, 5255, 5260, 5270, 5275, 5280, 5290, 5300, 5305, 5310, 5320, 5330, 5335, 5340, 5345, 5350, 5360, 5365, 5370, 5375, 5380, 5385, 5390, 5400, 5405, 5410, 5415, 5420, 5430, 5435, 5440, 5445, 5450, 5460, 5465, 5470, 5475, 5480, 5490, 5495, 5500, 5505, 5510, 5515, 5520, 5530, 5540, 5545, 5550, 5560, 5570, 5575, 5580, 5585, 5590, 5600, 5605, 5610, 5615, 5620, 5625, 5630, 5635, 5640, 5645, 5650, 5660, 5670, 5680, 5685, 5690, 5695, 5700, 5710, 5715, 5720, 5730, 5740, 5745, 5750, 5755, 5760, 5770, 5775, 5780, 5785, 5790, 5795, 5800, 5810, 5815, 5820, 5830, 5835, 5840, 5845, 5850, 5855, 5860, 5865, 5870, 5875, 5880, 5885, 5890, 5900, 5905, 5910, 5915, 5920, 5925, 5930, 5935, 5940, 5945, 5950, 5960, 5970, 5975, 5980, 5990, 6000
// 	//3095,3105,3115,3195,3295,3315,3335,3395,3445,3465,3495,3545,3555,3565,3575,3695,3705,3735,3795,3825,3835,3905,3915,3945,4035,4085,4125,4155,4265,4295,4305,4365,4375,4415,4435,4485,4495,4535,4595,4655,4675,4685,4735,4745,4755,4795,4875,4965,4995,5095,5105,5115,5125,5155,5215,5225,5275,5415,5435,5475,5505,5515,5545,5585,5625,5685,5695,5775,5795,5835,5865,5905,5915,5935,5975,6010, 6020, 6030, 6040, 6050, 6055, 6060, 6065, 6070, 6080, 6090, 6095, 6100, 6110, 6120, 6130, 6135, 6140, 6150, 6160, 6165, 6170, 6175, 6180, 6185, 6190, 6195, 6200, 6210, 6220, 6225, 6230, 6240, 6245, 6250, 6255, 6260, 6270, 6275, 6280, 6290, 6300, 6305, 6310, 6315, 6320, 6330, 6335, 6340, 6345, 6350, 6355, 6360, 6365, 6370, 6375, 6380, 6385, 6390, 6395, 6400, 6410, 6415, 6420, 6425, 6430, 6440, 6445, 6450, 6455, 6460, 6470, 6475, 6480, 6485, 6490, 6500, 6510, 6520, 6525, 6530, 6540, 6550, 6560, 6565, 6570, 6580, 6590, 6595, 6600, 6605, 6610, 6615, 6620, 6625, 6630, 6635, 6640, 6645, 6650, 6660, 6670, 6675, 6680, 6685, 6690, 6700, 6705, 6710, 6720, 6725, 6730, 6735, 6740, 6750, 6755, 6760, 6770, 6780, 6785, 6790, 6795, 6800, 6810, 6820, 6825, 6830, 6835, 6840, 6845, 6850, 6855, 6860, 6865, 6870, 6880, 6890, 6900, 6910, 6915, 6920, 6930, 6940, 6945, 6965, 6970, 6980, 6990, 6995, 7000, 7010, 7015, 7020, 7040, 7050, 7055, 7065, 7070, 7080, 7085, 7090, 7095, 7100, 7110, 7120, 7125, 7130, 7140, 7145, 7150, 7160, 7170, 7175, 7180, 7190, 7200, 7205, 7210, 7220, 7230, 7235, 7240, 7250, 7260, 7270, 7280, 7290, 7310, 7315, 7320, 7335, 7340, 7345, 7350, 7360, 7365, 7370, 7375, 7380, 7390, 7400, 7410, 7420, 7430, 7440, 7445, 7450, 7460, 7470, 7480, 7490, 7495, 7500, 7510, 7520, 7530, 7540, 7545, 7550, 7555, 7560, 7570, 7575, 7580, 7590, 7595, 7600, 7610, 7620, 7625, 7630, 7635, 7640, 7650, 7655, 7660, 7670, 7680, 7690, 7705, 7710, 7715, 7720, 7725, 7730, 7740, 7750, 7760, 7765, 7770, 7780, 7785, 7790, 7800, 7805, 7810, 7820, 7825, 7830, 7840, 7850, 7860, 7870, 7880, 7890, 7900, 7910, 7915, 7930, 7940, 7950, 7960, 7975, 7980, 7990, 7995, 8000, 8010, 8015, 8030, 8040, 8050, 8060, 8065, 8070, 8075, 8080, 8090, 8100, 8110, 8120, 8130, 8135, 8140, 8150, 8165, 8170, 8180, 8190, 8200, 8210, 8215, 8220, 8225, 8230, 8240, 8245, 8255, 8260, 8270, 8275, 8280, 8285, 8290, 8295, 8300, 8310, 8320, 8325, 8330, 8335, 8340, 8345, 8350, 8360, 8370, 8380, 8390, 8395, 8400, 8405, 8410, 8420, 8425, 8430, 8440, 8450, 8455, 8460, 8465, 8470, 8480, 8485, 8490, 8500, 8510, 8520, 8525, 8530, 8540, 8550, 8555, 8560, 8570, 8575, 8580, 8585, 8590, 8595, 8600, 8610, 8615, 8625, 8630, 8635, 8640, 8650, 8660, 8670, 8680, 8685, 8690, 8700, 8710, 8720, 8730, 8740, 8750, 8760, 8770, 8780, 8785, 8790, 8800, 8810, 8820, 8825, 8830, 8840, 8865, 8870, 8880, 8890, 8900, 8910, 8915, 8930, 8940, 8945, 8950, 8960, 8970, 8980, 8990, 9000, 9010, 9020, 9040, 9050, 9055, 9060, 9065, 9070, 9080, 9085, 9090, 9095, 9100, 9110, 9115, 9125, 9140, 9150, 9170, 9180, 9190, 9200, 9205, 9210, 9220, 9230, 9240, 9250, 9260, 9270, 9280, 9295, 9300, 9305, 9310, 9320, 9325, 9340, 9350, 9360, 9370, 9375, 9380, 9390, 9400, 9405, 9410, 9420, 9430, 9440, 9445, 9460, 9480, 9490, 9500, 9510, 9515, 9520, 9530, 9540, 9550, 9560, 9570, 9575, 9580, 9590, 9600, 9610, 9615, 9620, 9645, 9650, 9655, 9670, 9680, 9700, 9710, 9715, 9720, 9730, 9750, 9760, 9790, 9800, 9815, 9820, 9845, 9850, 9860, 9880, 9900, 9905, 9910, 9920, 9930, 9940, 9945, 9950, 9955, 9970, 9975, 9980, 9990, 9995, 10000
// 	//3005, 3010, 3015, 3020, 3025, 3030, 3035, 3040, 3045, 3050, 3055, 3060, 3065, 3070, 3075, 3080, 3085, 3090, 3095, 3100, 3105, 3110, 3115, 3120, 3125, 3130, 3135, 3140, 3145, 3150, 3155, 3160, 3165, 3170, 3175, 3180, 3185, 3190, 3195, 3200, 3205, 3210, 3215, 3220, 3225, 3230, 3235, 3240, 3245, 3250, 3255, 3260, 3265, 3270, 3275, 3280, 3285, 3290, 3295, 3300, 3305, 3310, 3315, 3320, 3325, 3330, 3335, 3340, 3345, 3350, 3355, 3360, 3365, 3370, 3375, 3380, 3385, 3390, 3395, 3400, 3405, 3410, 3415, 3420, 3425, 3430, 3435, 3440, 3445, 3450, 3455, 3460, 3465, 3470, 3475, 3480, 3485, 3490, 3495, 3500, 3510, 3515, 3520, 3525, 3530, 3535, 3540, 3545, 3550, 3555, 3560, 3565, 3570, 3575, 3580, 3590, 3595, 3600, 3605, 3610, 3615, 3620, 3625, 3630, 3640, 3645, 3650, 3655, 3660, 3665, 3670, 3675, 3680, 3685, 3690, 3695, 3700, 3705, 3710, 3715, 3720, 3725, 3730, 3735, 3740, 3745, 3750, 3755, 3760, 3765, 3770, 3775, 3780, 3785, 3790, 3795, 3800, 3805, 3810, 3815, 3820, 3825, 3830, 3835, 3840, 3845, 3850, 3855, 3860, 3865, 3870, 3875, 3880, 3885, 3890, 3895, 3900, 3905, 3910, 3915, 3920, 3925, 3930, 3935, 3940, 3945, 3950, 3955, 3960, 3965, 3970, 3975, 3980, 3985, 3990, 3995, 4000, 4005, 4010, 4015, 4020, 4025, 4030, 4035, 4040, 4045, 4050, 4055, 4060, 4065, 4070, 4075, 4080, 4085, 4090, 4095, 4100, 4105, 4110, 4115, 4120, 4125, 4130, 4135, 4140, 4145, 4150, 4155, 4160, 4165, 4170, 4175, 4180, 4185, 4190, 4195, 4200, 4210, 4215, 4220, 4225, 4230, 4235, 4240, 4245, 4250, 4255, 4260, 4265, 4270, 4275, 4280, 4285, 4290, 4295, 4300, 4305, 4310, 4315, 4320, 4325, 4330, 4335, 4340, 4345, 4350, 4360, 4365, 4370, 4375, 4380, 4385, 4390, 4395, 4400, 4405, 4410, 4415, 4420, 4425, 4430, 4435, 4440, 4445, 4450, 4455, 4460, 4465, 4470, 4475, 4480, 4485, 4490, 4495, 4500, 4505, 4510, 4515, 4520, 4530, 4535, 4540, 4545, 4550, 4555, 4560, 4565, 4570, 4575, 4580, 4585, 4590, 4595, 4600, 4605, 4610, 4615, 4620, 4625, 4630, 4635, 4640, 4645, 4650, 4655, 4660, 4665, 4670, 4675, 4680, 4685, 4690, 4695, 4700, 4705, 4710, 4715, 4720, 4730, 4735, 4740, 4745, 4750, 4755, 4760, 4765, 4770, 4780, 4785, 4790, 4795, 4800, 4805, 4810, 4815, 4820, 4825, 4830, 4835, 4840, 4845, 4850, 4855, 4860, 4865, 4870, 4875, 4880, 4885, 4890, 4900, 4905, 4910, 4915, 4920, 4925, 4930, 4935, 4940, 4950, 4955, 4960, 4965, 4970, 4975, 4980, 4985, 4990, 4995, 5000, 5005, 5010, 5015, 5020, 5025, 5030, 5035, 5040, 5045, 5050, 5060, 5070, 5075, 5080, 5085, 5090, 5095, 5100, 5105, 5110, 5115, 5120, 5125, 5130, 5140, 5145, 5150, 5155, 5160, 5170, 5175, 5180, 5190, 5200, 5210, 5215, 5220, 5225, 5230, 5235, 5240, 5245, 5250, 5255, 5260, 5270, 5275, 5280, 5290, 5300, 5305, 5310, 5320, 5330, 5335, 5340, 5345, 5350, 5360, 5365, 5370, 5375, 5380, 5385, 5390, 5400, 5405, 5410, 5415, 5420, 5430, 5435, 5440, 5445, 5450, 5460, 5465, 5470, 5475, 5480, 5490, 5495, 5500, 5505, 5510, 5515, 5520, 5530, 5540, 5545, 5550, 5560, 5570, 5575, 5580, 5585, 5590, 5600, 5605, 5610, 5615, 5620, 5625, 5630, 5635, 5640, 5645, 5650, 5660, 5670, 5680, 5685, 5690, 5695, 5700, 5710, 5715, 5720, 5730, 5740, 5745, 5750, 5755, 5760, 5770, 5775, 5780, 5785, 5790, 5795, 5800, 5810, 5815, 5820, 5830, 5835, 5840, 5845, 5850, 5855, 5860, 5865, 5870, 5875, 5880, 5885, 5890, 5900, 5905, 5910, 5915, 5920, 5925, 5930, 5935, 5940, 5945, 5950, 5960, 5970, 5975, 5980, 5990, 6000
// 	3005, 3010, 3015, 3020, 3025, 3030, 3035, 3040, 3045, 3050, 3055
// };

int CGameComponent::init(ITableHunded *table, data_pool_interface *data_pool, gamecontrol_interface *game_control, const void *rule, int rule_len)
{
	if (!table || !data_pool)
		return -1;

	m_table = table;
	m_data_pool = data_pool;
	m_game_control = game_control;
	m_tax = 0;
	m_game_logic.init();
	analy_game_rule(rule, rule_len);
	srand(time(0));
#ifdef GEN_GRAPH
	std::remove("graph.txt");
	user_game_info info;
	info.bet = 100;
	info.cur_mode = 4;
	info.client_id = 31;
	int test_count = 100000;

	for(int i=0; i<11;i++)
	{		
		int gen_num = 0;
		int g_num = 1;
		if (test_multer[i] <= 2000) 
		{
			g_num = 20;
		}
		else 
			g_num = 20;
		for (int j = 0; j < test_count; j++)
		{
			//MY_LOG_PRINT("isfree = %d", info.is_free);
			calcu_graph(info);
			if(info.total_multer == test_multer[i])
			{
				int wild_num = 0;
				int len = info.vec_remove.size();
				for (int i = 0; i < MAX_LIST; i++)
				{
					for (auto &c : info.vec_remove[len - 1].vec_now_box[i])
					{
						if (c.value == BOX_WILD)
						{
							wild_num++;
						}
					}
				}

				if (wild_num < 2 && len <= 8)
				{
					gen_num++;
					writeGraph(info);
					if (gen_num >= g_num)
					{
						MY_LOG_PRINT("%d ok", test_multer[i]);
						break;
					}
				}
			}
		}
		if(gen_num <= 0)
		{
			MY_LOG_PRINT("%d fail", test_multer[i]);
		}
		else if(gen_num < g_num)
		{
			MY_LOG_PRINT("%d not enough", test_multer[i]);
		}
	}
	
#endif
#ifdef TEST_BET
	std::remove("data.txt");
	std::ofstream ofs;
	ofs.open("data.txt", ios::out | ios::app);
	user_game_info info;
	info.bet = 100;
	info.cur_mode = 0;
	info.client_id = 31;

	int total = 0;
	int winCount = 0;
	int lianzhong = 0;
	int lianbuzhong = 0;
	int maxlianzhong = 0;
	int maxlianbuzhong = 0;
	int multer1 = 0;
	int multer2 = 0;
	int multer3 = 0;
	int multer4 = 0;
	int multer5 = 0;
	int multer6 = 0;
	int multer7 = 0;
	int multer8 = 0;
	int multer9 = 0;
	int test_count = 100000;
	int free = 0;

	for (int i = 0; i < test_count; i++)
	{
		MY_LOG_PRINT("The %d time", i+1);
		calcu_graph(info);
		calcu_result(info);
		//MY_LOG_PRINT("result = %d", info.result);		
		total += info.result;
		if(info.result > 0)
		{
			lianzhong++;
			winCount++;
			lianbuzhong = 0;
			if(lianzhong > maxlianzhong)
				maxlianzhong = lianzhong;							
		}
		else
		{
			lianzhong = 0;
			lianbuzhong++;
			if(lianbuzhong > maxlianbuzhong)
				maxlianbuzhong = lianbuzhong;
		}

		//int multer = info.result*100/405;
		int multer = info.result;
		if (multer <= 0)
			multer9++;
		else if (multer <= 100)
			multer1++;
		else if (multer > 100 && multer <= 300)
			multer2++;
		else if (multer > 300 && multer <= 500)
			multer3++;
		else if (multer > 500 && multer <= 1000)
			multer4++;
		else if (multer > 1000 && multer <= 2000)
			multer5++;
		else if (multer > 2000 && multer <= 5000)
			multer6++;
		else if (multer > 5000 && multer <= 10000)
			multer7++;
		else
			{
				multer8++;
			}
		if(info.is_free)
			{
				free++;
			}
	}

	float rtp = float(total) / (test_count*100);
	float winRate = float(winCount) / test_count;	
	float freeRate = float(free) / test_count;
	ofs << "中奖概率: " << winRate * 100 << "%" << endl;
	ofs << "RTP: " << rtp * 100 << "%" << endl;
	ofs << "最大连中: " << maxlianzhong << endl;
	ofs << "freegame概率: " << freeRate * 100 << "%" << endl;
	//ofs << "最大连不中: " << maxlianbuzhong<< endl;

	rtp = float(multer9) / test_count;
	ofs << "0倍占比" << rtp * 100 << "%" << endl;
	rtp = float(multer1) / test_count;
	ofs << "0~1倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer2) / test_count;
	ofs << "1~3倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer3) / test_count;
	ofs << "3~5倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer4) / test_count;
	ofs << "5~10倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer5) / test_count;
	ofs << "10~20倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer6) / test_count;
	ofs << "20~50倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer7) / test_count;
	ofs << "50~100倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer8) / test_count;
	ofs << "100以上倍占比: " << rtp * 100 << "%" << endl;

	ofs.close();
#endif
	set_timer(TID_CHECK_USER, 60 * 60 * 1000, 0, 0);
	srand(time(0));
	return 0;
}

bool CGameComponent::analy_game_rule(const void *rule, int rule_len)
{
	if (rule == NULL || rule_len == 0)
	{
		MY_LOG_WARN("%s,rule is null", __FUNCTION__);
		return false;
	}
	Json::Reader json_reader;
	Json::Value json_value;
	Json::Value va;
	if (!json_reader.parse((const char *)rule, json_value))
	{
		MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
		return false;
	}

	va = json_value["rule"];
	m_tax = va["tax"].asInt();
	MY_LOG_DEBUG("analy_game_rule tax(%d)", m_tax);
	return true;
}

void CGameComponent::reset_game_rule(const void *rule, int rule_len)
{
	if (rule == NULL)
	{
		MY_LOG_WARN("%s,rule is null", __FUNCTION__);
		return;
	}
	Json::Reader json_reader;
	Json::Value json_value;
	Json::Value va;
	if (!json_reader.parse((const char *)rule, json_value))
	{
		MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
		return;
	}

	va = json_value["rule"];
	m_tax = va["tax"].asInt();
	MY_LOG_DEBUG("reset_game_rule tax(%d)", m_tax);
	return;
}

void CGameComponent::clear()
{

	reset();
}

void CGameComponent::reset()
{
}

//**********************************定时器***************************************\\

bool CGameComponent::set_timer(_uint8 time_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat /* = 1 */)
{

	bool ret = m_table->set_timer(time_id, timeout_msec, param, repeat);
	if (!ret)
	{
		MY_LOG_ERROR("set timer fail. time_id=%d", time_id);
		return false;
	}

	return true;
}

bool CGameComponent::kill_timer(_uint8 time_id)
{

	bool ret = m_table->kill_timer(time_id);
	if (!ret)
	{
		MY_LOG_ERROR("kill timer fail. time_id=%d", time_id);
		return false;
	}

	return true;
}

void CGameComponent::on_time(_uint8 time_id, _uint64 param)
{
	time_t current = time(NULL);
	int kicked_total_count = 0;

	if (time_id == TID_CHECK_USER)
	{
		for (auto &info : m_map_user_game_info)
		{
			if (current - info.second._last_kick_user_offline_tm_sec > KICK_USER_OFFLINE_DURATION)
			{
				IUser *user = m_table->get_user_from_uid(info.first);
				if (current - user->get_enter_time() > KICK_USER_OFFLINE_DURATION)
				{
					m_table->on_user_leave(user);
					m_map_user_game_info.erase(info.first);
					kicked_total_count++;
				}
			}
		}
	}
}

//**********************************客户端消息处理***************************************\\

// 接收客户端消息;
int CGameComponent::on_recv(_uint8 mcmd, _uint8 scmd, const void *pData, int size, IUser *pUser)
{

	return 0;
}

void CGameComponent::on_http_recv(socket_id sid, int uid, int opid, const std::string &str_param)
{
	MY_LOG_DEBUG("on_http_recv");
	IUser *user = m_table->get_user_from_uid(uid);
	if (user == nullptr)
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::internal, "", "invalid user", nullptr, 0);
		MY_LOG_ERROR("user[%d] sid[%d] invalid user", uid, sid);
		return;
	}
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		user_game_info game_info;
		game_info.uid = uid;
		game_info.token = myredis::GetInstance()->get_data_by_uid_for_string(uid, DB_COMM, "current_token");
		game_info.user_type = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_USER_TYPE);
		game_info.web_token = myredis::GetInstance()->get_token_by_uid(uid);
		game_info.sub_client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_SUB_CHANNEL_ID);
		game_info.client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);		
		m_map_user_game_info[uid] = game_info;
		iter = m_map_user_game_info.find(uid);
	}
	iter->second._last_kick_user_offline_tm_sec = time(NULL);

	switch (opid)
	{
	case serverProto::AckType::spin:
	{
		_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
		user->set_gold(gold);
		on_user_bet_roll(user, str_param, iter->second);
	}
	break;

	case serverProto::AckType::info:
	{
		on_user_game_info(user, str_param, str_param.length(), iter->second);
	}
	break;

	case serverProto::AckType::jilijpSetting:
	{
		send_setting_result(user);
	}
	break;

	case serverProto::AckType::fullJpInfoAll:
	{
	}
	break;
	case serverProto::AckType::notice:
	{
		send_notice_result(user);
	}
	break;

	default:
		break;
	}
}

//**********************************房间消息处理***************************************\\

void CGameComponent::on_user_game_info(IUser *user, const std::string &data, int size, user_game_info &game_info)
{
	auto uid = user->get_user_id();
	// todo : add error process
	serverProto::Request pb_request;
	if (!pb_request.ParseFromString(data))
	{
		MY_LOG_ERROR("failed to parse pb request, uid:%d", uid);
		return;
	}

	// todo : add error process
	serverProto::InfoReq req;
	if (!req.ParseFromString(pb_request.req()))
	{
		MY_LOG_ERROR("user[%d] failed to parse pb spin request", uid);
		return;
	}

	std::string debugstr;
	using namespace google::protobuf;
	TextFormat::PrintToString(req, &debugstr);
	MY_LOG_DEBUG("user[%d] on_user_game_info recved spin request: %s", uid, debugstr.c_str());

	// 单一钱包请求第三方刷新金币
	int api_type = user->get_api_type();
	if (api_type == MODE_SINGLE)
	{
		m_table->get_flush_user_gold(uid, GameConfig::GetInstance()->get_game_id());
		return;
	}
	send_game_info_result(user);
}

void CGameComponent::handle_user_notice_request(IUser *user, const std::string &msg, int size)
{

	serverProto::NoticeReq req;
	if (req.ParseFromString(msg))
	{
		MY_LOG_ERROR("user[%d] failed to parse pb notice request", user->get_user_id());
		return;
	}
}

// 房间消息处理;
int CGameComponent::on_user_action(IUser *user, _uint8 type)
{
	if (!user)
		return -1;

	_uint32 nUserID = user->get_user_id();
	switch (type)
	{
	case comm_action_sit_down:
	case comm_action_online:
	{
		on_user_enter(user);
	}
	break;
	case comm_action_leave:
	case comm_action_offline:
	{
		on_user_leave(user);
	}
	break;
	case comm_action_update_gold:
	{
		on_update_user_gold_result(user);
	}
	break;
	}
	return 0;
}

void CGameComponent::db_get_data(void *obj, const char *data, int len)
{

	db_data *post_data = (db_data *)obj;
	if (post_data == NULL)
		return;
}

/*
 * 判断玩家下线是否能删除玩家（用于加金币等玩家结算判断）
 */
bool CGameComponent::is_can_clear_user(int chairid)
{

	return false;
}

/*
 * 进入房间
 */
void CGameComponent::on_user_enter(IUser *pUser)
{

	MY_LOG_DEBUG("on_user_enter user_id(%u)", pUser->get_user_id());
	m_table->send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_ROOM_SUCC, NULL);

	send_my_user_info(pUser);
	send_room_info(pUser);
}

// 离开房间
void CGameComponent::on_user_leave(IUser *pUser)
{

	MY_LOG_DEBUG("on_user_leave user_id(%u)", pUser->get_user_id());
}

int get_time_mic()
{
	auto now = std::chrono::system_clock::now(); // 获取当前系统时钟时间点
	auto duration = now.time_since_epoch();		 // 计算从1970年到现在经过的时长

	long microseconds = std::chrono::duration_cast<std::chrono::microseconds>(duration).count(); // 将时长转换为微秒

	return microseconds%100000000LL;
}

// 请求下注
void CGameComponent::on_user_bet_roll(IUser *user, const std::string &data, user_game_info &game_info)
{
	// todo : add error process
	serverProto::Request pb_request;
	if (!pb_request.ParseFromString(data))
	{
		MY_LOG_ERROR("failed to parse pb request, uid:%d", user->get_user_id());
		return;
	}

	// todo : add error process
	serverProto::SpinReq req;
	if (!req.ParseFromString(pb_request.req()))
	{
		MY_LOG_ERROR("failed to parse pb spin request, uid:%d", user->get_user_id());
		return;
	}

	// std::string debugstr;
	// using namespace google::protobuf;
	// TextFormat::PrintToString(req, &debugstr);
	// MY_LOG_PRINT("user[%d] on_user_bet_roll recved spin request: %s", user->get_user_id(), debugstr.c_str());

	int bet_count = req.bet() * 100;
	
	if(req.has_mall())
	{
		game_info.cur_mode = 1;
		game_info.mall_bet = req.mall().bet()*100;
		//MY_LOG_PRINT("mall_bet = %d", game_info.mall_bet);
	}
	else
	{
		game_info.cur_mode = 0;
		game_info.mall_bet = bet_count;
	}

	int uid = user->get_user_id();
	game_info.bet = bet_count;

	if (user->get_single_status() == 1)
	{
		MY_LOG_WARN("on_user_bet_roll status erro uid:%d", uid);
		return;
	}

	//std::string md5key;
	int now_time = get_time_mic();
	//calcu_md5key(uid, md5key, now_time);

	game_info.round_index = m_game_logic.gen_new_round_index();
	game_info.round_index_v2 = game_info.round_index * 1000 + 49;
	game_info.balance_before_round = user->get_gold();
	
	// 是否异步单一钱包模式，扣下注的钱
	if (user->get_api_type() == MODE_SINGLE)
	{
		write_transfe_inorut_one(-game_info.mall_bet,
								 0,
								 GOLD_HANDRED_BET,
								 false,
								 game_info.mall_bet,
								 uid,
								 false,
								 now_time
								 );

		return;
	}
	else
	{
		// 玩家是否金币不足
		_tint64 nUserGold = user->get_gold();
		if (bet_count > nUserGold)
		{
			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinReject, "", "gold not enough to bet", nullptr, 0);
			MY_LOG_ERROR("user[%d] on_user_bet_roll gold not enouth to bet, bet_count: %.3f, user gold: %lld", uid, bet_count, nUserGold);
			return;
		}
		user_bet_logic(user, game_info, false, bet_count, now_time);
	}
}

void CGameComponent::user_bet_logic(IUser *pUser, user_game_info &game_info, bool use_free, int total_bet, int now_time)
{
	int uid = pUser->get_user_id();

	if (!use_free)
	{
		m_table->update_gold_and_clore_single(uid, -total_bet, GOLD_HANDRED_BET, 0);
	}

	calcu_graph(game_info);
	calcu_result(game_info);

	if (game_info.result > 0)
	{
		m_table->update_gold_and_clore_single(uid, game_info.result, GOLD_DEL_GAME, 0);
	}
	//calcu_tax(pUser, md5key);
	write_transfe_inorut_one(game_info.result,
							 game_info.pay_out,
							 GOLD_DEL_GAME,
							 true,
							 game_info.mall_bet,
							 uid,
							 true,
							 now_time
							 );

	write_versus_bill(uid, now_time);

	int bet = game_info.bet;
    // SpinRoundHistory h(game_info.uid, game_info.round_index, GameConfig::GetInstance()->get_game_id(), std::round(bet/100.0), 
    //         std::round(game_info.result/100.0) - std::round(bet/100.0), \
    //         ((float)game_info.balance_before_round + game_info.result - bet) / 100.0, 0, "PHP", game_info.record_id);
    // if (m_table) {
    //     m_table->write_spin_round_history(h);
    // }

	send_bet_roll_result(pUser, get_error_msg(MSG_ROOM_BET_SUCCESS, pUser->get_user_country()), true);
}

void CGameComponent::insert_scatter(user_game_info &game_info)
{
	int r = getRand(1, 100);

	if (r < 10)
	{
		m_game_logic.insert_scatter(game_info.vec_remove, 2);
	}
	else if (r < 30)
	{
		m_game_logic.insert_scatter(game_info.vec_remove, 1);
	}
}
// 计算图形
void CGameComponent::calcu_graph(user_game_info &game_info, int i)
{
	game_info.reset();
	//m_game_logic.gen_no_reward_game_data(game_info);
#if 0
	m_game_logic.rand_card(game_info);
#else
	string graph;
	int index,multer;
	game_info.game_vec_remove.clear();	
	game_info.all_total_multer = 0;
	game_info.game_multer.clear();
	game_info.is_free = false;

	bool bRet = get_lottery_result(game_info.client_id, game_info.cur_mode, multer);
	if (!bRet)
	{
		m_game_logic.gen_no_reward_game_data(game_info);
		MY_LOG_ERROR("get_lottery_result failed");
		insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
		return;
	}
	//MY_LOG_PRINT("multer = %d", multer);
	if (multer == 0 && game_info.cur_mode == 0)
	{		
		m_game_logic.gen_no_reward_game_data(game_info);
		insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
		
		return;
	}
	// if(game_info.cur_mode == 1)
	// {
	// 	multer /=5;
	// }
	if(multer > 9200 || game_info.cur_mode == 1)
	{
		game_info.is_free = true;	
		
		vector<int> vec_multer;
		std::random_device rd;
		std::mt19937 g(rd());
		int multer1 = std::round(multer / 20.0)*20;
		int least = multer1/10;
		//MY_LOG_PRINT("least = %d", least);
		vec_multer.push_back(0);
		for(int i=0; i<10; i++)
		{
			//MY_LOG_PRINT("multer1 = %d", multer1);
			int left = std::round(least / 20.0);
			int r = getRand(left+1,500)*20;
			if(multer1 - r <=0 || i==9)
			{
				r = multer1;
			}
			multer1 -= r;
			
			vec_multer.push_back(r/2);
		}

		std::shuffle(vec_multer.begin()+1, vec_multer.end(), g);
		
		for(int j = 0; j<vec_multer.size(); j++)
		{
			game_info.reset();
			game_info.comboBonus = 2;
			int m = vec_multer[j];
			//MY_LOG_PRINT("m = %d", m);
			if(j==0)
			{
				m_game_logic.gen_no_reward_game_data(game_info);
				m_game_logic.insert_scatter(game_info.vec_remove, 3);														
			}
			else
			{
				if (m == 0)
				{
					m_game_logic.gen_no_reward_game_data(game_info);
				}
				else
				{
					string key = "FH_";
					key = key + to_string(m);

					if (myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
					{
						// MY_LOG_PRINT("graph = %s", graph.c_str());
						m_game_logic.gen_game_data_from_graph_data(game_info, graph);						
					}
					else
					{
						bool bHas = false;
						for(int i=0; i<10; i++)
						{
							//MY_LOG_PRINT("m=%d",m);
							m-=5;
							key = "FH_";
							key = key + to_string(m);
							if(myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
							{
								bHas = true;
								m_game_logic.gen_game_data_from_graph_data(game_info, graph);
								break;
							}	
						}
						if(!bHas)
						{
							m_game_logic.gen_no_reward_game_data(game_info);
							MY_LOG_ERROR("get_graph_from_redis failed, multer = %d", m);						
						}						
					}
				}
				insert_scatter(game_info);							
			}
			//MY_LOG_PRINT("game_info.total_multer = %d", game_info.total_multer);
			game_info.game_multer.push_back(game_info.total_multer);
			game_info.all_total_multer += game_info.total_multer;
			game_info.game_vec_remove.push_back(game_info.vec_remove);
		}
	}
	else
	{
		game_info.is_free = false;
		string key = "FH_";
		key = key + to_string(multer);

		if (myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
		{
			// MY_LOG_PRINT("graph = %s", graph.c_str());
			m_game_logic.gen_game_data_from_graph_data(game_info, graph);			
		}
		else
		{
			bool bHas = false;
			for (int i = 0; i < 10; i++)
			{
				// MY_LOG_PRINT("m=%d",m);
				multer -= 5;
				key = "FH_";
				key = key + to_string(multer);
				if (myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
				{
					bHas = true;
					m_game_logic.gen_game_data_from_graph_data(game_info, graph);
					break;
				}
			}
			if(!bHas)
			{
				m_game_logic.gen_no_reward_game_data(game_info);
				MY_LOG_ERROR("get_graph_from_redis failed, multer = %d", multer);
			}
		}
		
		insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
	}
#endif
}

// 计算游戏结果
void CGameComponent::calcu_result(user_game_info &game_info)
{
	game_info.result = (game_info.all_total_multer*game_info.bet)/100.0;
	game_info.pay_out = game_info.result - game_info.mall_bet;
}

// 计算税收
void CGameComponent::calcu_tax(IUser *pUser, const std::string &md5key)
{
	int uid = pUser->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
		return;

	// 输钱不用税收
	if (iter->second.pay_out <= 0)
		return;

	int tax = iter->second.pay_out * m_tax / 1000;
	if (tax == 0)
		return;

	MY_LOG_DEBUG("calcu_tax uid:%u tax:%d m_tax:%d",
				 uid, tax, m_tax);
	iter->second.tax = tax;
}

// 获得md5key值
void CGameComponent::calcu_md5key(int uid, string &strkey, int time)
{
	char md5str[36];
	char md5Key[33];
	memset(md5str, 0, sizeof(md5str));
	memset(md5Key, 0, sizeof(md5Key));
	mysprintf(md5str, sizeof(md5str), "%d%d%d%d", time,
			  m_table->get_room_id(), (int)m_table->get_node_id(), uid);
	g_com->_md5security(md5str, md5Key);
	strkey = md5Key;
}

/****************************************************/

// 打包玩家个人对局明细
void CGameComponent::pack_user_detail(string &detail, int uid)
{
	// todo: jili的游戏需要重新实现
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
		return;

	Json::Value json_req_value;
	int bet = iter->second.bet;

	json_req_value["result"] = Json::Value(iter->second.result);
	json_req_value["bet"] = Json::Value(bet);
	json_req_value["payout"] = Json::Value(iter->second.pay_out);
	Json::FastWriter json_writer;
	detail = json_writer.write(json_req_value);
	detail = replace_all(detail, "\"", "\\\'", 0);
}

// 对局流水
void CGameComponent::write_versus_bill(int uid, int now_time)
{
	char szlist[10] = {0};
	Json::Value va;
	Json::Value json_req_value;

	// 头部信息
	json_req_value["r_id"] = Json::Value("");
	int now = time(0);
	json_req_value["post_time"] = Json::Value(now);
	json_req_value["room_id"] = Json::Value(m_table->get_room_id());
	json_req_value["table_id"] = Json::Value(m_table->get_node_id());
	json_req_value["players"] = Json::Value(1);
	json_req_value["tax"] = Json::Value(0);
	json_req_value["play_time"] = Json::Value(0); /* 本局耗时 */
	json_req_value["match_id"] = Json::Value(m_table->get_room_id());
	json_req_value["game_detail"] = Json::Value("");
	json_req_value["game_id"] = Json::Value(GameConfig::GetInstance()->get_game_id());
	json_req_value["node_id"] = Json::Value(m_table->get_node_id());
	json_req_value["room_mode"] = Json::Value(TYPE_HUNDRED);

	// 一批批用户数据打包写入
	iter_user_game_info itr = m_map_user_game_info.find(uid);
	if (itr != m_map_user_game_info.end())
	{
		IUser *user = m_table->get_user_from_uid(itr->first);
		va["uid"] = Json::Value(uid);
		int gold = 0;
		if (user)
		{
			va["nickname"] = Json::Value(user->get_nick_name());
			va["client_id"] = Json::Value(user->get_client_id());
			va["version"] = Json::Value(user->get_version());
			va["usertype"] = Json::Value(user->get_user_type());

			gold = user->get_gold();
		}
		else
		{
			va["client_id"] = Json::Value(0);
			va["version"] = Json::Value("");
			va["usertype"] = Json::Value(0);
			va["nickname"] = Json::Value("");
		}

		if (itr->second.pay_out == 0)
		{
			va["result"] = Json::Value(3); /* 流局|平分 */
		}
		else
		{
			if (itr->second.pay_out < 0)
				va["result"] = Json::Value(1); /* 输 */
			else
				va["result"] = Json::Value(2); /* 赢 */
		}

		va["times"] = Json::Value(0);					 /* 倍数 */
		va["score1"] = Json::Value(itr->second.pay_out); /* 当局变化的积分 */
		va["score2"] = Json::Value(gold);				 /* 当前积分 */
		va["point1"] = Json::Value(0);
		va["point2"] = Json::Value(0);

		string user_detail;
		pack_user_detail(user_detail, uid);
		va["poker_detail"] = Json::Value(user_detail.c_str());
		va["tax"] = Json::Value(0);
		int bet = itr->second.bet;

		va["bet"] = Json::Value(bet);

		mysprintf(szlist, sizeof(szlist), "player_%d", 0);
		json_req_value[szlist] = Json::Value(va);
	}
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	m_table->write_versus_bill(json_req_data.c_str(), json_req_data.length());
	MY_LOG_DEBUG("versus_bill: %s", json_req_data.c_str());
}

// 投付记录
void CGameComponent::write_transfe_inorut_one(int out_score,
											  int pay_out,
											  int bill_type,
											  bool is_end,
											  int bet,
											  int uid,
											  bool write_db,
											  int time
											  )
{
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	IUser *user = m_table->get_user_from_uid(uid);
	if (iter == m_map_user_game_info.end() || user == NULL)
	{
		MY_LOG_ERROR("write_transfe_inorut_one error");
		return;
	}
	hundred_transfer_inout_one data;
	char parent_id[64] = "";
	sprintf(parent_id, "%d%d%d", GET_INT_CONFIG("game_id"),uid,time);
	data.parent_id = parent_id;
	data.bill_type = bill_type;
	data.is_end = is_end;
	data.bet = bet;
	data.result = out_score;
	data.pay_out = pay_out;
	data.uid = uid;
	data.time = time;
	data.call_back = (bill_type == GOLD_HANDRED_BET);
	data.write_db = (bill_type == GOLD_DEL_GAME || bill_type == GOLD_DEL_TAX_REVENUE);
	data.api_type = user->get_api_type();
	data.sub_client_id = iter->second.sub_client_id;
	data.client_id = iter->second.client_id;
	data.web_token = iter->second.web_token;
	data.user_type = iter->second.user_type;
	m_table->write_transfer_inout(data);
}

bool CGameComponent::transfer_inout_result(transfer_inout_result_info data)
{
	MY_LOG_DEBUG("user[%d] transfer_inout_result req_info: %s is_success: %d erro_code: %d",
				 data.uid, data.req_info.c_str(),
				 data.is_suc ? 1 : 0,
				 data.erro_code);

	IUser *user = m_table->get_user_from_uid(data.uid);
	if (user == NULL)
	{
		MY_LOG_ERROR("user[%d] transfer_inout_result cannot found user by uid", data.uid);
		return false;
	}

	iter_user_game_info iter = m_map_user_game_info.find(data.uid);
	if (iter == m_map_user_game_info.end())
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::spinCoinError, "", "transfer gold failed", nullptr, 0);
		MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
		return false;
	}

	int user_country = myredis::GetInstance()->get_data_by_uid(data.uid, DB_COMM, CUR_COUNTRY_CODE);
	if (!data.is_suc)
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::spinCoinError, "", "transfer gold failed", nullptr, 0);
		MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
		return true;
	}

	if (data.erro_code != SUCCESS)
	{

		switch (data.erro_code)
		{
		case NOT_ENOUGH_BALANCE:
		{

			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinReject, "", "gold not enough to bet", nullptr, 0);
			MY_LOG_ERROR("user[%d] on_user_bet_roll gold not enouth to bet, bet: %d", data.uid, data.bet);
			return true;
		}
		break;

		default:
		{
			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinCoinError, "", "transfer gold failed", nullptr, 0);
			MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
			return true;
		}
		break;
		}
	}

	// 扣注成功后才才开始游戏逻辑
	user_bet_logic(user, iter->second, false, data.bet, data.time);

	return true;
}

void CGameComponent::on_update_user_gold_result(IUser *user)
{
	_uint32 uid = user->get_user_id();
	_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	user->set_gold(gold);
	MY_LOG_PRINT("user[%d] send game info response with gold: %d", uid, gold);
	send_game_info_result(user);
}

//获得抽奖结果
bool CGameComponent::get_lottery_result(int client_id, int mode, int &result)
{
	int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();

	return Superace::winlose_control::GetInstance()->get_lottery_ctr_result(
		client_id, 
		game_id,
		nodeid,
		mode,
		result);
}

//获取底注
bool CGameComponent::get_base_bet(int client_id, int &result)
{
	int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();

	return Superace::winlose_control::GetInstance()->get_lottery_ctr_result(
		client_id, 
		game_id,
		nodeid,
		2,
		result);
}
﻿
#include "game_frame.h"
#include "log_manager.h"
#include "user_manager.h"

#include "table.h"
#include "table_manager.h"

#include "room_route_challenge.h"
#include "room_route_lobby.h"
#include "cmd_net.h"
#include "room_route.h"
#include "kw.h"
#include "client_cmd.h"
#include "redis_manager.h"
#include "my_redis.h"
#include "bill_fun.h"
#include "msg_manager.h"
#include "robot_manager.h"
#include "web_request.h"
#include "pair_manager.h"
#include <math.h>
#include "room_bill_fun.h"
#include "comm_fun.h"
#ifdef USE_GAME_DLL
#include "i_game_config.h"
#else
#include "game_component.h"
#endif
#include "common.h"

static UserManager *m_user_mgr = UserManager::GetInstance();

CTable::CTable()
{   
    m_game_com = 0;
    m_room_num = 0;
	m_play_count = 0;
	m_avatar_switch = 0;
    clear();
}

void CTable::clear()
{
	MY_LOG_DEBUG("clear Table[%d] ", m_table_id);
    m_playing = false;
    m_need_free = false;    
	m_ready_start = false;
	m_bankup_check = true;
    m_owner = INVALID_UID;
    m_room_num = 0;
	m_max_player = 0;
	m_cost_card = 0;	
	m_player_count = 0;
	m_play_count = 0;
	m_node_id = 0;
	m_btiyan = false;
    memset(m_game_users, 0, sizeof(m_game_users));
	memset(&m_private_limit, 0, sizeof(m_private_limit));
	m_end_game_time = time(0);
	
	m_nPlayMethod   = -1;
	m_nDefaultScore = 0;
	m_n64CreateTime = 0;
	UserManager::GetInstance()->delete_check_tick(m_room_num);
	UserManager::GetInstance()->delete_text_radio(m_room_num);
	UserManager::GetInstance()->delete_avatar(m_room_num);
    _reset_data();
}

CTable::~CTable()
{
    if (m_game_com)
    {
#ifdef USE_GAME_DLL
        m_game_dll.DeleteInstance();
#else
        delete m_game_com;
#endif
        m_game_com = 0;
    }
}

void CTable::set_playing_method(int nPlayMethod)
{ 
	MY_LOG_DEBUG("Table[%d] set_playing_method %d", m_table_id, nPlayMethod);
	m_nPlayMethod = nPlayMethod; 
}

int CTable::get_playing_method()
{ 
	MY_LOG_DEBUG("Table[%d] get_playing_method %d", m_table_id, m_nPlayMethod);
	return m_nPlayMethod; 
}

void CTable::set_owner(IUser *pUser)
{
    if (!pUser || m_owner != INVALID_UID)
        return;
    
    m_owner = pUser->get_user_id();
    m_version = pUser->get_ver_int();
	memset(m_owner_name, 0, sizeof(m_owner_name));
	memcpy(m_owner_name, pUser->get_nick_name(), strlen(pUser->get_nick_name()));

    MY_LOG_DEBUG("Table[%d] owner[%u] version[%s] PlayMethod[%d]", m_table_id, m_owner, pUser->get_version(), m_nPlayMethod);
}

//改变房主信息
void CTable::change_owner_info(_uint8 chairid)
{
	IUser *pUser = get_user_from_chair(chairid);
	if (pUser)
	{
		m_owner = pUser->get_user_id();
		m_version = pUser->get_ver_int();
		memset(m_owner_name, 0, sizeof(m_owner_name));
		memcpy(m_owner_name, pUser->get_nick_name(), strlen(pUser->get_nick_name()));
	}
}

//站起用户
void CTable::standup_user(_uint8 chairid)
{
	IUser *pUser = get_user_from_chair(chairid);
	if (!pUser)
		return;

	MY_LOG_DEBUG("standup_user...Table[%d] user %d", m_table_id, pUser->get_user_id());
	CClientCmd::do_update_user_vcoin(pUser->get_user_id(), pUser->get_gold(), 0,
		cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

	m_game_com->on_user_action(pUser, comm_action_leave);
	pUser->set_user_status(cmd_room::USER_STATUS_STANDUP);
	broadcast_user_info(pUser);
	_free_chair(pUser->get_chair_id());
	pUser->set_chair_id(INVALID_CHAIR);
	pUser->set_table_id(INVALID_TABLE);

	if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
	{
		CRoomRouteChallenge::send_challenge_user_leave(pUser->get_user_id(), m_room_num, 0, 0);
	}
	else
	{
		CRoomRouteChallenge::send_nomal_user_leave(pUser->get_user_id());
	}

	if (pUser->get_user_type() == ROBOT_TYPE && config_manager::GetInstance()->get_room_type() != TYPE_FMATCH)
	{
		robot_manager::GetInstance()->push_robot(pUser->get_user_id());
	}
}

//踢出用户
void CTable::tick_user(_uint8 chairid)
{	
	IUser *pUser = get_user_from_chair(chairid);
	if (!pUser)
		return;

	if (config_manager::GetInstance()->get_room_type() == TYPE_FMATCH)
		return;

	if (pUser->get_real_status() == cmd_room::USER_STATUS_PLAY)
		return;

	if (m_table_id != pUser->get_table_id())
	{
		//会偶现找的一张他不在的桌子进行清除，做个防护，直接投递给他所在的桌子进行处理
		int user_table_id = pUser->get_table_id();
		CTable* ptable = TableManager::GetInstance()->get_table(user_table_id);
		if (ptable != NULL)
		{
			ptable->tick_user(chairid);
		}
		MY_LOG_ERROR("tick_user erro...m_table_id:%d  user_table_id:%d", m_table_id, user_table_id);
		return;
	}
	
	MY_LOG_DEBUG("tick_user...Table[%d] user %d", m_table_id, pUser->get_user_id());
	CClientCmd::do_update_user_vcoin(pUser->get_user_id(), pUser->get_gold(), 0, 
		cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);

	m_game_com->on_user_action(pUser, comm_action_leave);
	pUser->set_user_status(cmd_room::USER_STATUS_NULL);
	broadcast_user_info(pUser);

    if (pUser->get_chair_id() == INVALID_CHAIR)
    {
		int chair_id = get_user_chair(pUser->get_user_id());
		if (chair_id != -1)
		{
			_free_chair(chair_id);
		}
    }
	else
	{
		_free_chair(pUser->get_chair_id());
	}
	pUser->set_chair_id(INVALID_CHAIR);
	pUser->set_table_id(INVALID_TABLE);

	if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
	{
		CRoomRouteChallenge::send_challenge_user_leave(pUser->get_user_id(),m_room_num, 0, 0);
	}
	else
	{
		CRoomRouteChallenge::send_nomal_user_leave(pUser->get_user_id());
	}

	if (pUser->get_user_type() == ROBOT_TYPE && !pUser->is_offline())
	{
		robot_manager::GetInstance()->push_robot(pUser->get_user_id());
	}
	else
	{
		UserManager::GetInstance()->delete_user(pUser);
	}
}

void CTable::set_owner(int uid)
{
	m_owner = uid;
	memset(m_owner_name, 0, sizeof(m_owner_name));
	sprintf(m_owner_name, "%d", uid);
}

void CTable::release()
{
	MY_LOG_DEBUG("回收桌子 delete_table(%d)delete_table！",m_table_id);
	for (_uint8 i = 0; i < m_max_player; i++)
	{
		m_user_mgr->delete_user(m_game_users[i]);
	}

	CRoomRouteChallenge::send_challenge_game_over(m_owner, m_room_num, 0,room_route::CHALLENGE_NORMAL_OVER);
	TableManager::GetInstance()->delete_table(this);
}

//解散返钻
void CTable::free_diamond(int code)
{
	const char *msg=NULL;
	switch(code)
	{
	case room_route::CHALLENGE_NOPLAY_DISMISSED:
	case room_route::CHALLENGE_NOPLAY_DISMISSED_EXT: //由房间自己发弹框提示
		{
			if (m_play_count > 1)
				break;
			if (m_play_count == 1 && !m_playing)
				break;

			//区分切磋场与约战场
			if (get_room_mode() == TYPE_MATCH)
			{				
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					if (m_game_users[i] <= 0)
						continue;

					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
					if (pUser != NULL)
						code = pUser->get_user_country();

					//发送解散消息
					msg = get_error_msg(MSG_ROOM_QIECUO_DISMISSION, code);
					CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOPLAY_DISMISSION, msg);

					if ( m_cost_card > 0)
					{
						char tips[128] = { 0 };
						msg = get_error_msg(MSG_ROOM_QIECUO_RETURN_DIAMOND, code);
						sprintf(tips, msg, m_cost_card);
						CGameFrame::GetInstance()->send_comm_message(m_owner, cmd_room::ROOM_QIECUO_NOPLAY_DISMISSION, tips);

						//返回报名费				
						CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, QIECUO_RUTURN_SIGNUP_BILL);
					}
				}
			}
			else
			{		
				if (m_game_com->get_pay_mode() == e_PMT_Creater)
				{
					for (_uint8 i = 0; i < m_max_player; i++)
					{
						if (m_game_users[i] <= 0)
							continue;

						int code = LANGUATE_EN;
						IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
						if (pUser != NULL)
							code = pUser->get_user_country();

						if (code == room_route::CHALLENGE_NOPLAY_DISMISSED)
						{
							msg = get_error_msg(MSG_ROOM_NOPLAY_DISMISSION, code);
							CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOPLAY_DISMISSION, msg);
						}

						if (m_game_users[i] == m_owner && m_cost_card > 0)
						{
							char tips[128] = { 0 };
							msg = get_error_msg(MSG_ROOM_ONE_UNFINISH_DISMISSION, code);
							sprintf(tips, msg, m_room_num, m_cost_card);
							CGameFrame::GetInstance()->send_comm_message(m_owner, cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);
						}
					}

					//返回钻石				
					CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, ROOM_RETURN_MONEY_BILL);
				}
				else
				{
					bool bOwnerNotInGame = true;
					for (_uint8 i = 0; i < m_max_player; i++)
					{
						if (m_game_users[i] <= 0)
							continue;

						if (m_game_users[i] == m_owner)
							bOwnerNotInGame = false;

						int code = LANGUATE_EN;
						IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
						if (pUser != NULL)
							code = pUser->get_user_country();

						if (code == room_route::CHALLENGE_NOPLAY_DISMISSED)
						{
							msg = get_error_msg(MSG_ROOM_NOPLAY_DISMISSION, code);
							CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOPLAY_DISMISSION, msg);
						}

						if (m_cost_card > 0)
						{
							char tips[128] = { 0 };
							if (m_game_users[i] != m_owner)
							{
								msg = get_error_msg(MSG_ROOM_JOIN_AA_NOPLAY_DISMISSION, code);
								sprintf(tips, msg, m_room_num, m_cost_card);
							}
							else
							{
								msg = get_error_msg(MSG_ROOM_CREATE_AA_NOPLAY_DISMISSION, code);
								sprintf(tips, msg, m_room_num,  m_cost_card);
							}
							CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);

							//返回钻石
							//MY_LOG_ERROR("11111111111111");
							CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_game_users[i], m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
						}
					}

					//房主不在游戏，返回钻石
					if (bOwnerNotInGame && m_cost_card > 0)
					{
						//MY_LOG_ERROR("222222222");
						CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_owner, m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
					}
				}
			}
		}
		break;
	case room_route::CHALLENGE_TIMEOUT_DISMISSED:
	case room_route::CHALLENGE_TIMEOUT_DISMISSED_EXT: //由房间自己发弹框提示
		{
			//由桌子做提示，兼容以前
			if (code == room_route::CHALLENGE_TIMEOUT_DISMISSED)
			{
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					if (m_game_users[i] <= 0)
						continue;

					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
					if (pUser != NULL)
						code = pUser->get_user_country();

					msg = get_error_msg(MSG_ROOM_TIMEOUT_DISMISSION, code);
					CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_ADMIN_DISMISSION, msg);				
				}
			}

			//超时解散，只有局数未达到，才给房间内所有人返钻
			bool bOwnerNotInGame = true;
			if (m_game_com->get_pay_mode() == e_PMT_Creater)
			{
				bool is_finish_round = (m_play_count == 0);
				bool is_can_back = (m_play_count == 1 && m_playing);
				if (is_finish_round || is_can_back)
				{
					if(m_cost_card > 0)
					{
						int code = LANGUATE_EN;
						IUser *pUser = m_user_mgr->get_user(m_owner);
						if (pUser != NULL)
							code = pUser->get_user_country();

						char tips[128] = { 0 };
						msg = get_error_msg(MSG_ROOM_ONE_UNFINISH_DISMISSION, code);
						sprintf(tips, msg, m_room_num, m_cost_card);
						CGameFrame::GetInstance()->send_comm_message(m_owner, cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);
					}
					CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, ROOM_RETURN_MONEY_BILL);
				}
			}
			else
			{
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
					if (pUser != NULL)
						code = pUser->get_user_country();

					char tips[128] = { 0 };
					if (m_game_users[i] != m_owner)
					{
						msg = get_error_msg(MSG_ROOM_JOIN_AA_NOPLAY_DISMISSION, code);
						sprintf(tips, msg, m_room_num, m_cost_card);
					}
					else
					{
						bOwnerNotInGame = false;
						msg = get_error_msg(MSG_ROOM_CREATE_AA_NOPLAY_DISMISSION, code);
						sprintf(tips, msg, m_room_num,  m_cost_card);
					}

					bool is_finish_round = (m_play_count == 0);
					bool is_can_back = (m_play_count == 1 && m_playing);
					if (m_cost_card > 0 && (is_finish_round || is_can_back))
					{
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);
						//MY_LOG_ERROR("33333333333");
						CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_game_users[i], m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
					}
				}
				if ( bOwnerNotInGame )
				{
					//MY_LOG_ERROR("***************");
					CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_owner, m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
				}
			}
		}
		break;
	default:
		{
			//区分切磋场与约战场
			if (get_room_mode() == TYPE_MATCH)
				break;
			
			if (m_game_com->get_pay_mode() == e_PMT_AA || m_game_com->get_pay_mode() == e_PMT_Creater)
			{
				bool bOwnerNotInGame = true;
				if (m_game_com->get_pay_mode() == e_PMT_AA)
				{
					for (_uint8 i = 0; i < m_max_player; i++)
					{
						if (m_game_users[i] == m_owner)
						{
							bOwnerNotInGame = false;
						}

						bool is_finish_round = (m_play_count == 0);
						bool is_can_back = (m_play_count == 1 && m_playing && code != room_route::CHALLENGE_NORMAL_OVER);
						if (m_cost_card > 0 && (is_finish_round || is_can_back))
						{
							//MY_LOG_ERROR("5555555555");
							CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_game_users[i], m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
						}

					}
					if (bOwnerNotInGame)
					{
						//MY_LOG_ERROR("6666666666666 code:%d.",code);
						CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_owner, m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
					}
				}
				else
				{
					bool is_finish_round = (m_play_count == 0);
					bool is_can_back = (m_play_count == 1 && m_playing && code != room_route::CHALLENGE_NORMAL_OVER);
					if (is_finish_round || is_can_back)
					{
						if(m_cost_card > 0)
							CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, ROOM_RETURN_MONEY_BILL);
					}
				}
			}
		}
		break;
	}
}

//解散返金币
void CTable::free_gold(int code)
{
	const char *msg=NULL;
	switch(code)
	{
	case room_route::CHALLENGE_NOPLAY_DISMISSED:
	case room_route::CHALLENGE_NOPLAY_DISMISSED_EXT: //由房间自己发弹框提示
		{
			if (m_play_count > 1)
				break;
			if (m_play_count == 1 && !m_playing)
				break;

			if (m_game_com->get_pay_mode() == e_PMT_Creater)
			{
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					if (m_game_users[i] <= 0)
						continue;

					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_owner);
					if (pUser != NULL)
						code = pUser->get_user_country();

					if (code == room_route::CHALLENGE_NOPLAY_DISMISSED)
					{
						msg = get_error_msg(MSG_ROOM_NOPLAY_DISMISSION, code);
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOPLAY_DISMISSION, msg);
					}

					if (m_game_users[i] == m_owner && m_cost_card > 0)
					{
						char tips[128] = { 0 };
						msg = get_error_msg(MSG_ROOM_ONE_UNFINISH_DISMISSION, code);
						sprintf(tips, msg, m_room_num, m_cost_card);
						CGameFrame::GetInstance()->send_comm_message(m_owner, cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);
					}
				}

				//返回钻石
				CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, ROOM_RETURN_MONEY_BILL);
			}
			else
			{
				bool bOwnerNotInGame = true;
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					if (m_game_users[i] <= 0)
						continue;

					if (m_game_users[i] == m_owner)
						bOwnerNotInGame = false;

					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
					if (pUser != NULL)
						code = pUser->get_user_country();

					if (code == room_route::CHALLENGE_NOPLAY_DISMISSED)
					{
						msg = get_error_msg(MSG_ROOM_NOPLAY_DISMISSION, code);
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOPLAY_DISMISSION, msg);
					}

					if (m_cost_card > 0)
					{
						char tips[128] = { 0 };
						if (m_game_users[i] != m_owner)
						{
							msg = get_error_msg(MSG_ROOM_JOIN_AA_NOPLAY_DISMISSION, code);
							sprintf(tips, msg, m_room_num, m_cost_card);
						}
						else
						{
							msg = get_error_msg(MSG_ROOM_CREATE_AA_NOPLAY_DISMISSION, code);
							sprintf(tips, msg, m_room_num,  m_cost_card);
						}
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);

						//返回钻石
						//MY_LOG_ERROR("77777777");
						CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_game_users[i], m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
					}
				}

				//房主不在游戏，返回钻石
				if (bOwnerNotInGame && m_cost_card > 0)
				{
					//MY_LOG_ERROR("88888888888888");
					CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_owner, m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
				}
			}
		}
		break;
	case room_route::CHALLENGE_TIMEOUT_DISMISSED:
	case room_route::CHALLENGE_TIMEOUT_DISMISSED_EXT: //由房间自己发弹框提示
		{
			//由桌子做提示，兼容以前
			if (code == room_route::CHALLENGE_TIMEOUT_DISMISSED)
			{
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					if (m_game_users[i] <= 0)
						continue;

					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
					if (pUser != NULL)
						code = pUser->get_user_country();

					msg = get_error_msg(MSG_ROOM_TIMEOUT_DISMISSION, code);
					CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_ADMIN_DISMISSION, msg);				
				}
			}

			//超时解散，只有局数未达到，才给房间内所有人返钻
			bool bOwnerNotInGame = true;
			if (m_game_com->get_pay_mode() == e_PMT_Creater)
			{
				bool is_finish_round = (m_play_count == 0);
				bool is_can_back = (m_play_count == 1 && m_playing);
				if (is_finish_round || is_can_back)
				{
					if(m_cost_card > 0)
					{
						int code = LANGUATE_EN;
						IUser *pUser = m_user_mgr->get_user(m_owner);
						if (pUser != NULL)
							code = pUser->get_user_country();

						char tips[128] = { 0 };
						msg = get_error_msg(MSG_ROOM_ONE_UNFINISH_DISMISSION, code);
						sprintf(tips, msg, m_room_num, m_cost_card);
						CGameFrame::GetInstance()->send_comm_message(m_owner, cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);
					}
					CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, ROOM_RETURN_MONEY_BILL);
				}
			}
			else
			{
				for (_uint8 i = 0; i < m_max_player; i++)
				{
					int code = LANGUATE_EN;
					IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
					if (pUser != NULL)
						code = pUser->get_user_country();

					char tips[128] = { 0 };
					if (m_game_users[i] != m_owner)
					{
						msg = get_error_msg(MSG_ROOM_JOIN_AA_NOPLAY_DISMISSION, code);
						sprintf(tips, msg, m_room_num, m_cost_card);
					}
					else
					{
						bOwnerNotInGame = false;
						msg = get_error_msg(MSG_ROOM_CREATE_AA_NOPLAY_DISMISSION, code);
						sprintf(tips, msg, m_room_num,  m_cost_card);
					}

					bool is_finish_round = (m_play_count == 0);
					bool is_can_back = (m_play_count == 1 && m_playing);
					if (m_cost_card > 0 && (is_finish_round || is_can_back))
					{
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_AA_NOPLAY_DISMISSION, tips);
						//MY_LOG_ERROR("99999");
						CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_game_users[i], m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
					}
				}
				if ( bOwnerNotInGame )
				{
					//MY_LOG_ERROR("00000");
					CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_owner, m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
				}
			}
		}
		break;
	default:
		{
			if (m_game_com->get_pay_mode() == e_PMT_AA || m_game_com->get_pay_mode() == e_PMT_Creater)
			{
				bool bOwnerNotInGame = true;
				if (m_game_com->get_pay_mode() == e_PMT_AA)
				{
					for (_uint8 i = 0; i < m_max_player; i++)
					{
						if (m_game_users[i] == m_owner)
						{
							bOwnerNotInGame = false;
						}

						bool is_finish_round = (m_play_count == 0);
						bool is_can_back = (m_play_count == 1 && m_playing && code != room_route::CHALLENGE_NORMAL_OVER);
						if (m_cost_card > 0 && (is_finish_round || is_can_back))
						{
							//MY_LOG_ERROR("aaaaa");
							CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_game_users[i], m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
						}

					}
					if (bOwnerNotInGame)
					{
						//MY_LOG_ERROR("bbbbbb");
						CRoomRouteChallenge::add_room_card_on_noplay_dismissed(m_owner, m_room_num, get_cost_card(), AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
					}
				}
				else
				{
					bool is_finish_round = (m_play_count == 0);
					bool is_can_back = (m_play_count == 1 && m_playing && code != room_route::CHALLENGE_NORMAL_OVER);
					if (is_finish_round || is_can_back)
					{
						if(m_cost_card > 0)
							CClientCmd::do_add_room_card_dbproxy(m_owner, m_room_num, m_cost_card, ROOM_RETURN_MONEY_BILL);
					}
				}
			}
		}
		break;
	}
}

void CTable::free(int code)
{
	MY_LOG_DEBUG("CTable::freeCTable::freeCTable::freeCTable::freeCTable::freeCTable::freeCTable::free.%d  code:%d  round:%d", 
		this->get_room_id(), code, m_game_com->get_play_count() );

	switch(config_manager::GetInstance()->get_create_pay_type())
	{
	case PAY_TYPE_DIAMOND:
		free_diamond(code);
		break;
	case PAY_TYPE_GOLD:
		free_gold(code);
		break;
	default:
		break;
	}

	//约战房
	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
	{
		//是否触发创建房间任务
		if (code == room_route::CHALLENGE_NORMAL_OVER)
		{
			bool bOwnerInGame = false;
			for (_uint8 i = 0; i < m_max_player; i++)
			{
				if (m_game_users[i] == m_owner)
				{
					bOwnerInGame = true;
					break;
				}
			}
		}

		/* 通知约战，同时如果是room_route::CHALLENGE_NOPLAY_DISMISSED，返钻 */
		CRoomRouteChallenge::send_challenge_game_over(m_owner, m_room_num, get_cost_card(),code);
	}

	//切磋场
	if (config_manager::GetInstance()->get_room_type() == TYPE_MATCH)
	{
		for (_uint8 i = 0; i < m_max_player; i++)
		{
			/* 牌局结束通知约战 */
			CRoomRouteChallenge::send_qiecuo_game_over(m_game_users[i],code);
		}
	}

	m_gpsLogic.clear();
    m_need_free = true;
}

int CTable::on_recv(_uint8 scmd, const void* pData, int size, IUser* pUser)
{
    if (!pUser)
    {
        MY_LOG_ERROR("Table recv param is error.");
        return -1;
    }

    m_recv_data = pData;
    m_recv_len = size;
    m_puser = pUser;

    if (pUser->get_chair_id() >= m_max_player)
    {
        MY_LOG_ERROR("User table chair is error.%d", pUser->get_table_id());
        return -2;
    }

	//聊天和发表情要限制间隔
	if (scmd == cmd_net::SUB_LOOK_AVATAR_REQ || scmd == cmd_net::SUB_CHAT_MSG_REQ)
	{
		if (time(0) - pUser->get_chat_time() < config_manager::GetInstance()->get_chat_distance())
		{
			//const char* tips = "请不要频繁发送聊天信息";
			const char* tips = get_error_msg(MSG_ROOM_DO_NOT_SEND_MSG_FREQUENTLY, pUser->get_user_country());

			send_common_msg(pUser->get_chair_id(), tips, strlen(tips), cmd_room::ROOM_NOMAL_TIPS);
			return 0;
		}
		pUser->set_chat_time();
	}

    switch (scmd)
    {
    case cmd_net::SUB_CHAT_MSG_REQ:
        on_chat(pUser, pData, size);
        break;
	//case cmd_net::SUB_GPS_UPLOAD:
		//on_gps_recv(pUser, pData, size);
		//break;
	case cmd_net::SUB_MASTER_KICK_USER:
		on_master(pUser, pData, size);
		break;
	case cmd_net::SUB_LOOK_AVATAR_REQ:
		on_look_avatar(pUser, pData, size);
		break;
	case cmd_net::SUB_TEXT_RADIO_REQ:
		on_text_radio(pUser, pData, size);
		break;
	//case cmd_net::SUB_USER_GPS_UPLOAD:
		//on_upload_gps(pData, size);
		//break;
    default:
		pUser->set_control_time(time(0));
        m_game_com->on_recv(cmd_net::CMD_ROOM, scmd, pData, size, pUser);
        break;
    }

    return 0;
}

int CTable::on_game_recv(_uint8 scmd, const void* pData, int size, IUser* pUser)
{
    if (m_game_com)
    {
        return m_game_com->on_recv(cmd_net::CMD_GAME, scmd, pData, size, pUser);
    }

    return 0;
}

void CTable::on_look_avatar(IUser *pUser, const void *pdata, int size)
{
	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
	{
		if (m_avatar_switch != 1)
			return;
	}

	if (UserManager::GetInstance()->check_avatar(pUser->get_user_id(), m_room_num))
	{
		return;
	}
	
	cmd_room::LookAvatarReq stReq;
	cmd_room::LookAvatarResp stRsp;

	if (stReq.unpack(pdata, size))
	{
		stRsp.set_Code(stReq.get_Code());
		stRsp.set_ToChairId(stReq.get_ToChairId());
		stRsp.set_SendChairId(pUser->get_chair_id());
		stRsp.set_Uid(pUser->get_user_id());
		send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_LOOK_AVATAR_RESP, &stRsp);
	}

	UserManager::GetInstance()->add_avatar(m_room_num, pUser->get_user_id());
}

void CTable::on_text_radio(IUser *pUser, const void *pdata, int size)
{
	if (UserManager::GetInstance()->check_text_radio(pUser->get_user_id(), m_room_num))
	{
		return;
	}

	cmd_room::TextRadioReq stReq;
	cmd_room::TextRadioResp stRsp;

	if (stReq.unpack(pdata, size))
	{
		stRsp.set_Code(stReq.get_Code());
		stRsp.set_ToChairId(stReq.get_ToChairId());
		stRsp.set_SendChairId(pUser->get_chair_id());
		send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_TEXT_RADIO_RESP, &stRsp);
	}

	UserManager::GetInstance()->add_text_radio(m_room_num, pUser->get_user_id());
}

int CTable::on_chat(IUser *pUser, const void *pdata, int size)
{
    cmd_room::TableChatReq stReq;
    cmd_room::TableChatRsp stRsp;


	if (stReq.unpack(pdata, size) && stReq.Msg_length() > 0)
    {
        stRsp.set_ChairID(pUser->get_chair_id());
        stRsp.set_Type(stReq.get_Type());
		stRsp.set_uid(pUser->get_user_id());

        if (stReq.get_Type() == cmd_room::TCHAT_MSG)
        {
            /* 文字聊天屏蔽敏感字、词 */
            int  iGbk = 0;
            char szGBK[1024] = {0};           

            _utf8_to_gbk(stReq.get_Msg(), stReq.Msg_length(), szGBK, sizeof(szGBK) - 1);

            if (-1 == kw_hexie(szGBK, &iGbk))
            {
                char szUTF8[512 + 1] = {0};
                _gbk_to_utf8(szGBK, iGbk, szUTF8, sizeof(szUTF8) - 1);
                stRsp.set_Msg(szUTF8, 0);
            }
            else
            {
                stRsp.set_Msg(stReq.get_Msg(), stReq.Msg_length());
            }
        }
        else
        {            
            stRsp.set_Msg(stReq.get_Msg(), stReq.Msg_length());
        }
        
        send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_CHAT_MSG_RSP, &stRsp);

        return 0;
    }

    return -1;
}

/*
 * 函数说明：接收、处理客户端上报的GPS信息
 * 参数说明：
 *          
 * 返回值：
 *        
 */
int CTable::on_gps_recv(IUser *pUser, const void *pdata, int size)
{
	cmd_room::Location userLocation(pdata, size);
	bool bMoved = m_gpsLogic.PushLocation(pUser->get_user_id(), userLocation);
	if ( bMoved )
	{
		cmd_room::UserDistanceInfo outDistanceInfo;
		outDistanceInfo.set_NoGPSUidList_item_count(0);
		outDistanceInfo.set_DistanceList_item_count(0);
		if(m_gpsLogic.CalculateUserDistanceInfo(outDistanceInfo))
		{
			// GPS状态及信息发送给本桌子上的所以客户端
			send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_GPS_SENT_DOWN, &outDistanceInfo);

			MY_LOG_DEBUG("SUB_GPS_UPLOAD bModed = %d uid:{%d}(%lld, %lld)   distaninfo_count:%d  \r\n", 
				bMoved, pUser->get_user_id(), userLocation.get_Longitude(), userLocation.get_Latitude(), outDistanceInfo.DistanceList_item_count());
		}
	}
	return 0;
}

int CTable::on_upload_gps(const void *pdata, int size)
{
	_uint8 chair_id = m_puser->get_chair_id();
	if (chair_id == INVALID_CHAIR)
	{
		MY_LOG_ERROR("User is not on table chair. uid=%u", m_puser->get_user_id());
		return -1;
	}

	cmd_room::GPSUpload gpsUpload;    

	if (gpsUpload.unpack(pdata, size))
	{
		cmd_room::GPSPush   gpsPush;

		MY_LOG_DEBUG("User upload gps position.uid=%u longitude=%d latitude=%d"
			, m_puser->get_user_id(), gpsUpload.get_Longitude(), gpsUpload.get_Latitude());

		m_puser->set_gps(gpsUpload.get_Longitude(), gpsUpload.get_Latitude());
		m_puser->set_site(gpsUpload.get_site(), gpsUpload.site_length());

		gpsPush.set_ChairID(chair_id);
		gpsPush.set_Longitude(m_puser->get_longitude());
		gpsPush.set_Latitude(m_puser->get_latitude());
		gpsPush.set_site(gpsUpload.get_site(), 0);
		gpsPush.set_Uid(m_puser->get_user_id());
		//send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_USER_GPS_PUSH, &gpsPush);

		return 0;
	}

	return -99;
}

int CTable::on_master(IUser *pUser, const void *pdata, int size)
{
	cmd_room::MasterKickUser stReq;
	if (!stReq.unpack(pdata, size))
		return 0;

	cmd_room::MasterKickUserResp stResp;
	stResp.set_ChairID(stReq.get_ChairID());

	if (m_playing)
	{
		stResp.set_Result(1);
		stResp.set_Msg(get_error_msg(MSG_ROOM_CAN_NOT_TICK_USER_FOR_GAME_RUNNING, pUser->get_user_country()), 0);

		//stResp.set_Msg("游戏正在进行，无法踢出玩家!", 0);
		send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_MASTER_KICK_USER_RESP, &stResp);
		return 0;
	}

	if (m_owner != pUser->get_user_id())
	{
		stResp.set_Result(1);
		stResp.set_Msg(get_error_msg(MSG_ROOM_CAN_NOT_TICK_USER_FOR_NOT_OWNER, pUser->get_user_country()), 0);

		//stResp.set_Msg("非房主无法踢出玩家!", 0);
		send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_MASTER_KICK_USER_RESP, &stResp);
		return 0;
	}
		
	//通知玩家
	_tint64 tick_chairid = stReq.get_ChairID();
	if (m_game_users[tick_chairid] == 0)
	{
		stResp.set_Result(1);
		stResp.set_Msg(get_error_msg(MSG_ROOM_CAN_NOT_TICK_USER_FOR_EMPTY, pUser->get_user_country()), 0);

		//stResp.set_Msg("踢出玩家失败，座位上没有此玩家！", 0);
		send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_MASTER_KICK_USER_RESP, &stResp);
		return 0;
	}

	//通知房主踢出结果
	stResp.set_Result(0);
	stResp.set_Msg(get_error_msg(MSG_ROOM_TICK_USER_SUCCESS, pUser->get_user_country()), 0);

	//stResp.set_Msg("踢出成功！", 0);
	send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_MASTER_KICK_USER_RESP, &stResp);

	//通知被T玩家
	stResp.set_Result(0);
	stResp.set_Msg(get_error_msg(MSG_ROOM_KICK_OUT_USER_TIPS, pUser->get_user_country()), 0);
	//stResp.set_Msg("您已经被房主踢出此房间，请选择其他房间进行游戏！", 0);
	send(tick_chairid, cmd_net::CMD_ROOM, cmd_net::SUB_MASTER_KICK_USER_RESP, &stResp);

	//记录时间搓
	UserManager::GetInstance()->add_check_tick(m_room_num, m_game_users[tick_chairid]);

	//清除用户
	IUser *tick_user = m_user_mgr->get_user(m_game_users[tick_chairid]);
	if (tick_user)
	{
		//AA支付退钻
		if (m_game_com->get_pay_mode() == e_PMT_AA)
		{
			//MY_LOG_ERROR("cccccc");
			CClientCmd::do_add_room_card_dbproxy(m_game_users[tick_chairid], m_room_num, m_cost_card, AA_LEAVE_ROOM_RUTURN_YUANBALL_BILL);
		}
		
		m_gpsLogic.RemoveLocation(m_game_users[tick_chairid]);
		CRoomRouteChallenge::send_challenge_user_leave(m_game_users[tick_chairid],m_room_num, 0, 1);
		tick_user->not_offline();
		tick_user->set_user_status(cmd_room::USER_STATUS_NULL);        
		broadcast_user_info(tick_user);

		_free_chair(tick_user->get_chair_id());
		tick_user->set_chair_id(INVALID_CHAIR);
		tick_user->set_table_id(INVALID_TABLE);

		UserManager::GetInstance()->delete_user(tick_user);
	}	
	return 0;
}

int CTable::init(const void * rule, int rule_len/* = 0*/, int is_web)
{
    int ret = 0;

    if (m_game_com == 0)
    {
#ifdef USE_GAME_DLL
        MY_LOG_DEBUG("Use game dll. name=[%s]", g_pstGameConfig->get_dll_name());

        DLL_MOBULE_INIT(m_game_dll, g_pstGameConfig->get_dll_name(), "_create_component_module", "_free_component_module");        

        m_game_com = m_game_dll.GetInterface();        
#else
        m_game_com = new CGameComponent();
#endif

        if (!m_game_com)
        {
            MY_LOG_ERROR("new game component fail.");
            return -10;
        }
    }

    do 
    {
		if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
		{
			int play_id = config_manager::GetInstance()->get_playid(m_node_id);
			m_game_com->set_game_ruleid(play_id);
			m_stGoldNodeInfo.min_gold = config_manager::GetInstance()->get_min_gold(m_node_id);
			m_stGoldNodeInfo.max_gold = config_manager::GetInstance()->get_max_gold(m_node_id);
			m_stGoldNodeInfo.base_score = config_manager::GetInstance()->get_base_score(m_node_id);
			m_stGoldNodeInfo.tax      = config_manager::GetInstance()->get_room_charge_gold(m_node_id);
			MY_LOG_DEBUG("CTable init. min_gold:%d  max_gold:%d  base_score:%d", m_stGoldNodeInfo.min_gold, m_stGoldNodeInfo.max_gold, m_stGoldNodeInfo.base_score);
		}
		if (config_manager::GetInstance()->get_room_type() == TYPE_FMATCH)
		{
			if (0 != m_game_com->init(this, NULL, rule, rule_len))
			{
				MY_LOG_ERROR("init game com fail");
				ret = -12;
				break;
			}
		}
		else
		{
			cmd_room::GameTableRule game_rule;
			if (game_rule.unpack(rule, rule_len))
			{
				int play_id = 0;
				if (game_rule.exist_RuleId())
				{
					play_id = game_rule.get_RuleId();
					m_game_com->set_game_ruleid(play_id);
				}
			}
		}

		if (is_web)
		{
			if (0 != m_game_com->web_init(this, rule, rule_len))
			{
				MY_LOG_ERROR("web_init game com fail");
				ret = -12;
				break;
			}
		}
		else
		{
			if (0 != m_game_com->init(this, NULL, rule, rule_len))
			{
				MY_LOG_ERROR("init game com fail");
				ret = -12;
				break;
			}
		}

    } while (0);

    if (ret)
    {
#ifdef USE_GAME_DLL
        m_game_dll.DeleteInstance();            
#else
        delete m_game_com;
#endif
        m_game_com = 0;
        return ret;
    }                

    //m_game_com->clear(); 	
	m_play_count = 0;
	m_game_com->get_opt(GET_OPT_READY_START,   0, 0, &m_ready_start,   sizeof(m_ready_start));
	m_game_com->get_opt(GET_OPT_PRIVATE_LIMIT, 0, 0, &m_private_limit, sizeof(m_private_limit));
	m_game_com->get_opt(GET_OPT_BANKUPR_CHECK, 0, 0, &m_bankup_check,  sizeof(m_bankup_check));
	m_game_com->get_opt(GET_OPT_IS_TIYAN,      0, 0, &m_btiyan,        sizeof(m_btiyan));
	
	m_max_player = m_game_com->get_max_player();           
    return 0;
}

IUser* CTable::create_user(_tint64 uid)
{
	return m_user_mgr->create_user(uid, 1, 1);
}

IUser * CTable::get_user_from_uid(_tint64 userID)
{
    return m_user_mgr->get_user(userID);
}

IUser * CTable::get_user_from_chair(int chairID)
{
    if (chairID < 0 || chairID >= m_max_player)
    {
        return NULL;
    }

    return m_user_mgr->get_user(m_game_users[chairID]);
}

_uint64 CTable::get_uid_from_chair(int chairID)
{
    if (chairID < 0 || chairID >= m_max_player)
        return 0;

    return m_game_users[chairID];
}

bool CTable::set_timer(_uint8 timer_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat/* = 1*/)
{
	MY_LOG_DEBUG("set_timer... timeout_msec:%d, timer_id:%d tableid: %d", timeout_msec, timer_id, m_table_id);
    bool bRet = g_timer_mgr->set_timer(this
        , on_timer
        , TABLE_TIMER_ID(m_table_id, timer_id)
        , timeout_msec
        , param
        , repeat);

    if (!bRet)
    {
        MY_LOG_WARN("设置定时器 %d 时间 %u 毫秒 失败");
    }

    return bRet;
}

bool CTable::kill_timer(_uint8 timer_id)
{
    return g_timer_mgr->kill_timer(TABLE_TIMER_ID(m_table_id, timer_id));
}

bool CTable::send(IUser * pUser, _uint8 mcmd, _uint8 scmd, const void * pData, int size)
{       
    if (pUser && !pUser->is_offline())
    {
        socket_id sid = pUser->get_socket_id();
        return g_server->send(sid, mcmd, scmd, pData, size);
    }    

    return false;
}

bool CTable::send(IUser * pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData)
{   
    if (pUser && !pUser->is_offline())
    {
        socket_id sid = pUser->get_socket_id();
        return g_server->send(sid, mcmd, scmd, pData);
    }  

    return false;
}

bool CTable::send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, const void * pData, int size)
{   
    IUser * pUser = get_user_from_chair(chairID);
    if (pUser && !pUser->is_offline())
    {
        socket_id sid = pUser->get_socket_id();
        return g_server->send(sid, mcmd, scmd, pData, size);
    }    

    return false;
}

bool CTable::send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData)
{   
    IUser * pUser = get_user_from_chair(chairID);
    if (pUser && !pUser->is_offline())
    {
        socket_id sid = pUser->get_socket_id();
        return g_server->send(sid, mcmd, scmd, pData);
    }    

    return false;
}

bool CTable::send(socket_id sid, const string &str)
{   
    return g_server->send(sid, str); 
}

bool CTable::send_user_batch(_uint8 mcmd, _uint8 scmd, const void * pdata, int size)
{   
    for (int i = 0; i < m_max_player; ++i)
    {
        IUser * pUser = m_user_mgr->get_user(m_game_users[i]);
        if (pUser && !pUser->is_offline())
        {
            g_server->send(pUser->get_socket_id(), mcmd, scmd, pdata, size);
        }
    }

    return true;
}

bool CTable::send_user_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData)
{   
    for (int i = 0; i < m_max_player; ++i)
    {
        IUser * pUser = m_user_mgr->get_user(m_game_users[i]);
        if (pUser && !pUser->is_offline())
        {
            g_server->send(pUser->get_socket_id(), mcmd, scmd, pData);
        }
    }

    return true;
}

bool CTable::on_start_game()
{
    if (m_playing)
        return false;

	MY_LOG_DEBUG("Table game start. tableid: %d", m_table_id);
	m_start_time = time(0);
    m_playing = true;  
	m_play_count++;
	robot_manager::GetInstance()->force_robot_mode(this);
	
	// play status 
	int pair_uid[MAX_PAIR_NUM] = { 0 };
	int pair_count = 0;
	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user && user->get_real_status() == cmd_room::USER_STATUS_READY)
		{
			if (user->get_user_type() != ROBOT_TYPE)
			{
				pair_uid[pair_count] = m_game_users[i];
				pair_count++;
			}

			user->set_user_status(cmd_room::USER_STATUS_PLAY);
			broadcast_user_info(user, true);
		}
	}

	//记录用户体验房次数
	if (m_btiyan)
	{
		int gameid = config_manager::GetInstance()->get_game_id();
		for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
		{
			IUser *user = m_user_mgr->get_user(m_game_users[i]);
			if (user == NULL)
				continue;

			if (user->get_user_type() == ROBOT_TYPE || user->get_real_status() != cmd_room::USER_STATUS_PLAY)
				continue;

			char key[32] = { 0 };
			sprintf(key, CUR_TIYAN_TIMES, m_game_users[i], gameid);
			myredis::GetInstance()->update_user_temp_data(key, 1);
			int left_time = today_left_time();
			myredis::GetInstance()->set_user_temp_expire_time(key, left_time);
		}
	}

	int pre_cheat_type = config_manager::GetInstance()->get_pre_cheat(m_node_id);
	int near_time = config_manager::GetInstance()->m_near_time;
	MY_LOG_DEBUG("Table game start. tableid:%d  pre_cheat_type:%d  near_time:%d", m_table_id, pre_cheat_type, near_time);
	if (pre_cheat_type == 2)
	{
		for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
		{
			IUser *user = m_user_mgr->get_user(m_game_users[i]);
			if (!user)
				continue;

			MY_LOG_DEBUG("pre_cheat_type.... tableid:%d   status:%d  user_type:%d", 
				m_table_id, user->get_user_status(), user->get_user_type());

			if (user->get_user_status() == cmd_room::USER_STATUS_PLAY && user->get_user_type() != ROBOT_TYPE)
			{
				pair_manager::GetInstance()->set_pair_list(m_game_users[i], pair_uid, pair_count);
				myredis::GetInstance()->set_data_by_uid(m_game_users[i], DB_COMM, CUR_LAST_TABLE_ID, m_table_id);

				//防作弊模式，记录用户曾经匹配过的玩家UID
				if (near_time > 0)
				{
					for (int n = 0; n < MAX_GAME_PLAY_USER_EX; n++)
					{
						if (n == i)
							continue;

						IUser *check_user = m_user_mgr->get_user(m_game_users[n]);
						if (check_user && check_user->get_real_status() == cmd_room::USER_STATUS_PLAY && check_user->get_user_type() != ROBOT_TYPE)
						{
							char key[32] = { 0 };
							sprintf(key, CUR_PAER_USER, m_game_users[i], m_game_users[n]);
							myredis::GetInstance()->set_user_temp_data(key, 1);
							myredis::GetInstance()->set_user_temp_expire_time(key, near_time);
						}
					}
				}
			}
		}
	}

    m_game_com->on_game_start();

	/* 通知约战 */
	CRoomRouteChallenge::send_challenge_game_info(m_owner,m_room_num,get_max_count(),get_play_count(),get_max_player());  
	return true;
}

void CTable::end_write_result(end_result result)
{
	int now_gold[MAX_TABLE_USER_COUNT] = { 0 };
	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user && user->get_real_status() == cmd_room::USER_STATUS_PLAY)
		{
			//更新内存
			user->update_gold(result.gold_result[i]);
			now_gold[i] = user->get_gold();

			//写流水
			write_gold_bill(user->get_user_id(), result.gold_result[i], user->get_gold(), user->get_client_id(), user->get_version(), GOLD_DEL_GAME);
			
			//更新限制房间数据
			//room_check::GetInstance()->on_game_end(user->get_user_id(), result.gold_result[i]);
		}
	}

	//写对局
	//write_gold_versus(result, now_gold);
}

//游戏写钻石
void CTable::end_write_diamond_result(end_result result)
{
	//区分切磋场与约战场
	int bill_type = CHALENGE_END;
	if (get_room_mode() == TYPE_MATCH)
	{
		bill_type = QIECUO_WIN_BILL;
	}

	int now_gold[MAX_TABLE_USER_COUNT] = { 0 };
	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user && user->get_real_status() == cmd_room::USER_STATUS_PLAY)
		{
			cmd_dbproxy::CDBProxyExceSection section;
			Json::FastWriter json_writer;
			Json::Value json_req_value;

			json_req_value["uid"] = Json::Value(m_game_users[i]);
			json_req_value["post_time"] = Json::Value(g_com->_get_time32());//unix时间戳
			json_req_value["type_id"] = Json::Value(bill_type);//
			json_req_value["type_id_sub"] = Json::Value(0);//没有特别要求填0
			json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
			json_req_value["quantity"] = Json::Value(result.gold_result[i]);//数量
			json_req_value["room_id"] = Json::Value(CGameFrame::GetInstance()->get_room_id());//没有或者取不到填0
			json_req_value["version"] = Json::Value(user->get_ver_int());
			json_req_value["kind_id"] = Json::Value(0);
			std::string json_req_data = json_writer.write(json_req_value);
			g_dbproxy_namager->post_exec(0,CClientCmd::on_sub_room_card_dbproxy,m_game_users[i],"lobby_del_yuanbao",json_req_data.c_str(),section);
		}
	}
}

//游戏结束后加钻石
void CTable::end_write_add_diamond_result(end_result result)
{
	MY_LOG_DEBUG("end_write_add_diamond_result begin.");
	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{
		MY_LOG_DEBUG("++++ uid:%lld gold_result:%d.", m_game_users[i], result.gold_result[i]); 
		//IUser *user = m_user_mgr->get_user(m_game_users[i]);
		//if (user && user->get_real_status() == cmd_room::USER_STATUS_PLAY && result.gold_result[i]>0)
		if ( result.gold_result[i]>0)
		{
			CClientCmd::do_add_room_card_dbproxy(m_game_users[i], m_room_num, result.gold_result[i], QIECUO_WIN_BILL);
		}
	}
	MY_LOG_DEBUG("end_write_add_diamond_result end.");
}

void CTable::write_gold_bill(IUser * pUser, int gold, int type_id, int lose_rate, bool agent)
{
	if(!pUser)
		return;
	bool bstatistics = pUser->get_user_type() != ROBOT_TYPE ? true : false;
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		pUser->get_client_id(), 
		m_room_num, 
		pUser->get_version(), 
		pUser->get_user_id(), 
		pUser->get_gold(), 
		gold, 
		pUser->get_gold()-gold, 
		(GOLD_BILL_TYPE)type_id,
		m_node_id,
		pUser->get_user_type(),
		bstatistics,
		get_room_mode(),
		lose_rate,
		0,
		agent);
}

//写用户金币流水
void CTable::write_gold_bill(int uid, int gold, _uint64 now_gold, int client_id, const char version[16], int type_id, int lose_rate, bool agent)
{  
	IUser *user = m_user_mgr->get_user(uid);
	bool bstatistics = false;
	int user_type = 0;
	if (user)
	{
		bstatistics = user->get_user_type() != ROBOT_TYPE ? true : false;
		user_type = user->get_user_type();
	}
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		client_id, 
		m_room_num, 
		version, 
		uid, 
		now_gold, 
		gold, 
		now_gold-gold, 
		(GOLD_BILL_TYPE)type_id,
		m_node_id,
		user_type,
		bstatistics,
		get_room_mode(),
		lose_rate,
		0,
		agent);

}

//写金币对局流水
void CTable::write_gold_versus(end_result result, int now_gold[MAX_TABLE_USER_COUNT])
{
	Json::Value va;
	Json::Value json_req_value;
	char szlist[10];
	int now_time = time(0);
	int tax = config_manager::GetInstance()->get_room_charge_gold(m_node_id);

	json_req_value["i_post_time"] = Json::Value(m_start_time);
	json_req_value["i_room_id"]   = Json::Value(g_server_id);
	json_req_value["i_desk_id"]  = Json::Value(m_table_id);
	json_req_value["i_players"]   = Json::Value(m_player_count);
	json_req_value["i_revenue"] = Json::Value(tax);
	json_req_value["i_duration"] = Json::Value(now_time-m_start_time); /* 本局耗时 */

	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{  
		IUser *user = get_user_from_chair(i);
		if (!user || user->get_real_status() != cmd_room::USER_STATUS_PLAY)
			continue;
		
		int user_now_gold = now_gold[i];
		int end_score = result.gold_result[i];

		//用户信息
		va["uid"] = Json::Value(m_game_users[i]);
		va["gold_before"] = Json::Value(user_now_gold-end_score);
		va["gold_win"] = Json::Value(end_score);
		va["gold_after"] = Json::Value(user_now_gold);
		va["room_cost"] = Json::Value(0);
		va["create_time"] = Json::Value(now_time);
		va["version"] = Json::Value(user->get_version());

		//输赢
		if (end_score < 0) 
			va["result"] = Json::Value(1);
		else 
			va["result"] = Json::Value(2);

		//倍数
		va["times"]  = Json::Value(0);
		mysprintf(szlist, sizeof(szlist), "player_%d", i);
		json_req_value[szlist] = Json::Value(va);
	}

	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	cmd_dbproxy::CDBProxyExceSection section;
	g_dbproxy_namager->post_exec(0, 0, 0, "write_gold_versus", json_req_data.c_str(), section);
}

//写玩法用户信息
void CTable::write_user_info(const char * data, int len)
{
	if (!data || data[0] == 0)
		return;

	if (get_room_mode() == TYPE_NOAML)
		return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, config_manager::GetInstance()->get_game_id(), "write_user_info", data, db_section, false);
}

//写玩法信息
void CTable::write_game_rule(const char * data, int len)
{
	if (!data || data[0] == 0)
		return;

	if (get_room_mode() == TYPE_NOAML)
		return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, config_manager::GetInstance()->get_game_id(), "write_game_rule", data, db_section, false);
}

//写战绩局数信息
void CTable::write_versus_relation(const char * data, int len)
{
	if (!data || data[0] == 0)
		return;

	if (get_room_mode() == TYPE_NOAML)
		return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, config_manager::GetInstance()->get_game_id(), "write_ins_versus_relation", data, db_section, false);
}

bool CTable::check_user_bankupt(int uid, bool is_check_tiyan, bool is_check_ready)
 {
	IUser *user = m_user_mgr->get_user(uid);
	if (user == NULL)
		return false;

	//if (!m_bankup_check)
		//return false;

	if (user->get_user_type() == ROBOT_TYPE)
		return false;

	int user_gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	if (user_gold != user->get_gold())
		user->set_gold(user_gold);

	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML && m_btiyan)
	{
		if (!is_check_tiyan)
			return false;
		
		if (user->get_score() >= TIYAN_CHECK_SCORE)
			return false;

	}
	else
	{
		int limit_gold = (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE ? 
			m_private_limit.min_limit : config_manager::GetInstance()->get_min_gold(m_node_id));

		if (is_check_ready && config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
			limit_gold = m_private_limit.leave_limit;

		//只有玩家钱小于0，那么就不能在房间
		if (user_gold > 0)
		{
			if (limit_gold == 0)
				return false;

			if (user_gold >= limit_gold && user_gold != 0)
				return false;
		}
	}
	return true;
}

bool CTable::check_all_robot()
{
	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user == NULL)
			continue;

		if (user->get_user_type() != ROBOT_TYPE)
			return false;
	}

	for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user == NULL)
			continue;

		robot_manager::GetInstance()->push_robot(m_game_users[i]);

		//修改用户状态,等待用户准备
		user->set_user_status(cmd_room::USER_STATUS_STANDUP);
		user->set_table_id(INVALID_TABLE);
		user->set_chair_id(INVALID_CHAIR);
		_free_chair(i);
	}
	
	MY_LOG_DEBUG("check_all_robot delete_table(%d)delete_table！",m_table_id);
	TableManager::GetInstance()->delete_table(this);
	return true;
}

void CTable::force_user_action(int chair_id, int action)
{
	IUser* puser = get_user_from_chair(chair_id);
	if (puser == NULL)
		return;

	switch(action)
	{
	case cmd_room::USER_ACTION_READY:
		on_user_action(puser, action);
		break;
	case cmd_room::USER_ACTION_SIT_DOWN:
		{
			puser->set_user_status(cmd_room::USER_STATUS_SIT_DOWN);
			broadcast_user_info(puser);
		}
		break;
	case cmd_room::USER_ACTION_STANDUP:
		{
			puser->set_user_status(cmd_room::USER_STATUS_STANDUP);
			broadcast_user_info(puser);
			puser->set_chair_id(INVALID_CHAIR);
			puser->set_table_id(INVALID_TABLE);
			_free_chair(chair_id);
			g_log->write_log(LOG_TYPE_DEBUG, "CTable force_user_action -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
		}
		break;
	default:
		break;
	}
}

bool CTable::on_end_game()
{
	MY_LOG_DEBUG(" 111 Table game end. tableid:%d robot_count:%d", m_table_id, get_robot_count());      
    
	m_game_com->on_game_end();

	//设置游戏结束时间
	m_end_game_time = time(NULL);

    const char * msg=NULL;
	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML ||
		config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
	{
		//用户破产校验
		//on_user_bankrupt();
		for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
		{
			//---用户信息校验---
			IUser *user = m_user_mgr->get_user(m_game_users[i]);
			MY_LOG_DEBUG("on_end_game.  uid:%lld", m_game_users[i]);   
			if (user)
			{
				//用户是否掉线
				bool is_kick  = false;
				if (user->get_end_clear_status())
				{
					is_kick = true;
				}

				//用户是否货币不足
				_uint64 gold = user->get_gold();
				MY_LOG_DEBUG("on_end_game.  tableid: %d uid:%lld gold:%llu min_gold:%d max_gold:%d", 
					m_table_id, m_game_users[i], gold, 
					config_manager::GetInstance()->get_min_gold(m_node_id), 
					config_manager::GetInstance()->get_max_gold(m_node_id));   

				//金币放校验金币上限
				if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML && user->get_user_type() != ROBOT_TYPE)
				{
					if (gold > config_manager::GetInstance()->get_max_gold(m_node_id) && config_manager::GetInstance()->get_max_gold(m_node_id) != 0)
					{
						//is_kick = true;
						msg = get_error_msg(MSG_ROOM_UPPER_GOLD_LIMIT, user->get_user_country());
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
					}
					else
					if (check_user_bankupt(m_game_users[i]))
					{
						//is_kick = true;
						msg = get_error_msg(MSG_ROOM_LOWER_GOLD_LIMIT, user->get_user_country());
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
					}
					else
					if (m_btiyan && user->get_score() <= TIYAN_CHECK_SCORE)
					{
						//is_kick = true;
						msg = get_error_msg(MSG_ROOM_LOWER_GOLD_LIMIT, user->get_user_country());
						CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
					}
				}

				//私人房校验下限踢出
				//掉线也直接踢出
				if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
				{
					if(user->is_offline())
					{
						is_kick = true;
					}
					else
					if(m_bankup_check)
					{
						if(gold < m_private_limit.leave_limit || gold == 0)
						{
							is_kick = true;
							msg = get_error_msg(MSG_ROOM_LOWER_GOLD_LIMIT, user->get_user_country());
							CGameFrame::GetInstance()->send_comm_message(m_game_users[i], cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
						}
					}
				}

				CClientCmd::do_update_user_vcoin(m_game_users[i], gold, 0, cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);
				if (is_kick && user->get_user_type() != ROBOT_TYPE)
				{
					m_game_com->on_user_action(user, comm_action_leave);
					user->not_offline();
					user->set_user_status(cmd_room::USER_STATUS_NULL);
					broadcast_user_info(user);
					
					if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
						CRoomRouteChallenge::send_challenge_user_leave(m_game_users[i],m_room_num, 0, 0);
					else
						CRoomRouteChallenge::send_nomal_user_leave(m_game_users[i]);
					
					m_user_mgr->delete_user(m_game_users[i]);
					_free_chair(i);
					g_log->write_log(LOG_TYPE_DEBUG, "CTable on_end_game -  tableid:%d m_player_count:%d",
						m_table_id, m_player_count);
					continue;
				}

				//房间变成维护状态，机器人玩了之后自动退出房间
				if (config_manager::GetInstance()->m_room_status == ROOM_STATUS_PAUSE && user->get_user_type() == ROBOT_TYPE)
				{
					m_game_com->on_user_action(user, comm_action_leave);
					user->not_offline();
					user->set_user_status(cmd_room::USER_STATUS_STANDUP);
					broadcast_user_info(user);
					robot_manager::GetInstance()->push_robot(m_game_users[i]);

					_free_chair(i);
					user->set_chair_id(INVALID_CHAIR);
					user->set_table_id(INVALID_TABLE);

					UserManager::GetInstance()->delete_user(user);
					//robot_manager::GetInstance()->release_one_free_robot(m_game_users[i]);
					continue;
				}

				if (user->get_user_type() == ROBOT_TYPE && !user->is_offline())
				{	
					bool check_conditoin = robot_manager::GetInstance()->check_robot_need_gold(m_game_users[i],
						config_manager::GetInstance()->get_min_gold(m_node_id),
						config_manager::GetInstance()->get_max_gold(m_node_id));

					bool is_need_tick = (m_btiyan && user->get_score() <= TIYAN_CHECK_SCORE);
					if (check_conditoin || is_need_tick)
					{
						m_game_com->on_user_action(user, comm_action_leave);
						user->not_offline();
						user->set_user_status(cmd_room::USER_STATUS_STANDUP);
						broadcast_user_info(user);
						robot_manager::GetInstance()->push_robot(m_game_users[i]);
				
						_free_chair(i);
						g_log->write_log(LOG_TYPE_DEBUG, "CTable on_end_game -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
						user->set_chair_id(INVALID_CHAIR);
						user->set_table_id(INVALID_TABLE);
						continue;
					}
				}

				if (user->is_offline())
				{
					if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
						CRoomRouteChallenge::send_challenge_user_leave(m_game_users[i], m_room_num, 0, 0);
					else
						CRoomRouteChallenge::send_nomal_user_leave(m_game_users[i]);

					user->not_offline();
					m_game_com->on_user_action(user, comm_action_leave);
					user->set_user_status(cmd_room::USER_STATUS_NULL);
					broadcast_user_info(user);

					_free_chair(user->get_chair_id());
					user->set_chair_id(INVALID_CHAIR);
					user->set_table_id(INVALID_TABLE);
					UserManager::GetInstance()->delete_user(user);
				}
				else
				{
					if (user->get_real_status() != cmd_room::USER_STATUS_READY)
					{
						user->set_user_status(cmd_room::USER_STATUS_SIT_DOWN);
						broadcast_user_info(user);
					}
				}
			}
		} 
	}
	else
	{
		// sit down status
		for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
		{
			IUser *user = m_user_mgr->get_user(m_game_users[i]);
			if (user)
			{
				MY_LOG_DEBUG("On table game end. uid=%u score=%d", user->get_user_id(), user->get_score());
				int now_gold = myredis::GetInstance()->get_data_by_uid(m_game_users[i], DB_COMM, CUR_GOLD);
				user->set_gold(now_gold);
				user->set_user_status(cmd_room::USER_STATUS_SIT_DOWN);
				broadcast_user_info(user);
			}
		}

		if (m_need_free)
		{
			MY_LOG_DEBUG("free table id %d", m_table_id);

			/* 清除桌子玩家 */
			for (_uint8 i = 0; i < m_max_player; i++)
			{
				m_user_mgr->delete_user(m_game_users[i]);
			}

			/* 回收桌子 */
			MY_LOG_DEBUG("回收桌子 delete_table(%d)delete_table！",m_table_id);
			TableManager::GetInstance()->delete_table(this);        
		} 
	}

	//所有旁观列表记录退出日志
	for (std::vector<IUser*>::iterator iter=ObserveManager::GetInstance()->get_observe_list()->begin();iter!=ObserveManager::GetInstance()->get_observe_list()->end();)
	{
		MY_LOG_DEBUG("send out card - userID:%u", (*iter)->get_user_id());
		iter++;
	}	

	//
	int time_len = config_manager::GetInstance()->m_delay_check_robot * 1000;
	g_timer_mgr->set_random_timer(this, on_robot_timer, time_len, 0, 1);

	//清空旁观列表
	m_playing = false;
	ObserveManager::GetInstance()->reset_user();
	MY_LOG_DEBUG(" 222 Table game end. table_id:%d robot_count:%d", m_table_id, get_robot_count());  
    return true;
}

void CTable::check_robot_leave()
{
	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML || config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
	{
		for (int i = 0; i < MAX_GAME_PLAY_USER_EX; i++)
		{
			IUser *user = m_user_mgr->get_user(m_game_users[i]);
			if (user == NULL || user->get_user_type() != ROBOT_TYPE)
				continue;

			if (user->get_real_status() >= cmd_room::USER_STATUS_PLAY)
				continue;
			
			MY_LOG_DEBUG(" check_robot_leave table_id:%d uid:%d status:%d", m_table_id, m_game_users[i], user->get_real_status());
			if (!user->is_offline())
			{
				bool check_conditoin = robot_manager::GetInstance()->check_robot_need_leave(m_game_users[i],
					config_manager::GetInstance()->get_min_gold(m_node_id),
					config_manager::GetInstance()->get_max_gold(m_node_id));

				bool is_need_tick = (m_btiyan && user->get_score() <= TIYAN_CHECK_SCORE);
				if (check_conditoin || is_need_tick)
				{
					m_game_com->on_user_action(user, comm_action_leave);
					user->not_offline();
					user->set_user_status(cmd_room::USER_STATUS_STANDUP);
					broadcast_user_info(user);
					robot_manager::GetInstance()->push_robot(m_game_users[i]);

					_free_chair(i);
					g_log->write_log(LOG_TYPE_DEBUG, "CTable on_end_game -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
					user->set_chair_id(INVALID_CHAIR);
					user->set_table_id(INVALID_TABLE);
					continue;
				}
			}
		}
	}
}

int CTable::on_forced_end(int uid)
{
	IUser *user = m_user_mgr->get_user(uid);
	if (user == NULL)
	{
		return 0;
	}

    MY_LOG_WARN("The table game is forced to end. %u", m_room_num);
    m_game_com->on_forced_end();

	//const char* tips = "您的账户异常, 强制退出房间";
	const char* tips = get_error_msg(MSG_ROOM_USER_ERROR, user->get_user_country());

	send_common_msg(user->get_chair_id(), tips, strlen(tips), cmd_room::ROOM_NOMAL_TIPS);

	user->not_offline();
	user->set_user_status(cmd_room::USER_STATUS_NULL);
	broadcast_user_info(user);

	
	if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
		CRoomRouteChallenge::send_challenge_user_leave(user->get_user_id(),m_room_num, 0, 0);
	else
		CRoomRouteChallenge::send_nomal_user_leave(user->get_user_id());

	_free_chair(user->get_chair_id());
	g_server->close(user->get_socket_id());
	m_user_mgr->delete_user(user->get_user_id());
	g_log->write_log(LOG_TYPE_DEBUG, "CTable on_forced_end -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
    return 0;
}

void CTable::on_user_game_info(IUser *pUser)
{
	if (m_game_com)
	{
		m_game_com->on_user_game_info(pUser);
	}
}

void CTable::broadcast_user_info(IUser *pUser, bool is_start)
{
    if (m_game_com)
    {
        m_game_com->broadcast_user_info(pUser, is_start);
    }
}

void CTable::broadcat_all_users_info()
{
	if (m_game_com)
	{
		m_game_com->send_all_users_info();
	}
}

bool CTable::is_free_status()
{
	if (m_game_com)
	{
		return m_game_com->is_free_scence();
	}

	return false;
}

int CTable::swap_chair(_uint8 chair_a, _uint8 chair_b)
{
    MY_LOG_DEBUG("Swap chair . a=%d b=%d", chair_a, chair_b);

    if (chair_a >= m_player_count || chair_b >= m_player_count)
    {
        MY_LOG_ERROR("Swap chair param is error. a=%d b=%d", chair_a, chair_b);
        return -1;
    }

    IUser *user_a = get_user_from_chair(chair_a);
    IUser *user_b = get_user_from_chair(chair_b);
    if (!user_a || !user_b)
    {
        MY_LOG_ERROR("chair user have null. a=0x%p b=0x%p", user_a, user_b);
        return -2;
    }

    _uint64 uid_tmp = m_game_users[chair_a];

    m_game_users[chair_a] = m_game_users[chair_b];
    m_game_users[chair_b] = uid_tmp;

    user_a->set_chair_id(chair_b);
    user_b->set_chair_id(chair_a);

    return 0;
}

void CTable::set_table_id(int table_id)
{
    m_table_id = table_id;
	g_log->write_log(LOG_TYPE_DEBUG, "set_table_id tableid:%d", table_id);
}



//int CTable::add_user(_tint64 userID)

int CTable::add_user(_tint64 userID, char chChairID)
{
	g_log->write_log(LOG_TYPE_DEBUG,"CTable add_user - uid:%llu  tableid:%d chairid:%d", userID, m_table_id, chChairID);

    if (m_player_count >= m_max_player)
    {
        return -1;
    }

    if (chChairID == -1)
    {
        for (int i = 0; i < m_max_player; i++)
        {
			g_log->write_log(LOG_TYPE_DEBUG, "CTable add_user now user uid:%llu  tableid:%d chairid:%d", m_game_users[i], m_table_id, i);
            if (m_game_users[i] == 0)
            {
                ++m_player_count;
                m_game_users[i] = userID;
				g_log->write_log(LOG_TYPE_DEBUG, "CTable add_user -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
				m_gpsLogic.InitUserLocation(userID);
                return i;
            }
        }
    }
    else
    {
        m_game_users[chChairID] = userID;
        m_player_count++;
		g_log->write_log(LOG_TYPE_DEBUG, "CTable add_user -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
        return chChairID;
    }

    MY_LOG_ERROR("Table game user is full.max=%d cur=%d", m_max_player, m_player_count);

    return -1;
}

int CTable::get_room_id()
{
    return CGameFrame::GetInstance()->get_room_id();
}

int CTable::get_player_count()
{
    return m_player_count;
}

int CTable::get_max_player()
{
    if (m_game_com)
    {
        return m_game_com->get_max_player();
    }

    return MAX_GAME_PLAY_USER;
}

int CTable::get_max_count()
{
    if (m_game_com)
    {
        return m_game_com->get_max_count();
    }

    return 0;
}

int CTable::get_play_count()
{
    if (m_game_com)
    {
        return m_game_com->get_play_count();
    }

    return 0;
}

int CTable::get_user_chair(_uint64 uid)
{
    for (int i = 0; i < m_max_player; i++)
    {
        if (m_game_users[i] == uid)
        {
            return i;
        }
    }

    return -1;
}

bool CTable::all_player_ready()
{
	if (m_ready_start && m_game_com)
	{
		return m_game_com->ready_for_start();
	}
	else
	{
		int game_user_count = m_max_player;
		if (m_game_com->is_starting())
		{
			game_user_count = m_player_count;
		}

		for (int i = 0; i < game_user_count; i++)
		{
			IUser *pUser = m_user_mgr->get_user(m_game_users[i]);
			if (!pUser || pUser->get_real_status() < cmd_room::USER_STATUS_READY)
			{
				return false;
			}
		}
	}
    return true;
}



void CTable::_free_chair(_uint8 chair_id)
{
	g_log->write_log(LOG_TYPE_DEBUG,"CTable _free_chair -  tableid:%d chairid:%d", m_table_id, chair_id);

    if (chair_id >= m_max_player)
        return;

	m_game_users[chair_id] = 0;
	m_player_count--;

	if (m_player_count < 0)
		m_player_count = 0;

	if (m_player_count == 0)
		m_node_id = 0;

	g_log->write_log(LOG_TYPE_DEBUG, "CTable _free_chair -  tableid:%d m_player_count:%d", m_table_id, m_player_count);
}

void CTable::on_time_out(_uint8 timer_id, _uint64 param)
{    
    if (timer_id <= MAX_GAME_COMM_TIMERID && m_game_com)
    {
        m_game_com->on_time(timer_id, param);
        return;
    }
}

FTYPE(void) CTable::on_timer(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{   
    CTable * pTable = (CTable*)obj;     
    if (pTable)
    {
        int table_id = TABLE_ID_FROM_TIMER_ID(timerID);
		_uint8 timer_id = LOGIC_ID_FROM_TIMER_ID(timerID);
		g_log->write_log(LOG_TYPE_INFO,"on_timer... -  tableid:%d timer_id:%d", table_id, timer_id);

        if (table_id != pTable->get_table_id())
        {
            MY_LOG_ERROR("Timer id is error. TimerID=0x%08X InTableID=%d TableID=%d", timerID, table_id, pTable->get_table_id());
            return;
        }
        pTable->on_time_out(timer_id, param);
    } 
}

FTYPE(void) CTable::on_robot_timer(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{
	CTable * pTable = (CTable*)obj;
	if (pTable)
	{
		pTable->check_robot_leave();
	}
}

void CTable::_reset_data()
{       
    m_puser = 0;
    m_recv_data = 0;
    m_recv_len = 0;     
}

int CTable::is_full()
{
    /* 已达最大人数 */
    if (m_player_count >= m_max_player)
    {
        return 1;
    }

    /* 游戏已经开始且不允许旁观 */
    //if (m_game_com->is_starting() )
    //{
       // return 2;
   // }

    return 0;
}


int CTable::on_user_enter(IUser *pUser, bool is_observe, bool is_change_table, bool is_create, bool is_quick)
{   
	MY_LOG_DEBUG("on_user_enter Table[%d] playing_method %d is_observe:%d", m_table_id, m_nPlayMethod, is_observe);
    if (!pUser)
        return -1;    

	if(is_observe)
	{
		m_game_com->enter_succ(pUser, is_change_table);
		MY_LOG_DEBUG("observe enter_succ Table[%d] userid:%u", m_table_id, pUser->get_user_id());
		return 0;
	}

	if (pUser->get_user_type() == ROBOT_TYPE)
	{
		robot_manager::GetInstance()->check_robot_gold(pUser->get_user_id(), 
			config_manager::GetInstance()->get_min_gold(m_node_id),
			config_manager::GetInstance()->get_max_gold(m_node_id), m_node_id);
	}

	if ( (config_manager::GetInstance()->get_room_type() != TYPE_CHALLENGE && 
		config_manager::GetInstance()->get_room_type() != TYPE_MATCH &&
		pUser->get_real_status() != cmd_room::USER_STATUS_PLAY &&
		pUser->get_real_status() != cmd_room::USER_STATUS_STANDUP &&
		pUser->get_real_status() != cmd_room::USER_STATUS_SIT_DOWN &&
		pUser->get_real_status() != cmd_room::USER_STATUS_READY) || is_change_table)
	{
		bool is_check_tiyan = (is_change_table ? true : false);
		if (check_user_bankupt(pUser->get_user_id(), is_check_tiyan))
		{
			cmd_room::UserActionResult result;
			result.set_Result(1);
			result.set_Action(cmd_room::USER_ACTION_SIT_DOWN);
			send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_USER_ACTION_RESULT, &result);
			return -1;
		}
	}

	//int lose_user[3] = { 125380 };
	//write_gold_bill(125390,100, 0, 0, "1.0.0", GOLD_DEL_TAX, lose_user, 1, 250);

	//agent_fun::GetInstance()->agent_gold_insert_mysql(pUser->get_user_id(), 100, TYPE_NOAML, 0, 0, 4110);

	/* 查询用户是否在桌子上 */
    int chair_id = get_user_chair(pUser->get_user_id());    
    int is_first_enter = false;

    if (chair_id >= 0)
    {   
        // in table
        on_user_reconn(pUser);

		/* 广播信息给其他人（包括自己） */
		broadcast_user_info(pUser);    
		return 0;
    }

	if(config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
	{
		_uint64 gold = pUser->get_gold();
		MY_LOG_DEBUG("on_user_enter check user gold Table[%d] userid:%u  min_gold:%d  max_gold:%d", 
			m_table_id, pUser->get_user_id(), config_manager::GetInstance()->get_min_gold(m_node_id), 
			config_manager::GetInstance()->get_max_gold(m_node_id));

		if (config_manager::GetInstance()->get_min_gold(m_node_id) != 0)
		{
			if (gold <  config_manager::GetInstance()->get_min_gold(m_node_id) || gold == 0)
			{
				return -1;
			}
		}
		if (gold > config_manager::GetInstance()->get_max_gold(m_node_id) && config_manager::GetInstance()->get_max_gold(m_node_id) != 0)
		{
			return -1;
		}
	}
    
	if (is_full())
	{
		MY_LOG_ERROR("Table user is full. player_count=%d max_user=%d ", m_player_count, m_max_player);
		return cmd_room::ROOM_ENTER_ROOM_FULL_LIMIT;
	}

	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
	{
		if (!m_game_com->is_free_scence())
		{
			MY_LOG_INFO("Table is not free scence.");
			return cmd_room::ROOM_IS_NOT_FREE;
		}
	}

    int new_chair_id = add_user(pUser->get_user_id());
    if (new_chair_id < 0)
    {
        MY_LOG_ERROR("Add user fail. %d", new_chair_id);
        return cmd_room::ROOM_ENTER_ROOM_FULL_LIMIT;
    }

    pUser->set_chair_id(new_chair_id);
	pUser->set_table_id(m_table_id);
	pUser->set_node_id(m_node_id);
	pUser->set_user_status(cmd_room::USER_STATUS_SIT_DOWN);  
	myredis::GetInstance()->set_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_ROOM_NODE, m_node_id);

	int ret = m_game_com->on_user_action(pUser, comm_action_sit_down);
	if (ret)
	{
		_free_chair(new_chair_id);
		MY_LOG_ERROR("sit user fail. ret=%d", ret);
		return ret;
	}
    m_game_com->enter_succ(pUser, is_change_table);
	if (pUser->get_user_type() == ROBOT_TYPE)
	{
		//为机器人添加虚拟UID，头像，昵称
		int vir_uid = UserManager::GetInstance()->get_vir_uid();
		pUser->set_vir_uid(vir_uid);
		pUser->set_vir_level();
		stRobotVirInfo info;
		if (config_manager::GetInstance()->pop_robot_vir_info(info))
		{
			pUser->set_head_image(info.face);
			pUser->set_nick_name(info.name);
			pUser->set_have_virinfo(true);
		}
		MY_LOG_DEBUG("Table set robot vir id:%d name:%s face:%s", vir_uid, info.name, info.face);
	}

	is_first_enter = true;
	broadcast_user_info(pUser);

	//robot_manager::GetInstance()->check_delete_robot(this);
	//robot_manager::GetInstance()->add_all_robot_table();

	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE ||
		config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
	{
		CRoomRouteChallenge::send_challenge_user_enter(pUser->get_user_id(),
			pUser->get_nick_name(),m_room_num, pUser->get_client_id(), pUser->get_head_img());
		
		//私人房自动准备
		if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE && 
			pUser->get_user_id() == m_owner && is_create)
			on_user_action(pUser, cmd_room::USER_ACTION_READY);
	}

	//全机器人桌防护
	//满人，并且全是机器人，解散桌子
	if (pUser->get_user_type() == ROBOT_TYPE)
	{
		if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML &&
			get_max_player() == get_player_count() &&
			get_real_count() == 0)
		{
			for (int i = 0; i < get_max_player(); i++)
				tick_user(i);

			return 0;
		}
	}

	if(config_manager::GetInstance()->get_room_type() == TYPE_MATCH)
	{
		//切磋场自动准备
		on_user_action(pUser, cmd_room::USER_ACTION_READY);
	}
	else
	{
		if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML &&
			is_quick)
		{
			//速配情况下，自动准备
			on_user_action(pUser, cmd_room::USER_ACTION_READY);
		}
	}
    return 0;
}

int CTable::on_user_reconn(IUser *pUser)
{
    MY_LOG_DEBUG("User reconn. uid=%u ChairID=%d", pUser->get_user_id(), pUser->get_chair_id());

    /* 重回 */    
    if (!pUser->is_offline())
    {
        MY_LOG_WARN("User multi reconn. uid=%u status=%d", pUser->get_user_id(), pUser->get_user_status());        
    }    
    pUser->not_offline();
	pUser->not_temp_leave();
	pUser->set_end_clear_status(false);
    
    m_game_com->enter_succ(pUser, false, true);       /* 游戏场景 */
    m_game_com->on_user_action(pUser, comm_action_online);

	send_gps_info();
    return 0;
}

//发送GPS定位信息
void CTable::send_gps_info()
{
	cmd_room::UserDistanceInfo outDistanceInfo;
	outDistanceInfo.set_NoGPSUidList_item_count(0);
	outDistanceInfo.set_DistanceList_item_count(0);
	m_gpsLogic.CalculateUserDistanceInfo(outDistanceInfo,true);
	send_user_batch(cmd_net::CMD_ROOM, cmd_net::SUB_GPS_SENT_DOWN, &outDistanceInfo);
}

int CTable::on_user_action(IUser *pUser, _uint8 type, bool is_patch)
{
    if (!pUser)
        return -1;
	
    int ret = 0;
	MY_LOG_DEBUG("on_user_action... uid=%u  type=%d", pUser->get_user_id(), type);
    if (type == cmd_room::USER_ACTION_READY)
    {
		if (config_manager::GetInstance()->get_room_type() != TYPE_CHALLENGE && 
			config_manager::GetInstance()->get_room_type() != TYPE_MATCH &&
			config_manager::GetInstance()->get_room_type() != TYPE_FMATCH)
		{
 			if (check_user_bankupt(pUser->get_user_id(), false, true))
			{
				cmd_room::UserActionResult result;
				result.set_Result(1);
				result.set_Action(cmd_room::USER_ACTION_READY);
				send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_USER_ACTION_RESULT, &result);
				return -1;
			}
		}

		if (config_manager::GetInstance()->get_patch_full() == 1)
		{
			if (pUser->get_user_status() <= cmd_room::USER_STATUS_READY)
			{
				//配置模式下，手动准备无效
				if (!is_patch && config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
					return 0;
			}
		}

		//切磋场和比赛场可自动准备
        if (pUser->get_user_status() <= cmd_room::USER_STATUS_READY || 
			config_manager::GetInstance()->get_room_type() == TYPE_MATCH ||
			config_manager::GetInstance()->get_room_type() == TYPE_FMATCH)  
        {   
			pUser->set_user_status(cmd_room::USER_STATUS_READY);
			broadcast_user_info(pUser);

            ret = m_game_com->on_user_action(pUser, comm_action_ready);
            if (ret)
            {
                MY_LOG_ERROR("On user game action is error. %d", ret);
                return -1;
            }

			if (m_owner == pUser->get_user_id())
			{
				if (all_player_ready())
				{
					on_start_game();
				}
			}            
			else
			{
				if (all_player_ready() )
				{
					on_start_game();
				}
			}
        }
    }
	else if (type == cmd_room::USER_TEMP_LEAVE)
	{
		if(!pUser->is_temp_leave())
		{
			pUser->set_user_status(cmd_room::USER_STATUS_TEMP_LEAVE);
			broadcast_user_info(pUser);
			m_game_com->on_user_action(pUser, comm_action_temp_leave);
		}
	}
	else if (type == cmd_room::USER_COME_BACK)
	{
		if(pUser->is_temp_leave())
		{
			pUser->not_temp_leave();
			broadcast_user_info(pUser);
			m_game_com->on_user_action(pUser, comm_action_come_back);
		}
	}

    return 0;
}

int CTable::on_user_stanup(IUser *pUser)
{
	if (pUser->get_real_status() == cmd_room::USER_STATUS_PLAY)
	{
		return -1;
	}

	if (pUser->get_chair_id() == INVALID_CHAIR)
	{
		return 0;
	}

	m_game_com->on_user_action(pUser, comm_action_leave);

	pUser->set_user_status(cmd_room::USER_STATUS_STANDUP);
	broadcast_user_info(pUser);
	_free_chair(pUser->get_chair_id());
	pUser->set_chair_id(INVALID_CHAIR);
	pUser->set_table_id(INVALID_TABLE);
	return 0;
}

int CTable::on_user_leave(IUser *pUser)
{
    if (!pUser)
        return 1;

	//约战开始了不允许退出
	if(config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
	{
		if(m_playing)
			return 1;

		int iRet = m_game_com->on_user_action(pUser, comm_action_leave);
		if (iRet == 0)
		{
			int now_gold = myredis::GetInstance()->get_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_GOLD);
			pUser->set_gold(now_gold);
			pUser->set_user_status(cmd_room::USER_STATUS_NULL);        
			broadcast_user_info(pUser);

			_free_chair(pUser->get_chair_id());
			pUser->set_chair_id(INVALID_CHAIR);
			pUser->set_table_id(INVALID_TABLE);
		
			m_gpsLogic.RemoveLocation(pUser->get_user_id());
			cmd_room::UserDistanceInfo outDistanceInfo;
			m_gpsLogic.CalculateUserDistanceInfo(outDistanceInfo);
			return 0;
		}

		on_user_offline(pUser);
	}
	else if(config_manager::GetInstance()->get_room_type() == TYPE_MATCH)
	{
		//切磋场开始不允许退出
		if(m_playing)
			return 1;

		pUser->set_user_status(cmd_room::USER_STATUS_NULL);        
		broadcast_user_info(pUser);

		_free_chair(pUser->get_chair_id());
		pUser->set_chair_id(INVALID_CHAIR);
		pUser->set_table_id(INVALID_TABLE);

		const char *msg=NULL;
		if ( m_cost_card > 0)
		{
			char tips[128] = { 0 };
			msg = get_error_msg(MSG_ROOM_QIECUO_RETURN_DIAMOND, pUser->get_user_country());
			sprintf(tips, msg, m_cost_card);
			CGameFrame::GetInstance()->send_comm_message(pUser->get_user_id(), cmd_room::ROOM_QIECUO_NOPLAY_DISMISSION, tips);

			//返回报名费				
			CClientCmd::do_add_room_card_dbproxy(pUser->get_user_id(), m_room_num, m_cost_card, QIECUO_RUTURN_SIGNUP_BILL);
		}
		return 0;
	}
    else
	{
		on_user_offline(pUser, true);
	}
    return 1;
}

int CTable::on_user_offline(IUser *pUser, bool is_leave)
{
	if (!pUser)
		return -1;

	MY_LOG_DEBUG("User offline. uid=%u ChairID=%d", pUser->get_user_id(), pUser->get_chair_id());
	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
	{
		if (pUser->is_offline())
			return 0;

		pUser->set_user_status(cmd_room::USER_STATUS_OFFLINE);
		broadcast_user_info(pUser);

		m_game_com->on_user_action(pUser, comm_action_offline);
	}
	else if (config_manager::GetInstance()->get_room_type() == TYPE_FMATCH)
	{
		MY_LOG_DEBUG("CTable::on_user_offline... uid[%d]", pUser->get_user_id());
		if (pUser->is_offline())
			return 0;

		if (pUser->get_real_status() == cmd_room::USER_STATUS_PLAY)
		{
			pUser->set_user_status(cmd_room::USER_STATUS_OFFLINE);
			m_game_com->on_user_action(pUser, comm_action_offline);
			broadcast_user_info(pUser);
		}
	}
	else
	{
		//切磋场，非离开掉线不处理
		if(config_manager::GetInstance()->get_room_type() == TYPE_MATCH)
			return 0;

		if (pUser->get_real_status() != cmd_room::USER_STATUS_PLAY)
		{
			//用户强制离开
			//私人房只要掉线，就直接离开
			//金币房钱不够，也离开
			bool is_limit_gold = false;
			if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
			{
				int limit_gold = config_manager::GetInstance()->get_min_gold(m_node_id);
				if (pUser->get_gold() < limit_gold)
					is_limit_gold = true;
			}
			//if (is_leave || config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE || is_limit_gold)
			{
				if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
				{
					CRoomRouteChallenge::send_challenge_user_leave(pUser->get_user_id(),m_room_num, 0, 0);
				}
				else
				{
					CRoomRouteChallenge::send_nomal_user_leave(pUser->get_user_id());
				}

				//不是打牌状态，直接清除
				m_game_com->on_user_action(pUser, comm_action_leave);
				pUser->set_user_status(cmd_room::USER_STATUS_NULL);      
				broadcast_user_info(pUser);

				_free_chair(pUser->get_chair_id());
				pUser->set_chair_id(INVALID_CHAIR);
				pUser->set_table_id(INVALID_TABLE);

				//if (pUser->get_user_type() == ROBOT_TYPE)
				//{
					//robot_manager::GetInstance()->push_robot(pUser->get_user_id());
				//}
				//else
				{
					UserManager::GetInstance()->delete_user(pUser);
				}
			}
			/*else
			{
				m_game_com->on_user_action(pUser, comm_action_stand_up);
				pUser->set_user_status(cmd_room::USER_STATUS_STANDUP);      
				broadcast_user_info(pUser);

				if(get_room_mode() != TYPE_MATCH)
				{
					_free_chair(pUser->get_chair_id());
					pUser->set_chair_id(INVALID_CHAIR);
					pUser->set_table_id(INVALID_TABLE);
				}

				if (pUser->get_user_type() == ROBOT_TYPE)
				{
					robot_manager::GetInstance()->push_robot(pUser->get_user_id());
				}
			}*/
		}
		else
		{
			if (pUser->is_offline())
				return 0;

			pUser->set_end_clear_status(is_leave);
			if (is_leave)
			{
				m_game_com->on_user_action(pUser, comm_action_leave);
			}
			else
			{
				m_game_com->on_user_action(pUser, comm_action_offline);
			}
			pUser->set_user_status(cmd_room::USER_STATUS_OFFLINE);
			broadcast_user_info(pUser);
		}
	}
	return 0;
}

void CTable::write_user_roominfo(IUser *puser, int type)
{
	if (!puser || puser->get_user_type() == ROBOT_TYPE)
		return;

	Json::Value json_req_value;
	if (type == RBT_LOGON_IN)
	{
		json_req_value["uid"] = Json::Value(puser->get_user_id());
		json_req_value["room_id"] = Json::Value(get_room_id());
		json_req_value["room_name"] = Json::Value(get_node_name());
		json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
	}
	else
	{
		json_req_value["uid"] = Json::Value(puser->get_user_id());
		json_req_value["room_id"] = Json::Value(0);
		json_req_value["room_name"] = Json::Value("");
		json_req_value["game_id"] = Json::Value(0);
	}
	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);

	MY_LOG_DEBUG("write_user_roominfo uid:%d, table json_data: %s", puser->get_user_id(), json_req_data.c_str());
	g_dbproxy_namager->post_exec(0, 0, puser->get_user_id(), "update_room_info", json_req_data.c_str(), db_section, false);
}

void CTable::write_versus_bill(const char * data, int len)
{
	if(!data || len == 0) return;

    cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, m_table_id, "mjq_write_versus_bill", data, db_section, false);
}

//投递投付结果
bool CTable::on_post_transfer_inout(transfer_inout_result_info inout_result)
{
	m_game_com->transfer_inout_result(inout_result);
	return true;
}

//写三方投付
bool CTable::write_transfer_inout(transfer_inout inout)
{
	inout.tableid = m_table_id;
	return room_bill_fun::GetInstance()->write_transfer_inout(inout);
}

void CTable::write_video_room(const char* data)
{
    if (!data || data[0] == 0)
        return;

	if (get_room_mode() == TYPE_NOAML)
		return;
	
    cmd_dbproxy::CDBProxyExceSection db_section;
    g_dbproxy_namager->post_exec(0, 0, config_manager::GetInstance()->get_game_id(), "mjq_write_video_room", data, db_section, false);
}

void CTable::write_video_bill(const char *data)
{
    if (!data || data[0] == 0)
        return;

	if (get_room_mode() == TYPE_NOAML)
		return;

    cmd_dbproxy::CDBProxyExceSection db_section;
    g_dbproxy_namager->post_exec(0, 0, config_manager::GetInstance()->get_game_id(), "mjq_write_video_bill", data, db_section, false);
}

int CTable::get_pay_mode()
{
	if ( m_game_com )
	{
		return m_game_com->get_pay_mode();
	}

	return 0;
}

void CTable::play_game_end(MAPSCORE &inMapScore)
{
	/* 通知约战 */
	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
	{
		CRoomRouteChallenge::send_challenge_play_game_end(m_owner, m_room_num ,get_max_count(), get_play_count(), get_max_player(), inMapScore);
	}
}

void CTable::send_common_msg(_uint8 chair_id, const char* msg, int len, int type, int tips_type)
{
	CGameFrame::GetInstance()->send_comm_message(m_game_users[chair_id], type, msg, tips_type);
}

void CTable::send_direct_tips(_uint8 chairid)
{
	MY_LOG_DEBUG("=====send_direct_tips chairid===%d======", chairid);
	IUser *user = m_user_mgr->get_user(m_game_users[chairid]);
	if (user == NULL || user->get_user_type() == ROBOT_TYPE)
		return;

	send(chairid, cmd_net::CMD_ROOM, cmd_net::SUB_DIRECT_TIPS, NULL);
}

void CTable::game_db_push_data(const char* type, const char *data, int len)
{
	MY_LOG_DEBUG("=====game_db_push_data %s  === %s======",type,data);

	if(!data || len == 0) return;

	cmd_dbproxy::CDBProxyExceSection db_section;
	g_dbproxy_namager->post_exec(0, 0, m_table_id, type, data, db_section, false);
}

int CTable::get_robot_count()
{
	int count = 0;
	for (int i = 0; i < m_max_player; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user == NULL)
			continue;

		if (user->get_user_type() == ROBOT_TYPE)
			count++;
	}
	return count;
}

int CTable::get_real_count()
{
	int count = 0;
	for (int i = 0; i < m_max_player; i++)
	{
		IUser *user = m_user_mgr->get_user(m_game_users[i]);
		if (user == NULL)
			continue;

		if (user->get_user_type() != ROBOT_TYPE)
			count++;
	}
	return count;
}

void CTable::print_table_info()
{
	MY_LOG_DEBUG("print_table_info...tableid:%d----uid0:%d uid1:%d uid2:%d uid3:%d uid4:%d", m_table_id, 
		m_game_users[0], m_game_users[1], m_game_users[2], m_game_users[3], m_game_users[4]);
}

void CTable::write_room_gold_statistic(int gold, int type_id)
{
	MY_LOG_DEBUG("write_room_gold_statistic...  gold(%d), type_id(%d)", gold, type_id);

	if (m_node_id == 0)
	{
		return;
	}
	BILL->room_gold_statistics(
		0
		, 0
		, config_manager::GetInstance()->get_game_id()
		, m_node_id
		, gold
		, type_id
		);
}

const char* CTable::get_game_web_rule(int nodeid)
{
	return config_manager::GetInstance()->get_game_rule(nodeid);
}

void CTable::write_normal_room_bet_gold_bill(IUser * pUser, int gold, int type_id, int lose_rate)
{
	if(!pUser)
		return;
	bool bstatistics = pUser->get_user_type() != ROBOT_TYPE ? true : false;
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		pUser->get_client_id(), 
		m_room_num, 
		pUser->get_version(), 
		pUser->get_user_id(), 
		pUser->get_gold()+gold,
		gold, 
		pUser->get_gold(), 
		(GOLD_BILL_TYPE)type_id,
		m_node_id,
		pUser->get_user_type(),
		bstatistics,
		get_room_mode(),
		lose_rate);

}

int CTable::on_user_change_table_standup(IUser *pUser)
{
	if (pUser->get_real_status() == cmd_room::USER_STATUS_PLAY)
	{
		return -1;
	}

	if (pUser->get_chair_id() == INVALID_CHAIR)
	{
		return 0;
	}

	m_game_com->on_user_action(pUser, comm_action_leave);

	pUser->set_user_status(cmd_room::USER_STATUS_STANDUP);
	broadcast_user_info(pUser);
	_free_chair(pUser->get_chair_id());
	pUser->set_chair_id(INVALID_CHAIR);
	pUser->set_table_id(INVALID_TABLE);
	return 0;
}

int CTable::get_next_id(int nId)
{
    if (nId < 0 || nId > 5) return 0;
	
    return (nId + 1) % 5;
}

void CTable::update_game_config(const char *op_type, _tint64 data)
{
    if (op_type == NULL)
    {
        MY_LOG_ERROR("CTable::update_game_config error op_type:%s, data:%d .", op_type, data);
		return;
    }
	
    myredis::GetInstance()->set_game_config_for_room(op_type, data);
	MY_LOG_DEBUG("CTable::update_game_config   op_type:%s, data:%d .", op_type, data);
}

_uint64 CTable::get_game_config_special(const char *op_type)
{
    if (op_type == NULL)
    {
        MY_LOG_ERROR("CTable::get_game_config_special error op_type:%s .", op_type);
		return 0;
    }

	_uint64 result = myredis::GetInstance()->get_game_config_for_room(op_type);
	MY_LOG_DEBUG("CTable::get_game_config_special   op_type:%s, data:%lld .", op_type, result);
	
	return result;
}

void CTable::reset_game_rule(string rule)
{
    if (m_game_com != NULL)
	    m_game_com->reset_game_rule(rule.c_str(), rule.length());
} 

int CTable::get_curr_room_status()
{
    if (config_manager::GetInstance()->m_room_status == ROOM_STATUS_PAUSE)
	{
		MY_LOG_ERROR("CTable::get_curr_room_status room[%d]处于维护状态", CGameFrame::GetInstance()->get_room_id());
		return 1;
	}

	return 0;
}

//强制解散桌子
void CTable::force_free_table()
{
	m_game_com->on_forced_end();

	for (_uint8 i = 0; i < m_max_player; i++)
	{
		IUser* pUser = get_user_from_uid(m_game_users[i]);

		if (pUser && ROBOT_TYPE == pUser->get_user_type())
			robot_manager::GetInstance()->push_robot(m_game_users[i]);

		m_user_mgr->delete_user(m_game_users[i]);
	}

	TableManager::GetInstance()->delete_table(this);
}

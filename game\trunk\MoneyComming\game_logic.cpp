#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
using json = nlohmann::json;
user_game_info::user_game_info()
{
    uid = 0;
    time = 0;
    result = 0;
    pay_out = 0;
    _last_kick_user_offline_tm_sec = 0;
    user_type = 2;
    client_id = 0;
	sub_client_id = 0;
    web_token = "";
    token = "";
    reset();
}

game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

bool game_logic::gen_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();
    int i = 0;
    for(auto& d:data[0])
    {
        game_info.normal_data[i++] = d;
    }
    if(i < 5)
    {
        return false;
    }
    i = 0;
    if(data.size() > 1)
    {
        for (auto &d : data[1])
        {
            game_info.respin_data[i++] = d;
        }
        game_info.hasRespin = true;
    }
    int valueConfig[3] = {1, 5, 10};
    int multerConfig[3] = {2, 5, 10};
    int v1 = 0,v2 = -1,v3 = -1;

    int tmp = game_info.normal_data[0];
    if(tmp >= BOX_1) 
    {
		v1 = valueConfig[tmp-BOX_1];
    }

    tmp = game_info.normal_data[1];
    if(tmp >= BOX_0) 
    {
        if(tmp == BOX_0 || tmp == BOX_00)
            v2 = 0;
        else
		    v2 = valueConfig[tmp-BOX_1];
    }

    tmp = game_info.normal_data[2];
    if(tmp >= BOX_0) 
    {
        if(tmp == BOX_0 || tmp == BOX_00)
            v3 = 0;
        else
		    v3 = valueConfig[tmp-BOX_1];
    }

    if(v3 < 0)
    {
        if(v2>=0)
        {
            tmp = game_info.normal_data[1];
            if(tmp == BOX_00 || tmp == BOX_10)
            {
                game_info.normal_platewin = v1*100 + v2;
            }
            else
            {
                game_info.normal_platewin = v1*10 + v2;
            }           
        }
        else
        {
            game_info.normal_platewin = v1;
        }
    }
    else
    {
        if (v2 >= 0)
        {
            tmp = game_info.normal_data[1];
            if(tmp == BOX_00 || tmp == BOX_10)
            {
                tmp = game_info.normal_data[2];
                if(tmp == BOX_00 || tmp == BOX_10)
                {
                    game_info.normal_platewin = v1 * 10000 + v2 * 100 + v3;
                }
                else
                {
                    game_info.normal_platewin = v1 * 1000 + v2 * 10 + v3;
                }
            }
            else
            {
                tmp = game_info.normal_data[2];
                if(tmp == BOX_00 || tmp == BOX_10)
                {
                    game_info.normal_platewin = v1 * 1000 + v2 * 100 + v3;
                }
                else
                {
                    game_info.normal_platewin = v1 * 100 + v2 * 10 + v3;
                }
            }
        }
        else
        {
            tmp = game_info.normal_data[2];
            if(tmp == BOX_00 || tmp == BOX_10)
            {
                game_info.normal_platewin = v1*100 + v3;
            }
            else
            {
                game_info.normal_platewin = v1*10 + v3;
            }
        }
    }
    tmp = game_info.normal_data[3];

    if (tmp > BOX_ALL)
    {
        game_info.normal_platewin *= multerConfig[tmp - BOX_X2];
    }

    if(game_info.hasRespin)
    {
        v1 = 0;
        v2 = -1;
        v3 = -1;

        tmp = game_info.respin_data[0];
        if (tmp >= BOX_1)
        {
            v1 = valueConfig[tmp - BOX_1];
        }

        tmp = game_info.respin_data[1];
        if (tmp >= BOX_0)
        {
            if (tmp == BOX_0 || tmp == BOX_00)
                v2 = 0;
            else
                v2 = valueConfig[tmp - BOX_1];
        }

        tmp = game_info.respin_data[2];
        if (tmp >= BOX_0)
        {
            if (tmp == BOX_0 || tmp == BOX_00)
                v3 = 0;
            else
                v3 = valueConfig[tmp - BOX_1];
        }

        if (v3 < 0)
        {
            if (v2 >= 0)
            {
                tmp = game_info.respin_data[1];
                if (tmp == BOX_00 || tmp == BOX_10)
                {                    
                    game_info.respin_platewin = v1 * 100 + v2;
                }
                else
                {
                    game_info.respin_platewin = v1 * 10 + v2;
                }
            }
            else
            {
                game_info.respin_platewin = v1;
            }
        }
        else
        {
            if (v2 >= 0)
            {
                tmp = game_info.respin_data[1];
                if (tmp == BOX_00 || tmp == BOX_10)
                {
                    tmp = game_info.respin_data[2];
                    if (tmp == BOX_00 || tmp == BOX_10)
                    {
                        game_info.respin_platewin = v1 * 10000 + v2 * 100 + v3;
                    }
                    else
                    {
                        game_info.respin_platewin = v1 * 1000 + v2 * 10 + v3;
                    }
                }
                else
                {
                    tmp = game_info.respin_data[2];
                    if (tmp == BOX_00 || tmp == BOX_10)
                    {
                        game_info.respin_platewin = v1 * 1000 + v2 * 100 + v3;
                    }
                    else
                    {
                        game_info.respin_platewin = v1 * 100 + v2 * 10 + v3;
                    }
                }
            }
            else
            {
                tmp = game_info.respin_data[2];
                if (tmp == BOX_00 || tmp == BOX_10)
                {
                    game_info.respin_platewin = v1 * 100 + v3;
                }
                else
                {
                    game_info.respin_platewin = v1 * 10 + v3;
                }
            }
        }
    }
    if(game_info.normal_data[3] == BOX_SCATTER)
    {
        game_info.wheel_win = game_info.normal_data[4];
    }
    // MY_LOG_PRINT("game_info.normal_platewin = %d", game_info.normal_platewin);
    // MY_LOG_PRINT("game_info.respin_platewin = %d", game_info.respin_platewin);
    // MY_LOG_PRINT("game_info.wheel_win = %d", game_info.wheel_win);

    return true;
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::check_card(user_game_info &game_info)
{
   
}

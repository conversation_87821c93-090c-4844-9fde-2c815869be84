﻿/***********************************************************
 * File Name          :       online_running.h
 * Author             :       
 * Version            :       1.0
 * Date               :      
 * Description        :       在线流水
***********************************************************/

#ifndef _ONLINE_RUNNING_H_
#define _ONLINE_RUNNING_H_

#include "room_common.h"

class COnlineRunning
{
public:
	static COnlineRunning *GetInstance()
	{
		static COnlineRunning stOnlineRunning;
		return &stOnlineRunning;
	}

private:
    COnlineRunning();
    virtual ~COnlineRunning();

public:
    virtual void start();

private:
    virtual void db_online_running_req();
	virtual void db_node_online_running_req();
	virtual void db_sys_online_running_req();

    static FTYPE(void) on_online_timer(void*obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);

	virtual void db_match_running_req(int match_type);
};

#endif //_ONLINE_RUNNING_H_

#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
using json = nlohmann::json;
user_game_info::user_game_info()
{
    uid = 0;
    time = 0;
    result = 0;
    pay_out = 0;
    _last_kick_user_offline_tm_sec = 0;
    user_type = 2;
    client_id = 0;
	sub_client_id = 0;
    web_token = "";
    token = "";
    cur_mode = 0;
    is_free = false;
    base_bet = 0;
    reset();
}

game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}


card game_logic::get_rand_card(user_game_info &gameinfo)
{
    int weight[8] = {1, 1, 1, 1, 2, 2, 2, 2};
    int total = 12;
    int size = 8;

    card c;
    int r = getRand(0, total - 1);
    for (int i = 0; i < size; i++)
    {
        if (r < weight[i])
        {
            c.value = i + 2;
            //MY_LOG_PRINT("c.value = %d", c.value);
            break;
        }
        else
        {
            r -= weight[i];
        }
    }

    if (getRand(1, 100) < 10)
    {
        c.is_gold = 1;
    }

    return c;
}

card game_logic::get_rand_card_diff(const map<int, int>& map_card_count)
{
    int weight[8] = {1, 1, 1, 1, 2, 2, 2, 2};
    int total = 12;
    int size = 8;

    for(auto& m:map_card_count)
    {
        total-=weight[m.first-2];
        weight[m.first-2] = 0;
    }
    card c;
    int r = getRand(0, total - 1);
    for (int i = 0; i < size; i++)
    {
        if (r < weight[i])
        {
            c.value = i + 2;
            //MY_LOG_PRINT("c.value = %d", c.value);
            break;
        }
        else
        {
            r -= weight[i];
        }
    }

    if (getRand(1, 100) < 10)
    {
        c.is_gold = 1;
    }
    return c;
}

void game_logic::gen_no_reward_game_data(user_game_info &game_info)
{
    map<int, int> map_card_count;
    //第一列
    for (int i = 0; i < MAX_ROW; i++)
    {
        card c = get_rand_card(game_info);
        c.is_gold = 0;
        game_info.vec_now_box[0].push_back(c);
        map_card_count[c.value]++;
    }
    int r = getRand(1,100);
    bool bDiff2 = false;
    //第二列
    if(r < 50)
    {
        bDiff2 = true;
    }
    if(bDiff2)
    {
        for (int i = 0; i < MAX_ROW; i++)
        {
            card c = get_rand_card_diff(map_card_count);
            game_info.vec_now_box[1].push_back(c);
        }
    }
    else
    {
        for (int i = 0; i < MAX_ROW; i++)
        {
            card c = get_rand_card(game_info);
            game_info.vec_now_box[1].push_back(c);
        }
    }

    //第三列
    if (!bDiff2)
    {
        for (int i = 0; i < MAX_ROW; i++)
        {
            card c = get_rand_card_diff(map_card_count);
            game_info.vec_now_box[2].push_back(c);
        }
    }
    else
    {
        for (int i = 0; i < MAX_ROW; i++)
        {
            card c = get_rand_card(game_info);
            game_info.vec_now_box[2].push_back(c);
        }
    }

    for (int i = 3; i < MAX_LIST; i++)
    {
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c = get_rand_card(game_info);
            game_info.vec_now_box[i].push_back(c);
        }
    }

    check_card(game_info);
}

bool game_logic::gen_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<int> data;

    data = parsedData.get<vector<int>>();
    for(auto& d:data)
    {
        game_info.graph_data.push_back(d);
    }

    for (int i = 0; i < MAX_LIST; i++)
    {
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c; 
            int v = game_info.graph_data[i*MAX_ROW+j];
            c.value = v%10;
            c.is_gold = v/10;

            game_info.vec_now_box[i].push_back(c);
            game_info.g_index++;
        }       
    }

    check_card_ex(game_info);
    // MY_LOG_PRINT("game_info.normal_platewin = %d", game_info.normal_platewin);
    // MY_LOG_PRINT("game_info.respin_platewin = %d", game_info.respin_platewin);
    // MY_LOG_PRINT("game_info.wheel_win = %d", game_info.wheel_win);

    return true;
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

// 消除扑克
bool game_logic::remove_card(remove_info &reInfo, user_game_info &game_info)
{
    bool is_remove = false;

    map<int, int> map_card_count[MAX_LIST];

    for (int i = 0; i < MAX_LIST; i++)
    {
        for (auto &c : game_info.vec_now_box[i])
        {
            if (c.value != BOX_SCATTER)
            {
                map_card_count[i][c.value]++;
            }
        }
    }

    // 检查wild
    int wild = BOX_WILD;
    for (int i = 1; i < MAX_LIST; i++)
    {
        if (map_card_count[i].find(wild) != map_card_count[i].end())
        {
            for (auto &c : map_card_count[0])
            {
                if (c.first != wild)
                {
                    map_card_count[i][c.first] += map_card_count[i][wild];
                }
            }
            map_card_count[i].erase(wild);
        }
    }

    // 消除结算
    vector<int> vec_card;
    for (auto &c1 : map_card_count[0])
    {
        int len = 1;

        for (int i = 1; i < MAX_LIST; i++)
        {
            bool flag = false;
            for (auto &c : map_card_count[i])
            {
                if (c1.first == c.first)
                {
                    len++;
                    flag = true;
                    break;
                }
            }

            if (!flag)
            {
                break;
            }
        }

        if (len >= 3)
        {
            vector<pos> vec_pos;
            int ways = 1;
            for (int i = 0; i < len; i++)
            {
                int j = 0;
                for (auto &c : game_info.vec_now_box[i])
                {
                    if (c.value == c1.first || c.value == BOX_WILD)
                    {
                        c.status = 1;                       
                        pos p;
                        p.x = j;
                        p.y = i;
                        vec_pos.push_back(p);
                    }
                    j++;
                }

                for (auto &c : map_card_count[i])
                {
                    if (c1.first == c.first)
                    {
                        ways *= c.second;
                    }
                }
            }

            int index = len - 3;
            if (index > 2)
            {
                index = 2;
            }
            //MY_LOG_PRINT("c1.first = %d, len = %d, ways = %d", c1.first, len, ways);
            int multer = card_multer[c1.first-2][index] * ways;
            //MY_LOG_PRINT("multer = %d", multer);
            reward rw(c1.first, len, ways, multer);
            rw.vec_pos = vec_pos;
            reInfo.vec_reward.push_back(rw);
            reInfo.multer += multer;
            is_remove = true;
        }
    }

    return is_remove;
}

// 打印数据
void game_logic::print_data(vector<card> vec_now_box[MAX_LIST])
{
    std::ofstream ofs;

    std::remove("test.txt");
    ofs.open("test.txt", ios::out | ios::app);
    ofs << "===============" << std::endl;

    card now_box[MAX_ROW][MAX_LIST];
    // for (int i = 0; i < MAX_ROW; i++)
    // {
    //     for (int j = 0; j < MAX_LIST; j++)
    //     {
    //         now_box[i][j].value = -1;
    //     }
    // }
    for (int i = 0; i < MAX_LIST; i++)
    {
        int j = 0;
        for (auto c : vec_now_box[i])
        {
            {
                now_box[j++][i] = c;
            }
        }
    }

    for (int i = 0; i < MAX_ROW; i++)
    {
        for (int j = 0; j < MAX_LIST; j++)
        {
            if (now_box[i][j].status == 0)
            {
                int value = now_box[i][j].value;
                if (now_box[i][j].is_gold > 0)
                {
                    value += 100;
                }

                ofs << value << "             ";
            }
            else
            {
                ofs << "X" << now_box[i][j].value << "             ";
            }
        }
        ofs << std::endl;
        if (i == 3)
        {
            ofs << "------------------" << std::endl;
        }
    }
    ofs.close();
}

void game_logic::check_card(user_game_info &game_info)
{
    do
    {
        remove_info remove_data;        
        bool is_remove = remove_card(remove_data, game_info);
        remove_data.comboBonus = game_info.comboBonus;
        
        if (is_remove)
        {
            remove_data.multer *= remove_data.comboBonus;
            game_info.total_multer += remove_data.multer;
            remove_data.comboBonus = game_info.comboBonus;
        }
        
        remove_data.save_box_info(game_info.vec_now_box);
        
        if (!is_remove)
        {
            //if(game_info.vec_remove.empty())
                game_info.vec_remove.push_back(remove_data);        
            // graph_data data;
            // data.vec_remove = game_info.vec_remove;
            // data.total_multer = game_info.total_multer;
            // game_info.all_total_multer = game_info.total_multer;

            //game_info.vec_graph_data.push_back(data);           
            break;
        }

        fill_remove_box(remove_data, game_info);

        game_info.vec_remove.push_back(remove_data);
        int i = game_info.vec_remove.size();
        if(i > 3)
        {
            i = 3;
        }
      
        game_info.comboBonus = combo_bonus[i];
    } while (true);
}

void game_logic::check_card_ex(user_game_info &game_info)
{
    do
    {
        remove_info remove_data;        
        bool is_remove = remove_card(remove_data, game_info);
        remove_data.comboBonus = game_info.comboBonus;
        
        if (is_remove)
        {
            remove_data.multer *= remove_data.comboBonus;
            game_info.total_multer += remove_data.multer;
            remove_data.comboBonus = game_info.comboBonus;
        }
        
        remove_data.save_box_info(game_info.vec_now_box);
        //print_data(game_info.vec_now_box);
        if (!is_remove)
        {
            game_info.vec_remove.push_back(remove_data);  
                          
            break;
        }

        fill_remove_box_ex(remove_data, game_info);

        game_info.vec_remove.push_back(remove_data);
        int i = game_info.vec_remove.size();
        if(i > 3)
        {
            i = 3;
        }
        if(game_info.is_free)
            game_info.comboBonus = combo_bonus[i]*2;
        else
            game_info.comboBonus = combo_bonus[i];
    } while (true);
}

void game_logic::insert_scatter(vec_remove_info& vec_remove, int num)
{    
    if (num <= 0 || vec_remove.empty())
    {
        return;
    }
    //MY_LOG_PRINT("insert_scatter num = %d", num);
    std::random_device rd;
    std::mt19937 g(rd());
    vector<int> vec_col;
    for (int i = 0; i < MAX_LIST; i++)
    {
        vec_col.push_back(i);
    }
    std::shuffle(vec_col.begin(), vec_col.end(), g);
    int len = vec_remove.size();

    if (len <= 1)
    {
        auto &rm = vec_remove[0];
        int i = 0;
        while (num > 0)
        {
            int r = getRand(1, 100);
            int j = r % rm.vec_now_box[vec_col[i]].size();
            rm.vec_now_box[vec_col[i]][j].value = BOX_SCATTER;
            rm.vec_now_box[vec_col[i]][j].is_gold = 0;
            i++;
            num--;
        }
    }
    else // if (len <= 2)
    {
        int i = 0;
        
        while (num > 0)
        {
            int col = vec_col[i]; //
            int r = getRand(1,100);
            vector<int> vec_pos;
            bool find = false;
            if(r < 50)
            {
                for(int i=0; i<MAX_ROW;i++)
                {
                    bool flag = false;
                    for(auto &rm:vec_remove)
                    {
                        for (auto &f:rm.vec_fill_box[col])
                        {
                            if(f.x == i)
                            {
                                flag = true;
                                break;
                            }
                        }
                        if(flag)
                            break;
                    }
                    if(!flag)
                    {
                        vec_pos.push_back(i);                        
                    }
                }
                if(vec_pos.size()>0)
                {
                    int p = getRand(1, 100) % vec_pos.size();
                    int x = vec_pos[p];
                    vec_remove[0].vec_now_box[col][x].value = BOX_SCATTER;
                    vec_remove[0].vec_now_box[col][x].is_gold = 0;
                    num--;
                    find = true;
                }
            }

            if(!find)
            {
                for (int j = 0; j < 4; j++)
                {
                    card c = vec_remove[len - 2].vec_now_box[col][j];

                    if (c.status == 1 && c.is_gold == 0)
                    {
                        vec_pos.push_back(j);
                    }
                }
                if (vec_pos.size() > 0)
                {
                    int p = getRand(1, 100) % vec_pos.size();
                    int x = vec_pos[p];
                    //MY_LOG_PRINT("col =%d, x = %d", col, x);
                    for (auto &f : vec_remove[len - 2].vec_fill_box[col])
                    {
                        if (f.x == x)
                        {
                            f.value = BOX_SCATTER;
                            f.is_gold = 0;
                            //MY_LOG_PRINT("num = %d", num);
                            // MY_LOG_PRINT("col =%d, x = %d", col, x);
                            num--;
                        }
                    }
                }
            }
           
            i++;
            if(i >= MAX_LIST)
                break;
        }
    }
}

// 填充被消除格子
void game_logic::fill_remove_box(remove_info &reInfo, user_game_info &game_info)
{
    for (int i = 0; i < MAX_LIST; i++)
    {
        reInfo.vec_fill_box[i].clear();
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c = game_info.vec_now_box[i][j];
            if (c.status == 1)
            {                
                if (c.value == BOX_WILD)
                {
                    card c1 = get_rand_card(game_info);
                    if (i == 0)
                    {
                        c1.is_gold = 0;
                    }
                    game_info.vec_now_box[i][j] = c1;
                    fillInfo f;
                    f.x = j;
                    f.y = i;
                    f.value = c1.value;
                    f.is_gold = c1.is_gold;
                    f.last = 101;
                    if(c.is_gold > 0)
                        f.last = 102;
                    
                    reInfo.vec_fill_box[i].push_back(f);
                }
                else
                {
                    card c1 = get_rand_card(game_info);
                    if (i == 0)
                    {
                        c1.is_gold = 0;
                    }
                    if (c.is_gold > 0)
                    {                  
                        if (c.is_gold == 2)
                        {
                            c1.value = BOX_WILD;
                            c1.is_gold = 1;
                            game_info.vec_now_box[i][j] = c1;
                            fillInfo f;
                            f.x = j;
                            f.y = i;
                            f.value = BOX_WILD;
                            f.is_gold = 1;
                            reInfo.vec_fill_box[i].push_back(f);

                            vector<int> vec_col;
                            for(int k=1; k<MAX_LIST; k++)
                            {
                                if(i != k)
                                {
                                    vec_col.push_back(k);
                                }
                            }

                            vector<pos> vec_pos;
                            for (auto &col : vec_col)
                            {
                                for(int l=0; l<MAX_ROW; l++)
                                {
                                    card c2 = game_info.vec_now_box[col][l];
                                    if(c2.status ==0 && c2.value!=BOX_WILD)
                                    {
                                        pos p;
                                        p.x = l;
                                        p.y = col;
                                        vec_pos.push_back(p);
                                    }
                                }
                            }
                            std::random_device rd;
                            std::mt19937 g(rd());
                            std::shuffle(vec_pos.begin(), vec_pos.end(), g);

                            int num = 0;
                            for(auto& p:vec_pos)
                            {
                                game_info.vec_now_box[p.y][p.x].value = BOX_WILD;
                                game_info.vec_now_box[p.y][p.x].is_gold = 1;
                                fillInfo f;
                                f.x = p.x;
                                f.y = p.y;
                                f.value = BOX_WILD;
                                f.is_gold = 1;
                                reInfo.vec_fill_box[i].push_back(f);
                                num++;
                                if(num >= 2)
                                    break;
                            }                            
                        }
                        else
                        {
                            c1.value = BOX_WILD;                            
                            game_info.vec_now_box[i][j] = c1;
                            fillInfo f;
                            f.x = j;
                            f.y = i;
                            f.value = BOX_WILD;
                            f.is_gold = 0;
                            reInfo.vec_fill_box[i].push_back(f);
                        }
                    }
                    else
                    {
                        game_info.vec_now_box[i][j] = c1;
                        fillInfo f;
                        f.x = j;
                        f.y = i;
                        f.value = c1.value;
                        f.is_gold = c1.is_gold;
                        reInfo.vec_fill_box[i].push_back(f);
                    }
                } 

                //c1.is_gold = false;
                             
            }
        }
    }
}

// 填充被消除格子
void game_logic::fill_remove_box_ex(remove_info &reInfo, user_game_info &game_info)
{
    for (int i = 0; i < MAX_LIST; i++)
    {
        reInfo.vec_fill_box[i].clear();
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c = game_info.vec_now_box[i][j];
            if (c.status == 1)
            {                
                if (c.value == BOX_WILD)
                {
                    card c1;
                    int v = game_info.graph_data[game_info.g_index++];
                    c1.value = v%10;
                    c1.is_gold = v / 10 %10;
                    // MY_LOG_PRINT("v = %d", v);
                    // MY_LOG_PRINT("i = %d, j = %d", i, j);
                    game_info.vec_now_box[i][j] = c1;
                    fillInfo f;
                    f.x = j;
                    f.y = i;
                    f.value = c1.value;
                    f.is_gold = c1.is_gold;
                    f.last = 101;
                    if(c.is_gold > 0)
                        f.last = 102;
                    
                    reInfo.vec_fill_box[i].push_back(f);
                }
                else
                {
                    if (c.is_gold > 0)
                    {                  
                        if (c.is_gold == 2)
                        {
                            card c1;
                            int v = game_info.graph_data[game_info.g_index++];
                            c1.value = v % 10;
                            c1.is_gold = v / 10%10;  
                            // MY_LOG_PRINT("v = %d", v);
                            // MY_LOG_PRINT("i = %d, j = %d", i, j);                        
                            game_info.vec_now_box[i][j] = c1;
                            fillInfo f;
                            f.x = j;
                            f.y = i;
                            f.value = BOX_WILD;
                            f.is_gold = 1;
                            reInfo.vec_fill_box[i].push_back(f);

                            for(int k = 0; k<2; k++)
                            {
                                v = game_info.graph_data[game_info.g_index++];
                                int y = v/1000;
                                int x = v%1000/100;

                                // MY_LOG_PRINT("v = %d", v);
                                // MY_LOG_PRINT("x = %d, y = %d", x, y);

                                game_info.vec_now_box[y][x].value = BOX_WILD;
                                game_info.vec_now_box[y][x].is_gold = 1;
                                fillInfo f;
                                f.x = x;
                                f.y = y;
                                f.value = BOX_WILD;
                                f.is_gold = 1;
                                reInfo.vec_fill_box[i].push_back(f);
                            }                            
                        }
                        else
                        {
                            card c1;
                            int v = game_info.graph_data[game_info.g_index++];
                            // MY_LOG_PRINT("v = %d", v);
                            // MY_LOG_PRINT("i = %d, j = %d", i, j);
                            c1.value = BOX_WILD;                            
                            game_info.vec_now_box[i][j] = c1;
                            fillInfo f;
                            f.x = j;
                            f.y = i;
                            f.value = BOX_WILD;
                            f.is_gold = 0;
                            reInfo.vec_fill_box[i].push_back(f);
                        }
                    }
                    else
                    {
                        card c1;
                        int v = game_info.graph_data[game_info.g_index++];
                        // MY_LOG_PRINT("v = %d", v);
                        // MY_LOG_PRINT("i = %d, j = %d", i, j);
                        c1.value = v % 10;
                        c1.is_gold = (v / 10)%10;                        

                        game_info.vec_now_box[i][j] = c1;
                        fillInfo f;
                        f.x = j;
                        f.y = i;
                        f.value = c1.value;
                        f.is_gold = c1.is_gold;
                        reInfo.vec_fill_box[i].push_back(f);
                    }
                } 

                //c1.is_gold = false;
                             
            }
        }
    }
}

void game_logic::rand_card(user_game_info &game_info)
{
    for (int i = 0; i < MAX_LIST; i++)
    {
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c = get_rand_card(game_info); 
            //第一列不能出现金色
            if(i == 0)
            {
                c.is_gold = 0;
            }
            game_info.vec_now_box[i].push_back(c);
        }       
    }

    vector<pos> vec_pos;
    for (int i = 0; i < MAX_LIST; i++)
    {
        for (int j = 0; j < MAX_ROW; j++)
        {
            card c = game_info.vec_now_box[i][j];

            if(c.is_gold == 1)
            {
                pos p;
                p.x = i;
                p.y = j;

                vec_pos.push_back(p);
            }
        }
    }
    if (vec_pos.size() > 0 && getRand(1, 100) < 60)
    {
        std::random_device rd;
        std::mt19937 g(rd());
        std::shuffle(vec_pos.begin(), vec_pos.end(), g);
        pos p = vec_pos[0];
        game_info.vec_now_box[p.x][p.y].is_gold = 2;
    }
    check_card(game_info);
}
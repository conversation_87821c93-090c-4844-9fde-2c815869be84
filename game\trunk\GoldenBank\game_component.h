/*
-----------------------------------------------------------------------------

File name        :   game_component.h
Author           :   
Version          :   1.0
Date             :   2018.4.3
Description      :   组件;

-----------------------------------------------------------------------------
*/

#ifndef __GAME_COMPONENT_H__
#define __GAME_COMPONENT_H__

#include "room_common.h"
#include "i_game_component.h"
#include "mjq_game_video.h"
#include "logic_def.h"
#include "game_config.h"
#include "table_hunded_interface.h"
#include "common_interface.h"
#include "log_manager.h"
#include "my_redis.h"
#include "copy_robot_logic.h"
#include "comm_msg.h"
//#include "sha256.h"
#include "game_logic.h"
#include "winlose_control.h"
#include "server.pb.h"

extern CDLLHelper<ICommon>     g_com;

// 通过配置名字符串，获取配置值（查看 xxx_game_config.xml）

class IUser; 
class CGameComponent : public IHundredGameComponent {

#pragma pack(1)	///struct	begin
    enum TIME_ID
	{
		TID_CHECK_USER = 1,			// 检测过期用户定时器
	};
	struct db_data
	{
		int uid;
		int type;
		int itemID;
	};

#pragma pack()	///struct	end

public:

	CGameComponent() {};
	~CGameComponent() {};
	void clear();
	void reset();

public:
	
	virtual int init(ITableHunded *table, data_pool_interface* data_pool, gamecontrol_interface* game_control, 
	const void * rule, int rule_len = 0);
	virtual bool set_timer(_uint8 time_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat = 1);
	virtual bool kill_timer(_uint8 time_id);
	virtual void on_time(_uint8 time_id, _uint64 param);
	virtual int on_recv(_uint8 mcmd, _uint8 scmd, const void* pdata, int size, IUser* pUser);
	virtual void on_http_recv(socket_id sid, int uid, int opid, const string& strParam);

public:

	virtual int  on_user_action(IUser *user, _uint8 type);
	virtual void on_forced_end(_uint64 uid) {};
	//是否需要外部补币
	virtual bool need_check_gold(IUser *puser) { return false; };
	/*判断玩家下线是否能删除玩家（用于加金币等玩家结算判断）*/
	virtual bool is_can_clear_user(int chairid);
	virtual bool skip_set_user_status() { return true; }
    /*用户金币改变*/ 
	virtual void on_user_game_info(IUser *pUser) {};
	//
	virtual int  gm_control(const char* buff, int buff_len, char* tips, int &tips_len, string ip, int account_id) { return 0; };
	virtual int  get_change_robot_uid(int *uids, int &uid_count) { return 0; };
	virtual bool check_is_Honor(int uid) { return false; };

public:
	//db数据
	virtual void db_get_data(void *obj,const char *data, int len);
	//获取走势图数据
	virtual int  get_game_info(char *buff, int buff_len, int &status_time) { return 0;  };

public:
	virtual bool is_user_in_game(int uid) { return true; }
	virtual void reset_game_rule(const void * rule, int rule_len = 0);
	virtual bool transfer_inout_result(transfer_inout_result_info data);

	bool analy_game_rule(const void * rule, int rule_len);

public:

	virtual void stop_game() {};
	virtual void start_game() {};

private:

	//进入房间*/
	void on_user_enter(IUser* user);
	//离开房间
	void on_user_leave(IUser* user);

	//请求下注
	void on_user_bet_roll(IUser* user, const std::string &data, user_game_info &game_info);
	void user_bet_logic(IUser* pUser, user_game_info& game_info, bool use_free, int total_bet, int now_time);
	//发送数据
	void send_data(IUser* user, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);
	//发送房间信息
	void send_room_info(IUser* user);
	//广播我的个人信息
	void send_my_user_info(IUser* user);
	//发送下注结果
	void send_bet_roll_err_result(IUser* user, serverProto::AckType type, serverProto::Error err, 
            const std::string& token, const std::string& msg, void* data, size_t size);

	void send_bet_roll_result(IUser* user, string msg, bool is_suc);
	void send_game_info_result(IUser* user, int client_id);
	void send_notice_result(IUser *user);
	void send_setting_result(IUser *user);
	void test_bet_roll_result();
	void on_user_game_info(IUser *user, const std::string &data, int size, user_game_info &game_info);
	void handle_user_notice_request(IUser* user, const std::string &msg, int size);

	//获得md5key值
	void calcu_md5key(int uid, string &strkey, int time);
	//计算税收
	void calcu_tax(IUser* pUser, const std::string &md5key);
	int  write_tax(IUser* user, user_game_info &info, const std::string &md5key);
	//计算游戏结果
	void calcu_result(user_game_info &game_info);
	//计算图形
	void calcu_graph(user_game_info &game_info, int i=-1);

	//获取用户免费数据
	int  get_user_free_data(int uid, const char str[32]);
	//更新用户免费数据
	void update_user_free_data(int uid, const char str[32], int value);
	//设置用户免费数据
	void set_user_free_data(int uid, const char str[32], int value);
	void on_update_user_gold_result(IUser *user);
	// 对局流水
	void write_versus_bill(int uid,int time);
	//投付记录
	void write_transfe_inorut_one(int out_score,
								  int pay_out,
								  int bill_type,
								  bool is_end,
								  int bet,
								  int uid,
								  bool write_db,
								  int time
								  );
	//打包玩家个人对局明细
	void pack_user_detail(string &detail, int uid);

	bool get_lottery_result(int client_id, int &result);

	void run_rtp();
private:
	int		m_tax;					//税收

private:
	game_logic m_game_logic;

private:
	map_user_game_info  m_map_user_game_info;
	const time_t KICK_USER_OFFLINE_DURATION = 3600*24*3000;

private:
    int sock;
};

#endif
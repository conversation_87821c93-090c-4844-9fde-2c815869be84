#include "user.h"
#include "room_common.h"
#include <string.h>
#include "my_redis.h"
#include "cmd_room.h"
#include "client_cmd.h"
#include "game_frame.h"
#include "config_manager.h"
#include "room_bill_fun.h"
#include "bill_fun.h"
#include "ip_location.h"

#define MY_CPY_STR(dst) do{if(my_##dst){__sprintf(dst, sizeof(dst), "%s", my_##dst);}}while(0);

CUser::CUser()
{
    reset_data(); 
}

CUser::~CUser()
{

}

void CUser::reset_data()
{
    sid = -1;
    uid = 0;
    sex = 0;
	m_end_clear = false;
	is_have_vir = false;
	is_single_post = 0;
	m_standup_time = 0;
	node_id = 0;
	last_gold = 0;
	last_bank_gold = 0;
	change_gold = 0;
	country_code = LANGUATE_EN;

	m_plat_type = 0;
	chat_time = 0;

    memset(head_img, 0x00, sizeof(head_img));
    memset(nick_name, 0x00, sizeof(nick_name));    
    memset(version, 0x00, sizeof(version));
    memset(ip_addr, 0x00, sizeof(ip_addr));
	memset(sign_info, 0x00, sizeof(sign_info));

    table_id  = INVALID_TABLE;
    chair_id  = INVALID_CHAIR;
	last_table_id = INVALID_TABLE;
	m_add_num = 0;
	vir_uid   = 0;
    status    = 0;    
    score     = 0;
    offline   = 0;
	temp_leave= 0;
    client_id = 0;
	m_control_time = 0;
	enter_time = time(0);
	m_play_count = 0;
	m_user_result = 0;
	m_add_bill = 0;
}


void CUser::set_table_id(_uint32 my_table_id)
{
	table_id = my_table_id;
	if (my_table_id != INVALID_TABLE)
		last_table_id = my_table_id;
}

void CUser::set_head_image(const char *my_head_img)
{
    MY_CPY_STR(head_img);
}

void CUser::set_nick_name(const char *my_nick_name)
{
    MY_CPY_STR(nick_name);

    if (my_nick_name)
    {
        int x,y,z;

        sscanf(version, "%d.%d.%d", &x, &y, &z);
        clt_ver = VER_CMD(x,y,z);
    }
}

void CUser::set_sign_info(const char *sign)
{
	sprintf(sign_info, "%s", sign);
}

void CUser::set_user_version(const char *my_version)
{
    MY_CPY_STR(version);
}

void CUser::set_ip(char  ip[64])
{
    //g_com->_net_strip(my_ip, ip_addr, sizeof(ip_addr) - 1);
	if (m_user_type == ROBOT_TYPE)
	{
		int a = rand() % 154 + 100;
		int b = rand() % 154 + 100;
		int c = rand() % 154 + 100;
		int d = rand() % 154 + 100;
		memset(ip_addr, 0, sizeof(ip_addr));
		sprintf(ip_addr, "%d.%d.%d.%d",a,b,c,d);
	}
	else
	{
		sprintf(ip_addr, "%s", ip);
	}
	g_log->write_log(LOG_TYPE_DEBUG, "set_ip user uid:%lld, ip_addr:%s", uid, ip_addr);
}
void CUser::set_playing_status(UserPlayingStatus status) {
    game_playing_status = status; 
}
UserPlayingStatus CUser::get_playing_status() {
    return game_playing_status;
}
bool CUser::is_running() {
    return game_playing_status == UserPlayingStatus::RUNNING;
}
bool CUser::is_idle() {
    return game_playing_status == UserPlayingStatus::IDLE; 
}
bool CUser::is_broken() {
    return game_playing_status == UserPlayingStatus::BROKEN;
}

void CUser::set_single_status(int status)
{ 
	g_log->write_log(LOG_TYPE_DEBUG, "set_single_status user uid:%lld, status:%d", uid, status);
	is_single_post = status; 
}

void CUser::update_add_bill(int bet)
{
	m_add_bill += bet;
}

void CUser::set_user_status(_uint8 my_status)
{
	g_log->write_log(LOG_TYPE_DEBUG, "set_user_status user uid:%lld, status:%d", uid, my_status);
    if (my_status == cmd_room::USER_STATUS_OFFLINE)
    {
        offline = 1;
    }
	else if (my_status == cmd_room::USER_STATUS_TEMP_LEAVE)
	{
		temp_leave = 1;
	}
    else
    {
        status = my_status;
    }
	if (status == cmd_room::USER_ACTION_STANDUP)
	{
		m_standup_time = time(0);
	}
	else
	{
		m_standup_time = 0;
	}
	myredis::GetInstance()->set_data_by_uid(uid, DB_COMM, CUR_GAME_STATUS, my_status);
}

void CUser::set_gold(_uint64 gold) 
{
	m_gold = gold;
	g_log->write_log(LOG_TYPE_DEBUG, "CUser set_gold... user uid:%lld, m_gold:%lld", uid, m_gold);
}

void CUser::update_gold(int result, bool need_flesh, bool is_add, bool is_tax, bool is_back)
{
	g_log->write_log(LOG_TYPE_DEBUG, "CUser update_gold... user uid:%lld, status:%d m_gold:%d", uid, result, m_gold);
	_tint64 now_gold = m_gold;
	if (now_gold + result < 0)
	{
		if (is_add)
		{
			m_user_result -= m_gold;
			m_add_num -= m_gold;
		}

		m_gold  = 0;
		myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, -now_gold);
		g_log->write_log(LOG_TYPE_ERROR, "update gold and clore not enough uid:%llu result:%d need_flesh:%d, is_add:%d, is_tax:%d", uid, result, need_flesh, is_add, is_tax);
	}
	else
	{
		if (is_add)
		{
			m_user_result += result;
			m_add_num += result;
		}

		m_gold += result;
		myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, result);
	}
	if(need_flesh)
	{
		CClientCmd::do_update_user_to_server(uid, m_gold, cmd_sys::GAME_ROOM_GOLD);
		CClientCmd::do_update_user_vcoin(uid, m_gold, 0, cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);
	}
	g_log->write_log(LOG_TYPE_DEBUG, "CUser update_gold... end user uid:%lld, status:%d m_gold:%d, m_add_num:%d", uid, result, m_gold, m_add_num);

	//防止机器人钱溢出
	if (m_user_type == ROBOT_TYPE)
	{
		m_add_num = 0;
		m_user_result = 0;
		myredis::GetInstance()->set_data_by_uid(uid, DB_COMM, CUR_GOLD, m_gold);
	}
}

//更新金币并且更新彩金
void CUser::update_gold_and_clore(int result, bool need_flesh, bool is_add, bool is_tax)
{
	g_log->write_log(LOG_TYPE_DEBUG, "user[%d] update_gold_and_clore, result:%d m_gold:%d", uid, result, m_gold);
	_tint64 now_gold = m_gold;
	if (now_gold + result < 0)
	{
		if (is_add)
		{
			m_user_result -= m_gold;
			m_add_num -= m_gold;
		}
		
		m_gold  = 0;
		myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, -now_gold);
		g_log->write_log(LOG_TYPE_ERROR, "user[%d] ERROR update gold and clore not enough result:%d need_flesh:%d, is_add:%d, is_tax:%d", uid, result, need_flesh, is_add, is_tax);
	}
	else
	{
		if (is_add)
		{
			m_user_result += result;
			m_add_num += result;
		}

		m_gold += result;
		myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_GOLD, result);
	}
	if(need_flesh)
	{
		CClientCmd::do_update_user_to_server(uid, m_gold, cmd_sys::GAME_ROOM_GOLD);
		CClientCmd::do_update_user_vcoin(uid, m_gold, 0, cmd_sys::SYSTEM_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);
	}
	g_log->write_log(LOG_TYPE_DEBUG, "CUser update_gold_and_clore... end user uid:%lld, status:%d m_gold:%d, m_add_num:%d", uid, result, m_gold, m_add_num);
}

_uint8 CUser::get_user_status()
{
    if (offline)
    {
        return cmd_room::USER_STATUS_OFFLINE;
    }

    return status;
}

_uint8 CUser::get_real_status()
{
    return status;
}

int CUser::cmp_user_ver(_uint32 ver)
{
    if (clt_ver > ver)
    {
        return 1;
    }

    if (clt_ver < ver)
    {
        return -1;
    }

    return 0;
}

void CUser::set_gps(int my_longitude, int my_latitude)
{
	m_longitude = my_longitude;
	m_latitude = my_latitude;
}

int CUser::get_longitude()
{
	return m_longitude;
}

int CUser::get_latitude()
{
	return m_latitude;
}

void CUser::set_site(const char* site, int len)
{
	int copy_len = min(len, 512);
	memcpy(m_site, site, copy_len);
}

char* CUser::get_site()
{
	return m_site;
}

void CUser::set_position_info(const char* position_info, int len)
{
	memset(positioninfo, 0, sizeof(positioninfo));
	int copy_len = min(len, 128);
	memcpy(positioninfo, position_info, copy_len);
}

char* CUser::get_position_info()
{
	if (positioninfo[0]=='\0' && m_user_type == ROBOT_TYPE)
	{
		db_ip_locationinfo info;
		if (iplocation_inst->get_rand_location(info))
		{
			strcpy(positioninfo, info.szcity);
		}
	}
	return positioninfo;
}

void CUser::set_vir_level()
{ 
	level =  rand() % 8 + 1;
}

int CUser::get_api_type() {
    return api_type;
}

void CUser::set_api_type(int api_mode_type) {
	this->api_type = api_mode_type;
}


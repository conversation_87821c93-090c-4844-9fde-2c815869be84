#include "http_url_parser.h"


ParseUrlParam::ParseUrlParam() {

}

ParseUrlParam::~ParseUrlParam() {

}


std::map<std::string, std::string> ParseUrlParam::decode(const std::string& param) {

    _url_param = param;
    std::vector<std::string> param_pairs = split(param, '&');
    for (int i = 0; i < param_pairs.size(); i ++) {
        std::vector<std::string> param_split_pair =  split(param_pairs[i], '=');
        if (2 == param_split_pair.size()) {
            _url_parsed_params.insert(std::make_pair(param_split_pair[0], param_split_pair[1]));
        }
    }

    return _url_parsed_params;
}

std::vector<std::string> ParseUrlParam::split(const std::string& s, char seperator) {

    std::vector<std::string> output;
    std::string::size_type prev_pos = 0, pos = 0;

    while((pos = s.find(seperator, pos)) != std::string::npos) {
        std::string substring( s.substr(prev_pos, pos-prev_pos) );
        output.push_back(substring);
        prev_pos = ++pos;
    }
    output.push_back(s.substr(prev_pos, pos-prev_pos));

    return output;
}

int ParseUrlParam::get_url_param_string(const std::string& key, std::string& value) {
    
    auto iter = _url_parsed_params.find(key);
    if (iter != _url_parsed_params.end()) {
        value = iter->second;
        return 0;
    }

    return -1;
}

int ParseUrlParam::get_url_param_int(const std::string& key, int *value) {

    auto iter = _url_parsed_params.find(key);
    if (iter != _url_parsed_params.end()) {
        try {
            *value = std::stoi(iter->second.c_str());
        } catch (...) {
            *value = 0;
        }
        return 0;
    }

    return -1;
}

int ParseUrlParam::get_url_param_float(const std::string& key, float *value) {

    auto iter = _url_parsed_params.find(key);
    if (iter != _url_parsed_params.end()) {
        try {
            *value = std::stof(iter->second.c_str());
        } catch (...) {
            *value = 0;
        }
        return 0;
    }

    return -1;
}

std::string ParseUrlParam::to_string() {
    
    std::string parsed;

    for (auto iter =_url_parsed_params.begin(); iter != _url_parsed_params.end(); iter++) {
        parsed += "key:" + iter->first + " value:" + iter->second + " ";
    }

    return parsed;
}

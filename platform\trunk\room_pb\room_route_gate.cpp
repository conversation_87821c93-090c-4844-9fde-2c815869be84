﻿#include "room_route_gate.h"
#include "cmd_gate.h"
#include "cmd_net_svr.h"
#include "user_manager.h"
#include "game_frame.h"
#include "table_manager.h"

static CRoomRouteGate _room_route_gate;

CRoomRouteGate::CRoomRouteGate()
{
#if DEBUG_VALUE
    DEBUG_LOG("CRoomRouteGate");
#endif
    __reg_action_sink(ACTION_ROOM_CONNECT_GATE_SUCCEED, connect_succeed);
    __reg_action_sink(ACTION_USER_LOGON_SUCCEED, user_enter);
    __reg_action_sink(ACTION_USER_LEAVE, user_leave);
}

CRoomRouteGate::~CRoomRouteGate()
{

}
   
FTYPE(void) CRoomRouteGate::connect_succeed(void * obj, enAction type, const void * pdata, int size)
{
    if (type != ACTION_ROOM_CONNECT_GATE_SUCCEED || sizeof(tagRoomConnectAction) != size) return ;

    const tagRoomConnectAction * pRoomConnectAction = (tagRoomConnectAction *)pdata;

	int user_count = UserManager::GetInstance()->user_count();
	cmd_gate::CRoomInfo roomInfo;
	roomInfo.set_RoomID(CGameFrame::GetInstance()->get_room_id());
	roomInfo.set_MaxUser(CGameFrame::GetInstance()->get_max_player());
	roomInfo.set_CurUser(user_count);
	
	g_server->send_gate(pRoomConnectAction->pid, cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_ROOM_USERLIST_BEGIN, &roomInfo);

    MY_LOG_PRINT("disable sync user data value: %d", pRoomConnectAction->disable_sync);

    if (user_count > 0 && !pRoomConnectAction->disable_sync)
    {
        int userInfoIndex = 0;
        cmd_gate::CRoomUserList userList;
		userList.set_GameID(config_manager::GetInstance()->get_game_id());
        userList.set_UserList_item_count(user_count);
        for (int i = 0; i < user_count; ++i)
        {
            IUser * pUser = UserManager::GetInstance()->enum_user(i);
            if (pUser)
            {
                cmd_gate::CRoomUserInfo * userInfo = userList.UserList_item(userInfoIndex++);
                if (userInfo)
                {
                    userInfo->set_UserID(pUser->get_user_id());
                }
            }
            if (userInfoIndex >= 1000)
            {
                g_server->send_gate(pRoomConnectAction->pid, cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_ROOM_USERLIST_DATA, &userList);
                userInfoIndex = 0;
                userList.clear();
            }
        }
        if (userInfoIndex > 0)
        {
			userList.set_GameID(config_manager::GetInstance()->get_game_id());
			userList.set_UserList_item_count(userInfoIndex);
            g_server->send_gate(pRoomConnectAction->pid, cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_ROOM_USERLIST_DATA, &userList);
        }
    }

	g_server->send_gate(pRoomConnectAction->pid, cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_ROOM_USERLIST_END, NULL);
}

FTYPE(void) CRoomRouteGate::user_enter(void * obj, enAction type, const void * pdata, int size)
{
    if (type != ACTION_USER_LOGON_SUCCEED || !pdata || size == 0) return ;

    const tagUserAction * pUserAction = (tagUserAction *)pdata;
    send_gate_user_info(pUserAction->userID, cmd_net_svr::CMD_ROUTE_GATE_ROOM_USER_ENTER);
}

FTYPE(void) CRoomRouteGate::user_leave(void * obj, enAction type, const void * pdata, int size)
{
    if (type != ACTION_USER_LEAVE || !pdata || size == 0) return ;

    const tagUserAction * pUserAction = (tagUserAction *)pdata;
    send_gate_user_info(pUserAction->userID, cmd_net_svr::CMD_ROUTE_GATE_ROOM_USER_LEAVE);

	if (!pUserAction->is_offlne)
	{
		cmd_room::HasLeaveRoom leave;
		leave.set_RoomID(g_server_id);
		g_server->send(pUserAction->user_sid,cmd_net::CMD_ROOM,cmd_net::SUB_HAS_LEAVE_ROOM,&leave);
	}
}

void CRoomRouteGate::send_gate_user_info(_tint64 userID, int cmd)
{
    IUser * pUser = UserManager::GetInstance()->get_user(userID);
    if (pUser || cmd == cmd_net_svr::CMD_ROUTE_GATE_ROOM_USER_LEAVE)
    {
        cmd_gate::CRoomUserInfo userInfo;
        userInfo.set_UserID(userID);
		userInfo.set_GameID(config_manager::GetInstance()->get_game_id());
        g_server->send_gate_batch(cmd_net::CMD_ROUTE_GATE, cmd, &userInfo);
    }
}

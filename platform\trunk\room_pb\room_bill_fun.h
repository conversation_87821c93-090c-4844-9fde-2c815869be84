#ifndef _ROOM_BIFF_FUN_
#define _ROOM_BIFF_FUN_

#include "room_common.h"
#include "user.h"

class room_bill_fun
{
public:
	room_bill_fun();
	~room_bill_fun();

public:
	static room_bill_fun *GetInstance()
	{
		static room_bill_fun room_bill;
		return &room_bill;
	}

public:
	void write_user_roominfo(_tint64 userID, bool is_leave, int nodeid, 
		int usertype, int result, int clientid, int bet);

public:
	void write_normal_room_bill(IUser *puser, int type, int room_id, int room_num, int node_id);
	void write_hund_room_bill(IUser *puser, int type, int node_id);

public:
	bool write_transfer_inout(transfer_inout inout);
	bool hund_write_transfer_inout(hundred_transfer_inout_one inout);
	bool hund_write_transfer_inout_ex(hundred_transfer_inout_one inout);

};


#endif
﻿/*
-----------------------------------------------------------------------------

File name        :   timer_manager.cpp         
Author           :        
Version          :   1.0            
Date             :   2014.3.13     
Description      :   定时器管理模块  

-----------------------------------------------------------------------------
*/
#include "timer_manager.h"

static CTimerManager  g_timer_manager;
ITimerManager* g_timer_mgr = &g_timer_manager;

CTimerManager::CTimerManager(void)
{
#if DEBUG_VALUE
    DEBUG_LOG("CTimerManager");
#endif
}

CTimerManager::~CTimerManager(void)
{
    final();
}

bool CTimerManager::init()
{
    m_timer.DLLInit(SVRLIB_NAME,"_timer_base_instance","_timer_base_free");
    if(!m_timer.GetInterface())   __return(false,LOG_TYPE_ERROR,"init m_room_timer fail!ERROR:%s",m_timer.GetError());

    m_random_timer.DLLInit(SVRLIB_NAME,"_timer_base_instance","_timer_base_free");
    if(!m_random_timer.GetInterface())  __return(false,LOG_TYPE_ERROR,"init m_table_timer fail!ERROR:%s",m_random_timer.GetError());

	g_apt->add_oncheck(on_timer_check,this);

    return true;
}

bool CTimerManager::final()
{
    if(m_timer.GetInterface()) m_timer->kill_all_timer();
    if(m_timer.GetInterface()) m_timer->kill_all_timer();
	if(g_apt.GetInterface()) g_apt->del_oncheck(on_timer_check);
    return true;
}

bool CTimerManager::set_timer(void*obj,FOnTimer sink,_uint32 timerid,_uint32 Elapse,_uint64 tag,_uint32 repeat)
{
    if(!sink || 0 == timerid) return false;
    tagTimerInfo info;
    info.TimerID      = timerid;
    info.Elapse       = Elapse;
    info.Repeat       = repeat;
    info.Param.tag    = tag;
    info.Param.lparam = (void*)sink;
    info.Param.obj    = obj;

	m_timer->kill_timer(timerid);
    return m_timer->set_timer(on_timer,info,0);
}

bool CTimerManager::kill_timer(_uint32 timerid)
{
    return m_timer->kill_timer(timerid);
}

_uint32 CTimerManager::set_random_timer(void*obj,FOnTimer sink,_uint32 Elapse,_uint64 tag,_uint32 repeat)
{
    if(!sink) return false;
    tagTimerInfo info;
    info.TimerID      = 0;
    info.Elapse       = Elapse;
    info.Repeat       = repeat;
    info.Param.tag    = tag;
    info.Param.lparam = (void*)sink;
	info.Param.obj    = obj;
    if(!m_random_timer->set_timer(on_random_timer,info,0)) return 0;
    return info.TimerID;
}

bool CTimerManager::kill_random_timer(_uint32 timerid)
{
    return m_random_timer->kill_timer(timerid);
}

FTYPE(void) CTimerManager::on_timer_check(void* obj)
{
    ((CTimerManager*)obj)->m_timer->check_timer(obj);
    ((CTimerManager*)obj)->m_random_timer->check_timer(obj);
}

FTYPE(void) CTimerManager::on_timer(void*p_obj,tagTimerInfo& info,void* pdata,_uint32 iRepeat,_uint32 delay)
{
	_uint32 t0 = g_com->_get_tick_count();
	FOnTimer sink = (FOnTimer)info.Param.lparam;
	sink(info.Param.obj,info.TimerID,info.Param.tag,info.Elapse,iRepeat,delay);
	_uint32 t = g_com->_cmp_tick_count(g_com->_get_tick_count(),t0);
	if(t >= ATPWARMTIME) g_log->write_log(LOG_TYPE_WARNING,"on_timer timer=%d 处理耗时=%d ms",info.TimerID,t);
}

FTYPE(void) CTimerManager::on_random_timer(void*p_obj,tagTimerInfo& info,void* pdata,_uint32 iRepeat,_uint32 delay)
{
	_uint32 t0 = g_com->_get_tick_count();
	FOnTimer sink = (FOnTimer)info.Param.lparam;
	sink(info.Param.obj,info.TimerID,info.Param.tag,info.Elapse,iRepeat,delay);
	_uint32 t = g_com->_cmp_tick_count(g_com->_get_tick_count(),t0);
	if(t >= ATPWARMTIME) g_log->write_log(LOG_TYPE_WARNING,"on_random_timer timer=%d 处理耗时=%d ms",info.TimerID,t);
}


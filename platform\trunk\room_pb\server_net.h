﻿/*
-----------------------------------------------------------------------------

File name        :   client_net.h      
Author           :        
Version          :   1.0            
Date             :   2014.2.13     
Description      :   网络通信模块  

-----------------------------------------------------------------------------
*/
#pragma once

#include "room_common.h"
#include "vm_sock_engine_interface.h"
#include <set>

using namespace std;

class CServerNet : 
    private IPacketVMServer,
    public  IServerNet
{
public:
    CServerNet(void);
    ~CServerNet(void);

	virtual bool       start();
	virtual bool       stop();
	virtual int        connect_count();
	virtual int        gate_count();

	//发送给用户
	virtual bool       send(socket_id sid,_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);
	virtual bool       send(socket_id sid,_uint8 mcmd,_uint8 scmd,const void* data,int size);
	virtual bool       send(socket_id sid,const string& str);

	virtual bool       allow_batch_send(socket_id sid,bool ballow);
	virtual bool       send_batch(_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);
	virtual bool       send_batch(_uint8 mcmd,_uint8 scmd,const void* data,int size);

    //发送给离开服务器用户
    virtual bool       send_leave_user(_tint64 userID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * data);
    virtual bool       send_leave_user(_tint64 userID, _uint8 mcmd, _uint8 scmd, const void * data, int size);
    virtual bool       send_leave_user(socket_id pid, _tint64 userID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * data);
    virtual bool       send_leave_user(socket_id pid, _tint64 userID, _uint8 mcmd, _uint8 scmd, const void * data, int size);

	//发送给一个网关消息(指定通道发送)
	virtual bool       send_gate(socket_id pid,_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data,SVREXTYPE extype=SVREXTYPE_NONE,const void*exdata=0,int exsize=0,bool b_batch_route=false);
	virtual bool       send_gate(socket_id pid,_uint8 mcmd,_uint8 scmd,const void* data,int size,SVREXTYPE extype=SVREXTYPE_NONE,const void*exdata=0,int exsize=0,bool b_batch_route=false);
	//发送给一个网关消息(随机一个网关)
	virtual bool       send_gate_random(_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data,SVREXTYPE extype=SVREXTYPE_NONE,const void*exdata=0,int exsize=0,bool b_batch_route=false);
	virtual bool       send_gate_random(_uint8 mcmd,_uint8 scmd,const void* data,int size,SVREXTYPE extype=SVREXTYPE_NONE,const void*exdata=0,int exsize=0,bool b_batch_route=false);
	//发送给网关消息(所有网关)
	virtual bool       send_gate_batch(_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data,SVREXTYPE extype=SVREXTYPE_NONE,const void*exdata=0,int exsize=0,bool b_batch_route=false);
	virtual bool       send_gate_batch(_uint8 mcmd,_uint8 scmd,const void* data,int size,SVREXTYPE extype=SVREXTYPE_NONE,const void*exdata=0,int exsize=0,bool b_batch_route=false);
	virtual bool       send_http_json(socket_id sid, std::map<std::string, std::string> http_headers_map, const void* data, size_t size);

    //路由其他服务器(大厅 列表  任务等）
    virtual bool    send_route_server_random(_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);                //随机一个服务器
    virtual bool    send_route_server_by_pid(_uint64 pid,_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);    //指定目标PID
    virtual bool    send_route_server_batch(_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);                 //群发

    //路由VM服务器（房间  报名服务器等）
    virtual bool    send_route_vm_server_by_pid(_uint64 pid,_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);             //指定目标PID
    virtual bool    send_route_vm_server_by_serverid(_uint64 serverid,_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);   //指定服务器ID，如房间ID
    virtual bool    send_route_vm_server_room(_uint64 userid,_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);            //指定用户ID发给用户所在的房间
    virtual bool    send_route_vm_server_batch(_uint8 mcmd,_uint8 scmd,ITBFOBJBase*data);                          //群发



	virtual bool       bind_user(socket_id sid,IUser* puser);
	virtual _uint32    net_ip(socket_id sid);
	virtual bool       is_online(socket_id sid);
	virtual bool       close(socket_id sid,int delay_msec=0);
	virtual socket_id  gate_pid(socket_id sid); //通过网络标识获取通道ID

	virtual FTYPE(void)  on_vm_recv(socket_id sid, const void* p_data, int size, _uint64 bind_tag, const void* p_bind);
private:
	CDLLHelper<IVMServer>    m_server_net;
	int m_disable_sync;  // 默认为0


	virtual FTYPE(void)  on_log(TLOGTYPE logtype,const char* sz_msg);
	virtual void  on_attemper_pipe_connecting(socket_id pid,_uint32 ip,_uint32 port);
	virtual void  on_attemper_pipe_connect_fail(socket_id pid,int code,_uint32 ip,_uint32 port);
	virtual void  on_attemper_pipe_connected(socket_id pid,_uint32 ip,_uint32 port);
	virtual void  on_attemper_pipe_disconnected(socket_id pid,int code,int ext_code,_uint32 ip,_uint32 port);
	virtual void  on_attemper_pipe_speed(socket_id pid,_uint32 time_msec,_uint32 ip,_uint32 port);
	virtual void  on_attemper_pipe_recv(socket_id pid,SNETHEAD* phead,const void* pdata, int dsize,const void* ex_data); 
	virtual void  on_attemper_vm_connected(socket_id sid);
	virtual void  on_attemper_vm_disconnected(socket_id sid,int code,int ext_code,_uint32 ip,_uint64 bind_tag,const void* p_bind);
	virtual void  on_attemper_vm_recv(socket_id sid,SNETHEAD* phead,const void* pdata, int dsize,const void* ex_data,_uint64 bind_tag,const void* p_bind); 


	inline bool load_gate();
	static FTYPE(void) on_update_gate_timer(void*obj,_uint32 timerid,_uint64 tag,_uint32 Elapse,_uint32 iRepeat,_uint32 delay);
};

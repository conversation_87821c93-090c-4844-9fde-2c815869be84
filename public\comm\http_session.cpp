#include "http_session.h"

namespace tnet {

HttpSession::HttpSession(svrlib::socket_id sid, const std::string& path) :
    sid_(sid), path_(path), keepalive_(true) {
    
    // jili的websocket连接不主动发送心跳包
    if (path.find("/lifeservice/ws2") != std::string::npos) {
        this->keepalive_ = false;
    }
}

HttpSession::~HttpSession() {

}


HttpSessionManager::HttpSessionManager() {

}

HttpSessionManager::~HttpSessionManager() {

    for (auto it = sessions_.begin(); it != sessions_.end(); it ++) {
        delete it->second;
    }
    
    this->sessions_.clear();
}

int HttpSessionManager::add_session(svrlib::socket_id sid, HttpSession* session) {
    
    auto it = this->sessions_.find(sid);
    if (it != this->sessions_.end()) {
        delete it->second;
        this->sessions_.erase(it);
        this->sessions_.insert(std::make_pair(sid, session));
        return 1;
    } else {
        this->sessions_.insert(std::make_pair(sid, session));
    }

    return 0;
}

int HttpSessionManager::del_session(svrlib::socket_id sid) {

    auto it = this->sessions_.find(sid);
    if (it != this->sessions_.end()) {
        delete it->second;
        this->sessions_.erase(it);
        return 0;
    }

    return -1;
}

HttpSession *HttpSessionManager::get_session(svrlib::socket_id sid) {

    auto it = this->sessions_.find(sid);
    if (it != this->sessions_.end()) {
        return it->second;
    }

    return nullptr;
}

}
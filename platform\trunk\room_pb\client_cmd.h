﻿#ifndef _CLIENT_CMD_H_
#define _CLIENT_CMD_H_

#include "room_common.h"

class CClientCmd
{
public:
    CClientCmd(void);
    ~CClientCmd(void);

public:
	static FTYPE(void) create_vir_robot(int begin_uid, int end_uid);
	static FTYPE(void) new_room_add_vir_robot();
	static FTYPE(bool) check_user_gold(int gold, int uid, int nodeid, IUser* RoomUserInfo);
	static FTYPE(bool) check_user_tiyantimes(int uid, int nodeid);
	static FTYPE(bool) on_with_hold(bool is_suc, int uid, const void* data, int size, socket_id sid, int code);

public:
	static FTYPE(void) send_enter_room_fail(socket_id sid,int room_code, int ret,const char* msg =NULL);
	static FTYPE(void) send_has_leave_room(socket_id sid,_tint64 userID, bool is_offline);

	static FTYPE(void) do_add_room_card_dbproxy(_tint64 userID,int room_num,int room_card, int bill_type);
	static FTYPE(void) do_sub_room_card_dbproxy(cmd_dbproxy::CDBProxyExceSection &section,int room_num,int bill_type);

	static FTYPE(void) do_add_room_gold_redis(_tint64 userID,int room_num,int room_gold, int bill_type);
	static FTYPE(int) do_sub_room_gold_redis(cmd_dbproxy::CDBProxyExceSection &section,int room_num,int bill_type);

public:
	//发送进入普通房结果
	static FTYPE(void) send_enter_nomal_room_result(socket_id sid, int ret,const char* msg =NULL);
	//发送离开房间结果
	static FTYPE(void) send_leave_nomal_room_result(socket_id sid, int ret,const char* msg =NULL);
	//发送准备开始结果
	static FTYPE(void) send_nomal_room_ready_result(socket_id sid, int ret,const char* msg =NULL);
	//发送配桌结果
	static FTYPE(void) send_pair_table_result(socket_id sid, int ret, int country, const char* msg = NULL);

	static FTYPE(void) on_enter_room_succ(socket_id sid,_tint64 userID);

	static FTYPE(void) do_update_user_to_server(_tint64 n64UserID, _tint64 n64CurCard, cmd_sys::GAME_MONEY_TYPE type);

	static FTYPE(void) do_update_user_vcoin(_tint64 n64UserID, _tint64 n64CurCard, _tint64 n64AddCoin, 
		cmd_sys::HEALTHE_CHANGE_TYPE change_type, cmd_sys::GAME_MONEY_TYPE money_type);

	static FTYPE(void) on_add_room_card_dbproxy(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);
	static FTYPE(void) on_sub_room_card_dbproxy(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);

private:
    static FTYPE(void) on_create_challenge_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);
    static FTYPE(void) on_enter_challenge_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);
    static FTYPE(void) on_offline_enter_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) regain_logon(socket_id sid, _tint64 userID);	
    static FTYPE(void) on_user_disconnect(void * obj, enAction action, const void * pdata, int size);
    
    static FTYPE(void) on_game_frame_message(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);

	static FTYPE(int) do_enter_room(_tint64 n64UserID, _tint64 n64SID, cmd_room::EnterRoomReq &reqEnterRoom, bool bCreate);

	static FTYPE(void) lobby_notice_action(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);

	static FTYPE(bool) create_user_info(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data, IUser* RoomUserInfo);

private:
	//进入普通房
	static FTYPE(void) on_enter_nomal_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(bool) on_enter_namal_logic(socket_id sid, _uint64 userID, const void* data, int size);
	//离开普通房
	static FTYPE(void) on_leave_nomal_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	//普通房准备开始
	static FTYPE(void) on_nomal_room_ready_start(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);

private:
	//进入WEB百人游戏
	static FTYPE(void) on_enter_web_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	//离开WEB百人游戏
	static FTYPE(void) on_leave_web_room(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* ex_data);

private:
	//发送进入普通房结果
	static FTYPE(void) send_enter_web_room_result(socket_id sid, int ret,const char* msg =NULL);
	//发送离开房间结果
	static FTYPE(void) send_leave_web_room_result(socket_id sid, int ret,const char* msg =NULL);

	// 背包消息记录分页请求
	static FTYPE(void) on_bag_message_record_startpage_req(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* exdata);
	static FTYPE(void) on_bag_message_record_startpage_dbproxy(void* obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection& section);

	// 背包消息删除请求
	static FTYPE(void) on_bag_message_record_del_req(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* exdata);
	static FTYPE(void) on_bag_message_record_del_dbproxy(void* obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection& section);

	//处理http请求
	static FTYPE(void) on_game_frame_http_message(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* exdata);
};


#endif

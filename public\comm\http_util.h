
#ifndef __PUBLIC_COMMON_HTTP_UTILS__
#define __PUBLIC_COMMON_HTTP_UTILS__

#include <string>
#include <map>
#include <string.h>
#include <vector>

namespace tnet {

enum RequestEvent {
    Request_Upgrade, 
    Request_Complete,
    Request_Error,   
};
    
//Request_Upgrade: context is &StackBuffer
//Request_Complete: context is 0
//Request_Error: context is &HttpError

enum ResponseEvent {
    Response_Complete,
    Response_Error,
};

struct CaseKeyCmp {
    bool operator() (const std::string& p1, const std::string& p2) const
    {
        return strcasecmp(p1.c_str(), p2.c_str()) < 0;
    }    
};

typedef std::multimap<std::string, std::string, CaseKeyCmp> Headers_t;
typedef std::multimap<std::string, std::string> Params_t;

class StackBuffer {
public:
    StackBuffer(const char* buf, size_t c) : buffer(buf), count(c) {}
    
    const char* buffer;
    size_t count;    
};

class StringUtil {
public:
    static std::string lower(const std::string& src);
    static std::string upper(const std::string& src);
    static std::vector<std::string> split(const std::string& src, const std::string& delim, size_t maxParts = size_t(-1));
};

class HttpUtil {
public:
    static const std::string& codeReason(int code);
    static const char* methodStr(uint8_t method);

    static std::string escape(const std::string& src);
    static std::string unescape(const std::string& src);

    //http header key is Http-Head-Case format
    static std::string normalizeHeader(const std::string& src);
};

}

#endif

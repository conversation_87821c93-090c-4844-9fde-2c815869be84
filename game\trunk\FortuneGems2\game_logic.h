#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_ODDS_CONFIG_COUNT 12       	//转盘中奖分布
#define  MAX_WAYS 5

/* 牌面值 */
enum EMUN_FG_JL_CRAD
{
	BOX_J = 0,//J
	BOX_Q = 1,//Q
	BOX_K = 2,//K
	BOX_A = 3,//A
	BOX_GB = 4,//绿宝石
	BOX_BB = 5,//蓝宝石
	BOX_RB = 6,//红宝石
	BOX_WILD = 7, //wild
	BOX_X1 = 8, //X1
	BOX_X2= 9,//X2
	BOX_X3 = 10,//X2
	BOX_X5 = 11,//X5
	BOX_X10 = 12,//X10
	BOX_X15 = 13,//X15
	BOX_WHEEL = 14,//WHEEL
};

const int wheel_odds_config[WHEEL_ODDS_CONFIG_COUNT] = {
	1, 3, 5, 8, 10, 15, 20, 30, 50, 100, 200, 1000
};
const int bet_config[MAX_BET_CONFIG_COUNT] = {
	10, 20, 30, 50, 80, 100, 200, 500, 1000, 2000, 3000, 4000, 5000, 7000, 10000
};

struct award
{
	int symbol;
	int line;
	int win;
};

struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int time; // 时间
    int bet;  // 下注数目
	int muter;//总倍数
    int result;  // 总奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;////当前模式 0-normal 1-extra
	int bs;//宝石倍数
	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

   	int plate_data[12];
	vector<award> vec_award;

	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info();
	void reset() 
	{		
		vec_award.clear();
		muter = 0;
		result = 0;
        tax = 0;
        pay_out = 0; 
		record_id = 0;
		bs = 0;		
	}

	void clear() 
	{
		result = 0;
        pay_out = 0;
	}
};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
    void init();
   
	//检查牌
	void check_card(user_game_info &game_info);

	//从图形库获取提取数据
	bool gen_game_data_from_graph_data(user_game_info &game_info, const string& graph);
	void gen_no_reward_game_data(user_game_info &game_info);
	_uint64 gen_new_round_index();


private:
	
};

#endif

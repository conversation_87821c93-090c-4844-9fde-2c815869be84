﻿/*
-----------------------------------------------------------------------------

File name        :   client_net.cpp
Author           :
Version          :   1.0
Date             :   2014.2.13
Description      :   网络通信模块

-----------------------------------------------------------------------------
*/
#include "comm_class.h"

#include "tinyxml.h"
#include "server_net.h"
#include "cmd_gate.h"
#include <set>

using namespace std;

static CServerNet net;
IServerNet *g_server = &net;

struct tagNetRcv
{
    void *sink;
};
struct tagCmdSinkList
{
    tagNetRcv list[256][256];

    tagCmdSinkList()
    {
        memset(list, 0, sizeof(list));
    }
};

tagCmdSinkList *get_user_cmd_sink_list()
{
    static tagCmdSinkList _user_cmd_sink_list;
    return &_user_cmd_sink_list;
}

tagCmdSinkList *g_user_rcv = 0;
tagCmdSinkList *g_gate_rcv = 0;

// 注册用户接收器
void __reg_user_rcv(USERRCV sink, _uint8 mcmd, _uint8 scmd)
{
    tagCmdSinkList *_user_rcv = get_user_cmd_sink_list();
    _user_rcv->list[mcmd][scmd].sink = (void *)sink;
}

tagCmdSinkList *get_gate_cmd_sink_list()
{
    static tagCmdSinkList _gate_cmd_sink_list;
    return &_gate_cmd_sink_list;
}

// 注册网关接收器
void __reg_gate_rcv(GATERCV sink, _uint8 mcmd, _uint8 scmd)
{
    tagCmdSinkList *_gate_rcv = get_gate_cmd_sink_list();
    _gate_rcv->list[mcmd][scmd].sink = (void *)sink;
}

CServerNet::CServerNet(void)
{
#if DEBUG_VALUE
    DEBUG_LOG("CServerNet");
#endif
    g_user_rcv = get_user_cmd_sink_list();
    g_gate_rcv = get_gate_cmd_sink_list();
    m_disable_sync = 0;
}

CServerNet::~CServerNet(void)
{
    g_user_rcv = 0;
    g_gate_rcv = 0;
}

bool CServerNet::load_gate()
{
    _ipbfs ipbf;
    static set<_uint64> iplist;
    set<_uint64>::iterator it;
    iplist.clear();

    TiXmlDocument xmlcfg;
    TiXmlElement *pRoot = 0;
    TiXmlElement *pItem = 0;
    char xmlfile[1024];
    if (!xmlcfg.LoadFile(_xmlfile(xmlfile, sizeof(xmlfile), CONFIG)))
        return false;
    if (!(pRoot = xmlcfg.FirstChildElement("config")))
        return false;
    pItem = pRoot->FirstChildElement("gate");
    while (pItem)
    {
        _uint64 ip = g_com->_net_nip(pItem->Attribute("ip"));
        _uint16 port = min_value(xml_attribute_int(pItem, "port"), 0);
        if (0 == ip || 0 == port)
            continue;
        iplist.insert((ip << 32) + port);
        pItem = pItem->NextSiblingElement("gate");
    }

    pItem = pRoot->FirstChildElement("game_dll");
    if (pItem) {
        m_disable_sync = xml_attribute_int(pItem, "disable_sync");
    }

    g_log->write_log(LOG_TYPE_PRINT, "[NET] 加载网关IP列表......");
    for (int i = 0; i < m_server_net->pipe_count(); i++)
    {
        _uint16 port = 0;
        _uint32 ip = 0;
        if (!m_server_net->pipe_enum(i, &ip, &port))
            continue;
        if (iplist.find((_uint64(ip) << 32) + port) == iplist.end())
        {
            g_log->write_log(LOG_TYPE_WARNING, "[NET] del gate : ip=%s port=%d", __tosip(ip, ipbf), port);
            m_server_net->pipe_del(ip, port);
        }
    }

    for (it = iplist.begin(); it != iplist.end(); it++)
    {
        _uint16 port = _uint16(*it);
        _uint32 ip = _uint32(*it >> 32);
        if (!m_server_net->pipe_add(ip, port))
            continue;
        g_log->write_log(LOG_TYPE_PRINT, "[NET] add gate : ip=%s port=%d", __tosip(ip, ipbf), port);
    }

    g_log->write_log(LOG_TYPE_PRINT, "[NET] 网关IP列表：共 %d 个", iplist.size());
    g_log->write_log(LOG_TYPE_PRINT, "[NET] -----------------------------------------");
    int i = 0;
    for (it = iplist.begin(); it != iplist.end(); it++)
    {
        _uint16 port = _uint16(*it);
        _uint32 ip = _uint32(*it >> 32);
        g_log->write_log(LOG_TYPE_PRINT, "[NET] gate %02d: ip=%s port=%d", ++i, __tosip(ip, ipbf), port);
    }
    g_log->write_log(LOG_TYPE_PRINT, "[NET] -----------------------------------------");

    iplist.clear();
    g_log->write_log(LOG_TYPE_PRINT, "[NET] 加载网关IP列表完成\r\n", iplist.size());

    return true;
}

bool CServerNet::start()
{
    if (!m_server_net.GetInterface())
    {
        TiXmlDocument xmlcfg;
        TiXmlElement *pRoot = 0;
        TiXmlElement *pItem = 0;
        char xmlfile[1024];
        if (!xmlcfg.LoadFile(_xmlfile(xmlfile, sizeof(xmlfile), CONFIG)))
            __return(false, LOG_TYPE_ERROR, "配置文件（config.xml）加载失败，请检查文件格式是否正确！");
        if (!(pRoot = xmlcfg.FirstChildElement("config")))
            __return(false, LOG_TYPE_ERROR, "配置文件（config.xml）文件格式错误!(无法找到<config>)");
        if (!(pItem = pRoot->FirstChildElement("server")))
        {
            g_log->write_log(LOG_TYPE_ERROR, "config.xml配置文件server没有配置，请重新配置后启动!!!");
            return false;
        }

        m_server_net.DLLInit(SVRLIB_NAME, "_vm_server_instance", "_vm_server_free");
        if (!m_server_net.GetInterface())
            __return(false, LOG_TYPE_ERROR, "init m_client_net fail!ERROR:%s", m_server_net.GetError());

        tagVMServerInit init_data;
        init_data.server_type = VMSVRTYPE_ROOM;
        init_data.server_id = g_server_id;
        init_data.psink = this;
        init_data.block.recv_block = min_value(xml_attribute_int(pItem, "seck_buf_size"), 1024 * 8);
        init_data.block.send_block = init_data.block.recv_block;
        init_data.block.max_send_buf = min_value(xml_attribute_int(pItem, "snd_buf_size"), 1024 * 8);
        init_data.max_conn = 30000;
        init_data.heart.snd_heart_time_sec = min_value(xml_attribute_int(pItem, "heart_sec"), 0);
        init_data.heart.heart_time_out_sec = min_value(xml_attribute_int(pItem, "heart_timeout"), 20);
        if (init_data.heart.snd_heart_time_sec > 0)
        {
            SNETHEAD head;
            head.mcmd = cmd_net::CMD_SYS;
            head.scmd = cmd_net::CMD_SYS_HEART_ASK;
            init_data.heart.heart_data_size = sizeof(head);
            init_data.heart.p_heart_data = &head;
        }

        if (!m_server_net->init(&init_data, sizeof(tagVMServerInit)))
        {
            __return(false, LOG_TYPE_ERROR, "[NET] 网络模块初始化失败");
        }
    }

    // 解包类需要
    m_p_packet_attemper = g_apt.GetInterface();
    m_p_vm_server = m_server_net.GetInterface();

    load_gate();

    g_timer_mgr->set_random_timer(this, on_update_gate_timer, 1000 * 60);
    return m_server_net->start();
}

FTYPE(void)
CServerNet::on_update_gate_timer(void *obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{
    ((CServerNet *)obj)->load_gate();
}

bool CServerNet::stop()
{
    if (!m_server_net.GetInterface())
        return false;
    return m_server_net->stop();
}

int CServerNet::connect_count()
{
    return m_server_net->get_online_count();
}

int CServerNet::gate_count()
{
    return m_server_net->pipe_count();
}

// 发送给用户
bool CServerNet::send(socket_id sid, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    int len = 0;
    const void *pdata = __obj_to_buffer(data, &len);
    return send(sid, mcmd, scmd, (void *)pdata, len);
}

bool CServerNet::send(socket_id sid, _uint8 mcmd, _uint8 scmd, const void *data, int size)
{
    SNETHEAD head;
    head.size = sizeof(SNETHEAD) + size;
    head.mcmd = mcmd;
    head.scmd = scmd;
    g_log->write_log(LOG_TYPE_INFO, "[send] sid=%d mcmd=%d scmd=%d ds=%d", sid, mcmd, scmd, head.size);
    return m_server_net->send(sid, &head, sizeof(head), (void *)data, size);
}

// 发送http给用户
bool CServerNet::send(socket_id sid, const string &str)
{
    cmd_room::UserOpHttpRoomReq result;
	result.set_result(str.c_str(), str.length());
	
	return send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_OP_HTTP_ROOM_RESP, &result);
}

bool CServerNet::allow_batch_send(socket_id sid, bool ballow)
{
    return m_server_net->allow_batch_send(sid, ballow);
}

bool CServerNet::send_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    int len = 0;
    const void *pdata = __obj_to_buffer(data, &len);
    return send_batch(mcmd, scmd, (void *)pdata, len);
}

bool CServerNet::send_batch(_uint8 mcmd, _uint8 scmd, const void *data, int size)
{
    SNETHEAD head;
    head.size = sizeof(SNETHEAD) + size;
    head.mcmd = mcmd;
    head.scmd = scmd;
    g_log->write_log(LOG_TYPE_INFO, "[send_batch] mcmd=%d scmd=%d ds=%d", mcmd, scmd, head.size);
    m_server_net->send_batch(&head, sizeof(head), (void *)data, size);
    return true;
}

bool CServerNet::send_leave_user(_tint64 userID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    if (userID <= 0)
        return false;

    cmd_gate::CUserTransSection userTransSection;
    userTransSection.set_UserID(userID);
    userTransSection.set_mcmd(mcmd);
    userTransSection.set_scmd(scmd);
    userTransSection.set_SourServerID(g_server_id);

    char szBuffer[512] = {0};
    int bufferLen = 0;
    userTransSection.pack(szBuffer, sizeof(szBuffer), bufferLen);

    return send_gate_random(cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_TRANSF_USER, data, SVREXTYPE_TRANSF, szBuffer, bufferLen);
}

bool CServerNet::send_leave_user(_tint64 userID, _uint8 mcmd, _uint8 scmd, const void *data, int size)
{
    if (userID <= 0)
        return false;

    cmd_gate::CUserTransSection userTransSection;
    userTransSection.set_UserID(userID);
    userTransSection.set_mcmd(mcmd);
    userTransSection.set_scmd(scmd);
    userTransSection.set_SourServerID(g_server_id);

    char szBuffer[512] = {0};
    int bufferLen = 0;
    userTransSection.pack(szBuffer, sizeof(szBuffer), bufferLen);

    return send_gate_random(cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_TRANSF_USER, data, size, SVREXTYPE_TRANSF, szBuffer, bufferLen);
}

bool CServerNet::send_leave_user(socket_id pid, _tint64 userID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    cmd_gate::CUserTransSection userTransSection;
    userTransSection.set_UserID(userID);
    userTransSection.set_mcmd(mcmd);
    userTransSection.set_scmd(scmd);
    userTransSection.set_SourServerID(g_server_id);

    char szBuffer[512] = {0};
    int bufferLen = 0;
    userTransSection.pack(szBuffer, sizeof(szBuffer), bufferLen);

    return send_gate(pid, cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_TRANSF_USER, data, SVREXTYPE_TRANSF, szBuffer, bufferLen);
}

bool CServerNet::send_leave_user(socket_id pid, _tint64 userID, _uint8 mcmd, _uint8 scmd, const void *data, int size)
{
    cmd_gate::CUserTransSection userTransSection;
    userTransSection.set_UserID(userID);
    userTransSection.set_mcmd(mcmd);
    userTransSection.set_scmd(scmd);
    userTransSection.set_SourServerID(g_server_id);

    char szBuffer[512] = {0};
    int bufferLen = 0;
    userTransSection.pack(szBuffer, sizeof(szBuffer), bufferLen);

    return send_gate(pid, cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_TRANSF_USER, data, size, SVREXTYPE_TRANSF, szBuffer, bufferLen);
}

// 发送给一个网关消息(指定通道发送)
bool CServerNet::send_gate(socket_id pid, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data, SVREXTYPE extype, const void *exdata, int exsize, bool b_batch_route)
{
    int len = 0;
    const void *pdata = __obj_to_buffer(data, &len);
    return send_gate(pid, mcmd, scmd, (void *)pdata, len, extype, exdata, exsize, b_batch_route);
}

bool CServerNet::send_gate(socket_id pid, _uint8 mcmd, _uint8 scmd, const void *data, int size, SVREXTYPE extype, const void *exdata, int exsize, bool b_batch_route)
{
    SNETHEAD head;
    head.mcmd = mcmd;
    head.scmd = scmd;
    head.b_batch_snd = b_batch_route;

    g_log->write_log(LOG_TYPE_INFO, "[send_gate]pid=%d mcmd=%d scmd=%d ds=%d et=%d es=%d", pid, mcmd, scmd, head.size, extype, exsize);

    if (extype == SVREXTYPE_NONE || !exdata || exsize <= 0)
    {
        head.size = sizeof(SNETHEAD) + size;
        return m_server_net->pipe_send(pid, &head, sizeof(head), (void *)data, size);
    }
    else
    {
        head.ex_type = extype;
        head.ex_size = exsize;
        head.size = sizeof(SNETHEAD) + size + exsize;
        return m_server_net->pipe_send(pid, &head, sizeof(head), (void *)__add_buffer((void *)data, size, exdata, exsize), size + exsize);
    }
}

// 发送给一个网关消息(随机一个网关)
bool CServerNet::send_gate_random(_uint8 mcmd, _uint8 scmd, ITBFOBJBase *data, SVREXTYPE extype, const void *exdata, int exsize, bool b_batch_route)
{
    int len = 0;
    const void *pdata = __obj_to_buffer(data, &len);
    return send_gate_random(mcmd, scmd, (void *)pdata, len, extype, exdata, exsize, b_batch_route);
}

bool CServerNet::send_gate_random(_uint8 mcmd, _uint8 scmd, const void *data, int size, SVREXTYPE extype, const void *exdata, int exsize, bool b_batch_route)
{
    static _uint8 g_index = 0;
    socket_id pid = 0;

    int count = m_server_net->pipe_count();
    _uint32 nip = 0;
    _uint16 port = 0;

    if (1 == count)
    {
        m_server_net->pipe_enum(0, &nip, &port);
        m_server_net->pipe_pid(nip, port, pid);
    }
    else if (0 < count)
    {
        int i = 0;
        while (i++ < count && count > 0)
        {
            int index = g_index++;
            index %= count;
            bool p_b_online = false;
            if (!m_server_net->pipe_enum(index, &nip, &port, &p_b_online))
                continue;
            ;
            if (!p_b_online)
                continue;
            if (!m_server_net->pipe_pid(nip, port, pid))
                continue;
            ;
            break;
        }
    }

    return send_gate(pid, mcmd, scmd, data, size, extype, exdata, exsize, b_batch_route);
}

// 发送给网关消息(所有网关)
bool CServerNet::send_gate_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase *data, SVREXTYPE extype, const void *exdata, int exsize, bool b_batch_route)
{
    int len = 0;
    const void *pdata = __obj_to_buffer(data, &len);
    return send_gate_batch(mcmd, scmd, (void *)pdata, len, extype, exdata, exsize, b_batch_route);
}

bool CServerNet::send_gate_batch(_uint8 mcmd, _uint8 scmd, const void *data, int size, SVREXTYPE extype, const void *exdata, int exsize, bool b_batch_route)
{
    SNETHEAD head;
    head.mcmd = mcmd;
    head.scmd = scmd;
    head.b_batch_snd = b_batch_route;

    g_log->write_log(LOG_TYPE_INFO, "[send_gate_batch]mcmd=%d scmd=%d ds=%d et=%d es=%d", mcmd, scmd, head.size, extype, exsize);

    if (extype == SVREXTYPE_NONE || !exdata || exsize <= 0)
    {
        head.size = sizeof(SNETHEAD) + size;
        return m_server_net->pipe_send_batch(&head, sizeof(head), (void *)data, size);
    }
    else
    {
        head.ex_type = extype;
        head.ex_size = exsize;
        head.size = sizeof(SNETHEAD) + size + exsize;
        return m_server_net->pipe_send_batch(&head, sizeof(head), (void *)__add_buffer((void *)data, size, exdata, exsize), size + exsize);
    }
}

// 随机发个一个其他服务器（大厅 列表 任务 等）
bool CServerNet::send_route_server_random(_uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_NONE, 0, 0, false);
}

// 指定PID发个一个其他服务器（大厅 列表 任务 等）
bool CServerNet::send_route_server_by_pid(_uint64 pid, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    char buf[1024];
    cmd_gate::CRouteSection section;
    section.set_DestPSID(pid);
    int len = section.pack(buf, sizeof(buf));
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_ROUTE, buf, len, false);
}

// 群发其他服务器（大厅 列表 任务 等）
bool CServerNet::send_route_server_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_NONE, 0, 0, true);
}

// 指定PID发个一个VM服务器（房间 比赛 等）
bool CServerNet::send_route_vm_server_by_pid(_uint64 pid, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    char buf[1024];
    cmd_gate::CRouteSection section;
    section.set_DestPSID(pid);
    int len = section.pack(buf, sizeof(buf));
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_ROUTE, buf, len, false);
}

// 指定serverid发个一个VM服务器（房间 比赛 等）
bool CServerNet::send_route_vm_server_by_serverid(_uint64 serverid, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    char buf[1024];
    cmd_gate::CRouteSection section;
    section.set_DestServerID(serverid);
    int len = section.pack(buf, sizeof(buf));
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_ROUTE, buf, len, false);
}

// 指定用户ID发给用户所在的房间
bool CServerNet::send_route_vm_server_room(_uint64 userid, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    char buf[1024];
    cmd_gate::CRouteSection section;
    section.set_DestRoomUserID(userid);
    int len = section.pack(buf, sizeof(buf));
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_ROUTE, buf, len, false);
}

// 群发VM服务器（房间 比赛 等）
bool CServerNet::send_route_vm_server_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase *data)
{
    int dlen = 0;
    const void *pdata = __obj_to_buffer(data, &dlen);
    return send_gate_random(mcmd, scmd, pdata, dlen, SVREXTYPE_NONE, 0, 0, true);
}

socket_id CServerNet::gate_pid(socket_id sid)
{
    socket_id pid = 0;
    m_server_net->get_pipe_sid(sid, &pid, 0);
    return pid;
}

bool CServerNet::bind_user(socket_id sid, IUser *puser)
{
    _uint64 uid = 0;
    if (puser != NULL)
    {
        uid = puser->get_user_id();
    }
    return m_server_net->set_bind_data(sid, (void *)uid);
}

_uint32 CServerNet::net_ip(socket_id sid)
{
    _uint32 ip = 0;
    m_server_net->get_addr(sid, &ip, 0);
    return ip;
}

bool CServerNet::is_online(socket_id sid)
{
    return m_server_net->is_online(sid);
}

bool CServerNet::close(socket_id sid, int delay)
{
    delay = 0; // 暂时不需要延迟关闭
    return m_server_net->close(sid, delay);
}

FTYPE(void)
CServerNet::on_log(TLOGTYPE logtype, const char *sz_msg)
{
    g_log->write_log(logtype, "%s", sz_msg);
}

void CServerNet::on_attemper_pipe_connecting(socket_id pid, _uint32 ip, _uint32 port)
{
    ;
    _ipbfs ipbf;
    g_log->write_log(LOG_TYPE_PRINT, "[NET] 连接(pid=%d ip=%s port=%d) ......", pid, __tosip(ip, ipbf), port);
}

void CServerNet::on_attemper_pipe_connect_fail(socket_id pid, int code, _uint32 ip, _uint32 port)
{
    _ipbfs ipbf;
    g_log->write_log(LOG_TYPE_WARNING, "[NET] 连接(pid=%d ip=%s port=%d)失败！code=%d", pid, __tosip(ip, ipbf), port, code);
}

void CServerNet::on_attemper_pipe_connected(socket_id pid, _uint32 ip, _uint32 port)
{
    _ipbfs ipbf;
    g_log->write_log(LOG_TYPE_PRINT, "[NET] 连接(pid=%d ip=%s port=%d)成功", pid, __tosip(ip, ipbf), port);

    tagRoomConnectAction roomConnectAction(pid, m_disable_sync == 1);
    __post_action(ACTION_ROOM_CONNECT_GATE_SUCCEED, &roomConnectAction, sizeof(roomConnectAction));
}

void CServerNet::on_attemper_pipe_disconnected(socket_id pid, int code, int ext_code, _uint32 ip, _uint32 port)
{
    _ipbfs ipbf;
    g_log->write_log(LOG_TYPE_WARNING, "[NET] 网关(pid=%d ip=%s port=%d)连接断开！code=%d excode=%d", pid, __tosip(ip, ipbf), port, code, ext_code);
}

void CServerNet::on_attemper_pipe_speed(socket_id pid, _uint32 time_msec, _uint32 ip, _uint32 port)
{
    _ipbfs ipbf;
    if (time_msec >= 200)
        g_log->write_log(LOG_TYPE_WARNING, "[NET] 网关(ip=%s port=%d)时延=%d毫秒", __tosip(ip, ipbf), port, time_msec);
}

void CServerNet::on_attemper_pipe_recv(socket_id pid, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data)
{
    g_log->write_log(LOG_TYPE_INFO, "[NET] gate recv: cmd(%d,%d) len=%d pid=%d et=%d es=%d", phead->mcmd, phead->scmd, dsize, pid, phead->ex_type, phead->ex_size);

    _uint32 t0 = g_com->_get_tick_count();
    if (g_gate_rcv && g_gate_rcv->list[phead->mcmd][phead->scmd].sink)
    {
        GATERCV(g_gate_rcv->list[phead->mcmd][phead->scmd].sink)
        (pid, phead, (void *)pdata, dsize, ex_data);
    }
    else if (g_gate_rcv && g_gate_rcv->list[phead->mcmd][0].sink)
    {
        GATERCV(g_gate_rcv->list[phead->mcmd][0].sink)
        (pid, phead, (void *)pdata, dsize, ex_data);
    }
    else
    { // 未注册协议
        g_log->write_log(LOG_TYPE_WARNING, "[NET] recv gate cmd(%d,%d) nonsupport!", phead->mcmd, phead->scmd);
    }
    _uint32 t = g_com->_cmp_tick_count(g_com->_get_tick_count(), t0);
    if (t >= 10)
        g_log->write_log(LOG_TYPE_WARNING, "[NET] gate recv: cmd(%d,%d) len=%d pid=%d et=%d es=%d 处理耗时=%d",
                         phead->mcmd, phead->scmd, dsize, pid, phead->ex_type, phead->ex_size, t);
}

void CServerNet::on_attemper_vm_connected(socket_id sid)
{
    _uint32 ip = 0;
    _ipbfs ipbf;
    m_server_net->get_addr(sid, &ip, 0);
    g_log->write_log(LOG_TYPE_DEBUG, "[NET] sid=%d ip=%s connect!", sid, __tosip(ip, ipbf));
}

void CServerNet::on_attemper_vm_disconnected(socket_id sid, int code, int ext_code, _uint32 ip, _uint64 bind_tag, const void *p_bind)
{
    _ipbfs ipbf;
    g_log->write_log(LOG_TYPE_DEBUG, "[NET] sid=%d  ip=%s disconnect! code=%d extcode=%d", sid, __tosip(ip, ipbf), code, ext_code);

    if (p_bind)
    {
        // IUser * pUser = (IUser*)p_bind;
        tagUserAction userAction((_tint64)p_bind);
        __post_action(ACTION_USER_DISCONNECTED, &userAction, sizeof(userAction));
    }
}

void CServerNet::on_attemper_vm_recv(socket_id sid, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data, _uint64 bind_tag, const void *p_bind)
{
    if (!p_bind)
        m_server_net->get_bind_data(sid, (void *&)p_bind);

    if (!(cmd_net::CMD_SYS == phead->mcmd && (cmd_net::CMD_SYS_HEART_ASK == phead->scmd || cmd_net::CMD_SYS_HEART_ACK == phead->scmd)))
        g_log->write_log(LOG_TYPE_INFO, "[NET] user recv: cmd(%d,%d) len=%d sid=%d et=%d es=%d", phead->mcmd, phead->scmd, dsize, sid, phead->ex_type, phead->ex_size);
    else
        return;

    _uint32 t0 = g_com->_get_tick_count();
    // CTimeLimitMS limit_ms(10, "User recv command deal");
    if (g_user_rcv && g_user_rcv->list[phead->mcmd][phead->scmd].sink)
    {
        USERRCV(g_user_rcv->list[phead->mcmd][phead->scmd].sink)
        (sid, (_uint64)p_bind, phead, (void *)pdata, dsize, ex_data);
    }
    else if (g_user_rcv && g_user_rcv->list[phead->mcmd][0].sink)
    {
        USERRCV(g_user_rcv->list[phead->mcmd][0].sink)
        (sid, (_uint64)p_bind, phead, (void *)pdata, dsize, ex_data);
    }
    else
    { // 未注册协议
        g_log->write_log(LOG_TYPE_WARNING, "[NET] user cmd(%d,%d) nonsupport!", phead->mcmd, phead->scmd);
    }

    _uint32 t = g_com->_cmp_tick_count(g_com->_get_tick_count(), t0);
    if (t >= 10)
        g_log->write_log(LOG_TYPE_WARNING, "[NET] user recv: cmd(%d,%d) len=%d sid=%d et=%d es=%d 处理耗时=%d",
                         phead->mcmd, phead->scmd, dsize, sid, phead->ex_type, phead->ex_size, t);
}

FTYPE(void)
CServerNet::on_vm_recv(socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
{
    if (size < sizeof(SNETHEAD))
        return;
    SNETHEAD *phead = (SNETHEAD *)((char *)p_data);
    if (cmd_net::CMD_ROOM == phead->mcmd && cmd_net::SUB_ROOM_PING_REQ == phead->scmd)
    {
        _uint64 last_send_t = 0;
        // 防止引起网络堵塞
        time_t tnow = time(0);
        if (m_server_net->get_bind_tag(sid, last_send_t) && tnow - last_send_t < 4)
            return;

        size -= sizeof(SNETHEAD);
        if (phead->ex_size > (_uint32)size)
            return;
        size -= phead->ex_size;
        void *data = size > 0 ? (char *)p_data + sizeof(SNETHEAD) : 0;
        void *ex_data = phead->ex_size ? (char *)p_data + sizeof(SNETHEAD) + size : 0;

        // 客户端到房间心跳
        cmd_room::RoomPingReq req(data, size);
        // g_log->write_log(LOG_TYPE_INFO, "[NET] on_vm_recv room_ping_req sid:%u pingt:%lld size:%d extsize:%d", sid,req.get_pingt(),size, phead->ex_size);
        cmd_room::RoomPongResp resp;
        resp.set_pingt(req.get_pingt());
        resp.set_servert(tnow);
        send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_ROOM_PONG_RESP, &resp);
        m_server_net->set_bind_tag(sid, tnow);
        return;
    }

    WLPARAM param;
    param.obj = this;
    param.tag = bind_tag;
    param.wparam = (void *)p_bind;
    _safe_set_pointer_i(param.lparam, sid);
    m_p_packet_attemper->post((FAttemperSink)on_atp_vm_recv, (void *)p_data, size, 0, 0, 0, 0, &param);
}

bool CServerNet::send_http_json(socket_id sid, std::map<std::string, std::string> http_headers_map, const void* data, size_t size) {

    size_t hlen = 0;
    char headers[1024 * 8] = {0};

    for (auto iter = http_headers_map.begin(); iter != http_headers_map.end(); iter++) {
        hlen += __sprintf(headers + hlen, sizeof(headers) - hlen, "%s: %s\r\n", iter->first.c_str(), iter->second.c_str());
    }

    hlen += __sprintf(headers + hlen, sizeof(headers) - hlen, "HTTP/1.1 200 OK\r\n");
    hlen += __sprintf(headers + hlen, sizeof(headers) - hlen, "Content-type: application/json\r\n");
    hlen += __sprintf(headers + hlen, sizeof(headers) - hlen, "Connection: keep-alive\r\n");
    hlen += __sprintf(headers + hlen, sizeof(headers) - hlen, "Content-Length: %d\r\n\r\n", size);

    return m_server_net->send(sid, headers, hlen, const_cast<void*>(data), size);
}

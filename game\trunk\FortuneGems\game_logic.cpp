#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
using json = nlohmann::json;
user_game_info::user_game_info()
{
    uid = 0;
    time = 0;
    result = 0;
    pay_out = 0;
    _last_kick_user_offline_tm_sec = 0;
    user_type = 2;
    client_id = 0;
	sub_client_id = 0;
    web_token = "";
    token = "";
    reset();
}

game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

void game_logic::gen_no_reward_game_data(user_game_info &game_info)
{
    game_info.muter = 0;
    const int wayInfos[MAX_WAYS][3] =
    {
        {3, 4, 5},
        {0, 1, 2},
        {6, 7, 8},
        {0, 4, 8},
        {2, 4, 6},
    };

    int weight[8] = {100,90,80,70,60,50,30,15};    
	int total = 510;
	int size = 8;   

    for(int kk = 0; kk<10000; kk++)
	{
        for (int n = 0; n < 9; n++)
        {
            int r = getRand(0, total - 1);
            for (int i = 0; i < size; i++)
            {
                if (r <= weight[i])
                {
                    game_info.plate_data[n] = i;
                    break;
                }
                else
                {
                    r -= weight[i];
                }
            }
        }
        {
            int weight[6] = {60, 40, 30, 20, 10, 5};
            int total = 165;
            int size = 6;
            if(game_info.cur_mode == 1)
            {
                weight[0] = 0;
                total = 105;
            }
            int r = getRand(1, total);
            for (int i = 0; i < size; i++)
            {
                if (r <= weight[i])
                {
                    game_info.plate_data[9] = i + BOX_X1-1;
                    break;
                }
                else
                {
                    r -= weight[i];
                }
            }

            int vb = game_info.plate_data[9] - BOX_X1+1;
            const int bs_multer[6] = {1, 2, 3, 5, 10, 15};
            game_info.bs = bs_multer[vb];
        }

        bool flag1 = true;
        for (int i = 0; i < MAX_WAYS; i++)
        {
            int v1 = -1;
            bool flag = false;

            for (int j = 0; j < 3; j++)
            {
                int v2 = wayInfos[i][j];
                if (game_info.plate_data[v2] != BOX_WILD)
                {
                    if (v1 < 0)
                    {
                        v1 = game_info.plate_data[v2];
                    }
                    else
                    {
                        if (v1 != game_info.plate_data[v2])
                        {
                            flag = true;
                            break;
                        }
                    }
                }
            }

            if(!flag && v1>=0)
            {
                flag1 = false;
                break;
            }
        }

        if(flag1)
        {
            break;
        }
    }
}
bool game_logic::gen_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();
    int n = 0;
    for(auto& d:data[0])
    {
        game_info.plate_data[n++] = d;
    }
    if(n < 10)
    {
        MY_LOG_ERROR("parse graph data error");
        return false;
    }

    const int wayInfos[MAX_WAYS][3] =
    {
        {3, 4, 5},
        {0, 1, 2},
        {6, 7, 8},
        {0, 4, 8},
        {2, 4, 6},
    };
    const int multer[8] = {
        40,
        100,
        160,
        200,
        240,
        300,
        400,
        500,
    };
    int vb = game_info.plate_data[9]-BOX_X1+1;
    const int bs_multer[6] = {1,2,3,5,10,15};    
    game_info.bs = bs_multer[vb];

    for(int i=0; i<MAX_WAYS; i++)
    {
        int v1 = -1;
        bool flag = false;

        for(int j=0; j<3;j++)
        {
            int v2 = wayInfos[i][j];
            if(game_info.plate_data[v2] != BOX_WILD)
            {
                if(v1 < 0)
                {
                    v1 = game_info.plate_data[v2];
                }
                else
                {
                    if(v1 != game_info.plate_data[v2])
                    {
                        flag = true;
                        break;
                    }
                }
            }
        }

        if(!flag)
        {
            award aw;
            aw.line = i;
            
            if(v1 < 0)
            {
                aw.symbol = BOX_WILD;                
            }
            else
            {
                aw.symbol = v1;
            }
            if(game_info.cur_mode == 1)
                aw.win = multer[aw.symbol]*game_info.bs*(game_info.bet/1.5);
            else
                aw.win = multer[aw.symbol]*game_info.bs*game_info.bet;

            game_info.vec_award.push_back(aw);
        }        
    }

    // for(auto& aw:game_info.vec_award)
    // {
    //     MY_LOG_PRINT("award s=%d,line=%d,win=%d", aw.symbol,aw.line,aw.win);
    // }

    return true;
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::check_card(user_game_info &game_info)
{
   
}

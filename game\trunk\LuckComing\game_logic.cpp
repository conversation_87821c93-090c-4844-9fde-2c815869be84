#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
#include <algorithm>
using json = nlohmann::json;


game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

int game_logic::CalcResultTimes(user_game_info &game_info)
{   
	game_info.result = 0;
	game_info.award_type = -1;
	int mul = game_info.bet / 100;
	int iconCount[9 + 1] = { 0 }; //统计图标个数
	int all_icon = 0;  //中间行图标个数
	for (int i = 0; i < 3; i++)
	{
		if (game_info.Icons[i] == 0)
		{
			continue;
		}
		++iconCount[game_info.Icons[i]];
		all_icon++;
	}
	if (all_icon < 3)  //少于三个，没中奖
	{
		return 0;
	}
	//判断是否是3个wild
	int nWildCount = iconCount[ICON_WILD] + iconCount[ICON_WILD_X3] + iconCount[ICON_WILD_X5] + iconCount[ICON_WILD_X9];
	if (nWildCount == 3)
	{
		game_info.plate_win = odd_config[ICON_WILD];
		game_info.result = game_info.plate_win;
		game_info.award_type = ICON_WILD;
        game_info.Icons[0] = ICON_WILD;
		game_info.Icons[1] = ICON_WILD;
		game_info.Icons[2] = ICON_WILD;
		return game_info.result;
	}
	int nMul = 0;
	nMul += iconCount[ICON_WILD_X3] * 3;
	nMul += iconCount[ICON_WILD_X5] * 5;
	nMul += iconCount[ICON_WILD_X9] * 9;
	if (nMul == 0)
	{
		nMul = 1;
	}
	//先判断是否是三个相等
	bool threeSame = false;
	for (int i = 1; i <= ICON_FLOWER_YELLOW; i++)
	{
		if ((iconCount[i] + nWildCount) == 3)
		{
			game_info.plate_win = odd_config[i] * nMul;
			threeSame = true;
			game_info.award_type = i;
		}
	}
	if (!threeSame) //三个不相同的
	{
		
		game_info.plate_win = odd_config[0] * nMul;
		game_info.award_type = 0;
	

	}
	game_info.plate_win = game_info.plate_win * mul;
	game_info.result = game_info.plate_win;
	return game_info.result;
	
}


_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::RandFillIconNoWin(int Icon[3])
{  
	int vec_zero_count_probability[3] = { 1000,400, 100 };
	int ntotal = 0;
	for (int i = 0; i < 3; i++)
	{
		ntotal += vec_zero_count_probability[i];
	}
	int nZeroCount = 0;
	int nRand = getRand(0, ntotal);
	for (int i = 0; i < 3; i++)
	{
		if (nRand <= vec_zero_count_probability[i])
		{
			nZeroCount = i + 1;
		}
		else
		{
			nRand -= vec_zero_count_probability[i];
		}
	}
	if (nZeroCount == 0)
	{
		nZeroCount = 1;
	}
	std::vector<int> vec_index = { 0,1, 2 };
	random_shuffle(vec_index.begin(), vec_index.end());
	if (nZeroCount < 3)
	{
		vec_index.erase(vec_index.begin() + nZeroCount, vec_index.end());
	}
	for (int i = 0; i < vec_index.size(); i++)
	{  
		int nIndex = vec_index[i];
		Icon[nIndex] = -1;
	}
	for (int i = 0; i < 3; i++)
	{  
		if (Icon[i] == -1)
		{  
			Icon[i] = 0;
			continue;
		}
		else
		{
			Icon[i] = getRand(ICON_FLOWER_RED, ICON_WILD);
		}
	}
}

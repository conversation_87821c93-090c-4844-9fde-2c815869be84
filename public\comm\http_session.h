#ifndef __PUBLIC_COMMON_HTTP_SESSION__
#define __PUBLIC_COMMON_HTTP_SESSION__

#include "typedef.h"

#include <string>
#include <map>

namespace tnet {

class HttpSession {

public:
    HttpSession(svrlib::socket_id sid, const std::string& path);
    ~HttpSession();

    svrlib::socket_id sid() { return sid_; }
    std::string path() { return path_; }

    bool is_enable_keepalive() { return keepalive_; }

private:
    svrlib::socket_id sid_;
    std::string path_;
    bool keepalive_;
};

class HttpSessionManager {

public:
    HttpSessionManager();
    virtual ~HttpSessionManager();

    int add_session(svrlib::socket_id, HttpSession*);
    int del_session(svrlib::socket_id);
    HttpSession *get_session(svrlib::socket_id);

private:
    std::map<svrlib::socket_id, HttpSession*> sessions_;
};

}

#endif

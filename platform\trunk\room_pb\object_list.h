﻿/***********************************************************
 * File Name          :       object_list.h
 * Author             :       原海洋
 * Version            :       1.0
 * Date               :       2015-08-28 16:34
 * Description        :       对象列表模板
***********************************************************/

#ifndef _OBJECT_LIST_H_
#define _OBJECT_LIST_H_

#include "room_common.h"
#include "object_pool.h"
#include <queue>
#include <set>

template<typename OBJECTTYPE>
class CObjectList
{
public:
    CObjectList();
    virtual ~CObjectList();

public:
    /**
     *  @brief: 初始化数据
     *  @count: IN 列表最大对象数，对象ID从1开始
     *  @return: 成功返回true, 失败返回false
    **/
    bool init(int count);
    /**
     *  @brief: 添加对象
     *  @obj: IN 对象
     *  @return: 成功返回对象ID, 失败返回0
    **/
    int add_object(OBJECTTYPE obj);
    /**
     *  @brief: 删除对象
     *  @objectID: IN 对象ID
     *  @return: 成功返回true, 失败返回false
    **/
    bool delete_object(int objectID);
    /**
     *  @brief: 通过ID获得对象
     *  @objectID: IN 对象ID
     *  @obj: OUT 返回对象
     *  @return: 成功返回true, 失败返回false
    **/
    bool get_object(int objectID, OBJECTTYPE& obj);
    /**
     *  @brief: 通过索引获得对象
     *  @index: IN 对象索引
     *  @obj: OUT 返回对象
     *  @return:成功返回true, 失败返回false
    **/
    bool get_index_object(int index, OBJECTTYPE& obj);
    /**
     *  @brief: 获得对象数量
     *  @return: 返回对象数量
    **/
    int object_count();
    /**
     *  @brief: 获得空闲对象数量
     *  @return: 返回空闲对象数量
    **/
    int free_count();
    /**
     *  @brief: 清除列表数据
     *  @return: 无返回值
    **/
    void clear();

private:
    key_vector<int, OBJECTTYPE>     m_object_list;                      //对象列表
    std::queue<int>                 m_object_id_list;                   //对象ID列表，从1开始
    std::set<int>                   m_sole_id_list;                     //对象ID列表，防止重复ID
};

template<typename OBJECTTYPE>
CObjectList<OBJECTTYPE>::CObjectList()
{

}

template<typename OBJECTTYPE>
CObjectList<OBJECTTYPE>::~CObjectList()
{

}

template<typename OBJECTTYPE>
bool CObjectList<OBJECTTYPE>::init(int count)
{
    for (int i = 1; i <= count; ++i)
    {
        if(m_sole_id_list.insert(i).second)
        {
            m_object_id_list.push(i);
        }
    }

	return true;
}

template<typename OBJECTTYPE>
int CObjectList<OBJECTTYPE>::add_object(OBJECTTYPE obj)
{
    if (m_object_id_list.empty()) return 0;
    int objectID = m_object_id_list.front();
    m_object_id_list.pop();
    m_sole_id_list.erase(objectID);
    m_object_list.add(objectID, obj);
    return objectID;
}

template<typename OBJECTTYPE>
bool CObjectList<OBJECTTYPE>::delete_object(int objectID)
{
    if (m_object_list.exists(objectID))
    {
        if (m_sole_id_list.insert(objectID).second)
        {
            m_object_list.remove_key(objectID);
            m_object_id_list.push(objectID);
            return true;
        }
    }

    return false;
}

template<typename OBJECTTYPE>
bool CObjectList<OBJECTTYPE>::get_object(int objectID, OBJECTTYPE& obj)
{
    if (objectID < 0) return false;
    m_object_list.get_key_data(objectID, obj);
    return true;
}

template<typename OBJECTTYPE>
bool CObjectList<OBJECTTYPE>::get_index_object(int index, OBJECTTYPE& obj)
{
    m_object_list.get_index_data(index, obj);
    return true;
}

template<typename OBJECTTYPE>
int CObjectList<OBJECTTYPE>::object_count()
{
    return m_object_list.count();
}

template<typename OBJECTTYPE>
int CObjectList<OBJECTTYPE>::free_count()
{
    return m_object_id_list.size();
}

template<typename OBJECTTYPE>
void CObjectList<OBJECTTYPE>::clear()
{
    m_object_list.clear();
    m_sole_id_list.clear();
    while (!m_object_id_list.empty())
    {
        m_object_id_list.pop();
    }
}

#endif

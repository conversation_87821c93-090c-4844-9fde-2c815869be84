#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_CONFIG_COUNT 8       	//转盘中奖分布

#define  MAX_ROW  4       //行数
#define  MAX_LIST 5       //列数
#define  REMOVE_LINE  3

/* 牌面值 */
enum EMUN_MC_JL_CRAD
{
	BOX_SCATTER = 1,//bouns
	BOX_A = 2,// A
	BOX_K = 3,//K
	BOX_Q = 4,//Q
	BOX_J = 5,//J
	BOX_HEI = 6,//黑桃
	BOX_HONG = 7,//红桃
	BOX_MEI = 8, //梅花
	BOX_FANG = 9, //方块
	BOX_WILD = 0,
};

const int whell_config[MAX_BET_CONFIG_COUNT][WHEEL_CONFIG_COUNT] = {
	{0, 0, 0, 0, 0, 0, 0, 0},							 // 1
	{25, 50, 75, 100, 150, 250, 500, 1000},				 // 5
	{50, 100, 150, 200, 300, 500, 1000, 2000},			 // 10
	{500, 1000, 1500, 2000, 3000, 5000, 10000, 20000},	 // 50
	{1000, 2000, 3000, 4000, 6000, 10000, 20000, 40000}, // 100
};
const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
	1, 2, 4, 6, 10, 20, 40, 60, 80, 100, 160, 200, 400, 1000, 2000
};

struct card
{
    int value;
    int status;		//0-正常 1-将被消除	
	int is_gold;    //0非金色 1金色	
	bool is_change;
    card()
    {
        value = 0;
        status = 0;
		is_gold = 0;
		is_change = false;
    }
};

struct fillInfo
{
    int value;
	int x;
	int y;
	int is_gold;
	int last;
    fillInfo()
    {
        value = 0;
		x = 0;
		y = 0;
		is_gold = 0;
		last = 0;
    }
};

struct pos
{
	int x;
	int y;
    pos()
    {
		x = 0;
		y = 0;
    }
};

// 卡牌赔率 *100之后的
const int card_multer[8][3] = 
{
    {50,150,250},
    {40,120,200},
    {30,90,150},
	{20,60,100},
    {10,30,50},
    {10,30,50},
	{5,15,25},
    {5,15,25},
};

const int combo_bonus[4] =
{
	1,2,3,5
};
struct reward
{
	int value;		//牌值
	int count;		//轴数
	int multer;		//倍率
	int LineNum;    //线数  
	vector<pos> vec_pos; 	 
	reward(int value, int count, int LineNum, int multer)
	{
		this->value = value;
		this->count = count;
		this->multer = multer;
		this->LineNum = LineNum;
	}
	reward()
	{
		value = 0;
		count = 0;
		multer = 0;
		LineNum = 0;
		vec_pos.clear();
	}
};

struct remove_info
{
	vector<reward> vec_reward;		//消除奖励
	vector<card> vec_now_box[MAX_LIST];//当前格子数据
	vector<fillInfo> vec_fill_box[MAX_LIST];//当前填充格子数据
	int comboBonus;
	int multer;
	remove_info()
	{
		vec_reward.clear();
		for (int i=0; i<MAX_LIST;i++)
		{
			vec_now_box[i].clear();
		}
		comboBonus = 1;
		multer = 0;
	}
	// remove_info(const remove_info &rmInfo)
	// {
	// 	this->vec_reward.clear();
	// 	for (auto &r : rmInfo.vec_reward)
	// 		this->vec_reward.push_back(r);

	// 	for (int i = 0; i < MAX_LIST;i++)
	// 	{
	// 		for (auto &c : rmInfo.vec_now_box[i])
	// 		{
	// 			this->vec_now_box[i].push_back(c);
	// 		}
	// 		for (auto &c : rmInfo.vec_fill_box[i])
	// 		{
	// 			this->vec_fill_box[i].push_back(c);
	// 		}
	// 	}
	// 	this->comboBonus = rmInfo.comboBonus;
	// 	this->multer = rmInfo.multer;
	// }
	void save_box_info(vector<card> vec_box[MAX_LIST])
	{
		for (int i = 0; i < MAX_LIST;i++)
		{
			vec_now_box[i].clear();
			for (auto &c:vec_box[i])
			{
				vec_now_box[i].push_back(c);
			}
		}
	}	
};
typedef vector<remove_info> vec_remove_info;
struct graph_data
{
	int scatter_count;
	int total_multer;
	int now_free_times;
	bool is_free;
	bool touch_free;
	vec_remove_info vec_remove;
	graph_data()
	{
		scatter_count = 0;
		total_multer = 0;
		now_free_times = 0;
		is_free = false;
		touch_free = false;
	}
};


struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式
	int mall_bet;
	int base_bet;
	int comboBonus;
	int all_total_multer; //freegame加上触发总赢
	int total_multer; // 当前总倍数	
	bool is_free;//是否在freegame中
	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

    vector<card> vec_now_box[MAX_LIST]; // 当前格子数据
	vec_remove_info vec_remove; //消除信息
	vector<int> graph_data;
	int g_index;
	vector<vec_remove_info> game_vec_remove; //总的消除信息
	vector<int> game_multer; //总的消除信息
	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info();
	void reset() 
	{
		graph_data.clear();
		for (int i = 0; i < MAX_LIST; i++)
        {
            vec_now_box[i].clear();
        }
		vec_remove.clear();
		comboBonus = 1;
		result = 0;
        tax = 0;
        pay_out = 0; 
		record_id = 0;
		total_multer = 0;
		g_index = 0;
	}

	void clear() 
	{
		result = 0;
        pay_out = 0;
	}
};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
    void init();
	// 洗牌
    void rand_card(user_game_info &game_info);
	// 获得随机扑克
	card get_rand_card(user_game_info &gameinfo);
	card get_rand_card_diff(const map<int, int>& map_card_count);
	//检查牌
	void check_card(user_game_info &game_info);
	void check_card_ex(user_game_info &game_info);

	// 检测牌获奖情况
    bool remove_card(remove_info& reInfo, user_game_info &game_info);

	 //填充被消除格子
    void fill_remove_box(remove_info &reInfo, user_game_info &game_info);
	void fill_remove_box_ex(remove_info &reInfo, user_game_info &game_info);
	void insert_scatter(vec_remove_info& vec_remove, int num);
	//从图形库获取提取数据
	bool init_static_graph_data_reply(const string& grah);
	void gen_no_reward_game_data(user_game_info &game_info);
	bool gen_game_data_from_graph_data(user_game_info &game_info, const string& graph);

	void print_data(vector<card> vec_now_box[MAX_LIST]);
	_uint64 gen_new_round_index();


private:
	
};

#endif

#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_ODDS_CONFIG_COUNT 11       	//转盘中奖分布
#define ARRAY_1             3
#define ARRAY_2             3
/* 牌面值 */
enum enumIcon
{
	ICON_FLOWER_RED = 1,
	ICON_FLOWER_SILVERY= 2,
	ICON_FLOWER_YELLOW = 3,
	ICON_WILD = 4,
	ICON_WILD_X3 = 5,
	ICON_WILD_X5 = 6,
	ICON_WILD_X9 = 7,
};


//const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
//	6, 12, 30, 60, 90, 150, 300, 450, 900, 2100, 3000, 4500, 6000, 9600, 19200
//};
//单位元
const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
	1, 2, 3, 5, 8, 10, 20, 50, 100, 200, 300, 500, 800, 1000, 2000
};

//单位分
const int odd_config[5] = { 100, 500, 2100, 11100, 111100 };

struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int status; // 游戏状态 1-已经初始化金币 0-未初始化
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式 1-15
	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

	int Icons[3];
	int award_type;
	int plate_win;
	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info()
	{
		plate_win = 0;
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		record_id = 0;
		award_type = 0;
		memset(Icons, 0, sizeof(Icons));
	}
	void reset() 
	{
		plate_win = 0;
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		award_type = 0;
		memset(Icons, 0, sizeof(Icons));
	}

};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
	static game_logic *GetInstance()
	{
		static game_logic instance;
		return &instance;
	}
    void init();

	_uint64 gen_new_round_index();

	/*计算连线 tempIcons图标位置 vec_line返回可组成连线的数据*/
	int CalcResultTimes(user_game_info &game_info);
	/*随机一个不中奖的结果*/
	void RandFillIconNoWin(int Icon[3]);
private:
	
};

#endif

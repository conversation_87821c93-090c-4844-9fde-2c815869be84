﻿

#ifndef _GAME_FRAME_H_
#define _GAME_FRAME_H_

#include "room_common.h"
#include "dll.h"

class CGameFrame 
{
public:
    static CGameFrame *GetInstance()
    {
        static CGameFrame stGame;
        return &stGame;
    }

protected:
    CGameFrame();
    virtual ~CGameFrame();

public:
    /**
     *  @brief: 初始化游戏模块     
     *  @roomID: IN 房间ID
     *  @game_dll_name IN 
     *  @return: 初始化成功返回true, 失败返回false
    **/
    int init(int room_id,const char *game_dll_name); 

	//该方法用于web刷新游戏配置时使用
	int init_ex(const char * game_config_xml);

	//获得配置名称
	char* get_config_name(){return m_conf_name;}

	//int get_game_id();

    int get_room_id()
    {
        return g_server_id;
    }

	int get_max_player();

	void set_room_status(_uint8 status)
	{
		m_room_status = status;
	}

	_uint8 get_room_status()
	{
		return m_room_status;
	}

	void set_game_rule(void* rule)
	{
		if(g_pstGameConfig) 
		{
			g_pstGameConfig->set_rule(rule);
		}
	}

    /**
     *  @brief: 接收网络数据
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 网络数据
     *  @size: IN 数据大小
     *  @pUser: IN 用户指针
     *  @return: 处理成功返回true, 失败返回false
    **/
    bool on_recv(_uint8 mcmd, _uint8 scmd, const void* pData, int size, IUser* pUser);

	/* 创建房间：*/
	int on_user_create(IUser *pUser, _uint32 room_num, void *rule, int rule_len = 0);

	/* web创建房间 */
	int on_web_create(int uid, _uint32 room_num, void *rule, int rule_len = 0);

    /* 通过房间号进入：创建/进入 */
    int on_user_enter(IUser *pUser, _uint32 room_num, bool is_observe=false);

    /* 用户进入：断线重回 */
    int on_user_enter(_uint64 uid, bool is_observe=false);

    /* 用户断线 */
    int on_user_offline(IUser *user);

    /* 解散桌子：强制解散 */
    int on_free_table(_uint32 room_num);

    /* 发送通用消息给客户端：code参考cmd_room.xml中COMM_MESSAGE_CODE */
	int send_comm_message(_uint64 uid, int code, const char *msg, int type=cmd_room::COMM_TIPS_TEXT);
	int send_comm_message_ex(_uint64 uid, int code, int msgid = 0, int type=cmd_room::COMM_TIPS_TEXT);

	//发送动作结果
	void send_action_result(_uint64 uid, int result, int action);

    //// test code
    //int on_user_leave(IUser *user)
    //{
    //    m_pUser = user;
    //    return on_leave();
    //}    
    
    IGameConfig *get_config();
	//int set_xml_config_game(const char* xml_config_game);

	int change_table(_uint64 uid=0);

private:
    int on_cmd_room(_uint8 scmd, const void *pdata, int size, IUser *pUser);

    int on_cmd_game(_uint8 scmd, const void *pdata, int size, IUser *pUser);    

    int on_table_msg();

    int on_table_action(_uint8 type);

    int on_user_action();    

    int on_leave();
	void read_game_config();
	void reset_user_win_gold(TiXmlElement* pXmlItem);
	void reset_user_lose_gold(TiXmlElement* pXmlItem);

    static void on_timer(void*obj,_uint32 timerid,_uint64 tag,_uint32 Elapse,_uint32 iRepeat,_uint32 delay);

private:
    int         m_room_id;
	char		m_conf_name[255];
	int			m_game_id;
	_uint8		m_room_status;

    IUser      *m_pUser;    
    _uint8      m_scmd;
    const void *m_recv_data;
    int         m_recv_len;
	//char		m_xml_config_game[GAME_CONFIG_XML_MAX_LEN];


    DLLInstance                    *m_dll_instance;
    CDLLHelper<DLLInstance>         m_game_instance;    
};

#endif

@echo off
echo GoldenBank 测试单元运行脚本
echo ================================

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请确保已安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译简化测试...
g++ -std=c++11 -Wall -g -O0 -I. -I../../../public -I../../../public/comm simple_test.cpp game_logic.cpp game_config.cpp -o simple_test.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！正在运行测试...
echo.

simple_test.exe

echo.
echo 测试完成！
pause

#pragma once
#define MAX_PPTR_BUFF_SIZE  4096
#define MAX_PPTR_COUNT      200
class  make_buf_pptr
{
public:
    make_buf_pptr()
    {
        m_len = 0;
        m_count = 0;
    }
    virtual ~make_buf_pptr()
    {
    }
    void add_pptr(const char* pname)
    {
        m_ppt[m_count] = &m_szbuff[m_len];
        strcpy(&m_szbuff[m_len], pname);
        m_len += strlen(pname) + 1;
        m_count++;
        m_ppt[m_count] = nullptr;

    }
    void add_pptr(const char* pkey, const char* pvalue)
    {
        add_pptr(pkey);
        add_pptr(pvalue);
    }
    void add_pptr(const char* pkey, int value)
    {
        add_pptr(pkey);
        char szvalue[20];
        sprintf(szvalue, "%d", value);
        add_pptr((const char*)szvalue);
    }
    void add_pptr(const char* pkey, long long value)
    {
        add_pptr(pkey);
        char szvalue[20];
        sprintf(szvalue, "%lld", value);
        add_pptr((const char*)szvalue);
    }
    const char** get_pptr() { return m_ppt; }
    int   get_pptr_count() { return m_count; }
private:
    int  m_len;
    char m_szbuff[MAX_PPTR_BUFF_SIZE];
    const char* m_ppt[MAX_PPTR_COUNT];
    int  m_count;
};
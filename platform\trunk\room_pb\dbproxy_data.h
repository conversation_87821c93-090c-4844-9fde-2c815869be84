﻿#ifndef _DBPROXY_DATA_H_
#define _DBPROXY_DATA_H_

#include "room_common.h"

struct tagUserBaseInfo
{
    _uint64    userid;  
    _uint64    guserid;    
    _uint32    user_type;      
    _uint32    gender; 
    char       user_name[33];
    char       nick_name[33];
    char       pass_word[33];
    _uint32    face_id;
    _uint32    vcoin;
    _uint32    banned_time;

    tagUserBaseInfo()
    {
        userid = 0;
        guserid = 0;
        user_type = 0;
        gender = 0;
        memset(user_name,0,sizeof(user_name));
        memset(nick_name,0,sizeof(nick_name));
        memset(pass_word,0,sizeof(pass_word));
        face_id = 0;
        vcoin = 0;
        banned_time = 0;
    }
};

struct tagUserGameInfo
{
    _uint64     userid;  
    _uint32     experience;    
    _uint32     bean;      
    _uint32     entry_stamp; 
    _uint32     master_score;
    _uint32     win_count;
    _uint32     lose_count;
    _uint32     escape_count;

    tagUserGameInfo()
    {
        userid = 0;
        experience = 0;
        bean = 0;
        entry_stamp = 0;
        master_score = 0;
        win_count = 0;
        lose_count = 0;
        escape_count =0;
    }
};

inline bool get_dbproxy_resp_value(const char* data_json,int size,Json::Value &resp_value,_tint32 *error_code) 
{
    if (!data_json || size<=0)
    {
        MY_LOG_WARN( "%s,收到错误的json数据包", __FUNCTION__);
        return false;
    }

    Json::Reader json_reader;

    if (!json_reader.parse(data_json, resp_value))
    {
        MY_LOG_WARN( "%s,解析json数据包出错", __FUNCTION__);
        return false;
    }
    else if (resp_value["result"].asInt() != 0) //检查db代理返回结果
    {
        if (error_code)
        {
            *error_code = resp_value["result"].asInt();   
        }
        MY_LOG_WARN( "%s,result:%d,ErrorMsg:%s", __FUNCTION__, resp_value["result"].asInt(), resp_value["msg"].asString().c_str()); 
        return false;
    } 

    return true;
}

inline bool get_dbproxy_user_game_info(Json::Value resp_value, tagUserGameInfo &user_game_info)
{
    user_game_info.userid = resp_value["recordset0"][0]["uid"].asUInt64();
    user_game_info.experience = resp_value["recordset0"][0]["exp"].asUInt();
    user_game_info.bean = resp_value["recordset0"][0]["gold"].asUInt();
    user_game_info.entry_stamp = resp_value["recordset0"][0]["ticket"].asUInt();
    user_game_info.master_score = resp_value["recordset0"][0]["master_score"].asUInt();
    user_game_info.win_count = resp_value["recordset0"][0]["win"].asUInt();
    user_game_info.lose_count = resp_value["recordset0"][0]["lose"].asUInt();
    user_game_info.escape_count = resp_value["recordset0"][0]["escape"].asUInt();

    MY_LOG_DEBUG( "%s\nuserid:%llu\nexperience:%u\nbean:%u\nentry_stamp:%u\nmaster_score:%u\nwin_count:%u\nlose_count:%u\nescape_count:%u", 
        __FUNCTION__,user_game_info.userid, user_game_info.experience, user_game_info.bean, user_game_info.entry_stamp,
        user_game_info.master_score,user_game_info.win_count,user_game_info.lose_count,user_game_info.escape_count);

    return true;
}

inline bool get_dbproxy_user_base_info(Json::Value resp_value, tagUserBaseInfo &user_base_info)
{
    user_base_info.userid = resp_value["recordset0"][0]["uid"].asUInt64();
    user_base_info.guserid = resp_value["recordset0"][0]["guid"].asUInt64();
    user_base_info.user_type = resp_value["recordset0"][0]["account_type"].asUInt();
    user_base_info.gender = resp_value["recordset0"][0]["gender"].asUInt();
    user_base_info.face_id = resp_value["recordset0"][0]["avatar"].asUInt();
    user_base_info.vcoin = resp_value["recordset0"][0]["yuanbao"].asUInt();
    user_base_info.banned_time = resp_value["recordset0"][0]["banned_time"].asUInt();
    mysprintf(user_base_info.user_name,sizeof(user_base_info.user_name),"%s",resp_value["recordset0"][0]["username"].asString().c_str());
    mysprintf(user_base_info.nick_name,sizeof(user_base_info.nick_name),"%s",resp_value["recordset0"][0]["nickname"].asString().c_str());
    mysprintf(user_base_info.pass_word,sizeof(user_base_info.pass_word),"%s",resp_value["recordset0"][0]["password"].asString().c_str());

    MY_LOG_DEBUG( "%s\nuserid:%llu\nguserid:%llu\nuser_type:%u\ngender:%u\nface_id:%u\nvcoin:%u\nbanned_time:%u\nuser_name:%s\nnick_name:%s\npass_word:%s", 
        __FUNCTION__,user_base_info.userid, user_base_info.guserid, user_base_info.user_type, user_base_info.gender,
        user_base_info.face_id,user_base_info.vcoin,user_base_info.banned_time,user_base_info.user_name,
        user_base_info.nick_name,user_base_info.pass_word);

    return true;
}

#endif


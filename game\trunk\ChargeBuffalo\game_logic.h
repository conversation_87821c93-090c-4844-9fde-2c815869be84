#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_ODDS_CONFIG_COUNT 11       	//转盘中奖分布
#define ARRAY_1             3
#define ARRAY_2             3
/* 牌面值 */
enum enumIcon
{  
	ICON_9 = 0,
	ICON_10 = 1,
	ICON_J = 2,
	ICON_Q = 3,
	ICON_K = 4,
	ICON_A = 5,
	ICON_DEER = 6,
	ICON_WOLF = 7,
	ICON_BRAR = 8,
	ICON_EAGLE = 9,
	ICON_OX = 10,
	ICON_SCA = 11,
	ICON_WILD = 12,
	ICON_WILD_X2 = 13,
	ICO<PERSON>_WILD_X3 = 14,
	ICON_WILD_X4 = 15,
	ICO<PERSON>_WILD_X5 = 16
};


const double buffalo_bet_config_count[15] = {
	0.5, 1, 2, 3, 5, 10, 20, 30, 40, 50, 80, 100, 200, 500, 1000
};

const int normal_scatter_times[7] = { 0,0,0,8,15,25,100 };
const int respin_scatter_times[7] = { 0,0,5,8,15,25,100 };

const int kIconsTimes[11][7] = {
	{ 0, 0, 0, 25, 50, 75, 100 },
	{ 0, 0, 0, 25, 50, 75, 100 },
	{ 0, 0, 0, 25, 75, 100, 150 },
	{ 0, 0, 0, 25, 75, 100, 150 },
	{ 0, 0, 0, 50, 100, 150, 200 },
	{ 0, 0, 0, 50, 100, 150, 200 },
	{ 0, 0, 25, 100, 150, 225, 375 },
	{ 0, 0, 25, 100, 150, 225, 375 },
	{ 0, 0, 50, 150, 225, 300, 500 },
	{ 0, 0, 50, 150, 225, 300, 500 },
	{ 0, 0, 100, 200, 300, 500, 750 }
};
struct IconItem
{
	int nIcon;
	int nIndex;
};
struct IconInfo
{
	int nWin;
	int nIcon;
	std::vector<IconItem> vec_item;
	IconInfo()
	{
		nWin = 0;
		nIcon = 0;
		vec_item.clear();
	}
};
struct GameIconResultInfo 
{
	int Icons[6][4];
	int total_win;
	int get_free_count;
	std::map<int, std::vector<IconInfo>> map_line_data; //中奖信息
	GameIconResultInfo()
	{
		memset(Icons, 0, sizeof(Icons));
		map_line_data.clear();
		total_win = 0;
		get_free_count = 0;
	}
};

struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int status; // 游戏状态 1-已经初始化金币 0-未初始化
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式 1-15
	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

	GameIconResultInfo game_normal_icon; //正常玩法
	std::vector<GameIconResultInfo> vec_free_game_icon;  //免费玩法Icons列表
	int award_type;
	bool has_free;
	bool buy_bonus;   //是否是buy bonus玩法
	int buy_bonus_bet; //buy bonus玩法下注额度
	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info()
	{
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		record_id = 0;
		has_free = false;
		buy_bonus = false;
		award_type = 0;
		vec_free_game_icon.clear();
		game_normal_icon.map_line_data.clear();
		game_normal_icon.get_free_count = 0;
		game_normal_icon.total_win = 0;
		buy_bonus_bet = 0;
	}
	void reset() 
	{
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		has_free = false;
		award_type = 0;
		vec_free_game_icon.clear();
		game_normal_icon.map_line_data.clear();
		game_normal_icon.get_free_count = 0;
		game_normal_icon.total_win = 0;
		buy_bonus_bet = 0;
		buy_bonus = false;
	}

};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
	static game_logic *GetInstance()
	{
		static game_logic instance;
		return &instance;
	}
    void init();
   
	//检查牌
	void check_card(user_game_info &game_info);

	_uint64 gen_new_round_index();

	//计算结果
	int CalcResultTimes(user_game_info &game_info, GameIconResultInfo& result_info, bool bFree = false);
	void RandFillIcons(int Icon[6][4], bool bFree = false, bool bwinFree = true);
	void RandFillIconNoWin(int Icon[6][4], bool bFree = false, bool bwinFree = false);
	void hasValidPath(int Icon[6][4], int row, int col, int target, std::map<int, std::vector<IconInfo>>& map_icon_list, IconInfo& vec_info, bool bFree = false);
	//检查下一列是否有和目标icon相同的icon
	bool check_next_column_has_target_icon(int Icon[6][4], int row, int nIcon, bool bFree = false);
	//检查上一列是否有目标icon相同的icon
	bool check_pre_column_has_target_icon(int Icon[6][4], int row, int nIcon, bool bFree = false);
	//计算免费次数
	int calc_free_game_times(int nScaCount, bool bInFreeGame = false);
	//正常模式下，将没中奖的icon随机替换为sca
	void normal_mode_replace_no_win_icon_to_sca(user_game_info &game_info);
private:
	
};

#endif

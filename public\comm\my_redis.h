#ifndef __MY_REDIS_H__
#define __MY_REDIS_H__
#pragma once

#include "redis_manager.h"
#include "comm_def.h"
#include <time.h>
#include <set>
#include <list>

using namespace std;

#define GOLD_LOCK_ING		2
#define GOLD_LOCK_FREE		1
#define ONE_DAY_SECONDS     86400 //1������
#define WEB_SEND_EX_TIME	3600  //�������?���?��
#define MAX_GAME_HISTORY_RECORD_COUNT 100 //��?��?�����???1�7

//redis �?�����?1�7
static int DB_COMM = 0;		   //������������?1�7 key:uid ���ID
static int DB_POOL = 2;		   //�?����� key, key+type
static int DB_LUCKY_USER = 4;  //���?���������?1�7
static int DB_SAVE_DATA = 5;   //�����?�?���?�
static int DB_USER_DATA_RECORD = 6; //������?��
enum enApiMode
{
	MODE_SINGLE = 0,	//��??��??
	MODE_TRANSFER = 1,	//?��??
};
	
//================��?������?1�7==============0�?�
#define CUR_GOLD					"gold"					//��?�?��hashkey��field?
#define CUR_DIAMOND					"diamond"				//��?
#define CUR_GOLD_LOCK				"gold_lock"				//����?1�7
#define CUR_WHTH_GOLD				"whth_gold"				//?�?�?1�7
	
//-------------------------------------------------------------------------------------------------------------------------------
#define CUR_UNIQUE_STRING       	"web_player_token"		//����TOKEN
#define CUR_LOGON_IP				"logon_ip"				//������IP
#define CUR_HARDCODE				"hard_code"				//���?����
#define CUR_USER_TYPE				"user_type"				//�������
#define CUR_PLAER_NAME				"player_name"			//����??��?
#define CUR_COUNTRY_CODE		    "country_code"			//���?���

#define CUR_API_MODE				"api_mode"				//?��??
#define CUR_GAME_STATUS				"game_status"			//��???
#define CUR_GAME_ID					"game_id"				//��?ID
#define CUR_ROOM_ID					"room_id"				//����ID
#define CUR_ROOM_NODE				"node_id"				//���?���NODEID

#define CUR_SUB_CHANNEL_ID			"play_type_sub_id"		//������ID
#define CUR_CHANNEL_ID				"channel_id"			//?��������
#define CUR_LAST_TABLE_ID			"last_table_id"			//�?���?����ID

#define CUR_TODAY_RESULT			"today_result"			//������?
#define CUR_TODAY_RESULT_TIME		"today_result_time"		//������??��
#define CUR_HISTORY_RESULT			"history_result"		//��?��?

//================�??�?1�7==============2�?�

//�?�?��?�?�?1�7
struct stStockInfo
{
	_tint64		tax;			//�������?���
	_tint64		actual_tax;		//?�?�����
	_tint64		pool_extract;   //���?�?��
	_tint64     actual_pool;	//??����
	_tint64		pool_rate;		//���?�������
	_tint64		min_touch;		//��?���?1�7
	_tint64		actual_num;		//����?��K?
	_tint64		actual_stock;	//??��?1�7
	_tint64		stock_min;		//������?1�7
	_tint64		stock_max;		//������?1�7
	_tint64		stock_switch;   //���?�?��?1�7
	_tint64		stock_pool_lose;//������������
};

#define CUR_STOCK					"STOCK_%d_%d"			//����?GAMEID+NODEID
#define CUR_STOCK_TAX				"STOCK_TAX"				//��?��
#define CUR_STOCK_ACTUAL_TAX		"STOCK_ACTUAL_TAX"		//??��?
#define CUR_STOCK_EXTRACT			"STOCK_EXTRACT"			//���?�?��
#define CUR_STOCK_ACTUAL_POOL		"STOCK_ACTUAL_POOL"		//??����
#define CUR_STOCK_POOL_RATE			"STOCK_POOL_RATE"		//���?�������
#define CUR_STOCK_MIN_TOUCH			"STOCK_MIN_TOUCH"		//��?���?1�7
#define CUR_ACTUAL_NUM				"STOCK_ACTUAL_NUM"		//???��
#define CUR_ACTUAL_STOCK			"STOCK_ACTUAL_STOCK"	//??��?1�7
#define CUR_STOCK_MIN				"STOCK_MIN"				//������?1�7
#define CUR_STOCK_MAX				"STOCK_MAX"				//������?1�7
#define CUR_STOCK_SWITCH			"STOCK_SWITCH"			//���?�����?1�7
#define CUR_POOL_MAX_LOSE			"STOCK_POOL_LOSE"		//������������
#define CUR_STOCK_KEY_COUNT			12
const char CUR_STOCK_STR[CUR_STOCK_KEY_COUNT][128] =
{
	CUR_STOCK_TAX, 
	CUR_STOCK_ACTUAL_TAX,
	CUR_STOCK_EXTRACT,
	CUR_STOCK_ACTUAL_POOL,
	CUR_STOCK_POOL_RATE,
	CUR_STOCK_MIN_TOUCH,
	CUR_ACTUAL_NUM,
	CUR_ACTUAL_STOCK,
	CUR_STOCK_MIN,
	CUR_STOCK_MAX,
	CUR_STOCK_SWITCH,
	CUR_POOL_MAX_LOSE
};

#define CUR_SSS_JP_POOL           "ssss_jp_pool"
#define CUR_SSS_JP_POOL_F_1       "sss_jp_pool_f_1"
#define CUR_SSS_JP_POOL_B_1       "sss_jp_pool_b_1"
#define CUR_SSS_JP_POOL_F_2       "sss_jp_pool_f_2"
#define CUR_SSS_JP_POOL_B_2       "sss_jp_pool_b_2"
#define CUR_SSS_JP_POOL_F_3       "sss_jp_pool_f_3"
#define CUR_SSS_JP_POOL_B_3       "sss_jp_pool_b_3"

const char CUR_SSS_JP_POOL_STR[6][128] =
{
	CUR_SSS_JP_POOL_F_1,
	CUR_SSS_JP_POOL_B_1,
	CUR_SSS_JP_POOL_F_2,
	CUR_SSS_JP_POOL_B_2,
	CUR_SSS_JP_POOL_F_3,
	CUR_SSS_JP_POOL_B_3,
};
struct sssPoolInfo 
{
	_tint64 sss_jp_pool_f_1;
	_tint64 sss_jp_pool_b_1;
	_tint64 sss_jp_pool_f_2;
	_tint64 sss_jp_pool_b_2;
	_tint64 sss_jp_pool_f_3;
	_tint64 sss_jp_pool_b_3;

};
//color game�������??�����?��
struct stColorGameProbability 
{
	_tint64 color_yellow;
	_tint64 color_white;
	_tint64 color_powder;
	_tint64 color_blue;
	_tint64 color_red;
	_tint64 color_green;
};
#define COLOR_GAME_PROBABILITY   "COLOR_GAME_PROBABILITY_%d"   //nodeid
#define CUR_COLOR_YELLOW         "COLOR_YELLOW"
#define CUR_COLOR_WHITE          "COLOR_WHITE"
#define CUR_COLOR_POWDER         "COLOR_POWDER"
#define CUR_COLOR_BLUE           "COLOR_BLUE"
#define CUR_COLOR_RED            "COLOR_RED"
#define CUR_COLOR_GREEN          "COLOR_GREEN"

const char CUR_COLOR_GAME_STR[6][64] = {
	CUR_COLOR_YELLOW,CUR_COLOR_WHITE,CUR_COLOR_POWDER,CUR_COLOR_BLUE,CUR_COLOR_RED,CUR_COLOR_GREEN
};

//���?1�7
#define CUR_STOCK_CFG		"stock_ctr_%d_%d_%d"			//stock_ctr_??ID_��?ID_�?�ID
#define CUR_STOCK_CFG_TIME  "stock_update_time_%d_%d_%d"	//stock_update_time_??ID_��?ID_�?�ID
#define CUR_STOCK_VALUE		"stock_value_%d_%d_%d"			//stock_value_??ID_��?ID_�?�ID
#define CUR_STOCK_IMP_VALUE "stock_imp_value_%d_%d_%d"		//stock_imp_value_??ID_��?ID_�?�ID

//GM����
#define CUR_GM_CONTROL_STATUS "gm_control_status_%d_%d%s"     //��?��?? gm_control_status_[gameid]_[nodeid] + ��?�?���
#define CUR_GM_CONTROL_INFO "gm_control_info_%d_%d%s"			//�??������� gm_control_[gameid]_[nodeid] + ��?�?���
#define CUR_ROUND_ID	 "round_id_%d_%d%s"					//�?�ID������ round_id_[gameid]_[nodeid] + ��?�?���
#define CUR_GM_CONTROL_LOCK "gm_control_lock_%d_%d%s"       // ��?gm������??
#define CUR_GM_CONTROL_STATIC "gm_control_static_%d_%d%s"     // �?����?�������?��?
#define CUR_GM_CONTROL_ZSET  "gm_control_zset_%s"            // �?���?��?��?1�7
#define CUR_GM_CONTROL_BET_USER_INFO "gm_control_bet_user_info_%d_%s"  // �?���?������?1�7
#define CUR_GM_CONTROL_RESULT "gm_control_result_%s"    // �?����?�����?1�7
#define CUR_GM_DAY_RTP_INFO "gm_rtp_day_info_%d_%d%s%s"         // �??���rtp��?1�7
#define USER_lOTTERY_SET                         "user_lottery_set_%d"          // uid ���free_game�����?���
#define USER_SCATTER_SET                         "user_scatter_set_%d"          // uid ���free_game�����?���

//================�������?==============4�?�
enum copy_bet_type
{
	bet_n = 0, //����������
	bet_s = 1, //??
	bet_m = 2, //��?
	bet_b = 3, //��?
};

#define MAX_COPY_LEN		1000
#define COPY_USER_S_BET		"copy_s_bet_%d"		//?�������?? copy_s_bet_��?1�7 ,��? 3,10000,4,100 (��?��?1�7,��?,��?��?1�7,��?)
#define COPY_USER_M_BET		"copy_m_bet_%d"		//?���������?
#define COPY_USER_B_BET		"copy_b_bet_%d"		//?��������?
#define COPY_S_BET_INDEX	"copy_s_index"		//??��?����
#define COPY_M_BET_INDEX	"copy_m_index"		//��?��?����
#define COPY_B_BET_INDEX	"copy_b_index"		//��?��?����


//==============�����?���?�==============5�?�
struct UidRedisInfo
{
	_tint64		uid;		      //
	_tint64		user_type;		  //�������
	_tint64		gold;		      //��?��?1�7
};

#define CUR_ROOM_GOLD_STATISTIC                "room_gold_statistic_%d_%d"        //key  GAMEID NODEID
#define CUR_ROOM_GOLD_STATISTIC_TIME           "room_gold_statistic_time_%d_%d"   //key  GAMEID NODEID
#define CUR_GAME_DATE_NUMBER                   "game_date_number_%d_%d"           //key  GAMEID NODEID
#define CUR_GAME_DATE_NUMBER_TIME              "game_date_number_time_%d_%d"      //key  GAMEID NODEID

#define CUR_AUTO_UID						   "auto_uid"						  //�������UID
#define CUR_PAER_USER						   "pair_%d_%d"						  //UID + ����?������?�UID
#define CUR_TIYAN_TIMES						   "tiyan_%d_%d"					  //���?����������?1�7 UID+GAMEID
#define CUR_IS_LIMIT_LEAVE					   "robot_limit_leave_%d"			  //�����������?��?
#define CUR_GAME_HISTORY_RECORD_DATA                "%s"                               //��?��?��������?���
#define CUR_GAME_HISTORY_RECORD_KEY_NODE        "history_record_key_%lld_%d_%d"      //uid+gameid+nodeid
#define CUR_GAME_HISTORY_RECORD_KEY_GAME        "history_record_key_%lld_%d"       //uid+gameid
#define CUR_COLOR_GAME_HISTORY_RECORD_KEY_GAME  "color_history_record_key_%d_%d_%d" //gameid+nodeid+roomid
#define CUR_GAME_USER_BET_RECORD               "user_bet_record_%d_%d"            //uid+gameid
#define CUR_GAME_ROUND_NUM                     "game_round_num_%d_%d"                //gameid+nodeid
#define CUR_COLOR_JK_DICE                      "color_jk_dice_%d_%d_%d"               //gameid+nodeid+id
#define CUR_COLOR_JK_CUR_GOLD                  "color_jk_cur_gold_%d_%d"              //gameid+nodeid
#define CUR_COLOR_JK_NEXT_GOLD                 "color_jk_next_gold_%d"             //
#define CUR_SLOT_FREE_TIMES					   "free_times_%d_%d"				   //gameid+uid
#define CUR_SLOT_FREE_BET					   "free_bet_%d_%d"				   //gameid+uid
#define CUR_SLOT_FREE_IF_USE_PROPS             "free_if_use_props_%d_%d"       //gameid+uid ��?�?�?�����
#define CUR_SLOT_FREE_REWARD                   "free_reward_%d_%d"             // gameid+uid ��?��?����?�
#define CUR_SLOT_FREE_JEW					   "free_jew_%d_%d"				   //gameid+uid ��?freegame������?���?1�7
#define CUR_COLOR_JK_UPDATE_TIME                "color_jk_update_time_%d_%d"   //gameid+nodeid
#define CUR_SEVER_SEED						   "server_seed_%d"	 //��?ID		
#define CUR_PUBLIC_SEED						   "public_seed_%d"  //��?ID
#define CUR_USER_GAME_ROUND_NUM                "game_user_roound_num_%d_%d"   //uid+gameid ?�������?����
#define CUR_BET_LOSE_TIMES					   "bet_lose_time_%d_%d_%d_%d"	  //uid+gameid+nodeid+bet �����?��?����?1�7

#define CUR_USER_PROPS                         "user_props_%d"              // uid ?������?��?��?1�7
#define CUR_PROPS_NUMBER                       "props_number_%d"            // ��?����?������?1�7
#define CUR_PROPS_CONSUME                      "props_consume_%d"           // ������?������?1�7 ��?���� ��?��������
#define CUR_PROPS_BET_COUNT                    "props_betcount_%d"          // ����?���?��?���
#define CUR_PROPS_GAME_RUN_COUNT               "props_gameruncount_%d"      // ���������?����?�?1�7

#define CUR_USER_REDPOINT                      "user_redpoint_%d"           // uid ��?���?���
#define CUR_REDPOINT_PROPS_SECTION             "props_section_redpoint"     // ��?�����??���
#define CUR_REDPOINT_PROPS_CARD                "props_card_redpoint"        // ��?��?�??���
#define CUR_REDPOINT_MESSAGE                   "props_message_redpoint"     // �����??���

#define USER_PROPS_SET                         "user_props_set_%d"          // uid ����?��?��?��?1�7

//��?ID + ���?�
#define LOTTERY_PERIOD_ID					   "lottery_period_id_%d"				//������?,������?1�7
#define LOTTERY_PERIOD_RESULT				   "lottery_period_result_%d_%s"		//���?�?1�7,������?1�7
#define LOTTERY_PERIOD_START_TIME              "lottery_period_start_time_%d_%s"	//���?�??��,��������?1�7
#define LOTTERY_PERIOD_END_TIME                "lottery_period_end_time_%d_%s"		//���?���?��,��������?1�7
#define LOTTERY_PERIOD_STATUS				   "lottery_period_status_%d_%s"		//����??,��������?1�7

// ������������
#define CUR_ONLINE_USER_LIST  "online_user_list"  // ������������
#define CUR_PLAYING_USER_LIST "playing_user_list" // ������������

struct User_list_info
{
	int uid; // 玩家id
	int gid; // 游戏id
	int rid; // roomID

	User_list_info()
	{
		memset(this, 0, sizeof(User_list_info));
	}
};
 
//*********************************************************
class myredis
{
public:
	myredis();
	~myredis();

	static myredis *GetInstance()
	{
		static myredis my_redis;
		return &my_redis;
	}

public:
	void init(const char *ip, int len, int port, char *pass_word, int pass_len);
	
public:
	//��?����ID��?
	bool get_auto_uid(int begin_id, int &result_uid);

public:
	//��?�������?1�7
	_tint64 get_gold_lock(int uid);
	//�?��������?1�7
	void release_gold_lock(int uid);

public:
	//���������?����
	bool set_user_temp_data(const char* key, _tint64 value);
	bool set_user_temp_data(const char* key, const char* op_type, _tint64 data);
	bool set_user_temp_data(const char* key, const char* op_type, const char* val);

	// ����?������������ (���������� ����������) 
	_tint64 update_common_data(const char* key, int db_type, const char* op_type, _tint64 data);
	// ��??�������?���
	_tint64 get_common_data(const char* key, int db_type, const char* op_type);
	// ����?�?�?���?�����
	_tint64 sadd_common_data(const char* key, int db_type, const char* member);
	// ����?�?��?�?���?�����
	_tint64 sadd_common_data_list(const char* key, int db_type, vector<User_list_info>& members, _tint32 data = 0);
	// ��?�����?�?���?�
	void get_set_data(const char* key, int db_type, vector<int>& members);
	void get_set_data(const char* key, int db_type, vector<string>& members);
	// 删除集合中的某个元素
	bool srem_set_data(const char* key, int db_type, const char* member);
	// ����?��������
	void set_common_data(const char* key, int db_type, const char* op_type, _tint32 data);
	// �?�?��key�?���?1�7
	int check_common_exist_key(const char* key, int db_type);


	//���������?����
	bool update_user_temp_data(const char* key, _tint64 value);
	//��?�����?����
	_tint64 get_user_temp_data(const char* key);
	_tint64 get_user_temp_data(const char* key, const char* op_type);
	char* get_user_temp_data_for_string(const char* key, const char* op_type);

	//���������?����
	bool set_user_temp_data_for_string(const char* key, const char* value, int len);
	//��?�����?����
	char* get_user_temp_data_for_string(const char* key);
	//������?���?���?��
	void set_user_temp_expire_time(const char* key, _tint64 data);
	//�?���?�����?���?1�7
	int check_exit_user_temp_data(const char* key);
	//?������
	bool del_key(int db_type, const char *op_key);
public:
	//��?������?���?
	bool get_all_uid_info(_uint64 uid, UidRedisInfo &info);

	//�?�������?1�7
	bool is_exits_data_by_uid(_uint64 uid, int db_type, const char *op_type);

	//�������ID��?��?����? 
	_tint64 get_data_by_uid(_uint64 uid, int db_type, const char *op_type);
	_tint64 get_gold_by_uid(_uint64 uid);
	char* get_data_by_uid_for_string(_uint64 uid, int db_type, const char *op_type);
	void get_data_by_uid_for_s_string(_uint64 uid, int db_type, const char *op_type, string &s);
	_tint64 get_data_by_uid_optime(_uint64 uid, int db_type, const char *op_type,_tint64& optime);

	//��?�������TOKEN
	char* get_token_by_uid(_uint64 uid);

	//�������ID�?���?����?(���������� ����������) 
	bool update_data_by_uid(_uint64 uid, int db_type, const char *op_type, _tint64 data);

	//�����������
	bool set_data_by_uid(_uint64 uid, int db_type, const char *op_type, _tint64 data);
	//data?������+��-�� result?������?��
	bool safe_incr_data_by_uid(_uint64 uid, int db_type, const char *op_type, int data, _tint64& result);
	bool set_data_by_uid(_uint64 uid, int db_type, const char *op_type, const char* data, int len);
	bool set_data_by_uid_optime(_uint64 uid, int db_type, const char *op_type, _tint64 data, _tint64 data_time);

	//�������?���?��(��)
	void set_expire_time(int db_type, const char *op_type, _tint64 data);

public:
	//��󝝝���?1�7
	int get_pool_data_count(const char* key, int uid = 0);
	//����?���
	int set_pool_data_count(const char* key, int count, int uid = 0);
	//������?���
	int update_pool_data_count(const char* key, int count, int uid = 0);

public:
	//����??�?1�7
	void set_stock_info(int gameid, int nodeid, stStockInfo info);
	//��?�??�?1�7
	void get_stock_info(int gameid, int nodeid, stStockInfo &info);
	//����??����
	void set_actual_data(int gameid, int node, int data, const char *op_type);
	//��???����
	int get_actual_data(int gameid, int nodeid, const char *op_type);

public:
	//���������
	char* get_stock_config(int plat, int gameid, int nodeid);
	//�����?��?��
	_tint64 get_update_stock_time(int plat, int gameid, int nodeid);
	//�����?
	_tint64 get_stock_value(int plat, int gameid, int nodeid);
	//������?1�7
	_tint64 update_stock_value(int plat, int gameid, int nodeid, int value);
	//����?�?��?1�7
	void set_imp_stock_value(int plat, int gameid, int nodeid, _tint64 value);
	//���?�?��?
	_tint64 get_imp_stock_value(int plat, int gameid, int nodeid);

public:
	//���gm���?�?����?
	char* get_gm_now_round(int gameid, int nodeid, string ext="");
	//���gm����?���?���?
	char* get_gm_control_info(int gameid, int nodeid, string ext = "");
	//�����?����?
	void set_now_round_info(int gameid, int nodeid, string info, string ext = "");
	//����?���?���?
	void set_control_round_info(int gameid, int nodeid, string info, string ext = "");
	void get_control_round_info(int gameid, int nodeid, string ext, int nstart, int nstop, vector<string>& vec_str);
	void add_control_round_info(int gameid, int nodeid, string ext, const vector<string>& vec_str, const int& length);
	void update_control_round_info(int gameid, int nodeid, string ext, const string& vec_str);
	//���??1�7
	_tint64 get_round_id(int gameid, int nodeid, string ext = "");
	//����??���?��
	int set_round_id_expire(int gameid, int nodeid, int left_time, string ext = "");
	//����gm���??�?��
	bool set_gm_control_lock_info(int gameid, int nodeid, int left_time, string ext = "");
	// ?��gm���??�?��
	bool del_gm_control_lock_info(int gameid, int nodeid, string ext = "");
	// �������?��?���?�?��?1�7
	bool add_user_bet_info_set(string rid, int uid, _tint64 total_score, int left_time);
	// ����?���?�������??1�7
	bool zrem_user_bet_info_set(string rid, int uid);
	// ���������?��?1�7
	void set_round_user_bet_info(const char* key, char** data_str, int left_time);
	// �����?����?�����?1�7
	void set_round_result_info(string rid, const char* value, int left_time);
public:
	//��������������?1�7
	string get_video_round_info(int gameid);
	//����������?��
	string get_video_game_result(int gameid, string roundid);
	//������������??
	void set_video_game_status(int gameid, string roundid, int status);
	//�����������??1�7
	int get_video_game_status(int gameid, string roundid);
	//�����������?�??��
	void set_video_game_start_time(int gameid, string roundid, int start_time);
	//�����������?���?��
	void set_video_game_end_time(int gameid, string roundid, int end_time);
	//�����������?�??��
	int get_video_game_start_time(int gameid, string roundid);
	//�����������?���?��
	int get_video_game_end_time(int gameid, string roundid);


public:
	//����?��?��?1�7
	void reset_user_times(int gameid, int nodeid, int uid, int bet);
	//?������?����?1�7
	void update_user_times(int gameid, int nodeid, int uid, int bet, int times);
	//�����?��?�����
	_tint64 get_user_times(int gameid, int nodeid, int uid, int bet);

public:
	//���������?��?
	void save_user_bet_model(const char* str, int len, int type);
	//�����?��?��?��?1�7
	char* get_user_bet_model(int type);

public:
	void set_user_jetton_info(_tint64 uid, int gameId, int nodeId, vector<int> jettonIndex);
	void get_user_jetton_info(_tint64 uid, int gameId, int nodeId, vector<int> &jettonIndex);

public:
	bool set_super_control_info_for_string(const char* key, const char* value);
    bool set_user_comm_data(int db_type, const char *op_type, _tint64 data);
	bool set_game_config_for_room(const char *op_type, _tint64 data);
	int  get_game_config_for_room(const char *op_type);

	bool update_data_by_comm(int db_type, const char* op_type, _tint64 data);
	_tint64 get_user_comm_data(int db_type, const char *op_type);
	//���������?��?���?��
	void add_user_history_game_record_data(const char* key, const char* value, int nLimitLen = MAX_GAME_HISTORY_RECORD_COUNT);
	//��?�����?��?���?��
	void get_user_history_game_record_data(const char*key,int nstart,int nstop, vector<string> &vec_str);
	//��?�����?��?���?��������
	int  get_user_history_game_record_data_num(const char*key);
	//color_game��?��??��
	void get_color_game_probability_data(int nodeid, stColorGameProbability& info);
	//����color_game_��??��
	bool update_color_game_probability_data(int nodeid, const char* op_type, _tint64 data);

	bool add_freegame_info(int uid,int score);
	void get_freegame_info(int uid, vector<int>& members);
	bool get_graph_from_redis(const string&key, string& ret, int& r,int db = DB_COMM);
	int add_freeGame_index(const string&key);
	void del_freeGame_index(const string&key);
	bool add_freegame_scatter_info(int uid, int count);
	void get_freegame_scatter_info(int uid, vector<int>& members);
	char* get_str(const char* key);

	bool get_graph_from_redis_by_index(const string& key, string& ret, int r, int db = 0);
	bool set_sss_jp_pool_info(sssPoolInfo poolInfo);
	bool get_sss_jp_pool_info(sssPoolInfo& poolInfo);
private:
	char m_redis_ip[128];
	int  m_port;
	char m_pass_word[128];
	bulk_t * m_ptr;
};

#endif//__MY_REDIS_H__
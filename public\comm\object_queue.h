﻿/***********************************************************
 * File Name          :       object_queue.h
 * Author             :       原海洋
 * Version            :       1.0
 * Date               :       2015-09-28 20:05
 * Description        :       对象队列
***********************************************************/

#ifndef _OBJECT_QUEUE_H_
#define _OBJECT_QUEUE_H_

#include "room_common.h"
#include <list>

template<typename OBJECTTYPE>
class CObjectQueue
{
public:
    CObjectQueue();
    virtual ~CObjectQueue();

public:
    /**
     *  @brief: 添加数据到队列尾
     *  @return: 成功返回true, 失败返回false
    **/
    bool push(OBJECTTYPE obj);
    /**
     *  @brief: 获得队列头数据
     *  @return: 返回队列头数据并删除
    **/
    OBJECTTYPE pop();
    /**
     *  @brief: 获得队列数据数量
     *  @return: 返回队列元素数量
    **/
    int count();
    /**
     *  @brief: 检查数据是否在队列中
     *  @return: 在返回true, 不在返回false
    **/
    bool exists(OBJECTTYPE obj);
    /**
     *  @brief: 检查队列是否为NULL
     *  @return: 为NULL 返回 true, 不为NULL 返回false
    **/
    bool empty();
    /**
     *  @brief: 清除队列数据
     *  @return: 无返回值
    **/
    void clear();
    /**
     *  @brief: 删除队列指定对象
     *  @return:成功返回true, 失败返回false
    **/
    bool erase(OBJECTTYPE obj);

private:
    std::list<OBJECTTYPE>           m_object_list;
};

template<typename OBJECTTYPE>
CObjectQueue<OBJECTTYPE>::CObjectQueue()
{
    clear();
}

template<typename OBJECTTYPE>
CObjectQueue<OBJECTTYPE>::~CObjectQueue()
{

}

template<typename OBJECTTYPE>
bool CObjectQueue<OBJECTTYPE>::push(OBJECTTYPE obj)
{
    m_object_list.push_back(obj);
	return true;
}

template<typename OBJECTTYPE>
OBJECTTYPE CObjectQueue<OBJECTTYPE>::pop()
{
    OBJECTTYPE obj = m_object_list.front();
    m_object_list.pop_front();
    return obj;
}

template<typename OBJECTTYPE>
int CObjectQueue<OBJECTTYPE>::count()
{
    return m_object_list.size();
}

template<typename OBJECTTYPE>
bool CObjectQueue<OBJECTTYPE>::exists(OBJECTTYPE obj)
{
    bool exists = false;
    typename std::list<OBJECTTYPE>::iterator it = m_object_list.begin(); 
    while (it != m_object_list.end())
    {
        if (*it == obj)
        {
            exists = true;
            break;
        }
        ++it;
    }

    return exists;
}

template<typename OBJECTTYPE>
bool CObjectQueue<OBJECTTYPE>::empty()
{
    return m_object_list.empty();
}

template<typename OBJECTTYPE>
void CObjectQueue<OBJECTTYPE>::clear()
{
    m_object_list.clear();
}

template<typename OBJECTTYPE>
bool CObjectQueue<OBJECTTYPE>::erase(OBJECTTYPE obj)
{
    if (exists(obj))
    {
        typename std::list<OBJECTTYPE>::iterator it = m_object_list.begin();
        while (it != m_object_list.end())
        {
            if (*it == obj)
            {
                m_object_list.erase(it);
                return true;
            }
            ++it;
        }
    }
    return false;
}

#endif //_OBJECT_QUEUE_H_

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <cstdlib>
#include <ctime>

// 包含必要的头文件
#include "game_logic.h"
#include "game_component.h"
#include "game_component_send.cpp" // 包含实现文件以获取所有函数定义

// 模拟日志宏定义（如果没有定义的话）
#ifndef MY_LOG_DEBUG
#define MY_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#endif

#ifndef MY_LOG_PRINT
#define MY_LOG_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#endif

#ifndef MY_LOG_ERROR
#define MY_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
#endif

class TestGameComponent : public CGameComponent
{
public:
    TestGameComponent() {}
    virtual ~TestGameComponent() {}
    
    // 重写必要的虚函数
    virtual int on_user_action(IUser *user, _uint8 type) override { return 0; }
    virtual bool is_can_clear_user(int chairid) override { return true; }
    
    
  
    void run_compare_test()
    {
        std::cout << "=== 开始两个protobuf数据进行比对  ===" << std::endl;
        int icons[] = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};

        

            try {
                user_game_info game_info;
                game_info.cur_mode = 0;
                game_info.bet = 1000;
                
                // 设置测试图标
                for (int icon : icons) {
                    game_info.graph_data.push_back(icon);
                }
                
                // 计算结果
                game_logic::GetInstance()->CalcResultTimes(game_info);
                
                // 编码为协议
                cbtProto::SpinAck ack;
                encodeACKProto(ack, game_info);

                // 从test.txt 读取数据
                std::ifstream ifs("test.txt");
                std::string json_string;
                if (ifs.is_open()) {
                    std::getline(ifs, json_string);
                    ifs.close();
                } else {
                    std::cout << "无法打开 test.txt 文件" << std::endl;
                    continue;
                }

                // Test with direct JSON string instead of base64 decode
                cbtProto::SpinAck demo;
                google::protobuf::util::JsonParseOptions parse_options;
                google::protobuf::util::JsonStringToMessage(json_string, &demo, parse_options);

                std::string debugstr;
                using namespace google::protobuf;
                TextFormat::PrintToString(demo, &debugstr);
                MY_LOG_DEBUG("test demo: %s", debugstr.c_str());

                // 两个protobuf对象进行比较 
                string diff;
                if (!compare_graph(ack, demo, diff))
                {
                    MY_LOG_PRINT("graph test diff = %s", diff.c_str());
                }
                else
                {
                    MY_LOG_PRINT("graph test equal");
                }

                ofs.close();
            } catch (const std::exception& e) {
                std::cout << "测试用例 " << (test_idx + 1) << " 发生异常: " << e.what() << std::endl;
            }
        }
 
    
};

// 主函数
int main()
{
    std::cout << "GoldenBank 游戏测试单元" << std::endl;
    std::cout << "========================" << std::endl;
    
    // 初始化随机种子
    srand(time(nullptr));
    
    try {
        TestGameComponent test_component;
        
        // 运行基本测试
        test_component.run_compare_test();
        
        std::cout << "\n";
        
        // 运行多组合测试
        test_component.run_multiple_tests();
        
    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}

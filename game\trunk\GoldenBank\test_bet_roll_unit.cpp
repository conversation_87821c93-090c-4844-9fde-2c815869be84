#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <algorithm>
#include <iomanip>

// 包含 protobuf 相关头文件
#include <google/protobuf/util/json_util.h>
#include <google/protobuf/text_format.h>
#include <google/protobuf/util/message_differencer.h>

// 包含必要的头文件
#include "game_logic.h"
#include "game_component.h"

// 简化的日志宏
#define MY_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

// 包含实现文件以获取 encodeACKProto 和 compare_graph 函数
#include "game_component_send.cpp"

// Protobuf 比较测试类
class CompareTest
{
public:
    void run_compare_test()
    {
        std::cout << "=== 开始两个 Protobuf 数据比对测试 ===" << std::endl;

        // 测试数据：包含多种图标的复杂组合
        int icons[] = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};
        int icons_count = sizeof(icons) / sizeof(icons[0]);

        std::cout << "测试数据长度: " << icons_count << " 个图标" << std::endl;

        try {
            // 步骤1: 生成当前数据的 protobuf 对象
            std::cout << "\n--- 步骤1: 生成当前数据的 protobuf ---" << std::endl;
            user_game_info game_info;
            game_info.cur_mode = 0;
            game_info.bet = 1000;

            // 设置测试图标
            for (int i = 0; i < icons_count; i++) {
                game_info.graph_data.push_back(icons[i]);
            }

            // 计算结果
            game_logic::GetInstance()->CalcResultTimes(game_info);
            std::cout << "游戏计算结果: " << game_info.result << ", 奖励类型: " << game_info.award_type << std::endl;

            // 编码为 protobuf
            cbtProto::SpinAck current_ack;
            encodeACKProto(current_ack, game_info);

            // 将当前 protobuf 转换为 JSON 并保存
            std::string current_json;
            google::protobuf::util::JsonPrintOptions print_options;
            print_options.add_whitespace = true;
            print_options.preserve_proto_field_names = true;
            auto status = google::protobuf::util::MessageToJsonString(current_ack, &current_json, print_options);
            if (!status.ok()) {
                std::cout << "错误: 无法将当前 protobuf 转换为 JSON - " << status.ToString() << std::endl;
                return;
            }

            std::ofstream current_file("current_protobuf.json");
            if (current_file.is_open()) {
                current_file << current_json;
                current_file.close();
                std::cout << "当前 protobuf JSON 已保存到 current_protobuf.json" << std::endl;
            }

            // 步骤2: 从 test.txt 读取参考 JSON 数据
            std::cout << "\n--- 步骤2: 读取参考 protobuf 数据 ---" << std::endl;
            std::ifstream ifs("test.txt");
            std::string reference_json;

            if (ifs.is_open()) {
                std::string line;
                while (std::getline(ifs, line)) {
                    reference_json += line;
                }
                ifs.close();
                std::cout << "成功从 test.txt 读取参考数据，长度: " << reference_json.length() << " 字符" << std::endl;
            } else {
                std::cout << "错误: 无法打开 test.txt 文件" << std::endl;
                std::cout << "请确保 test.txt 文件存在并包含有效的 JSON 数据" << std::endl;
                return;
            }

            // 步骤3: 解析参考数据为 protobuf
            std::cout << "\n--- 步骤3: 解析参考数据为 protobuf ---" << std::endl;
            cbtProto::SpinAck reference_ack;
            google::protobuf::util::JsonParseOptions parse_options;
            parse_options.ignore_unknown_fields = true;

            auto parse_status = google::protobuf::util::JsonStringToMessage(reference_json, &reference_ack, parse_options);
            if (!parse_status.ok()) {
                std::cout << "错误: JSON 解析失败 - " << parse_status.ToString() << std::endl;
                std::cout << "请检查 test.txt 中的 JSON 格式是否正确" << std::endl;
                return;
            }
            std::cout << "参考数据解析成功" << std::endl;

            // 步骤4: 比较两个 protobuf 对象
            std::cout << "\n--- 步骤4: 比较两个 protobuf 对象 ---" << std::endl;

            // 输出基本信息比较
            std::cout << "基本信息比较:" << std::endl;
            std::cout << "  当前 totalwin: " << current_ack.totalwin() << std::endl;
            std::cout << "  参考 totalwin: " << reference_ack.totalwin() << std::endl;
            std::cout << "  当前 award: " << current_ack.award() << std::endl;
            std::cout << "  参考 award: " << reference_ack.award() << std::endl;

            // 使用 compare_graph 进行详细比较
            std::string diff;
            bool is_equal = compare_graph(current_ack, reference_ack, diff);

            if (is_equal) {
                std::cout << "\n✅ 比较结果: 两个 protobuf 对象完全相同!" << std::endl;
            } else {
                std::cout << "\n❌ 比较结果: 发现差异" << std::endl;
                std::cout << "差异详情: " << diff << std::endl;

                // 详细分析差异
                analyze_differences(current_ack, reference_ack);
            }

            // 步骤5: 输出调试信息
            std::cout << "\n--- 步骤5: 调试信息 ---" << std::endl;
            std::string current_debug, reference_debug;
            using namespace google::protobuf;
            TextFormat::PrintToString(current_ack, &current_debug);
            TextFormat::PrintToString(reference_ack, &reference_debug);

            std::cout << "当前 protobuf 文本格式 (前200字符):" << std::endl;
            std::cout << current_debug.substr(0, 200) << "..." << std::endl;

            std::cout << "\n参考 protobuf 文本格式 (前200字符):" << std::endl;
            std::cout << reference_debug.substr(0, 200) << "..." << std::endl;

        } catch (const std::exception& e) {
            std::cout << "比较测试发生异常: " << e.what() << std::endl;
        }

        std::cout << "\n=== Protobuf 比对测试完成 ===" << std::endl;
    }

private:
    // 详细分析两个 protobuf 的差异
    void analyze_differences(const cbtProto::SpinAck& current, const cbtProto::SpinAck& reference)
    {
        std::cout << "\n=== 详细差异分析 ===" << std::endl;

        // 比较基本字段
        if (std::abs(current.totalwin() - reference.totalwin()) > 0.001) {
            std::cout << "差异: totalwin - 当前:" << current.totalwin() << " vs 参考:" << reference.totalwin() << std::endl;
        }

        if (current.award() != reference.award()) {
            std::cout << "差异: award - 当前:" << current.award() << " vs 参考:" << reference.award() << std::endl;
        }

        // 比较 ackqueue 数量
        int current_size = current.ackqueue_size();
        int reference_size = reference.ackqueue_size();

        if (current_size != reference_size) {
            std::cout << "差异: ackqueue 数量 - 当前:" << current_size << " vs 参考:" << reference_size << std::endl;
        }

        // 比较每个 ackqueue 的内容
        int min_size = std::min(current_size, reference_size);
        for (int i = 0; i < min_size; i++) {
            const auto& current_queue = current.ackqueue(i);
            const auto& reference_queue = reference.ackqueue(i);

            if (current_queue.has_platesymbol() != reference_queue.has_platesymbol()) {
                std::cout << "差异: ackqueue[" << i << "] platesymbol 存在性不同" << std::endl;
            } else if (current_queue.has_platesymbol() && reference_queue.has_platesymbol()) {
                const auto& current_plate = current_queue.platesymbol();
                const auto& reference_plate = reference_queue.platesymbol();

                if (current_plate.col_size() != reference_plate.col_size()) {
                    std::cout << "差异: ackqueue[" << i << "] col 数量 - 当前:"
                              << current_plate.col_size() << " vs 参考:" << reference_plate.col_size() << std::endl;
                } else {
                    for (int j = 0; j < current_plate.col_size(); j++) {
                        if (current_plate.col(j) != reference_plate.col(j)) {
                            std::cout << "差异: ackqueue[" << i << "] col[" << j << "] - 当前:"
                                      << current_plate.col(j) << " vs 参考:" << reference_plate.col(j) << std::endl;
                        }
                    }
                }
            }
        }
    }
};

// 主函数
int main()
{
    std::cout << "GoldenBank Protobuf 比较测试" << std::endl;
    std::cout << "============================" << std::endl;

    try {
        CompareTest test;
        test.run_compare_test();

    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n按回车键退出..." << std::endl;
    std::cin.get();

    return 0;
}

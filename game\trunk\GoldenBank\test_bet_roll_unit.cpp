#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <algorithm>

// 包含必要的头文件
#include "game_logic.h"
#include "game_component.h"

// 简化的日志宏
#define MY_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

// 前置声明
void encodeACKProto(cbtProto::SpinAck &ack, const user_game_info &game_info);
bool compare_graph(const cbtProto::SpinAck &g1, const cbtProto::SpinAck &g2, std::string &diff);

// 简化的比较测试类
class CompareTest
{
public:
    void run_compare_test()
    {
        std::cout << "=== 开始两个protobuf数据进行比对 ===" << std::endl;

        // 测试数据：包含多种图标的复杂组合
        int icons[] = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};
        int icons_count = sizeof(icons) / sizeof(icons[0]);

        std::cout << "测试数据长度: " << icons_count << " 个图标" << std::endl;

        try {
            // 步骤1: 生成当前数据的游戏结果
            std::cout << "\n--- 步骤1: 计算当前数据 ---" << std::endl;
            user_game_info game_info;
            game_info.cur_mode = 0;
            game_info.bet = 1000;

            // 设置测试图标
            for (int i = 0; i < icons_count; i++) {
                game_info.graph_data.push_back(icons[i]);
            }

            // 计算结果
            game_logic::GetInstance()->CalcResultTimes(game_info);
            std::cout << "游戏计算结果: " << game_info.result << ", 奖励类型: " << game_info.award_type << std::endl;

            // 显示图标矩阵
            std::cout << "图标矩阵:" << std::endl;
            for (int i = 0; i < 3; i++) {
                std::cout << "  ";
                for (int j = 0; j < 3; j++) {
                    std::cout << game_info.Icons[i][j] << " ";
                }
                std::cout << std::endl;
            }

            // 步骤2: 保存当前结果到文件
            std::cout << "\n--- 步骤2: 保存当前结果 ---" << std::endl;
            std::ofstream current_file("current_result.txt");
            if (current_file.is_open()) {
                current_file << "当前计算结果:\n";
                current_file << "totalwin: " << (double)game_info.result / 100.0 << "\n";
                current_file << "award: " << game_info.award_type << "\n";
                current_file << "图标矩阵:\n";
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        current_file << game_info.Icons[i][j] << " ";
                    }
                    current_file << "\n";
                }
                current_file.close();
                std::cout << "当前结果已保存到 current_result.txt" << std::endl;
            }

            // 步骤3: 读取参考数据
            std::cout << "\n--- 步骤3: 读取参考数据 ---" << std::endl;
            std::ifstream ifs("test.txt");
            if (ifs.is_open()) {
                std::string line;
                std::cout << "test.txt 内容:" << std::endl;
                while (std::getline(ifs, line)) {
                    std::cout << line << std::endl;
                }
                ifs.close();
            } else {
                std::cout << "错误: 无法打开 test.txt 文件" << std::endl;
                std::cout << "请先运行其他方法生成 test.txt 文件" << std::endl;
            }

            std::cout << "\n--- 比较说明 ---" << std::endl;
            std::cout << "1. 当前计算结果已保存到 current_result.txt" << std::endl;
            std::cout << "2. 参考数据在 test.txt 中" << std::endl;
            std::cout << "3. 请手动比较两个文件的内容差异" << std::endl;

        } catch (const std::exception& e) {
            std::cout << "比较测试发生异常: " << e.what() << std::endl;
        }

        std::cout << "\n=== protobuf 比对测试完成 ===" << std::endl;
    }
};

// 主函数
int main()
{
    std::cout << "GoldenBank Protobuf 比较测试" << std::endl;
    std::cout << "============================" << std::endl;

    try {
        CompareTest test;
        test.run_compare_test();

    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n按回车键退出..." << std::endl;
    std::cin.get();

    return 0;
}

#ifndef __BLOCK_TIMEOUT_SOCKET__
#define __BLOCK_TIMEOUT_SOCKET__

#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>
#include <assert.h>


int block_timeout_connect (int sockfd, struct sockaddr_in *servaddr, int nsec, int usec);
int block_timeout_recv(int sockfd, char *buf, int len,  int nsec, int usec);
int block_connection_is_closed_by_remote(int sockfd, int ms);

#endif

﻿#include "observe_manager.h"
#include <time.h>

ObserveManager::ObserveManager()
{
	max_observe_num = 0;
	m_observe_list.clear();
}

ObserveManager::~ObserveManager()
{

}

void ObserveManager::add_user(IUser *user)
{	
	if(!is_observe_user(user->get_user_id()))
	{
		m_observe_list.push_back(user);
		g_log->write_log(LOG_TYPE_DEBUG,"ObserveManager add_user success  userID:%u size:%d", user->get_user_id(), m_observe_list.size());
	}
	else
	{
		g_log->write_log(LOG_TYPE_WARNING,"ObserveManager add_user fail the userid is exist. userID:%u size:%d", user->get_user_id(), m_observe_list.size());
	}	
	return;
}

bool ObserveManager::delete_user(_tint64 userID)
{
	g_log->write_log(LOG_TYPE_DEBUG,"ObserveManager delete_user -  userID:%lld size:%d", userID);

	for(std::vector<IUser*>::iterator iter=m_observe_list.begin();iter!=m_observe_list.end();)
	{
		if(userID==(*iter)->get_user_id())
		{
			iter = m_observe_list.erase(iter);
			g_log->write_log(LOG_TYPE_DEBUG,"ObserveManager delete_user success. userID:%lld size:%d", userID, m_observe_list.size());
			return true;
		}
		else
		{
			iter++;
		}
	}
	g_log->write_log(LOG_TYPE_DEBUG,"ObserveManager delete_user success the userid is not in observe list. userID:%lld", userID);
    return false;
}


bool ObserveManager::is_observe_user(_tint64 userID)
{
	g_log->write_log(LOG_TYPE_DEBUG,"ObserveManager is_observe_user userID:%lld size:%d", userID, m_observe_list.size());

	for (std::vector<IUser*>::iterator iter=m_observe_list.begin();iter!=m_observe_list.end();)
	{
		if(userID==(*iter)->get_user_id())
		{			
			return true;
		}
		else
		{
			iter++;
		}
	}	
	return false;
}
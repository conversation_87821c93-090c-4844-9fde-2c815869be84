# GoldenBank 测试单元使用说明

本目录包含了用于测试 `test_bet_roll_result` 方法的测试单元。

## 文件说明

### 测试文件
- `simple_test.cpp` - 简化版测试程序，不依赖复杂的类继承
- `test_bet_roll_unit.cpp` - 完整版测试程序，包含 protobuf 支持和比较功能
- `simple_compare_test.cpp` - 简化版比较测试程序
- `run_test.bat` - Windows 批处理脚本，一键编译运行基本测试
- `run_compare_test.bat` - Windows 批处理脚本，运行完整比较测试
- `run_simple_compare.bat` - Windows 批处理脚本，运行简化比较测试

### 编译文件
- `Makefile.simple` - 简化测试的 Makefile
- `Makefile.test` - 完整测试的 Makefile

## 使用方法

### 方法1: 使用批处理脚本（推荐 Windows 用户）

#### 基本测试
```bash
# 直接双击运行基本测试
run_test.bat

# 或在命令行中运行
./run_test.bat
```

#### Protobuf 比较测试
```bash
# 运行完整比较测试（需要 protobuf 库）
run_compare_test.bat

# 运行简化比较测试（推荐）
run_simple_compare.bat
```

### 方法2: 使用 Makefile（Linux/Unix 用户）

```bash
# 编译并运行简化测试
make -f Makefile.simple test

# 或分步执行
make -f Makefile.simple all
make -f Makefile.simple run

# 清理编译文件
make -f Makefile.simple clean
```

### 方法3: 手动编译

```bash
# 编译简化测试
g++ -std=c++11 -Wall -g -O0 -I. -I../../../public -I../../../public/comm \
    simple_test.cpp game_logic.cpp game_config.cpp -o simple_test

# 运行测试
./simple_test
```

## 测试内容

### 简化测试 (simple_test.cpp)
1. **基本功能测试**: 测试 `test_bet_roll_result` 的核心逻辑
2. **多种图标组合测试**: 测试不同的图标组合情况
3. **get_odds 方法测试**: 测试赔率计算和 curLight 参数

### 完整测试 (test_bet_roll_unit.cpp)
1. **完整流程测试**: 包含 protobuf 序列化/反序列化
2. **JSON 输出测试**: 生成 test.txt 文件
3. **多组合测试**: 测试各种图标组合
4. **Protobuf 比较测试**: 比较两个 protobuf 对象的差异

### 比较测试 (simple_compare_test.cpp)
1. **数据比较**: 比较当前计算结果与参考数据
2. **差异分析**: 详细分析 totalwin、award 等字段的差异
3. **简化解析**: 不依赖复杂的 protobuf 库，使用简化 JSON 解析

## 测试用例

程序会测试以下图标组合：

1. `{0,11,0,1,0,2,0,1,0}` - 原始测试用例
2. `{1,1,1,2,2,2,3,3,3}` - 三种不同的三连
3. `{4,4,4,0,0,0,0,0,0}` - ICON_7 三连
4. `{5,5,5,0,0,0,0,0,0}` - ICON_BONUS 三连
5. `{5,5,0,0,0,0,0,0,0}` - ICON_BONUS 两个
6. `{5,0,0,0,0,0,0,0,0}` - ICON_BONUS 一个
7. `{0,0,0,0,0,0,0,0,0}` - 全空测试
8. `{11,11,11,0,0,0,0,0,0}` - WILD 三连测试

## 输出说明

测试程序会输出：
- 输入的图标数据
- 计算后的游戏结果
- 奖励类型
- 图标矩阵（3x3）
- get_odds 方法的赔率和灯光值

## 故障排除

### 编译错误
1. 确保已安装 C++ 编译器（g++）
2. 检查头文件路径是否正确
3. 确保相关的 .cpp 和 .h 文件存在

### 运行错误
1. 检查是否有权限创建文件
2. 确保游戏逻辑单例正确初始化
3. 检查日志输出中的错误信息

### 依赖问题
- 简化测试只依赖标准 C++ 库和游戏逻辑文件
- 完整测试需要 protobuf 库支持

## 注意事项

1. 测试程序会在当前目录生成 `test.txt` 文件
2. 程序使用随机种子，每次运行结果可能不同
3. 确保有足够的磁盘空间用于输出文件
4. 建议在开发环境中运行，避免影响生产环境

## 扩展测试

如需添加更多测试用例，可以修改 `test_cases` 向量：

```cpp
std::vector<std::vector<int>> test_cases = {
    // 添加新的测试用例
    {你的图标组合},
};
```

## 联系信息

如有问题或建议，请联系开发团队。

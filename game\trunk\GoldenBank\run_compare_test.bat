@echo off
echo GoldenBank Protobuf 比较测试
echo ===============================

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请确保已安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译比较测试程序...
g++ -std=c++11 -Wall -g -O0 ^
    -I. ^
    -I../../../public ^
    -I../../../public/comm ^
    -I../../../public/lib/protobuf/include ^
    test_bet_roll_unit.cpp game_logic.cpp game_config.cpp ^
    -L../../../public/lib/protobuf/lib ^
    -lprotobuf ^
    -o compare_test.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    echo.
    echo 可能的解决方案:
    echo 1. 检查 protobuf 库是否正确安装
    echo 2. 检查路径是否正确
    echo 3. 尝试使用简化版本测试
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 检查是否存在 test.txt 文件
if not exist "test.txt" (
    echo 警告: 未找到 test.txt 文件
    echo 正在创建示例 test.txt 文件...
    echo {"totalwin":0,"award":0,"ackqueue":[{"platesymbol":{"col":[5,0,4]}}]} > test.txt
    echo 已创建示例文件，您可以替换为实际的测试数据
    echo.
)

echo 正在运行比较测试...
echo.

compare_test.exe

echo.
echo 测试完成！
echo.
echo 生成的文件:
if exist "current_result.txt" (
    echo - current_result.txt: 当前计算结果
)
if exist "test.txt" (
    echo - test.txt: 参考数据
)

pause

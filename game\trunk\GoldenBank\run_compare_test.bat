@echo off
echo GoldenBank 简化比较测试
echo ========================

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请确保已安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译比较测试程序...
g++ -std=c++11 -Wall -g -O0 -I. -I../../../public -I../../../public/comm test_bet_roll_unit.cpp game_logic.cpp game_config.cpp -o compare_test.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    echo 请检查以下问题:
    echo 1. 确保 game_logic.cpp 和 game_config.cpp 文件存在
    echo 2. 确保头文件路径正确
    echo 3. 检查代码语法错误
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 检查是否存在 test.txt 文件
if not exist "test.txt" (
    echo 警告: 未找到 test.txt 文件
    echo 正在创建示例 test.txt 文件...
    echo 示例参考数据 > test.txt
    echo totalwin: 15.5 >> test.txt
    echo award: 1 >> test.txt
    echo 已创建示例文件
    echo.
)

echo 正在运行比较测试...
echo.

compare_test.exe

echo.
echo 测试完成！
echo.
echo 生成的文件:
if exist "current_result.txt" (
    echo - current_result.txt: 当前计算结果
)
if exist "test.txt" (
    echo - test.txt: 参考数据
)

pause

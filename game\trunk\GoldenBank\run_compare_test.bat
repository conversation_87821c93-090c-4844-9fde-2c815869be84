@echo off
echo GoldenBank Protobuf 比较测试
echo ============================

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请确保已安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译 Protobuf 比较测试程序...
echo 注意: 需要 protobuf 库支持

REM 尝试不同的编译方式
echo 尝试方式1: 使用系统 protobuf 库...
g++ -std=c++11 -Wall -g -O0 ^
    -I. ^
    -I../../../public ^
    -I../../../public/comm ^
    test_bet_roll_unit.cpp game_logic.cpp game_config.cpp ^
    -lprotobuf ^
    -o compare_test.exe

if %errorlevel% equ 0 (
    echo 编译成功！
    goto :run_test
)

echo 方式1失败，尝试方式2: 指定 protobuf 路径...
g++ -std=c++11 -Wall -g -O0 ^
    -I. ^
    -I../../../public ^
    -I../../../public/comm ^
    -I../../../public/lib/protobuf/include ^
    test_bet_roll_unit.cpp game_logic.cpp game_config.cpp ^
    -L../../../public/lib/protobuf/lib ^
    -lprotobuf ^
    -o compare_test.exe

if %errorlevel% equ 0 (
    echo 编译成功！
    goto :run_test
)

echo 编译失败！
echo 可能的原因:
echo 1. 缺少 protobuf 库
echo 2. protobuf 库路径不正确
echo 3. 缺少必要的源文件
echo.
echo 解决方案:
echo 1. 安装 protobuf 库: vcpkg install protobuf
echo 2. 或使用系统包管理器安装 protobuf
echo 3. 确保 protobuf 库在系统路径中
pause
exit /b 1

:run_test

echo 编译成功！
echo.

REM 检查是否存在 test.txt 文件
if not exist "test.txt" (
    echo 警告: 未找到 test.txt 文件
    if exist "sample_test.txt" (
        echo 使用示例文件 sample_test.txt 作为参考数据...
        copy sample_test.txt test.txt >nul
        echo 已复制示例文件到 test.txt
    ) else (
        echo 正在创建简单的示例 test.txt 文件...
        echo {"totalwin":15.5,"award":1,"ackqueue":[{"platesymbol":{"col":[5,0,4]}}]} > test.txt
        echo 已创建示例文件
    )
    echo.
)

echo 正在运行比较测试...
echo.

compare_test.exe

echo.
echo 测试完成！
echo.
echo 生成的文件:
if exist "current_protobuf.json" (
    echo - current_protobuf.json: 当前 protobuf JSON 格式
)
if exist "test.txt" (
    echo - test.txt: 参考 protobuf JSON 数据
)
echo.
echo 比较结果请查看上方的输出信息

pause

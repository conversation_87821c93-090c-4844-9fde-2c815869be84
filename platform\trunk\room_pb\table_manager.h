﻿

#ifndef _TABLE_MANAGER_H_
#define _TABLE_MANAGER_H_

#include "table.h"
#include "object_list.h"
#include "object_pool.h"
#include "table_Hundred.h"
using namespace svrlib;

#define MAX_PER_ROOM_HUNDERD 20
typedef std::map<_uint32, CTable*> MAPNumTable;
class TableManager
{
public:
    static TableManager *GetInstance();

protected:
    TableManager();
    virtual ~TableManager();

public:
    /**
     *  @brief: 初始化管理器
     *  @return: 成功返回true, 失败返回false
    **/
    int init(int table_count);
    
    /**
     *  @brief: 删除桌子
     *  @pTable: IN 桌子指针
     *  @return: 成功返回true, 失败返回false
    **/
    bool delete_table(CTable *pTable);
    
    /**
     *  @brief: 删除桌子
     *  @tableID: IN 桌子ID
     *  @return: 成功返回true, 失败返回false
    **/
    bool delete_table(int tableID);
    
    /**
     *  @brief: 获得桌子指针
     *  @tableID: IN 桌子ID
     *  @return: 成功返回桌子指针，失败返回NULL
    **/
    CTable *get_table(int tableID);

    /* @bref 通过房间号获取空闲房间
     * @param num房间号
     * @ret 0失败 !0成功
    */
    CTable *get_table_from_num(_uint32 num, const void * rule, int rule_len = 0, int is_web=0);

    /* @bref 通过房间号查找已经存在的房间
     * @param num房间号
     * @ret 0失败 !0成功
    */
    CTable *find_table_from_num(_uint32 num);
    
    /**
     *  @brief: 获得桌子数量
     *  @return: 返回房间中已创建桌子数量
    **/
    int get_table_count();

	/*
	*/
	int get_robot_table_count(int nodeid);

    /**
     *  @brief: 获得空闲桌子数量
     *  @return: 返回房间空闲桌子数量
    **/
    int get_free_count();

	/*
	*  @brief: 寻找合适的金币房桌子
	*  @return: 返回桌子
	*/
	CTable* find_nomal_table(int now_table=INVALID_TABLE, int nodeid = 0);

	/*
	*  @brief: 寻找合适的金币房桌子,先选多真人桌
	*  @return: 返回桌子
	*/	
	CTable* find_nomal_real_table(int now_table = INVALID_TABLE, int nodeid = 0);

	/*
	* 寻找没有真人的桌子,只允许放入一个真人
	*/
	CTable* find_nomal_real_one(int now_table = INVALID_TABLE, int nodeid = 0);

	/*
	*  @brief: 寻找合适的切磋房桌子
	*  @return: 返回桌子
	*/
	CTable* find_qiecuo_table(int now_table=INVALID_TABLE, int nodeid = 0, const char * rule = NULL);
	
	/*
	*  @brief: 寻找空闲桌子（0用户）
	*  @return: 返回桌子
	*/
	CTable* find_free_table(int nodeid);

	/*
	*  @brief: 寻找比赛空闲桌子（0用户）
	*  @return: 返回桌子
	*/
	CTable* find_match_free_table(int matchsn);

    /**
     *  @brief: 获得所有桌子指针
    **/
	const MAPNumTable*	get_table_list() {return &m_map_table;}
	CObjectList<CTable*>*	get_table_object_list() {return &m_table_list;}

	//释放所有空闲机器人
	void release_all_free_robot();

public:
	/*
		初始化百人场
	*/
	void init_hundred_table(map_node_info  m_map_node_info);

	/*
		重置百人场桌子
	*/
	void reset_hundred_table(int nodeid);

	/*
		重置普通房规则
	*/
	void reset_normal_rule(string game_rule, int nodeid);

	//重置百人场规则
	void reset_hundred_rule(map_node_info  m_map_node_info);

	/*
		查找对应节点场次
	*/
	CTableHundred* get_hundred_table(int node_id);
	/*
		获得百人场总限制人数
	*/
	int get_hundred_limit_user() { return  m_limit_user; }

	/*
		挑选一个百人场节点
	*/
	int get_rand_hundred_node();

	/*
		读取所有百人场节点
	*/
	void get_all_hundred_node(vector<int> &node_list);

	//寻找空闲百人桌子
	CTableHundred* get_free_hundred_table();

public:
	//打印桌子详细信息
	void print_table_info();


private:
    CObjectPool<CTable>             m_table_pool;                       //桌子缓冲
    CObjectList<CTable*>            m_table_list;                       //桌子列表
    MAPNumTable                     m_map_table;                        /* table num for table */

private:
	CTableHundred					m_tableHundred[MAX_PER_ROOM_HUNDERD]; //百人场列表
	int								m_limit_user;
};

#endif

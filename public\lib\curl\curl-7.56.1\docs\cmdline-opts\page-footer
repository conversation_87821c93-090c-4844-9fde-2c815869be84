.SH FILES
.I ~/.curlrc
.RS
Default config file, see --config for details.
.SH ENVIRONMENT
The environment variables can be specified in lower case or upper case. The
lower case version has precedence. http_proxy is an exception as it is only
available in lower case.

Using an environment variable to set the proxy has the same effect as using
the --proxy option.

.IP "http_proxy [protocol://]<host>[:port]"
Sets the proxy server to use for HTTP.
.IP "HTTPS_PROXY [protocol://]<host>[:port]"
Sets the proxy server to use for HTTPS.
.IP "[url-protocol]_PROXY [protocol://]<host>[:port]"
Sets the proxy server to use for [url-protocol], where the protocol is a
protocol that curl supports and as specified in a URL. FTP, FTPS, POP3, IMAP,
SMTP, LDAP etc.
.IP "ALL_PROXY [protocol://]<host>[:port]"
Sets the proxy server to use if no protocol-specific proxy is set.
.IP "NO_PROXY <comma-separated list of hosts>"
list of host names that shouldn't go through any proxy. If set to a asterisk
\&'*' only, it matches all hosts.

Since 7.53.0, this environment variable disable the proxy even if specify
--proxy option. That is
.B NO_PROXY=direct.example.com curl -x http://proxy.example.com
.B http://direct.example.com
accesses the target URL directly, and
.B NO_PROXY=direct.example.com curl -x http://proxy.example.com
.B http://somewhere.example.com
accesses the target URL through proxy.

.SH "PROXY PROTOCOL PREFIXES"
Since curl version 7.21.7, the proxy string may be specified with a
protocol:// prefix to specify alternative proxy protocols.

If no protocol is specified in the proxy string or if the string doesn't match
a supported one, the proxy will be treated as an HTTP proxy.

The supported proxy protocol prefixes are as follows:
.IP "socks4://"
Makes it the equivalent of --socks4
.IP "socks4a://"
Makes it the equivalent of --socks4a
.IP "socks5://"
Makes it the equivalent of --socks5
.IP "socks5h://"
Makes it the equivalent of --socks5-hostname
.SH EXIT CODES
There are a bunch of different error codes and their corresponding error
messages that may appear during bad conditions. At the time of this writing,
the exit codes are:
.IP 1
Unsupported protocol. This build of curl has no support for this protocol.
.IP 2
Failed to initialize.
.IP 3
URL malformed. The syntax was not correct.
.IP 4
A feature or option that was needed to perform the desired request was not
enabled or was explicitly disabled at build-time. To make curl able to do
this, you probably need another build of libcurl!
.IP 5
Couldn't resolve proxy. The given proxy host could not be resolved.
.IP 6
Couldn't resolve host. The given remote host was not resolved.
.IP 7
Failed to connect to host.
.IP 8
Weird server reply. The server sent data curl couldn't parse.
.IP 9
FTP access denied. The server denied login or denied access to the particular
resource or directory you wanted to reach. Most often you tried to change to a
directory that doesn't exist on the server.
.IP 10
FTP accept failed. While waiting for the server to connect back when an active
FTP session is used, an error code was sent over the control connection or
similar.
.IP 11
FTP weird PASS reply. Curl couldn't parse the reply sent to the PASS request.
.IP 12
During an active FTP session while waiting for the server to connect back to
curl, the timeout expired.
.IP 13
FTP weird PASV reply, Curl couldn't parse the reply sent to the PASV request.
.IP 14
FTP weird 227 format. Curl couldn't parse the 227-line the server sent.
.IP 15
FTP can't get host. Couldn't resolve the host IP we got in the 227-line.
.IP 16
HTTP/2 error. A problem was detected in the HTTP2 framing layer. This is
somewhat generic and can be one out of several problems, see the error message
for details.
.IP 17
FTP couldn't set binary. Couldn't change transfer method to binary.
.IP 18
Partial file. Only a part of the file was transferred.
.IP 19
FTP couldn't download/access the given file, the RETR (or similar) command
failed.
.IP 21
FTP quote error. A quote command returned error from the server.
.IP 22
HTTP page not retrieved. The requested url was not found or returned another
error with the HTTP error code being 400 or above. This return code only
appears if --fail is used.
.IP 23
Write error. Curl couldn't write data to a local filesystem or similar.
.IP 25
FTP couldn't STOR file. The server denied the STOR operation, used for FTP
uploading.
.IP 26
Read error. Various reading problems.
.IP 27
Out of memory. A memory allocation request failed.
.IP 28
Operation timeout. The specified time-out period was reached according to the
conditions.
.IP 30
FTP PORT failed. The PORT command failed. Not all FTP servers support the PORT
command, try doing a transfer using PASV instead!
.IP 31
FTP couldn't use REST. The REST command failed. This command is used for
resumed FTP transfers.
.IP 33
HTTP range error. The range "command" didn't work.
.IP 34
HTTP post error. Internal post-request generation error.
.IP 35
SSL connect error. The SSL handshaking failed.
.IP 36
Bad download resume. Couldn't continue an earlier aborted download.
.IP 37
FILE couldn't read file. Failed to open the file. Permissions?
.IP 38
LDAP cannot bind. LDAP bind operation failed.
.IP 39
LDAP search failed.
.IP 41
Function not found. A required LDAP function was not found.
.IP 42
Aborted by callback. An application told curl to abort the operation.
.IP 43
Internal error. A function was called with a bad parameter.
.IP 45
Interface error. A specified outgoing interface could not be used.
.IP 47
Too many redirects. When following redirects, curl hit the maximum amount.
.IP 48
Unknown option specified to libcurl. This indicates that you passed a weird
option to curl that was passed on to libcurl and rejected. Read up in the
manual!
.IP 49
Malformed telnet option.
.IP 51
The peer's SSL certificate or SSH MD5 fingerprint was not OK.
.IP 52
The server didn't reply anything, which here is considered an error.
.IP 53
SSL crypto engine not found.
.IP 54
Cannot set SSL crypto engine as default.
.IP 55
Failed sending network data.
.IP 56
Failure in receiving network data.
.IP 58
Problem with the local certificate.
.IP 59
Couldn't use specified SSL cipher.
.IP 60
Peer certificate cannot be authenticated with known CA certificates.
.IP 61
Unrecognized transfer encoding.
.IP 62
Invalid LDAP URL.
.IP 63
Maximum file size exceeded.
.IP 64
Requested FTP SSL level failed.
.IP 65
Sending the data requires a rewind that failed.
.IP 66
Failed to initialise SSL Engine.
.IP 67
The user name, password, or similar was not accepted and curl failed to log in.
.IP 68
File not found on TFTP server.
.IP 69
Permission problem on TFTP server.
.IP 70
Out of disk space on TFTP server.
.IP 71
Illegal TFTP operation.
.IP 72
Unknown TFTP transfer ID.
.IP 73
File already exists (TFTP).
.IP 74
No such user (TFTP).
.IP 75
Character conversion failed.
.IP 76
Character conversion functions required.
.IP 77
Problem with reading the SSL CA cert (path? access rights?).
.IP 78
The resource referenced in the URL does not exist.
.IP 79
An unspecified error occurred during the SSH session.
.IP 80
Failed to shut down the SSL connection.
.IP 82
Could not load CRL file, missing or wrong format (added in 7.19.0).
.IP 83
Issuer check failed (added in 7.19.0).
.IP 84
The FTP PRET command failed
.IP 85
RTSP: mismatch of CSeq numbers
.IP 86
RTSP: mismatch of Session Identifiers
.IP 87
unable to parse FTP file list
.IP 88
FTP chunk callback reported error
.IP 89
No connection available, the session will be queued
.IP 90
SSL public key does not matched pinned public key
.IP XX
More error codes will appear here in future releases. The existing ones
are meant to never change.
.SH AUTHORS / CONTRIBUTORS
Daniel Stenberg is the main author, but the whole list of contributors is
found in the separate THANKS file.
.SH WWW
https://curl.haxx.se
.SH "SEE ALSO"
.BR ftp (1),
.BR wget (1)

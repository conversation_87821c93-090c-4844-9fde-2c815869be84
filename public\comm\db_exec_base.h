﻿/*
-----------------------------------------------------------------------------

File name        :   db_exec_base.h        
Author           :     
Version          :   1.0            
Date             :      
Description      :   DB代理客户端实现  

-----------------------------------------------------------------------------
*/
#ifndef __DB_EXEC_MODEL_HEAD_FILE______
#define __DB_EXEC_MODEL_HEAD_FILE______

#include "typedef.h"


#include "stdlib.h"
#include <stdio.h>
#ifdef _WINNT_SYSTEM
#include <stdarg.h>
#include <wtypes.h>
#else
#include <string.h>
#endif

#include "dll_model_helper.h"
#include "dbproxy_interface.h"
#include "sock_engine_interface.h"
#include "buffer_interface.h"
#include "thread_interface.h"

#include <map>
#include <list>


using namespace std;
using namespace svrlib;


class CDBProxyClient:
    private ISockSink,
    public IDBPClient
{
public:
    CDBProxyClient(void);
    ~CDBProxyClient(void);

public:
	//初始化配置
	void init_config(const char* ip, int port);
	//获取链接状态
	bool is_connect() { return m_b_connecting; }

public:
	//设置平台标识
	void set_plat(int plat) { m_plat = plat; }
	//获取平台标识
	int get_plat() { return m_plat; }

public:
	virtual  void  set_connect_event(CONNET_EVENT event_func) { m_connect_func = event_func; }
    virtual  bool  start(ICommon* pcom,ILog* plog,IAttemper*pattemper);
    virtual  bool  stop();

    virtual  bool  post_exec(void*obj,DBPSINK sink,_uint64 UserID,const char* cmd,const char* data,cmd_dbproxy::CDBProxyExceSection &section,bool b_wait_dbresult);

    //获取等待队列数
    virtual  int   wait_count(){ CGuard guard(m_setionlist_lock);  return m_setionlist.size();}

private:
    virtual FTYPE(void)  on_log(TLOGTYPE logtype,const char* sz_msg);
    static  FTYPE(void)  on_check_connect(void* obj);
    virtual FTYPE(void)  on_connected(_uint32 type,socket_id sid);
    virtual FTYPE(void)  on_disconnected(_uint32 type,socket_id sid,int code,int ext_code);
    virtual FTYPE(int)   on_recv(_uint32 type,socket_id sid,const void* p_data,int size,_uint64 bind_tag,const void* p_bind); 
    virtual FTYPE(void)  on_event(_uint32 type,socket_id sid,SEVTCODE code,_uint32 lparam,_uint32 wparam);
    static  FTYPE(void)  on_request_timeout(void*p_obj,socket_id requestid,_uint32 delay,_uint32 repeat,void* pdata);

    static FTYPE(void)   on_attemper_timeout(void* pData,int size,const WLPARAM *pParam);
    static FTYPE(void)   on_attemper_recv(void* pData,int size,const WLPARAM *pParam);

#pragma pack(push,1)
    struct tagSectionHead
    {
        void*    obj;
        DBPSINK  sink;
        _uint64  uid;
        bool     b_wait_dbresult;
        int      slen;
    };
#pragma pack(pop)

    inline bool    __send(_uint32 requestid,_uint64 uid,const char* cmd,const char* data,bool b_wait_dbresult,bool bcheck);
    inline void    __check_sendlist();
    inline bool    __get_section(_uint32 requestid,cmd_dbproxy::CDBProxyExceSection &section,tagSectionHead &sechead,bool bremove);
    inline void   on_atp_recv(const char* head,int hlen,const char* data,int dlen); 
    inline void   on_atp_timeout(_uint32 requestid);
    inline bool   __get_dbproxy_ipinfo(char* szip,int ipbfs,int &port);

private:
	string					   m_ip;
	int						   m_port;
	int						   m_plat;

private:
	CONNET_EVENT			   m_connect_func;
    CDLLHelper<ITCPClient>     m_client;
    CDLLHelper<IBlockPoolEx>   m_bfpool;
    socket_id                  m_socketid;
    ICommon*                   m_com;
    ILog*                      m_log;
    IAttemper*                 m_pattemper;

    map<_uint32,TBlock*>       m_setionlist;
    CLock                      m_setionlist_lock;
    list<_uint32>              m_send_list;
    CLock                      m_send_list_lock;
    bool                       m_b_connecting;
    _uint32                    m_last_check_time;
    _uint32                    m_last_sndheart_time;
};


//****************************多DB代理管理器*************************************
#define MAX_DBCLIENT_COUNT  1000

typedef map<int, string> map_db_name;
typedef map_db_name::iterator iter_db_name;

class CDBProxyClientManager : public IDBPClientManager
{
public:
	CDBProxyClientManager();
	~CDBProxyClientManager();

public:
	//加载库名配置
	virtual bool load_db_name();
	//获取对应数据库名
	virtual bool get_db_name(int plat, char *dbname, int buff_len);

public:
	//初始化
	virtual bool start(ICommon* pcom, ILog* plog, IAttemper*pattemper, CONNET_EVENT event_func);
	//停止
	virtual bool stop();
	//投递
	virtual bool post_exec(void*obj, DBPSINK sink, _uint64 UserID, const char* cmd, const char* data, cmd_dbproxy::CDBProxyExceSection &section, bool b_wait_dbresult=true, int plat=0);

private:
	int			   m_main_plat;
	int			   m_dbproxy_count;
	CDBProxyClient m_dbproxy_client[MAX_DBCLIENT_COUNT];

private:
	ICommon*                   m_com;
	ILog*                      m_log;

private:
	map_db_name				   m_map_db_name;

};




#endif//__DB_EXEC_MODEL_HEAD_FILE______
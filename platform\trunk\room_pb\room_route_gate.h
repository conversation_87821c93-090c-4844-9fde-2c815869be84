﻿

#ifndef _ROOM_ROUTE_GATE_H_
#define _ROOM_ROUTE_GATE_H_

#include "room_common.h"

class CRoomRouteGate
{
public:
    CRoomRouteGate();
    virtual ~CRoomRouteGate();

public:
    /**
     *  @brief: 房间与网关连接成功事件
     *  @pid: IN 通道ID
     *  @return: 无返回值
    **/
    static FTYPE(void) connect_succeed(void * obj, enAction type, const void * pdata, int size);
    static FTYPE(void) user_enter(void * obj, enAction type, const void * pdata, int size);
    static FTYPE(void) user_leave(void * obj, enAction type, const void * pdata, int size);

private:
    static void send_gate_user_info(_tint64 userID, int cmd);
};

#endif

#include "winlose.h"
#include "game_config.h"
#include "log_manager.h"
#define NORMAL_MULT_COUNT 25
#define SPRIN_MULT_COUNT 27
namespace FG {

	const int normal_mult[NORMAL_MULT_COUNT] = { 80,160,200,280,400,600,800,1000,1600,2000,2080,2200,2400,2800,4000,4200,6000,8000,10000,20000,20200,22000,40000,100000,200000 };
	const int sprin_mult[SPRIN_MULT_COUNT] = { 160,240,320,400,480,600,800,1000,1200,1600,2000,2400,3200,4000,4800,6000,8000,10000,12000,16000,20000,24000,40000,60000,80000,100000,120000 };
	winlose_control::winlose_control()
	{
        
	}

	winlose_control::~winlose_control()
	{

	}

	//获得配置
	iter_stock_cfg winlose_control::get_config(int plat_id, int gameid, int nodeid)
	{
		char key_str[64] = { 0 };
		sprintf(key_str, CUR_STOCK_CFG, plat_id, gameid, nodeid);
		string k = key_str;
		return m_map_stock_cfg.find(k);
	}

	//初始化配置
	void winlose_control::init_config(int plat_id, int gameid, int nodeid)
	{
		bool need_flush_cfg = false;
		int times = myredis::GetInstance()->get_update_stock_time(plat_id, gameid, nodeid);
		iter_stock_cfg conf_iter = get_config(plat_id, gameid, nodeid);
		if (conf_iter == m_map_stock_cfg.end())
		{
			srand(time(0));
			need_flush_cfg = true;
			MY_LOG_DEBUG("init_config need_flush_cfg.");
		}
		else
		{
			MY_LOG_DEBUG("init_config need_flush_cfg times:%d update_time:%d", times, conf_iter->second.update_time);
			if (times != conf_iter->second.update_time)
			{
				need_flush_cfg = true;
			}
		}

		if (!need_flush_cfg)
			return;

		load_config(plat_id, gameid, nodeid, times);
	}

	//加载表权重
	void winlose_control::load_form_weight(Json::Value json_value, const char* str, vector<form_item>& form, int& total_weight)
	{
		total_weight = 0;
		int player_form_count = json_value[str].size();
		for (int i = 0; i < player_form_count; i++)
		{
			form_item item;
			item.multer = json_value[str][i]["multer"].asInt();
			item.touch = json_value[str][i]["rate"].asInt();

			form.push_back(item);
			total_weight += item.touch;
		}
	}

	//加载配置
	void winlose_control::load_config(int plat_id, int gameid, int nodeid, int update_time)
	{
		char* str = myredis::GetInstance()->get_stock_config(plat_id, gameid, nodeid);
		if (str == NULL)
			return;

		stock_cfg_item cfg_item;
		Json::Reader json_reader;
		Json::Value json_value;
		Json::Value va;
		if (!json_reader.parse((const char*)str, json_value))
			return;

		cfg_item.update_time = update_time;
		cfg_item.kill_rate = json_value["kill_rate"].asInt();
		if (!json_value["base_bet"].isNull())
		{
			cfg_item.base_bet = json_value["base_bet"].asInt();
		}
		//string key_arry[15] = { "bet_0.6_win", "bet_1.2_win", "bet_3_win", "bet_6_win", "bet_9_win",
		//                      "bet_15_win","bet_30_win","bet_45_win","bet_90_win", "bet_210_win",
		//                        "bet_300_win", "bet_450_win", "bet_666_win","bet_960_win", "bet_1950_win"};

		string cfg_key = "bet_0.6_win";
		vector<form_item> vec_bet_form_win;
		int ntotal_win = 0;
		load_form_weight(json_value, cfg_key.c_str(), vec_bet_form_win, ntotal_win);
		cfg_item.map_bet_form_win[1].bet_form_win_total = ntotal_win;
		cfg_item.map_bet_form_win[1].vec_bet_form_win = vec_bet_form_win;
		for (int i = 2; i < 16; i++)
		{   
			cfg_item.map_bet_form_win[i].bet_form_win_total = cfg_item.map_bet_form_win[1].bet_form_win_total;
			for (int j = 0; j < cfg_item.map_bet_form_win[1].vec_bet_form_win.size(); j++)
			{  
				form_item item;
				item.touch = cfg_item.map_bet_form_win[1].vec_bet_form_win[j].touch;
				item.multer = cfg_item.map_bet_form_win[1].vec_bet_form_win[j].multer;

				cfg_item.map_bet_form_win[i].vec_bet_form_win.push_back(item);
				MY_LOG_DEBUG("load_config mode multer:%d touch:%d",i, item.multer, item.touch);
			}
		}
		//各个表格权重

		char key_str[64] = { 0 };
		sprintf(key_str, CUR_STOCK_CFG, plat_id, gameid, nodeid);
		string k = key_str;
		m_map_stock_cfg[k] = cfg_item;
	}

	bool winlose_control::get_ctrl_result(iter_stock_cfg iter, int type, int &result, bool &bRespin)
	{
		int lose = 0;
		int win = 0;
		int free_game = 0;
		int normal_game = 0;
		int total = 0;
		int kill_rate = iter->second.kill_rate;
		result = 0;
		vector<form_item> form;
		map<int, vec_from_item>::iterator formIter = iter->second.map_bet_form_win.find(type);
		if (formIter != iter->second.map_bet_form_win.end())
		{
			form = formIter->second.vec_bet_form_win;
			total = formIter->second.bet_form_win_total;
		}
		else
		{
			MY_LOG_WARN("get_ctrl_result config type :%d erro", type);
			return false;
		}
		if (kill_rate > 0)
		{
			int nRandKillRate = getRand(0, 100);
			if (nRandKillRate <= kill_rate)
			{
				result = 0;
				bRespin = false;
				return true;
			}
		}
		int r = getRand(0, total - 1);
		MY_LOG_DEBUG("winlose_control::get_ctrl_result type :%d randNum:%d total:%d", type, r, total);
		for (int i = 0; i < form.size(); i++)
		{
			if (r < form[i].touch)
			{
				result = form[i].multer;	
				bool bNoral = check_mult_is_normal(result);
				if (bNoral){
					int respin_rate = GameConfig::GetInstance()->get_respin_rate();
					int nRand = getRand(0, 100);
					if (respin_rate > 0 && nRand <= respin_rate)
					{
						bRespin = true;
					}
				}
				else {
					bRespin = true;
				}
			    
				MY_LOG_DEBUG("winlose_control::get_ctrl_result type :%d result:%d", type, result);
				return true;
			}
			else
			{
					r -= form[i].touch;
			}
		}
		return false;
		
	}

	// 获得抽奖结果控制参数
	bool winlose_control::get_lottery_ctr_result(int plat_id, int gameid, int nodeid, int type, int& result, bool &bRespin)
	{
		init_config(plat_id, gameid, nodeid);
		iter_stock_cfg iter = get_config(plat_id, gameid, nodeid);
		if (iter == m_map_stock_cfg.end())
		{
			MY_LOG_WARN("get_lottery_ctr_result config erro");
			return false;
		}

		return get_ctrl_result(iter, type, result, bRespin);
	}
	bool winlose_control::check_mult_is_normal(int nMult)
	{
		for (int i = 0; i < NORMAL_MULT_COUNT; i++)
		{
			if (nMult == normal_mult[i])
			{
				return true;
			}
		}
		return false;
	}
	
	int winlose_control::get_base_bet(int plat_id, int gameid, int nodeid)
	{
		init_config(plat_id, gameid, nodeid);
		iter_stock_cfg iter = get_config(plat_id, gameid, nodeid);
		if (iter == m_map_stock_cfg.end())
		{
			MY_LOG_WARN("get_base_bet config erro");
			return 0;
		}

		return iter->second.base_bet;
	}
}


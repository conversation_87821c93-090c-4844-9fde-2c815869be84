@echo off
echo GoldenBank 问题修复和运行脚本
echo ===============================

echo 步骤1: 环境检查...
call diagnose.bat
echo.

echo 步骤2: 尝试简化版本...
echo 这个版本不依赖复杂的库，应该能够运行
call run_simple.bat

if %errorlevel% equ 0 (
    echo.
    echo ✅ 简化版本运行成功！
    echo.
    echo 如果需要完整的 protobuf 比较功能，请:
    echo 1. 安装 protobuf 库
    echo 2. 运行 run_compare_test.bat
    goto :end
)

echo.
echo ❌ 简化版本也失败了
echo.
echo 让我们尝试最基本的测试...

echo 创建最简单的测试程序...
echo #include ^<iostream^> > minimal_test.cpp
echo #include ^<fstream^> >> minimal_test.cpp
echo #include ^<vector^> >> minimal_test.cpp
echo. >> minimal_test.cpp
echo int main() { >> minimal_test.cpp
echo     std::cout ^<^< "GoldenBank 最小测试" ^<^< std::endl; >> minimal_test.cpp
echo     std::vector^<int^> icons = {5,0,4,0,5,0,5,0,1}; >> minimal_test.cpp
echo     std::cout ^<^< "测试数据: "; >> minimal_test.cpp
echo     for(int i : icons) std::cout ^<^< i ^<^< " "; >> minimal_test.cpp
echo     std::cout ^<^< std::endl; >> minimal_test.cpp
echo     std::ofstream f("minimal_result.txt"); >> minimal_test.cpp
echo     f ^<^< "最小测试结果" ^<^< std::endl; >> minimal_test.cpp
echo     f.close(); >> minimal_test.cpp
echo     std::cout ^<^< "结果已保存到 minimal_result.txt" ^<^< std::endl; >> minimal_test.cpp
echo     return 0; >> minimal_test.cpp
echo } >> minimal_test.cpp

echo 编译最小测试...
g++ -std=c++11 minimal_test.cpp -o minimal_test.exe

if %errorlevel% equ 0 (
    echo ✅ 最小测试编译成功！
    echo 运行最小测试...
    minimal_test.exe
    echo.
    echo 如果看到这个消息，说明基本环境是正常的
    echo 问题可能在于:
    echo 1. 游戏逻辑文件缺失或有问题
    echo 2. 头文件路径不正确
    echo 3. 依赖库缺失
) else (
    echo ❌ 连最基本的 C++ 编译都失败了
    echo 请检查:
    echo 1. C++ 编译器是否正确安装
    echo 2. 系统环境变量是否正确设置
    echo 3. 是否有权限在当前目录创建文件
)

echo.
echo 清理临时文件...
if exist "minimal_test.cpp" del minimal_test.cpp
if exist "minimal_test.exe" del minimal_test.exe

:end
echo.
echo 修复脚本执行完成
pause

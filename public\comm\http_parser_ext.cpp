#include "http_parser_ext.h"
#include "http_util.h"
#include "log_manager.h"

using namespace std;

namespace tnet {

    size_t HttpParser::ms_maxHeaderSize = 80 * 1024;
    size_t HttpParser::ms_maxBodySize = 10 * 1024 * 1024;
    struct http_parser_settings ms_settings;

    class HttpParserSettings
    {
    public:
        HttpParserSettings();

        static int onMessageBegin(struct http_parser*);
        static int onUrl(struct http_parser*, const char*, size_t);
        static int onStatusComplete(struct http_parser*);
        static int onHeaderField(struct http_parser*, const char*, size_t);
        static int onHeaderValue(struct http_parser*, const char*, size_t);
        static int onHeadersComplete(struct http_parser*);
        static int onBody(struct http_parser*, const char*, size_t);
        static int onMessageComplete(struct http_parser*); 
    };

    HttpParserSettings::HttpParserSettings() {

        ms_settings.on_message_begin = &HttpParserSettings::onMessageBegin;
        ms_settings.on_url = &HttpParserSettings::onUrl;
        ms_settings.on_status_complete = &HttpParserSettings::onStatusComplete;
        ms_settings.on_header_field = &HttpParserSettings::onHeaderField;
        ms_settings.on_header_value = &HttpParserSettings::onHeaderValue;
        ms_settings.on_headers_complete = &HttpParserSettings::onHeadersComplete;
        ms_settings.on_body = &HttpParserSettings::onBody;
        ms_settings.on_message_complete = &HttpParserSettings::onMessageComplete;    
    }    

    static HttpParserSettings initObj;

    int HttpParserSettings::onMessageBegin(struct http_parser* parser) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_MessageBegin, 0, 0);
    }

    int HttpParserSettings::onUrl(struct http_parser* parser, const char* at, size_t length) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_Url, at, length);
    }

    int HttpParserSettings::onStatusComplete(struct http_parser* parser) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_StatusComplete, 0, 0);
    }

    int HttpParserSettings::onHeaderField(struct http_parser* parser, const char* at, size_t length) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_HeaderField, at, length);
    }

    int HttpParserSettings::onHeaderValue(struct http_parser* parser, const char* at, size_t length) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_HeaderValue, at, length);
    }

    int HttpParserSettings::onHeadersComplete(struct http_parser* parser) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_HeadersComplete, 0, 0);
    }

    int HttpParserSettings::onBody(struct http_parser* parser, const char* at, size_t length) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_Body, at, length);
    }

    int HttpParserSettings::onMessageComplete(struct http_parser* parser) {

        HttpParser* p = (HttpParser*)parser->data;
        return p->onParser(HttpParser::Parser_MessageComplete, 0, 0);
    }

    HttpParser::HttpParser(enum http_parser_type type, svrlib::socket_id sid, const RequestCallback_t& callback) {

        http_parser_init(&m_parser, type);
        m_parser.data = this;
   
        m_lastWasValue = true;
        m_callback = callback;
        m_fd = sid;
    }
   
    HttpParser::~HttpParser() {
    }

    int HttpParser::onParser(Event event, const char* at, size_t length) {
        switch(event) {
            case Parser_MessageBegin:
                return handleMessageBegin();
            case Parser_Url:
                return onUrl(at, length);
            case Parser_StatusComplete:
                return 0;
            case Parser_HeaderField:
                return handleHeaderField(at, length);
            case Parser_HeaderValue:
                return handleHeaderValue(at, length);
            case Parser_HeadersComplete:
                return handleHeadersComplete();
            case Parser_Body:
                return onBody(at, length);
            case Parser_MessageComplete:
                return onMessageComplete();
            default:
                break;
        }

        return 0;
    }

    int HttpParser::handleMessageBegin() {

        m_curField.clear();
        m_curValue.clear();
        m_lastWasValue = true;
        
        m_errorCode = 0;

        return onMessageBegin();
    }        
        
    int HttpParser::handleHeaderField(const char* at, size_t length) {

        if(m_lastWasValue) {
            if(!m_curField.empty()) {  
                onHeader(HttpUtil::normalizeHeader(m_curField), m_curValue);
            }
            
            m_curField.clear();    
            m_curValue.clear();
        }

        m_curField.append(at, length);

        m_lastWasValue = 0;

        return 0;
    }
        
    int HttpParser::handleHeaderValue(const char* at, size_t length) {

        m_curValue.append(at, length);
        m_lastWasValue = 1;

        return 0;
    }
        
    int HttpParser::handleHeadersComplete() {
        if(!m_curField.empty()) {
            string field = HttpUtil::normalizeHeader(m_curField); 
            onHeader(field, m_curValue);    
        }    

        return onHeadersComplete();
    }

    int HttpParser::execute(const char* buffer, size_t count) {

        int n = http_parser_execute(&m_parser, &ms_settings, buffer, count);
        if(m_parser.upgrade) {
            onUpgrade(buffer + n, count - n); 
            return 0;
        } else if(n != count) {
            int code = (m_errorCode != 0 ? m_errorCode : 400);
            
            HttpError error(code, http_errno_description((http_errno)m_parser.http_errno));

            MY_LOG_ERROR("parser error %s", error.message.c_str());
            
            onError(error);

            return code;
        }

        return 0;
    }

    int HttpParser::onMessageBegin() {
        m_request.clear();
        return 0;
    }

    int HttpParser::onUrl(const char* at, size_t length) {

        m_request.url.append(at, length);
        return 0;
    }

    int HttpParser::onHeader(const string& field, const string& value) {        

        if(m_parser.nread >= ms_maxHeaderSize) {
            m_errorCode = 413;
            return -1;
        }
 
        m_request.headers.insert(make_pair(field, value));
        return 0;
    }

    int HttpParser::onBody(const char* at, size_t length) {

        if(m_request.body.size() + length > ms_maxBodySize) {
            m_errorCode = 413;
            return -1;
        }

        m_request.body.append(at + 1, length);
        return 0;
    }

    int HttpParser::onHeadersComplete() {

        m_request.majorVersion = m_parser.http_major;
        m_request.minorVersion = m_parser.http_minor;
        m_request.method = (http_method)m_parser.method;

        m_request.parseUrl();

        return 0;
    }

    int HttpParser::onMessageComplete() {

        if(!m_parser.upgrade) {
            m_callback(m_fd, m_request, Request_Complete, 0);   
        }

        return 0;
    }

    int HttpParser::onUpgrade(const char* at, size_t length) {

        StackBuffer buffer(at, length);
        m_callback(m_fd, m_request, Request_Upgrade, &buffer);
        m_request.upgrade = true;

        return 0;
    }

    int HttpParser::onError(const HttpError& error) {

        m_callback(m_fd, m_request, Request_Error, (void*)&error);
        return 0;
    }
}

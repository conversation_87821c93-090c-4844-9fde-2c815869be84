﻿#include "gate_route_room.h"
#include "user_manager.h"
#include "log_manager.h"
#include "table_manager.h"
#include "config_manager.h"
#include "table_Hundred.h"
static CGateRouteRoom _gate_route_room;
CGateRouteRoom::CGateRouteRoom()
{
    __reg_gate_rcv(update_user_game_info, cmd_net::CMD_FROM_GATE, cmd_net_svr::CMD_FROM_GATE_UPDATE_USER_GAME_INFO);        //更新用户游戏数据
    //__reg_gate_rcv(update_user_logon_info, cmd_net::CMD_FROM_GATE, cmd_net_svr::CMD_FROM_GATE_UPDATE_USER_LOGON_INFO);        //更新用户游戏数据
}

CGateRouteRoom::~CGateRouteRoom()
{

}

FTYPE(void) CGateRouteRoom::update_user_game_info(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{
    if (!head || !data || size == 0) return ;

	cmd_gate::CUserGameInfo gameinfo;
	gameinfo.unpack(data,size);

	if(!gameinfo.exist_UserID()) return;


	IUser* puser = UserManager::GetInstance()->get_user(gameinfo.get_UserID());
	if(puser && puser->get_user_type() != ROBOT_TYPE)
	{
		if(gameinfo.exist_RoomCard())
		{
			puser->set_room_card(gameinfo.get_RoomCard());
		}
		//if (gameinfo.exist_Gold())
		//{
			_tint64 now_gold = myredis::GetInstance()->get_data_by_uid(gameinfo.get_UserID(), DB_COMM, CUR_GOLD);
			int changegold   = now_gold - puser->get_gold();
			int last_gold    = puser->get_last_gold();
			MY_LOG_DEBUG("update_user_game_info uid:%lld last_gold:%d changegold=%d gold=%lld",
				gameinfo.get_UserID(), last_gold, changegold, now_gold);

			puser->set_gold(now_gold);
			//puser->set_changegold(changegold);
			//puser->set_last_gold(last_gold + changegold);
		//}

		if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
		{
			int node_id = puser->get_node_id();
			CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
			if (ptable)
			{
				ptable->on_user_game_info(puser);
			}
		}
		else
		{
			CTable* pTable = TableManager::GetInstance()->get_table(puser->get_table_id());
			if (pTable != NULL)
			{
				pTable->broadcast_user_info(puser);

				if (gameinfo.exist_Gold())
				{
					pTable->on_user_game_info(puser);
				}
			}
		}
	}
}

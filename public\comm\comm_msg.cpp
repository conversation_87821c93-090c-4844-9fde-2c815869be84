#include "comm_msg.h"
#include "log_manager.h"
#include "typedef.h"
#include "md5encrypt.h"
#include "cmd_room.h"

#include <map>

static char g_map_language_str_md5[33] = "1111";
static char  g_language_error_str[128];

typedef std::map<int, std::string> map_int_str;
typedef map_int_str::iterator  iter_map_int_str;

typedef std::map<int, map_int_str> map_map_language_str;
typedef map_map_language_str::iterator iter_map_map_language_str;


static map_map_language_str g_map_map_language_str;
static map_int_str g_map_int_str;


int get_languate_code(string country)
{
	for (iter_map_int_str iter = g_map_int_str.begin(); iter != g_map_int_str.end(); iter++)
	{
		if (country == iter->second)
		{
			return iter->first;
		}
	}
	return 0;
}

const char* get_error_msg(int error, int country_code)
{
	iter_map_map_language_str iter = g_map_map_language_str.find(country_code);
	if (iter == g_map_map_language_str.end())
	{
		MY_LOG_ERROR("get_error_msg unkown error type %d", country_code);
		__sprintf(g_language_error_str, sizeof(g_language_error_str), "unkown error(%d)", error);
		return  g_language_error_str;
	}

    auto i = iter->second.find(error);
    if (i != iter->second.end() && i->second.length() > 0)
        return i->second.c_str();

	MY_LOG_ERROR("get_error_msg unkown error error %d", error);
	__sprintf(g_language_error_str, sizeof(g_language_error_str), "unkown error(%d)", error);
    return  g_language_error_str;
}

inline bool parse_map_mode(FILE* file, map_int_str& mapstr)
{
	//g_language_type = LANGUAGE_TYPE_ENGLISH;
    char buf[4096];
    char sz_id[32];
    char sz_string[4000];
	//bool bgetlanguage_type = false;
    while (fgets(buf, sizeof(buf), file))
    {
		/*if (!bgetlanguage_type)
		{
			bgetlanguage_type = true;
			if (memcmp(buf, "thai",4) == 0)
				g_language_type = LANGUAGE_TYPE_THAI;
			else if (memcmp(buf, "spain", 5) == 0)
				g_language_type = LANGUAGE_TYPE_SPAIN;
			if(strchr(buf,'=')==NULL) continue;			
		}*/
		if (strchr(buf, '=') == NULL) 
			continue;

        int l = strlen(buf);
        int si = 0;
        for (int i = 0; i < 32; i++)
        {
            if (buf[i] != '=')
                sz_id[i] = buf[i];
            else
            {
                sz_id[i] = 0;
                si = i + 1;
                break;
            }
        }
        if (strlen(sz_id) == 0)
            return false;
        int id = atoi(sz_id);
        int index = 0;
        for (int i = si; i < l; i++)
        {
            if (buf[i] != '\r' && buf[i] != '\n')
            {
                sz_string[index] = buf[i];
                index++;
            }
        }
        sz_string[index] = 0;

        mapstr[id] = std::string(sz_string);
    }

    return true;
}

bool  load_languate_entry()
{
	char pfilename[64] = "languate_entry.txt";
	char szfilename[256] = { 0 };
	char* penv = getenv("server_text_path");
	bool benvpath = false;
	if (penv)
	{
		__sprintf(szfilename, sizeof(szfilename), "%s%s", penv, pfilename);
		benvpath = true;
	}
	else
		__sprintf(szfilename, sizeof(szfilename), "%s", pfilename);

	FILE* file = fopen(szfilename, "rb");
	if (!file && benvpath)
	{
		__sprintf(szfilename, sizeof(szfilename), "%s", pfilename);
		file = fopen(szfilename, "rb");
	}
	if (!file)
	{
		MY_LOG_ERROR("(%s)open fail", szfilename);
		return false;
	}

	map_int_str maplanguage_str;
	bool bres = parse_map_mode(file, maplanguage_str);
	fclose(file);
	if (bres)
	{
		g_map_int_str = maplanguage_str;
		for (iter_map_int_str iter = maplanguage_str.begin(); iter != maplanguage_str.end(); iter++)
		{
			bool s = load_language_str(iter->first);
			if (!s)
				return false;
		}
	}
	return true;
}

bool load_language_str(int country_code)
{
    char pfilename[64] = "string.txt";
	sprintf(pfilename, "%s%d.%s", "string", country_code, "txt");

	char szfilename[256] = { 0 };
	char* penv = getenv("server_text_path");

	bool benvpath = false;
	if (penv)
	{
		__sprintf(szfilename, sizeof(szfilename), "%s%s", penv, pfilename);
		benvpath = true;
	}
	else
		__sprintf(szfilename, sizeof(szfilename),"%s",  pfilename);

    FILE* file = fopen(szfilename, "rb");
	if (!file && benvpath)
	{
		__sprintf(szfilename, sizeof(szfilename), "%s", pfilename);
		file = fopen(szfilename, "rb");
	}
	if (!file)
	{
		MY_LOG_ERROR("(%s)open fail", szfilename);
		return false;
	}
	map_int_str maplanguage_str;
    bool bres = parse_map_mode(file, maplanguage_str);
    fclose(file);
	if (bres)
	{
		MY_LOG_PRINT("load (%s) ok", szfilename);
		char destmd5[33] = { 0 };
		CMD5 md5obj;
		char sztemp[10] = { 0 };
		int tmplen = 0;
		unsigned char digest[16] = { 0 };
		for (auto& r : maplanguage_str)
		{
			memset(sztemp, 0, sizeof(sztemp));
			tmplen = __sprintf(sztemp, sizeof(sztemp), "%d", r.first);
			md5obj.MD5Update((unsigned char *)sztemp, tmplen);
			md5obj.MD5Update((unsigned char *)r.second.c_str(), r.second.length());
		}
		md5obj.MD5Final(digest);
		for (int i = 0; i < 16; ++i)
		{
			__sprintf(destmd5+i*2,sizeof(destmd5)-2*i,"%02x",digest[i]);
		}
		if (memcmp(destmd5, g_map_language_str_md5, 32) == 0)
		{
			//相同返回
			MY_LOG_PRINT("load (%s) same return;", szfilename);
			return true;
		}
		memcpy(g_map_language_str_md5, destmd5, 32);

		g_map_map_language_str[country_code] = maplanguage_str;
	}
    return bres;
}

std::string get_room_error_msg(int error) {

    static std::map<cmd_room::ROOM_ENTER_FAIL_TYPE, std::string> room_error_msgs { 
			{ cmd_room::ROOM_ENTER_ROOM_EXPIRE_LIMIT  ,     "expire limit"     },
			{ cmd_room::ROOM_ENTER_ROOM_FULL_LIMIT    ,     "full limit"       },
			{ cmd_room::ROOM_ENTER_ROOM_START_LIMIT   ,     "start limit"      },
			{ cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT    ,     "card limit"       },
			{ cmd_room::ROOM_ENTER_ROOM_CFG_LIMIT     ,     "cfg limit"        },
			{ cmd_room::ROOM_IS_NOT_FREE              ,     "is not free"      },
			{ cmd_room::ROOM_NUM_IS_EXISTS            ,     "num is exists"    },
			{ cmd_room::ROOM_TABLE_NOT_ENOUGH         ,     "table not enough" },
			{ cmd_room::ROOM_NUM_NOT_FIND             ,     "num not find"     },
			{ cmd_room::ROOM_USER_IN_TABLE            ,     "user in table"    },
			{ cmd_room::ROOM_VERSION_NOT_MATCH        ,     "version not match"},
			{ cmd_room::ROOM_VIP_CREATE_LIMIT         ,     "vip crate limit"  },
			{ cmd_room::ROOM_NOMAL_LIMIT_GOLD         ,     "nomal limit gold" },
			{ cmd_room::ROOM_NOMAL_NODE_ERROR         ,     "nomal node error" },
			{ cmd_room::ROOM_NOMAL_IN_ORDER           ,     "nomal in order"   },
			{ cmd_room::ROOM_NOMAL_NOR_FIND           ,     "nomal not find"   },
			{ cmd_room::ROOM_NOMAL_LEAVE_FAILE        ,     "nomal leave faile"},
			{ cmd_room::ROOM_CHECK_TICK               ,     "check tick"       },
			{ cmd_room::ROOM_ENTER_ROOM_OBSERVE_LIMIT ,     "observe limit"    }, 
			{ cmd_room::ROOM_ENTER_ROOM_LIMIT         ,     "room limit"       },
			{ cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT  ,     "room server limit"},
		} ;

	auto iter = room_error_msgs.find((cmd_room::ROOM_ENTER_FAIL_TYPE)error);
	if (iter != room_error_msgs.end()) {
        return iter->second;
	}

    return "";
}
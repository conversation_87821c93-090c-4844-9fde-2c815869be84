#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_CONFIG_COUNT 8       	//转盘中奖分布

#define  MAX_ROW  5       //行数
#define  MAX_LIST 5       //列数
#define  MAX_WAYS 88 
#define  REMOVE_LINE  3

/* 牌面值 */
enum EMUN_MC_JL_CRAD
{
	BOX_PLUS = 12,
	BOX_SCATTER = 11,//bouns
	BOX_A = 3,// A
	BOX_K = 2,//K
	BOX_Q = 1,//Q
	BOX_J = 0,//J
	BOX_LANDK = 4,//蓝短裤
	BOX_HANDK = 5,//红短裤
	BOX_LQ = 6, //蓝拳
	BOX_HQ = 7, //红拳
	BOX_LT = 8, //老头
	BOX_REN = 9, //人
	BOX_WILD = 10, //wild
};

const int whell_config[MAX_BET_CONFIG_COUNT][WHEEL_CONFIG_COUNT] = {
	{0, 0, 0, 0, 0, 0, 0, 0},							 // 1
	{25, 50, 75, 100, 150, 250, 500, 1000},				 // 5
	{50, 100, 150, 200, 300, 500, 1000, 2000},			 // 10
	{500, 1000, 1500, 2000, 3000, 5000, 10000, 20000},	 // 50
	{1000, 2000, 3000, 4000, 6000, 10000, 20000, 40000}, // 100
};
const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
	1, 2, 3, 5, 8, 10, 20, 50, 100, 200, 300, 400, 500, 700, 1000
};
#define  ALL_CARD_LEN 10

struct pos
{
	int x;
	int y;
    pos()
    {
		x = 0;
		y = 0;
    }
	pos(int x, int y) 
	{
		this->x = x;
		this->y = y;
	}
};
const pos pay_lines[MAX_WAYS][5] =
{
	{{0, 2}, {1, 2}, {2, 2}, {3, 2}, {4, 2}}, //  1
	{{0, 3}, {1, 3}, {2, 3}, {3, 3}, {4, 3}}, //  2
	{{0, 1}, {1, 1}, {2, 1}, {3, 1}, {4, 1}}, //  3
	{{0, 4}, {1, 4}, {2, 4}, {3, 4}, {4, 4}}, //  4
	{{0, 0}, {1, 0}, {2, 0}, {3, 0}, {4, 0}}, //  5
	{{0, 4}, {1, 3}, {2, 2}, {3, 1}, {4, 0}}, //  6
	{{0, 0}, {1, 1}, {2, 2}, {3, 3}, {4, 4}}, //  7
	{{0, 0}, {1, 4}, {2, 4}, {3, 4}, {4, 0}}, //  8

	{{0, 4}, {1, 0}, {2, 0}, {3, 0}, {4, 4}}, //  9
	{{0, 4}, {1, 4}, {2, 3}, {3, 4}, {4, 4}}, // 10
	{{0, 0}, {1, 0}, {2, 1}, {3, 0}, {4, 0}}, // 11
	{{0, 2}, {1, 2}, {2, 4}, {3, 2}, {4, 2}}, // 12
	{{0, 2}, {1, 2}, {2, 0}, {3, 2}, {4, 2}}, // 13
	{{0, 3}, {1, 4}, {2, 3}, {3, 4}, {4, 3}}, // 14
	{{0, 1}, {1, 0}, {2, 1}, {3, 0}, {4, 1}}, // 15
	{{0, 3}, {1, 0}, {2, 3}, {3, 0}, {4, 3}}, // 16

	{{0, 1}, {1, 4}, {2, 1}, {3, 4}, {4, 1}}, // 17
	{{0, 2}, {1, 1}, {2, 2}, {3, 1}, {4, 2}}, // 18
	{{0, 2}, {1, 3}, {2, 2}, {3, 3}, {4, 2}}, // 19
	{{0, 3}, {1, 2}, {2, 3}, {3, 2}, {4, 3}}, // 20
	{{0, 1}, {1, 2}, {2, 1}, {3, 2}, {4, 1}}, // 21
	{{0, 2}, {1, 0}, {2, 0}, {3, 0}, {4, 2}}, // 22
	{{0, 2}, {1, 4}, {2, 4}, {3, 4}, {4, 2}}, // 23
	{{0, 3}, {1, 2}, {2, 2}, {3, 2}, {4, 3}}, // 24

	{{0, 1}, {1, 2}, {2, 2}, {3, 2}, {4, 1}}, // 25
	{{0, 2}, {1, 1}, {2, 1}, {3, 1}, {4, 2}}, // 26

	{{0, 2}, {1, 3}, {2, 3}, {3, 3}, {4, 2}}, // 27
	{{0, 1}, {1, 0}, {2, 0}, {3, 0}, {4, 1}}, // 28
	{{0, 3}, {1, 4}, {2, 4}, {3, 4}, {4, 3}}, // 29
	{{0, 4}, {1, 3}, {2, 3}, {3, 3}, {4, 4}}, // 30
	{{0, 0}, {1, 1}, {2, 1}, {3, 1}, {4, 0}}, // 31
	{{0, 2}, {1, 4}, {2, 2}, {3, 4}, {4, 2}}, // 32

	{{0, 1}, {1, 2}, {2, 3}, {3, 2}, {4, 1}}, // 33
	{{0, 1}, {1, 3}, {2, 0}, {3, 3}, {4, 1}}, // 34
	{{0, 3}, {1, 1}, {2, 4}, {3, 1}, {4, 3}}, // 35
	{{0, 4}, {1, 1}, {2, 1}, {3, 1}, {4, 4}}, // 36
	{{0, 0}, {1, 3}, {2, 3}, {3, 3}, {4, 0}}, // 37
	{{0, 4}, {1, 2}, {2, 2}, {3, 2}, {4, 4}}, // 38
	{{0, 0}, {1, 2}, {2, 2}, {3, 2}, {4, 0}}, // 39
	{{0, 1}, {1, 3}, {2, 1}, {3, 3}, {4, 1}}, // 40

	{{0, 4}, {1, 1}, {2, 4}, {3, 1}, {4, 4}}, // 41
	{{0, 1}, {1, 1}, {2, 3}, {3, 1}, {4, 1}}, // 42
	{{0, 3}, {1, 3}, {2, 1}, {3, 3}, {4, 3}}, // 43
	{{0, 1}, {1, 4}, {2, 0}, {3, 4}, {4, 1}}, // 44
	{{0, 3}, {1, 0}, {2, 4}, {3, 0}, {4, 3}}, // 45
	{{0, 2}, {1, 2}, {2, 3}, {3, 2}, {4, 2}}, // 46
	{{0, 2}, {1, 2}, {2, 1}, {3, 2}, {4, 2}}, // 47
	{{0, 4}, {1, 4}, {2, 2}, {3, 4}, {4, 4}}, // 48

	{{0, 0}, {1, 0}, {2, 2}, {3, 0}, {4, 0}}, // 49
	{{0, 1}, {1, 4}, {2, 2}, {3, 4}, {4, 1}}, // 50
	{{0, 3}, {1, 0}, {2, 2}, {3, 0}, {4, 3}}, // 51
	{{0, 4}, {1, 2}, {2, 4}, {3, 2}, {4, 4}}, // 52
	{{0, 0}, {1, 2}, {2, 0}, {3, 2}, {4, 0}}, // 53
	{{0, 1}, {1, 3}, {2, 2}, {3, 3}, {4, 1}}, // 54
	{{0, 3}, {1, 1}, {2, 2}, {3, 1}, {4, 3}}, // 55
	{{0, 4}, {1, 1}, {2, 3}, {3, 1}, {4, 4}}, // 56

	{{0, 0}, {1, 3}, {2, 1}, {3, 3}, {4, 0}}, // 57
	{{0, 2}, {1, 1}, {2, 0}, {3, 1}, {4, 2}}, // 58
	{{0, 2}, {1, 3}, {2, 4}, {3, 3}, {4, 2}}, // 59
	{{0, 1}, {1, 3}, {2, 3}, {3, 3}, {4, 1}}, // 60
	{{0, 3}, {1, 1}, {2, 1}, {3, 1}, {4, 3}}, // 61
	{{0, 4}, {1, 4}, {2, 0}, {3, 4}, {4, 4}}, // 62
	{{0, 0}, {1, 0}, {2, 4}, {3, 0}, {4, 0}}, // 63
	{{0, 0}, {1, 4}, {2, 0}, {3, 4}, {4, 0}}, // 64

	{{0, 4}, {1, 0}, {2, 4}, {3, 0}, {4, 4}}, // 65
	{{0, 1}, {1, 4}, {2, 4}, {3, 4}, {4, 1}}, // 66
	{{0, 3}, {1, 0}, {2, 0}, {3, 0}, {4, 3}}, // 67
	{{0, 0}, {1, 1}, {2, 0}, {3, 1}, {4, 0}}, // 68
	{{0, 4}, {1, 3}, {2, 4}, {3, 3}, {4, 4}}, // 69
	{{0, 3}, {1, 1}, {2, 3}, {3, 1}, {4, 3}}, // 70
	{{0, 4}, {1, 0}, {2, 1}, {3, 0}, {4, 4}}, // 71
	{{0, 3}, {1, 4}, {2, 1}, {3, 4}, {4, 3}}, // 72

	{{0, 1}, {1, 0}, {2, 3}, {3, 0}, {4, 1}}, // 73
	{{0, 0}, {1, 3}, {2, 2}, {3, 3}, {4, 0}}, // 74
	{{0, 4}, {1, 1}, {2, 2}, {3, 1}, {4, 4}}, // 75
	{{0, 4}, {1, 2}, {2, 0}, {3, 2}, {4, 4}}, // 76
	{{0, 0}, {1, 2}, {2, 4}, {3, 2}, {4, 0}}, // 77
	{{0, 2}, {1, 4}, {2, 1}, {3, 4}, {4, 2}}, // 78
	{{0, 2}, {1, 0}, {2, 3}, {3, 0}, {4, 2}}, // 79
	{{0, 0}, {1, 0}, {2, 3}, {3, 0}, {4, 0}}, // 80

	{{0, 4}, {1, 4}, {2, 1}, {3, 4}, {4, 4}}, // 81
	{{0, 3}, {1, 3}, {2, 4}, {3, 3}, {4, 3}}, // 82
	{{0, 1}, {1, 1}, {2, 0}, {3, 1}, {4, 1}}, // 83
	{{0, 1}, {1, 0}, {2, 4}, {3, 0}, {4, 1}}, // 84
	{{0, 3}, {1, 4}, {2, 0}, {3, 4}, {4, 3}}, // 85
	{{0, 2}, {1, 1}, {2, 3}, {3, 1}, {4, 2}}, // 86
	{{0, 2}, {1, 3}, {2, 1}, {3, 3}, {4, 2}}, // 87
	{{0, 4}, {1, 2}, {2, 3}, {3, 2}, {4, 4}}, // 88
};
struct card
{
    int value;
    int status;		//0-正常 1-将被消除		
    card()
    {
        value = 0;
        status = 0;
    }
};

struct fillInfo
{
    int value;
	int x;
	int y;
	int is_gold;
	int last;
    fillInfo()
    {
        value = 0;
		x = 0;
		y = 0;
		is_gold = 0;
		last = 0;
    }
};



// 卡牌赔率 *100之后的
const int card_multer[10][3] = 
{
    {5,20,50},
    {5,20,50},
    {10,40,100},
	{10,40,100},
    {15,60,150},
    {20,80,200},
	{25,100,250},
    {30,120,300},
	{40,160,400},
	{50,200,500},
};

const int combo_bonus[6] =
{
	1,2,3,5,6,8
};
struct reward
{
	int value;		//牌值
	int count;		//轴数
	int multer;		//倍率
	int LineNum;    //线数  
	vector<pos> vec_pos; 	 
	reward(int value, int count, int LineNum, int multer)
	{
		this->value = value;
		this->count = count;
		this->multer = multer;
		this->LineNum = LineNum;
	}
	reward()
	{
		value = 0;
		count = 0;
		multer = 0;
		LineNum = 0;
		vec_pos.clear();
	}
};

struct remove_info
{
	vector<reward> vec_reward;		//消除奖励
	vector<card> vec_now_box[MAX_LIST];//当前格子数据
	vector<fillInfo> vec_fill_box[MAX_LIST];//当前填充格子数据
	int comboBonus;
	int multer;
	remove_info()
	{
		vec_reward.clear();
		for (int i=0; i<MAX_LIST;i++)
		{
			vec_now_box[i].clear();
		}
		comboBonus = 1;
		multer = 0;
	}
	// remove_info(const remove_info &rmInfo)
	// {
	// 	this->vec_reward.clear();
	// 	for (auto &r : rmInfo.vec_reward)
	// 		this->vec_reward.push_back(r);

	// 	for (int i = 0; i < MAX_LIST;i++)
	// 	{
	// 		for (auto &c : rmInfo.vec_now_box[i])
	// 		{
	// 			this->vec_now_box[i].push_back(c);
	// 		}
	// 		for (auto &c : rmInfo.vec_fill_box[i])
	// 		{
	// 			this->vec_fill_box[i].push_back(c);
	// 		}
	// 	}
	// 	this->comboBonus = rmInfo.comboBonus;
	// 	this->multer = rmInfo.multer;
	// }
	void save_box_info(vector<card> vec_box[MAX_LIST])
	{
		for (int i = 0; i < MAX_LIST;i++)
		{
			vec_now_box[i].clear();
			for (auto &c:vec_box[i])
			{
				vec_now_box[i].push_back(c);
			}
		}
	}	
};
typedef vector<remove_info> vec_remove_info;
struct graph_data
{
	int scatter_count;
	int total_multer;
	int now_free_times;
	bool is_free;
	bool touch_free;
	vec_remove_info vec_remove;
	graph_data()
	{
		scatter_count = 0;
		total_multer = 0;
		now_free_times = 0;
		is_free = false;
		touch_free = false;
	}
};


struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式
	int mall_bet;
	int base_bet;
	int comboBonus;
	int scatter_count;
	int all_total_multer; //freegame加上触发总赢
	int total_multer; // 当前总倍数	
	bool is_free;//是否在freegame中
	bool has_wild;//是否产生了wild
	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

    vector<card> vec_now_box[MAX_LIST]; // 当前格子数据
	vec_remove_info vec_remove; //消除信息
	vector<int> graph_data;
	int g_index;
	vector<vec_remove_info> game_vec_remove; //总的消除信息
	vector<int> game_multer; //总的消除倍率数组
	vector<int> free_total_round; //总的freegame次数
	vector<int> has_plus; //freegame 是否触发plus
	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info();
	void reset() 
	{
		graph_data.clear();
		for (int i = 0; i < MAX_LIST; i++)
        {
            vec_now_box[i].clear();
        }
		vec_remove.clear();
		comboBonus = 1;
		result = 0;
        tax = 0;
        pay_out = 0; 
		record_id = 0;
		total_multer = 0;
		g_index = 0;
		has_wild = false;
	}

	void clear() 
	{
		result = 0;
        pay_out = 0;
	}
	
};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
    void init();
	void init_weights();
	// 洗牌
    void rand_card(user_game_info &game_info);
	// 获得随机扑克
	card get_rand_card();
	card get_rand_card_diff(const map<int, int>& map_card_count);
	//检查牌
	void check_card(user_game_info &game_info);
	void check_card_ex(user_game_info &game_info);

	// 检测牌获奖情况
    bool remove_card(remove_info& reInfo, user_game_info &game_info);

	void gen_list(user_game_info &game_info, int l, int num, int wild_rate = 0);
	void gen_list_ex(user_game_info &game_info, int l,map<int, int>& map_card_count);
	 //填充被消除格子
    void fill_remove_box(remove_info &reInfo, user_game_info &game_info);
	void fill_remove_box_ex(remove_info &reInfo, user_game_info &game_info);
	void insert_scatter(vec_remove_info& vec_remove, int num, bool isPlus = false);
	//从图形库获取提取数据
	bool init_static_graph_data_reply(const string& grah);
	void gen_no_reward_game_data(user_game_info &game_info);
	bool gen_game_data_from_graph_data(user_game_info &game_info, const string& graph);
	bool gen_freegame_data_from_graph_data(user_game_info &game_info, const string& graph);
	void print_data(vector<card> vec_now_box[MAX_LIST]);
	_uint64 gen_new_round_index();


private:
	int card_weights[ALL_CARD_LEN];

	int all_weights;
};

#endif

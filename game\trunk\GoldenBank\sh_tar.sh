#!/bin/bash

version_file=$1
dest_dir=$2
dest_file=$3

if [ ! -e "$version_file" ]; then
   echo "$rc_file not find"
   exit
fi
if [ ! -e "$dest_dir/$dest_file" ]; then
   echo "$dest_dir/$dest_file file not exist"
   exit
fi

ver=`cat $version_file | grep 'VERSION ' | awk '{print $2,$3}'`
v1=`echo "$ver" | grep MAJOR_VERSION | awk '{print $2}'`
v2=`echo "$ver" | grep MINOR_VERSION | awk '{print $2}'`
v3=`echo "$ver" | grep REVISE_VERSION | awk '{print $2}'`
v4=`echo "$ver" | grep BUILD_VERSION | awk '{print $2}'`
ver="$v1"".""$v2"".""$v3"".""$v4"

sysbit=`getconf LONG_BIT`
sys=`head -n 1 /etc/issue | awk '{print $1;}'`
sysver=`head -n 1 /etc/issue | awk '{print $3;}'`
tarname="$dest_file""_""$ver""_""$sys$sysver""_x""$sysbit"".tar.gz"
tarname=$(echo $tarname | tr A-Z a-z)

bm=`echo "$dest_file" | awk -F'.' '{print $1}'`
pub="$bm""_game_config.xml.bk config.xml.bk libsvr.so"

#echo -e "\033[31mTAR \033[35m==> \033[33m$tarname\033[0m"
#cd $dest_dir
#tar -C ./ -zcf $tarname $dest_file $pub

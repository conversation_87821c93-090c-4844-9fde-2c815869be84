#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  15       	//下注选项数目
#define  WHEEL_ODDS_CONFIG_COUNT 11       	//转盘中奖分布
#define ARRAY_1             3
#define ARRAY_2             3
/* 牌面值 */
enum enumIcon
{
	//GOLDEN BANK 牌值定义
	ICON_BLANK = 0,
	ICON_BAR_1 = 1,
	ICON_BAR_2 = 2,
	ICON_BAR_3 = 3,
	ICON_7 = 4,
	ICON_BONUS = 5,
	ICON_BONUS_SUPER = 6,
	ICON_FREE = 7,
	ICON_WILD_2X = 8,
	ICON_WILD_3X = 9,
	ICO<PERSON>_WILD_5X = 10,
	ICON_WILD = 11,
};


//const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
//	6, 12, 30, 60, 90, 150, 300, 450, 900, 2100, 3000, 4500, 6000, 9600, 19200
//};
//单位元
const double bet_config_count[MAX_BET_CONFIG_COUNT] = {
	0.5, 1, 2, 3, 5, 10, 20, 30, 40, 50, 80, 100, 200, 500, 1000
};

//单位分
const int odd_config[5] = { 100, 500, 2100, 11100, 111100 };

const int odd_info[] = {
    1, 3, 5, 8, 10, 40, 135, 625
};

struct free_info
{
	int pool[3]; //奖金游戏每列基础奖励
	int curColNum[3]; //当前轮每列bonus的次数
	int showBonus[3]; //每列显示的bonus
	int winPool[3]; //每列奖池
	int mul;//倍数
	int round_result;//当前回合奖励
	int odds;
	int superBonusCol[3]; //super bonus 出现在那列
	int superBonusCount; //super bonus 出现的次数
	int plus;
	int light;
	free_info() {
		memset(pool, 0, sizeof(pool));
		memset(curColNum, 0, sizeof(curColNum));
		memset(showBonus, 0, sizeof(showBonus));
		memset(winPool, 0, sizeof(winPool));
		memset(superBonusCol, 0, sizeof(superBonusCol));
		mul = 0;
		round_result = 0;
		odds = 0;
		superBonusCount = 0;
		plus = 0;
		light = 0;
	}
};

struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int status; // 游戏状态 1-已经初始化金币 0-未初始化
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
	int round_result;//当前回合奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式 1-15
	int mul;//倍数
	int free_count;//free次数
	int bonus;//总奖励
	int light;

	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

	int Icons[3][3];
	vector<int> graph_data;
	vector<free_info> free_data;
	int pool[3]; //奖金游戏每列基础奖励
	int bonus_count[3]; //每列累计bonus出现的次数
	int freeRemainRound;
	int freeTotalRound;

	int award_type;
	int plate_win;
	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info()
	{
		plate_win = 0;
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		record_id = 0;
		award_type = 0;
		freeRemainRound = 0;
		freeTotalRound = 0;
		light = 0;
		graph_data.clear();
		free_data.clear();
		memset(Icons, 0, sizeof(Icons));
		memset(pool, 0, sizeof(pool));
		memset(bonus_count, 0, sizeof(bonus_count));
	}
	void reset() 
	{
		plate_win = 0;
		result = 0;
		tax = 0;
		pay_out = 0;
		status = 0;
		award_type = 0;
		free_count = 0;
		mul = 0;
		freeRemainRound = 0;
		freeTotalRound = 0;
		light = 0;
		graph_data.clear();
		free_data.clear();
		memset(Icons, 0, sizeof(Icons));
		memset(pool, 0, sizeof(pool));
		memset(bonus_count, 0, sizeof(bonus_count));
	}

};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
	static game_logic *GetInstance()
	{
		static game_logic instance;
		return &instance;
	}
    void init();

	_uint64 gen_new_round_index();

	/*计算连线 tempIcons图标位置 vec_line返回可组成连线的数据*/
	void CalcResultTimes(user_game_info &game_info);
	int CalcResultTimes(user_game_info &game_info, int icons[3][3], int pageNum);
	/*随机一个不中奖的结果*/
	void RandFillIconNoWin(user_game_info &game_info);
	//解析数据
	bool get_game_data_from_graph_data(user_game_info &game_info, const string& graph);
	//根据图像获取赔率
	int get_odds(user_game_info &game_info, int iconCount[ICON_WILD + 1], int nWildCount);
private:
	int getMul(int count, int num);
};

#endif

#ifndef _SUPERACE_WONLOSE_CONTROL_
#define _SUPERACE_WONLOSE_CONTROL_

#include "typedef.h"
#include "redis_manager.h"
#include "my_redis.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
#include "user_interface.h"
#include "game_logic.h"

namespace MoneyComming {

    //*************************slot输赢控制公共类**************************
    struct form_item
{
    int multer;     // 倍数
    int touch;      // 触发权重
    form_item()
    {
        multer = 0;
        touch = 0;
    }
};

struct stock_cfg_item
{
    int kill_rate; // 杀数
    int update_time;  //刷新时间

    vector<form_item> bet_form_win1;
    int bet_form_win_total_1;

    vector<form_item> bet_form_win5;
    int bet_form_win_total_5;

    vector<form_item> bet_form_win10;
    int bet_form_win_total_10;

    vector<form_item> bet_form_win50;
    int bet_form_win_total_50;

    vector<form_item> bet_form_win100;
    int bet_form_win_total_100;
};


typedef std::map<string, stock_cfg_item>  map_stock_cfg;
typedef map_stock_cfg::iterator iter_stock_cfg;
    class winlose_control
    {
    public:
        winlose_control();
        ~winlose_control();


    public:
        static winlose_control* GetInstance()
        {
            static winlose_control stGameCfg;
            return &stGameCfg;
        }

    public:
        bool get_lottery_ctr_result(int plat_id, int gameid, int nodeid, int type, int& result);

    private:
        //初始化配置
		void init_config(int plat_id, int gameid, int nodeid);
		//加载配置
		void load_config(int plat_id, int gameid, int nodeid, int update_time);
        //获得配置
		iter_stock_cfg get_config(int plat_id, int gameid, int nodeid);
        //加载表权重
		void load_form_weight(Json::Value json_value, const char* str, vector<form_item>& form, int& total_weight);
        bool get_ctrl_result(iter_stock_cfg iter, int type, int &result);
    private:
        map_stock_cfg  m_map_stock_cfg;
    };
}
#endif


#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
using json = nlohmann::json;

typedef struct stPointCrazy
{
	int  x;
	int  y;
} stPointCrazy;

//中奖连线
const stPointCrazy kLines_fortune[1][3] = {
	{ { 1,0 },{ 1,1 },{ 1,2 } },
};

game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

int game_logic::CalcResultTimes(user_game_info &game_info)
{   
	game_info.result = 0;
	game_info.mult = 1;
	game_info.hasRespin = false;
	int mul = game_info.bet/ODDFACTOR;  
	int iconCount[5 + 1] = { 0 }; //统计图标个数
	int all_icon = 0;  //中间行图标个数
	for (int i = 0; i < 3; i++)
	{
		if (game_info.Icons[i] == 0)
		{
				continue;
		}
		++iconCount[game_info.Icons[i]];
		all_icon++;
	}
	if (all_icon < 3)  //少于三个，没中奖
	{
		return 0;
	}

	//先判断是否是三个相等
	bool threeSame = false;
	for (int i = 1; i < 6; i++)
	{
		if (iconCount[i] == 3)
		{
			game_info.plate_win = wheel_odds_config[i];
			threeSame = true;
			game_info.award_type = i;
		}
	}
	if (!threeSame)
	{
		//判断777  77 7 个数
		if (iconCount[ICON_777] + iconCount[ICON_77] + iconCount[ICON_7] == 3)
		{
			game_info.plate_win = wheel_odds_config[8];
			game_info.award_type = 8;
		}
		else if (iconCount[ICON_BAR2] + iconCount[ICON_BAR1] == 3)
		{
			game_info.plate_win = wheel_odds_config[7];
			game_info.award_type = 7;
		}
		else
		{
			game_info.plate_win = wheel_odds_config[6];
			game_info.award_type = 6;
		}

	}
	game_info.plate_win = game_info.plate_win * mul ;
	//判断右侧是否中奖
	if (game_info.Icons[3] != 0)
	{
		switch (game_info.Icons[3])
		{
		case ICON_X2:
		{
			game_info.mult_win = game_info.plate_win;
			game_info.mult = 2;
		}break;
		case ICON_X5:
		{
			game_info.mult_win = game_info.plate_win * 4;
			game_info.mult = 5;
		}break;
		case ICON_X10:
		{
			game_info.mult_win = game_info.plate_win * 9;
			game_info.mult = 10;
		}break;
		case ICON_SS:
		{
			game_info.extra_win = mul * wheel_odds_config[10];
		}break;
		case ICON_S:
		{
			game_info.extra_win = mul * wheel_odds_config[9];
		}break;
		case ICON_RESPIN:
		{
			game_info.respin_platewin = game_info.RespinTimes * game_info.plate_win;
			game_info.hasRespin = true;
		}break;
		default:
			break;
		}
	}
	game_info.result = game_info.plate_win + game_info.mult_win + game_info.extra_win + game_info.respin_platewin;
	return game_info.result;
}


int game_logic::GetRandLeftIconID()
{
	int nIcon = rand() % 6;
	return nIcon;
}

int game_logic::GetRandRightIconID()
{
	int nIcon = (rand() % 7) + 5;
	if (nIcon == 5)
	{
		return 0;
	}

	return nIcon;
}

void game_logic::RandFillIcons(int Icon[4])
{
	memset(Icon, 0, sizeof(Icon));
	//先填充左边随机
	for (size_t i = 0; i < 3; i++)
	{
		Icon[i] = GetRandLeftIconID();
	}
	Icon[3] = GetRandRightIconID();
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::RandFillIconNoWin(int Icon[4])
{
	int nRandZeroIndix = rand() % 3;
	for (int i = 0; i < 3; i++)
	{
		if (i == nRandZeroIndix)
		{
			Icon[i] = 0;
		}
		else
		{
			Icon[i] = rand() % 6;
		}
	}
	int nIcon = (rand() % 7) + 5;
	if (nIcon == 5)
	{
		nIcon = 0;
	}
	Icon[3] = nIcon;
}

int game_logic::check_cur_mode(int bet)
{  
	int nmode = 0;
	for (int i = 0; i < MAX_BET_CONFIG_COUNT; i++)
	{  
		int cfg_bet = bet_config_count[i] * 100;
		if (bet == cfg_bet)
		{
			nmode = i+1;
			break;
		}
	}
	return nmode;
}
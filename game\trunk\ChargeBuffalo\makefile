# Make command to use for dependencies
RM=rm
MKDIR=mkdir

OUTDIR=_obj
SODIR=../../../bin/game/bfs
LIBNAME=bfs.so
OUTFILE=$(SODIR)/$(LIBNAME)
PROTOBUF_PROTOCOL=../../../public/protobuf
PROTOBUF=../../../public/lib/protobuf/include
INC=-I../../../public/share -I../../../public/comm -I../../../public/lib/svrlib/include -I../../../public/lib/tinyxml -I../../../public/protocol -I../comm -I../../ -I../../../public/lib/json_ex/include -I$(PROTOBUF_PROTOCOL) -I$(PROTOBUF)
LIB=-lpthread -lrt -ldl -lprotobuf -lcrypto
LIBDIR=../../../public/lib/protobuf/lib
DEF=-DSVRLIB_EXPORTS
TINYXMLDIR=../../../public/lib/tinyxml
PUBDIR=../../../public
COMMDIR=../../../public/comm
PUBLICDIR=../../../public/share
CPPFLAGS=

#obj dir
SRC := $(wildcard *.cpp $(TINYXMLDIR)/*.cpp $(PUBDIR)/*.cpp $(COMMDIR)/*.cpp $(PUBLICDIR)/*.cpp $(PROTOBUF_PROTOCOL)/*.cpp)
OBJ := $(patsubst %.cpp, $(OUTDIR)/%.o, $(notdir ${SRC}))


COMPILE=g++ $(CPPFLAGS) -std=c++11 -g -Wall  -c -o "$(OUTDIR)/$(*F).o" $(INC) -fPIC -w "$<"
LINK=g++ -o "$(OUTFILE)" -L $(LIBDIR) $(OBJ) $(LIB) $(DEF) -shared -fPIC

# Pattern rules
$(OUTDIR)/%.o : ./%.cpp
	$(COMPILE)

$(OUTDIR)/%.o : $(TINYXMLDIR)/%.cpp
	$(COMPILE)

$(OUTDIR)/%.o: $(PUBDIR)/%.cpp
	$(COMPILE)

$(OUTDIR)/%.o: $(COMMDIR)/%.cpp
	$(COMPILE)

$(OUTDIR)/%.o: $(PUBLICDIR)/%.cpp
	$(COMPILE)

$(OUTDIR)/%.o: $(PROTOBUF_PROTOCOL)/%.cpp
	$(COMPILE)

# Build rules
all: $(OUTFILE)

$(OUTFILE): $(OUTDIR)  $(OBJ)
	$(LINK)
	#sh sh_tar.sh ./version.h $(SODIR) $(LIBNAME)

$(OUTDIR):
	$(MKDIR) -p "$(OUTDIR)"
	$(MKDIR) -p "$(SODIR)"

# Rebuild this project
rebuild: cleanall all

# Clean this project
clean:
	$(RM) -f $(OUTFILE)
	$(RM) -f $(OBJ)
	$(RM) -f $(SODIR)/*.gz

# Clean this project and all dependencies
cleanall: clean

install: 
	cp ${SODIR}/${LIBNAME} ../../../bin/room_pb/mw2

﻿#ifndef __COMM_DEF_H__
#define __COMM_DEF_H__

#pragma once
#include "stdio.h"
#include <string>

/****************************************
注意：

****************************************/

//-----------------------------------------------------------------------
#define  JCBY_GAME_ID		1500 //金蝉捕鱼
#define  LKPY_GAME_ID		1501 //李逵劈鱼
#define  QMPY_GAME_ID		1502 //全民捕鱼

#define  ZRSX_GAME_ID		3002 //真人视讯
#define  TYJC_GAME_ID		3003 //体育竞猜

#define  NNQZ_GAME_ID		4100 //抢庄牛牛
#define  NNJD_GAME_ID		4101 //经典牛牛
#define  DZNN_GAME_ID		4102 //德州牛牛
#define  LDNN_GAME_ID		4105 //漏洞牛牛
#define  SG_GAME_ID		    4103 //三公
#define  ZJH_GAME_ID		4110 //炸金花
#define  SSS_GAME_ID		4150 //十三水
#define  DDZ_GAME_ID        4160 //斗地主
#define  ERMJ_GAME_ID		4170 //二人麻将
#define  RBMJ_GAME_ID		4270 //日本麻将
#define  DZPK_GAME_ID		4420 //德州扑克
#define  BLACKJACK_GAME_ID  4800 //21点


#define  LHDB_GAME_ID		4400 //连环夺宝
#define  SHZ_GAME_ID		4410 //水浒传
#define  KYDB_GAME_ID		4401 //开运夺宝
#define  CSD_GAME_ID		4430 //财神到
#define  RBLHJ_GAME_ID		4450 //日本老虎机

#define  BRNN_GAME_ID		4500 //百人牛牛
#define  LFD_GAME_ID		4510 //龙凤斗
#define  HHDZ_GAME_ID		4530 //红黑大战
#define  BJL_GAME_ID		4540 //百家乐
#define  WXHH_GAME_ID		4550 //五星宏辉
#define  EBG_GAME_ID		4560 //二八杠
#define  BCBM_GAME_ID		4570 //奔驰宝马
#define  FQZS_GAME_ID		4580 //飞禽走兽
#define  BRZJH_GAME_ID		4590 //百人金花
#define  SLWH_GAME_ID		4610 //森林舞会
#define  COLOR_GAME_ID		4620 //COLOR GAME
#define  CRASH_GAME_ID		4630 //CRASH
#define  DICE_GAME_ID		4640 //DICE
#define  WHEEL_GAME_ID		4650 //WHEEL
#define  PJ_GAME_ID		    4900 //牌九
#define  HBSL_GAME_ID		4600 //红包扫雷
#define  BRSB_GAME_ID		6000 //百人骰宝

enum enThGameId
{
	TH_GAME_DG = 3002, //DG视讯
	TH_TG_SPORT = 3003, //体育竞猜
	TH_GAME_WM = 3004, //WM视讯
	TH_BTI_SPORT = 3005, //BTI体育
	TH_GAME_BG = 3006, //BG视讯
	TH_GAME_BG_FISH = 3007, //BG西游捕鱼
	TH_GAME_CP = 3008, //彩票
	TH_GAME_MG = 3009, //MG视讯
	TH_GAME_JDB = 3010, //JDB视讯
	TH_GAME_CQ9 = 3011, //CQ9视讯
	TH_GAME_JDB_WL_FISH = 3012, //JDB五龙捕鱼
	TH_GAME_PG = 3013, //PG电子
	TH_GMAE_EVO=3014,//EVO  Evolution Live Casino
	TH_GAME_DDZ_MATCH = 4161, //斗地主比赛
};

#define  QJHH_GAME_ID	    3101
#define  ZXMJ_GAME_ID		3102
#define  LQMJ_GAME_ID		3103
#define  XYMJ_GAME_ID		3104
#define  WLMJ_GAME_ID       3106
#define  LZMJ_GAME_ID	    3201
#define  DZSJZ_GAME_ID	    3301
#define  HMMJ_GAME_ID		3401
#define  AYMJ_GAME_ID       3501
#define  RGMJ_GAME_ID       3109


#define MAX_GAME_COUNT 100
static int GAME_ID_LIST[MAX_GAME_COUNT] = 
{
	QJHH_GAME_ID, //晃晃麻将
	ZXMJ_GAME_ID, //钟祥麻将
	LQMJ_GAME_ID, //乐清麻将
	LZMJ_GAME_ID, //柳州麻将
	DZSJZ_GAME_ID,//邓州十九张
	HMMJ_GAME_ID, //黄梅麻将
	AYMJ_GAME_ID, //安阳麻将
	WLMJ_GAME_ID, //温岭麻将
	XYMJ_GAME_ID, //信阳麻将
	RGMJ_GAME_ID, //如皋麻将
};


static const char WM[16] =  { "WM" };
static const char BTI[16] = { "BTI" };
static const char BG[16] =  { "BG" };
static const char MG[16] = { "MG" };
static const char JDB[16] = { "JDB" };
static const char CQ9[16] = { "CQ9" };
static const char PG[16] = { "PG" };
static const char JDBWLBY[16] = { "JDBWLBY" };
static const char EVO[16] = { "EVO" };


static char g_game_name[MAX_GAME_COUNT][128] = 
{
	"晃晃麻将",
	"钟祥麻将",
	"乐清麻将",
	"柳州麻将",
	"邓州十九张",
	"黄梅麻将",
	"安阳麻将",
	"温岭麻将",
	"信阳麻将",
	"如皋麻将",
};

static char GAME_CONFIG_FILE[MAX_GAME_COUNT][128] = 
{
	"qjhh_game_config.xml",
	"zxmj_game_config.xml",
	"lqmj_game_config.xml",
	"lzmj_game_config.xml",
	"dzsjz_game_config.xml",
	"hmmj_game_config.xml",
	"aymj_game_config.xml",
	"wlmj_game_config.xml",
	"xymj_game_config.xml",
	"cdmj_game_config.xml",
};

enum enExchangeMode
{
	EXCHANGE_MODE_ALL_BILL = 1, //总流水兑换模式
	EXCHANGE_MODE_PAY_BILL = 2, //支付流水兑换模式
};

enum PROP_TYPE
{
	PROP_GOLD = 0,
	PROP_DIAMOND = 1,
	PROP_MATCH_TICKET = 2,			//比赛入场券(跨周清0)
	PROP_CARD_REMENBER = 3,			//记牌器
	PROP_MATCH_TICKET_DAY = 4,      //比赛入场券(跨天清0)
	PROP_RENAMED_CARD = 5,			//改名卡
	PROP_TIANTI_SCORE = 200,		//天梯积分
	PROP_TYPE_COUNT,
};

enum PROP_EX_TYPE
{
	EX_TYPE_WEEK = 1,				//跨周过期
	EX_TYPE_DAY  = 2,				//跨天过期
};

#define ROBOT_TYPE	100
#define REAL_USER  2
#define MAX_SQL_COUNT 100
#define SUPER_USER_TYPE 10
#define UNIT_RATI0 100
#define MAIN_CHANNEL 10000
#define VIP_MOVIE_LEVEL				5 //默认动画等级
#define SERVER_STIMULATE_USER_FLAG  999999 //服务器默认客户端协议ID，标识

//转换版本
inline void __soft_ver_to_str(int softVer, char * pStrVer, int size)
{
	if (!pStrVer || size == 0) return ; 
	int ver1 = (softVer & 0x0000000000FFFFFF) >> 16;
	int ver2 = (softVer & 0x000000000000FF00) >> 8;
	int ver3 = (softVer & 0x00000000000000FF);
	sprintf(pStrVer, "%d.%d.%d", ver1, ver2, ver3);
}

#define VER_CMD(x,y,z)      ( (((unsigned int)(x))<<16) + (((unsigned int)(y))<<8) + z ) //客户端协议版本号规则
inline int __soft_ver_to_int(const char * pStrVer)
{
	if(!pStrVer) return 0;

	int a,b,c;
	sscanf(pStrVer, "%d.%d.%d", &a,&b,&c);
	return VER_CMD(a, b, c);
}

enum CLT_VERSION
{
	VER_FIRST_VER    =   VER_CMD(1,0,0),      // 初版 1.0.0
	VER_MULTI_TABLE  =   VER_CMD(1,1,0),	  // 开多桌 1.1.0
};
#endif

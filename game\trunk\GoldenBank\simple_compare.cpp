#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <iomanip>

// 简化的日志宏
#define MY_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

// 尝试包含游戏逻辑头文件
#ifdef INCLUDE_GAME_LOGIC
#include "game_logic.h"
#endif

// 简化的游戏信息结构（如果无法包含原始头文件）
struct SimpleGameInfo {
    int cur_mode;
    int bet;
    int result;
    int award_type;
    int Icons[3][3];
    std::vector<int> graph_data;
    
    SimpleGameInfo() {
        cur_mode = 0;
        bet = 1000;
        result = 0;
        award_type = 0;
        memset(Icons, 0, sizeof(Icons));
    }
};

// 简化的游戏逻辑计算（模拟）
void calculateSimpleResult(SimpleGameInfo& game_info) {
    // 简单的模拟计算
    game_info.result = 1550; // 15.5 * 100
    game_info.award_type = 1;
    
    // 模拟填充图标矩阵
    int idx = 0;
    for (int i = 0; i < 3 && idx < game_info.graph_data.size(); i++) {
        for (int j = 0; j < 3 && idx < game_info.graph_data.size(); j++) {
            game_info.Icons[i][j] = game_info.graph_data[idx++];
        }
    }
}

// 简化的比较测试类
class SimpleCompareTest
{
public:
    void run_compare_test()
    {
        std::cout << "=== 简化版游戏数据比对测试 ===" << std::endl;
        
        // 测试数据：包含多种图标的复杂组合
        int icons[] = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};
        int icons_count = sizeof(icons) / sizeof(icons[0]);
        
        std::cout << "测试数据长度: " << icons_count << " 个图标" << std::endl;

        try {
            // 步骤1: 生成当前数据的游戏结果
            std::cout << "\n--- 步骤1: 计算当前数据 ---" << std::endl;
            
#ifdef INCLUDE_GAME_LOGIC
            // 使用真实的游戏逻辑
            user_game_info game_info;
            game_info.cur_mode = 0;
            game_info.bet = 1000;
            
            for (int i = 0; i < icons_count; i++) {
                game_info.graph_data.push_back(icons[i]);
            }
            
            game_logic::GetInstance()->CalcResultTimes(game_info);
#else
            // 使用简化的游戏逻辑
            SimpleGameInfo game_info;
            for (int i = 0; i < icons_count; i++) {
                game_info.graph_data.push_back(icons[i]);
            }
            
            calculateSimpleResult(game_info);
#endif
            
            double totalwin = (double)game_info.result / 100.0;
            std::cout << "游戏计算结果:" << std::endl;
            std::cout << "  totalwin: " << std::fixed << std::setprecision(2) << totalwin << std::endl;
            std::cout << "  award: " << game_info.award_type << std::endl;
            std::cout << "  bet: " << game_info.bet << std::endl;
            
            // 显示图标矩阵
            std::cout << "图标矩阵:" << std::endl;
            for (int i = 0; i < 3; i++) {
                std::cout << "  ";
                for (int j = 0; j < 3; j++) {
                    std::cout << std::setw(3) << game_info.Icons[i][j] << " ";
                }
                std::cout << std::endl;
            }
            
            // 步骤2: 保存当前结果到文件
            std::cout << "\n--- 步骤2: 保存当前结果 ---" << std::endl;
            std::ofstream current_file("current_simple_result.txt");
            if (current_file.is_open()) {
                current_file << "=== 当前计算结果 ===" << std::endl;
                current_file << "totalwin: " << std::fixed << std::setprecision(2) << totalwin << std::endl;
                current_file << "award: " << game_info.award_type << std::endl;
                current_file << "bet: " << game_info.bet << std::endl;
                current_file << "图标矩阵:" << std::endl;
                for (int i = 0; i < 3; i++) {
                    for (int j = 0; j < 3; j++) {
                        current_file << std::setw(3) << game_info.Icons[i][j] << " ";
                    }
                    current_file << std::endl;
                }
                
                // 输出简化的 JSON 格式
                current_file << "\n简化 JSON 格式:" << std::endl;
                current_file << "{" << std::endl;
                current_file << "  \"totalwin\": " << totalwin << "," << std::endl;
                current_file << "  \"award\": " << game_info.award_type << "," << std::endl;
                current_file << "  \"ackqueue\": [" << std::endl;
                for (int i = 0; i < 3; i++) {
                    current_file << "    {" << std::endl;
                    current_file << "      \"platesymbol\": {" << std::endl;
                    current_file << "        \"col\": [";
                    for (int j = 0; j < 3; j++) {
                        current_file << game_info.Icons[i][j];
                        if (j < 2) current_file << ", ";
                    }
                    current_file << "]" << std::endl;
                    current_file << "      }" << std::endl;
                    current_file << "    }";
                    if (i < 2) current_file << ",";
                    current_file << std::endl;
                }
                current_file << "  ]" << std::endl;
                current_file << "}" << std::endl;
                
                current_file.close();
                std::cout << "当前结果已保存到 current_simple_result.txt" << std::endl;
            }

            // 步骤3: 读取参考数据
            std::cout << "\n--- 步骤3: 读取参考数据 ---" << std::endl;
            std::ifstream ifs("test.txt");
            if (ifs.is_open()) {
                std::string line;
                std::cout << "test.txt 内容:" << std::endl;
                std::cout << "----------------------------------------" << std::endl;
                while (std::getline(ifs, line)) {
                    std::cout << line << std::endl;
                }
                std::cout << "----------------------------------------" << std::endl;
                ifs.close();
            } else {
                std::cout << "警告: 无法打开 test.txt 文件" << std::endl;
                std::cout << "将创建示例参考数据" << std::endl;
                
                // 创建示例文件
                std::ofstream sample_file("test.txt");
                if (sample_file.is_open()) {
                    sample_file << "{" << std::endl;
                    sample_file << "  \"totalwin\": 15.5," << std::endl;
                    sample_file << "  \"award\": 1," << std::endl;
                    sample_file << "  \"ackqueue\": [" << std::endl;
                    sample_file << "    {\"platesymbol\": {\"col\": [5, 0, 4]}}," << std::endl;
                    sample_file << "    {\"platesymbol\": {\"col\": [0, 5, 0]}}," << std::endl;
                    sample_file << "    {\"platesymbol\": {\"col\": [5, 0, 1]}}" << std::endl;
                    sample_file << "  ]" << std::endl;
                    sample_file << "}" << std::endl;
                    sample_file.close();
                    std::cout << "已创建示例 test.txt 文件" << std::endl;
                }
            }

            // 步骤4: 比较结果
            std::cout << "\n--- 步骤4: 比较分析 ---" << std::endl;
            std::cout << "✅ 当前计算结果已保存到 current_simple_result.txt" << std::endl;
            std::cout << "✅ 参考数据在 test.txt 中" << std::endl;
            std::cout << "📊 主要数据:" << std::endl;
            std::cout << "   - 总赢分: " << totalwin << std::endl;
            std::cout << "   - 奖励类型: " << game_info.award_type << std::endl;
            std::cout << "   - 下注金额: " << game_info.bet << std::endl;
            std::cout << "💡 请手动比较 current_simple_result.txt 和 test.txt 的内容差异" << std::endl;

        } catch (const std::exception& e) {
            std::cout << "比较测试发生异常: " << e.what() << std::endl;
        }
        
        std::cout << "\n=== 简化版比对测试完成 ===" << std::endl;
    }
};

// 主函数
int main()
{
    std::cout << "GoldenBank 简化比较测试" << std::endl;
    std::cout << "========================" << std::endl;
    
    try {
        SimpleCompareTest test;
        test.run_compare_test();
        
    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n按回车键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}

#pragma once

#include <string>
#include <map>
#include <stdint.h>

#include "http_parse.h"
#include "http_util.h"

namespace tnet
{
    class HttpRequest
    {
    public:
        HttpRequest();
        ~HttpRequest();

        std::string get_request_uri();
        std::string get_parsed_body();
        std::string get_header(const std::string& key);
        std::string get_params_string(const std::string& key);
        bool get_params_int(const std::string& key, int& value);
        bool get_params_float(const std::string& key, float& value);
        std::string get_request_path();

        void clear();
        void parseUrl();
        std::string dump();

        std::string url;
        std::string body;

        std::string schema;
        
        std::string host;
        std::string path;
        std::string query;
        bool upgrade;

        Headers_t headers;

        Params_t params;
        
        unsigned short majorVersion;
        unsigned short minorVersion;

        enum http_method method;

        uint16_t port;

        void parseQuery();
    };

}

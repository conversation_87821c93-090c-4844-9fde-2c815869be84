#ifndef __PUBLIC_COMMON_HTTP_ROUTER__
#define __PUBLIC_COMMON_HTTP_ROUTER__

#include <regex>
#include <map>
#include <string>
#include <functional>

#include "typedef.h"
#include "http_request.h"

namespace tnet {

using routeHandleFunc = std::function<void (svrlib::socket_id, HttpRequest*)>;

class HttpRouter {

  public:

    int add_route(const std::string&, routeHandleFunc);
    routeHandleFunc get_route(const std::string&);

  private:

    std::map<std::string, routeHandleFunc> routes;
    std::regex reg; 
}; 
}

#endif
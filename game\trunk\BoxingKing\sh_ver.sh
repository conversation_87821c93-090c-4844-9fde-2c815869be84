#!/bin/sh
#encond=utf-8

dir_path=`dirname $0`
svn_info=`svn info`

svn_ver="0"
for i in `svn info`;
do
#echo $i;
    if [ "$i" == "Revision:" ]; then
        svn_ver="1"
    elif [ "$svn_ver" == "1" ]; then
        svn_ver=$i
        break    
    fi
done

cur_date=`date +%Y%m%d`

version_file="${dir_path}/version.h"
v1=1
v2=0
v3=0
v4=0

name=`pwd`
name=`basename "${name}"`

echo "${name} version: ${v1}.${v2}.${v3}.${v4}.${svn_ver}_${cur_date}"
echo "#ifndef __VERSION_H__">$version_file
echo "#define __VERSION_H__">>$version_file
echo "">>$version_file
echo "#define MAJOR_VERSION ${v1}">>$version_file
echo "#define MINOR_VERSION ${v2}">>$version_file
echo "#define REVISE_VERSION ${v3}">>$version_file
echo "#define BUILD_VERSION ${v4}">>$version_file
echo "#define SVNINFO_REVERSION ${svn_ver}">>$version_file
echo "#define BUILD_DATE ${cur_date}">>$version_file
echo "">>$version_file
echo "#endif">>$version_file;


﻿/*
-----------------------------------------------------------------------------

File name        :   net_def.h
Author           :
Version          :   1.0
Date             :
Description      :   网络定义

-----------------------------------------------------------------------------
*/
#ifndef __NET_DEF_H_______
#define __NET_DEF_H_______

#include <ctype.h>
#include "typedef.h"
#include "sock_engine_interface.h"
#include "tiny_buf_interface.h"

#include "queue_interface.h"
#include "vm_sock_engine_interface.h"
#include "log_interface.h"
#ifdef _WINNT_SYSTEM
#include "..\protocol\cmd_net.h"
#include "..\protocol\cmd_net_svr.h"
#else
#include "cmd_net.h"
#include "cmd_net_svr.h"
#endif

#include "log_manager.h"
#include "http_request.h"
#include "http_parser_ext.h"

#include <string>
using namespace svrlib;

// 客户端上行包最大限制
#define DEF_MAX_CLIENT_PACK_SIZE 16384 // 16K
// 客户端下行最大发送缓存
#define DEF_MAX_CLIENT_SEND_BUFF 262144 //(256K)

// 服务器之间包最大限制
#define DEF_MAX_SERVER_PACK_SIZE (DEF_MAX_CLIENT_PACK_SIZE + 8192)
#define DEF_MAX_SERVER_SEND_BUFF 10485760 //(10M)

#define CHECK_KEY_LEN 4
#define CHECK_VER 4

// 虚拟服务器通道类型
enum VMSVRTYPE
{
	VMSVRTYPE_ROOM = 1,	 // 房间服务器通道
	VMSVRTYPE_MATCH = 2, // 比赛服务器通道
};

inline const char *__vm_name(int t)
{
	switch (t)
	{
	case VMSVRTYPE_ROOM:
		return "房间";
	case VMSVRTYPE_MATCH:
		return "比赛";
	}
	return "VM";
}

// 服务器类型
enum svrtype
{
	SVRTYPE_STATUS,	   // 状态服务器
	SVRTYPE_LIST,	   // 列表服务器
	SVRTYPE_LOBBY,	   // 大厅服务器
	SVRTYPE_MESSAGE,   // 消息服务器
	SVRTYPE_MISSION,   // 任务服务器
	SVRTYPE_CHALLENGE, // 约战服务器
	SVRTYPE_GATE,	   // 网关服务器
	SVRTYPE_BRC,	   // 百人场服务器
	SVRTYPE_ACCOUNT,   // 登录服务器
	SVRTYPE_EOF		   // 结束
};

inline const char *__svrname(int t)
{
	switch (t)
	{
	case SVRTYPE_STATUS:
		return "状态";
	case SVRTYPE_LIST:
		return "列表";
	case SVRTYPE_LOBBY:
		return "大厅";
	case SVRTYPE_MESSAGE:
		return "消息";
	case SVRTYPE_MISSION:
		return "任务";
	case SVRTYPE_CHALLENGE:
		return "约战";
	case SVRTYPE_BRC:
		return "百人场";
	case SVRTYPE_ACCOUNT:
		return "登录服务器";
	}
	return "SVR";
}

enum op_value
{
	CONTINUATION = 0x0,
	TEXT = 0x1,
	BINARY = 0x2,
	RSV3 = 0x3,
	RSV4 = 0x4,
	RSV5 = 0x5,
	RSV6 = 0x6,
	RSV7 = 0x7,
	CLOSE = 0x8,
	PING = 0x9,
	PONG = 0xA,
	CONTROL_RSVB = 0xB,
	CONTROL_RSVC = 0xC,
	CONTROL_RSVD = 0xD,
	CONTROL_RSVE = 0xE,
	CONTROL_RSVF = 0xF
};

// masks for fields in the basic header
_uint8 const BHB0_OPCODE = 0x0F;
_uint8 const BHB0_RSV3 = 0x10;
_uint8 const BHB0_RSV2 = 0x20;
_uint8 const BHB0_RSV1 = 0x40;
_uint8 const BHB0_FIN = 0x80;
_uint8 const BHB1_PAYLOAD = 0x7F;
_uint8 const BHB1_MASK = 0x80;

_uint8 const payload_size_code_8bit = 0x7D;	 // 125
_uint8 const payload_size_code_16bit = 0x7E; // 126
_uint8 const payload_size_code_64bit = 0x7F; // 127

#pragma pack(push, 1)
struct CheckHead
{
	_uint32 key;
	_uint32 ver;
};
#pragma pack(pop)

#define CHECK_VER_VALUE 10000
inline int check_key_ver(void *pData, int size)
{
	if (size < sizeof(CheckHead))
		return 0;

	CheckHead head;
	memcpy(&head, (char *)pData, sizeof(CheckHead));
	return head.ver == CHECK_VER_VALUE ? sizeof(CheckHead) : 0;
}

// 与娃娃机通讯协议头
#pragma pack(push, 1)
struct WWJCNETHEAD
{
	_uint8 head;
	_uint8 pid_a;
	_uint8 pid_b;
	_uint8 buff0;
	_uint8 buff1;
	_uint8 buff2;
	_uint8 size;

	WWJCNETHEAD()
	{
		head = 0;
		pid_a = 0;
		pid_b = 0;
		buff0 = 0;
		buff1 = 0;
		buff2 = 0;
		size = 0;
	}
};
#pragma pack(pop)

// 与客户端通信包头结构
#pragma pack(push, 1)
struct CNETHEAD
{
	_uint32 size; // 数据包大小（包头+包体）
	_uint8 mcmd;  // 主协议
	_uint8 scmd;  // 子协议
	_uint16 ver;  // 版本（暂填0）
	_uint16 res;  // 保留（暂填0）

	CNETHEAD()
	{
		size = sizeof(CNETHEAD);
		mcmd = 0;
		scmd = 0;
		ver = 0;
		res = 0;
	}
};
#pragma pack(pop)

// websocket单包
#define MAX_WEBSOCKET_DATA_BUFF 10240 // 捕鱼单条消息较大，故缓存加大
#define MAX_FRAME_HEAD_LEN 4		  // 不考虑127情况，2个字节已经够了
#define BigLittleSwap16(A) ((((_uint16)(A) & 0xff00) >> 8) | (((_uint16)(A) & 0x00ff) << 8))

struct WEBSOCKET_PACKER
{
	_uint8 fin;	 // 帧标识
	_uint8 RSV1; // 拓展字段 为0
	_uint8 RSV2;
	_uint8 RSV3;
	_uint32 oppcode; // 帧状态
	_uint8 mask;	 // 掩码
	_uint16 paload_len;
	_uint8 index_len;

	WEBSOCKET_PACKER(char *in_buff, int in_len, char *out_buff)
	{
		index_len = 0;
		paload_len = 0;
		char *temp_buf = in_buff;

		fin = temp_buf[0] & BHB0_FIN;
		mask = temp_buf[1] & BHB1_MASK;
		paload_len = temp_buf[1] & BHB1_PAYLOAD;
		oppcode = temp_buf[0] & BHB0_OPCODE;
		index_len += 2;

		switch (paload_len)
		{
		case payload_size_code_16bit:
		{
			// paload_len = (temp_buf[2]&0xFF) << 8 | (temp_buf[3]&0xFF);
			_uint16 n = 0;
			memcpy(&n, &temp_buf[2], 2);
			paload_len = BigLittleSwap16(n);
			index_len += 2;
		}
		break;
		case payload_size_code_64bit:
		{
			_uint64 n;
			char temp[8] = {0};
			for (int i = 0; i < 8; i++)
				temp[i] = temp_buf[9 - i];

			memcpy(&n, temp, 8);
			paload_len = n;
			index_len += 8;
		}
		break;
		default:
			break;
		}

		// has mask
		char mask_data[4] = {0};
		if (mask > 0)
		{
			memcpy(mask_data, temp_buf + index_len, 4);
			index_len += 4;
		}
		unmask_imprecise(out_buff, temp_buf + index_len, mask_data, paload_len);
	}

	void unmask_imprecise(char *dst, char *src, char *mask, unsigned int length)
	{
		for (unsigned int n = (length >> 2) + 1; n; n--)
		{
			*(dst++) = *(src++) ^ mask[0];
			*(dst++) = *(src++) ^ mask[1];
			*(dst++) = *(src++) ^ mask[2];
			*(dst++) = *(src++) ^ mask[3];
		}
	}
};

// 用户基本会话信息(要求网关返回用户数据需带该结构)
#pragma pack(push, 1)
struct RBUSEC
{
	socket_id user_sid; // 用户网络标示（来自网关）
	_uint32 res;		// 保留(暂时填0)
	_uint64 user_id;	// 用户ID

	RBUSEC()
	{
		user_sid = 0;
		res = 0;
		user_id = 0;
	}

	RBUSEC(socket_id sid, _uint64 uid)
	{
		user_sid = sid;
		res = 0;
		user_id = uid;
	}
};
#pragma pack(pop)

// 扩展类型定义(0-15)
enum SVREXTYPE
{
	SVREXTYPE_NONE = 0,		// 无扩展信息
	SVREXTYPE_RBUSEC = 1,	// 网关用户会话数据，数据类型：RBUSEC。注：其他服务器要求网关返回用户数据，扩展信息必须带该结构。
	SVREXTYPE_USERINFO = 2, // 用户信息和软件信息，数据类型：cmd_gate.xml  -> CGateUserInfo
	SVREXTYPE_ROUTE = 3,	// 路由协议，数据类型：cmd_gate.xml  -> CRouteSection
	SVREXTYPE_TRANSF = 4,	// 要求网关转发客户端（任何一个网关都可以），数据类型：cmd_gate.xml  -> CUserTransSection
};

// 服务器之间通信包头结构
#pragma pack(push, 1)
struct SNETHEAD : public CNETHEAD
{
	SVREXTYPE ex_type;		// 扩展数据类型
	_uint32 ex_size;		// 扩展数据长度
	_uint8 b_batch_snd : 1; // 是否群发标记
	_uint8 res1;			// 保留
	_uint16 res2;			// 保留

	SNETHEAD()
	{
		size = sizeof(SNETHEAD);
		mcmd = 0;
		scmd = 0;
		ver = 0;
		res = 0;
		ex_type = SVREXTYPE_NONE;
		ex_size = 0;
		b_batch_snd = 0;
		res1 = 0;
		res2 = 0;
	}

	SNETHEAD(CNETHEAD &h)
	{
		size = sizeof(SNETHEAD);
		mcmd = h.mcmd;
		scmd = h.scmd;
		ver = h.ver;
		res = h.res;
		ex_type = SVREXTYPE_NONE;
		ex_size = 0;
		b_batch_snd = 0;
		res1 = 0;
		res2 = 0;
	}
};
#pragma pack(pop)

struct PATPHEAD
{
	_uint32 _type;
	_uint32 _ip;
	int _code;
	int _ex_code;

	PATPHEAD(_uint32 type, _uint32 ip, int code, int ex_code)
	{
		_type = type;
		_ip = ip;
		_code = code;
		_ex_code = ex_code;
	}
};

// 判断是否以某个字符串开始，返回值： 大于0- 成功  0-失败
inline int __is_begin_str(const char *str, int len, const char *sub)
{
	if (!str || !sub)
		return 0;

	int lensub = (int)strlen(sub);
	if (lensub > len)
		return 0;
	for (int i = 0; i < lensub; i++)
		if (toupper(str[i]) != toupper(sub[i]))
			return 0;
	return lensub;
}

inline const char *__strstr_il(const char *str, int size, const char *sub)
{
	const char *cp = str;
	const char *s1, *s2;
	const char *end = str ? (str + (size <= 0 ? strlen(str) : size)) : 0;

	if (!*sub)
		return str;

	while (*cp)
	{
		s1 = cp;
		s2 = sub;

		while (*s1 && *s2 && s1 < end && toupper(*s1) == toupper(*s2))
			s1++, s2++;

		if (!*s2)
			return (cp);
		cp++;
	}

	return (NULL);
}

// 获取http数据头大小，返回值： >0- 成功  0 - 数据不完整  -1-错误数据
inline int __get_http_head_size(const char *szdata, int dsize)
{
	if (dsize <= 4)
		return 0;
	const char *phead_end = __strstr_il(szdata, dsize, "\r\n\r\n");
	if (!phead_end)
	{
		if (dsize > 1024 * 4)
			return -1;
		return 0;
	}
	return (int)(phead_end - szdata) + 4;
}

// 获取一行数据，返回值： 0- 找不到 ，否则返回行首地址
inline const char *__get_http_section_line(const char *szhead, int hsize, const char *szsection, int *linelen)
{
	const char *pline = __strstr_il(szhead, hsize, szsection);
	if (!pline)
		return 0;
	if (szhead != pline)
	{
		if (pline - szhead < 2)
			return 0;
		if (*(pline - 1) != '\n' || *(pline - 2) != '\r')
			return 0;
	}
	const char *pline_end = __strstr_il(pline, hsize - (pline - szhead), "\r\n");
	if (!pline_end)
		return 0;
	if (linelen)
		*linelen = pline_end - pline + 2;
	return pline;
}

static const char *__safe_log_string(const char *str)
{
	static char buf[1024 * 1];
	int len = str ? strlen(str) : 0;
	if (len >= sizeof(buf) - 1)
		len = sizeof(buf) - 1;
	if (len > 0)
		memcpy(buf, str, len);
	buf[len] = '\0';
	for (int i = 0; i < len; i++)
		if (buf[i] == '%')
			buf[i] = '*';
	return buf;
}

inline _uint64 __stoint64(const char *str, _uint64 def = 0)
{
	_uint64 ret = def;
#pragma warning(disable : 4996)
	if (str)
		sscanf(str, "%llu", &ret);
	return ret;
}

// 0-非HTTP 1-GET 2-POST
inline int is_http(const char *szhttp)
{
	if (szhttp && strlen(szhttp) > 5)
	{
		if (
			((char)('G') == (char)(toupper(szhttp[0]))) &&
			((char)('E') == (char)(toupper(szhttp[1]))) &&
			((char)('T') == (char)(toupper(szhttp[2]))) &&
			((char)(' ') == (char)(szhttp[3])))
			return 1;

		if (
			((char)('P') == (char)(toupper(szhttp[0]))) &&
			((char)('O') == (char)(toupper(szhttp[1]))) &&
			((char)('S') == (char)(toupper(szhttp[2]))) &&
			((char)('T') == (char)(toupper(szhttp[3]))) &&
			((char)(' ') == (char)(szhttp[4])))
			return 2;
	}
	return 0;
}

// 分析http数据是否完整 0- 完整数据 1-未完整数据 -1错误数据
inline int __str_array_http_data(char *szhttp, int httpsize, int *out_head_size, const char **out_content, int *out_content_size, int *out_packet_size)
{
	if (out_head_size)
		*out_head_size = 0;
	if (out_content_size)
		*out_content_size = 0;
	if (out_content)
		*out_content = 0;
	if (out_packet_size)
		*out_packet_size = 0;

	if (httpsize < 5)
		return 1;

	if (!is_http(szhttp))
		return -1;

	const char *phead_end = strstr(szhttp, "\r\n\r\n");
	if (!phead_end || phead_end - szhttp + 4 > httpsize)
		return 1;

	int head_len = (int)(phead_end - szhttp) + 4;
	char *szcontent = (char *)phead_end + 4;
	int content_len = httpsize - head_len;

	// check bom
	int bom_flag_size = 0;
	if (content_len > 3 && _uint8(*(szcontent + 0)) == 0xEF && _uint8(*(szcontent + 1)) == 0xBB && _uint8(*(szcontent + 2)) == 0xBF)
	{
		content_len -= 3;
		szcontent += 3;
		bom_flag_size = 3;
	}

	// have length flag
	const char *p_len_sec = strstr(szhttp, "Content-Length:");
	if (p_len_sec)
	{
		int datalen = (int)strtol(p_len_sec + 15, 0, 10);
		if (datalen < 0)
			return -2;
		if ((head_len + datalen) > httpsize)
			return 1;

		if (out_head_size)
			*out_head_size = head_len;
		if (out_content_size)
			*out_content_size = datalen - bom_flag_size;
		if (out_content)
			*out_content = datalen ? szcontent : 0;
		if (out_packet_size)
			*out_packet_size = head_len + bom_flag_size + datalen + (szhttp - szhttp);
		return 0;
	}

	const char *sztransfer = strstr(szhttp, "Transfer-Encoding:");
	int chunked_flag = 0;
	if (sztransfer)
	{
		chunked_flag = strstr(sztransfer + 18, "chunked") ? 1 : 0;
	}
	if (!sztransfer || 0 != chunked_flag) // no size info;
	{
		if (out_head_size)
			*out_head_size = head_len;
		if (out_content_size)
			*out_content_size = 0; // content_len - bom_flag_size;
		if (out_content)
			*out_content = 0; // szcontent;
		if (out_packet_size)
			*out_packet_size = head_len + bom_flag_size;
		return 0;
	}

	struct tagHTTPDataOffset
	{
		int dlen;
		const char *pdata;
	};
	int dlist_count = 0;
	const int _DLIST_MAX_COUNT = 200;
	tagHTTPDataOffset list[_DLIST_MAX_COUNT];

	// check data length  (16进制数据长度\r\n数据\r\n)
	int offset = 0;
	do
	{
		const char *szbegin = szcontent + offset;
		int data_len = content_len - offset;
		const char *szchunked_end = strstr(szbegin, "\r\n");
		if (!szchunked_end)
			return 1;

		int dlen = (int)strtol(szbegin, 0, 16);
		if (dlen < 0)
			return -3; // error

		if (0 == dlen) // end
		{
			offset = (szchunked_end - szcontent + 2);
			if ((szchunked_end - szhttp + 2 + 0 + 2) <= httpsize)
				if (*(szcontent + offset + 0) == '\r' && *(szcontent + offset + 1) == '\n') // 是否存在空数据行
					offset += 2;
			break;
		}

		if ((szchunked_end - szhttp + 2 + dlen + 2) > httpsize) //(16进制数据长度 \r\n 数据 \r\n)
			return 1;

		offset = (szchunked_end - szcontent + 2) + dlen + 2;
		list[dlist_count].dlen = dlen;
		list[dlist_count].pdata = szchunked_end + 2;

		if (++dlist_count >= _DLIST_MAX_COUNT)
			return -4;
	} while (1);

	if (out_head_size)
		*out_head_size = head_len;
	if (out_content)
		*out_content = szcontent;
	if (out_packet_size)
		*out_packet_size = head_len + bom_flag_size + offset;

	// move data
	offset = 0;
	int index = 0;
	while (index < dlist_count)
	{
		if (list[index].dlen)
			memmove((void *)(szcontent + offset), list[index].pdata, list[index].dlen);
		offset += list[index++].dlen;
	}
	szcontent[offset] = '\0';
	if (out_content_size)
		*out_content_size = offset;

	return 0;
}

// 客户端数据解包接口
class IPacketBase
{
public:
	IAttemper *m_p_packet_attemper; // 调度器（把网络线程转成调度器线程）

	// 应用层实现接口
	virtual void on_base_log(TLOGTYPE type, const char *msg) = 0;
	virtual void on_get_ip(socket_id sid, _uint32 &ip) = 0;
	virtual void on_get_bind(socket_id sid, _uint64 &bind_tag, void *&p_bind) = 0;
	virtual void on_attemper_connecting(_uint32 type, socket_id sid) = 0;
	virtual void on_attemper_connect_fail(_uint32 type, socket_id sid, SEVTCODE code) = 0;
	virtual void on_attemper_connected(_uint32 type, socket_id sid, _uint32 ip) = 0;
	virtual void on_attemper_disconnected(_uint32 type, socket_id sid, _uint32 ip, int code, int ext_code, _uint64 bind_tag, const void *p_bind) = 0;
	virtual void on_attemper_recv(_uint32 type, socket_id sid, CNETHEAD *p_head, const void *p_data, int dsize, _uint64 bind_tag, const void *p_bind) = 0;
	virtual void on_attemper_http_recv(svrlib::socket_id sid, tnet::HttpRequest *request) {};
    virtual bool on_net_recv(_uint32 type, socket_id sid, CNETHEAD *p_head, const void *p_data, int dsize, _uint64 bind_tag, const void *p_bind)
	{
		return true;
	}
	virtual void on_attemper_dbp_packet(socket_id sid, const char *szcmd, const char *szhead, int hsize, const char *szdata, int dsize) {}
	virtual bool on_attemper_http_packet(socket_id sid, tnet::HttpRequest *request) {}

	// 网络回调
	// 连接中
	inline void __on_connecting(_uint32 type, socket_id sid)
	{
		PATPHEAD sec(type, 0, 0, 0);
		WLPARAM lwparam;
		on_get_ip(sid, sec._ip);
		lwparam.obj = this;
		_safe_set_pointer_i(lwparam.lparam, sid);
		m_p_packet_attemper->post((FAttemperSink)on_atp_connecting, &sec, sizeof(sec), 0, 0, 0, 0, &lwparam);
	}
	// 连接失败
	inline void __on_connect_fail(_uint32 type, socket_id sid, SEVTCODE code)
	{
		PATPHEAD sec(type, 0, code, 0);
		WLPARAM lwparam;
		on_get_ip(sid, sec._ip);
		lwparam.obj = this;
		_safe_set_pointer_i(lwparam.lparam, sid);
		m_p_packet_attemper->post((FAttemperSink)on_atp_connect_fail, &sec, sizeof(sec), 0, 0, 0, 0, &lwparam);
	}

	inline void __on_connected(_uint32 type, socket_id sid)
	{
		PATPHEAD sec(type, 0, 0, 0);
		WLPARAM lwparam;
		on_get_ip(sid, sec._ip);
		lwparam.obj = this;
		_safe_set_pointer_i(lwparam.lparam, sid);
		m_p_packet_attemper->post((FAttemperSink)on_atp_connected, &sec, sizeof(sec), 0, 0, 0, 0, &lwparam);
	}

	inline void __on_disconnected(_uint32 type, socket_id sid, int code, int ext_code)
	{
		_uint64 bind_tag = 0;
		void *p_bind = 0;
		PATPHEAD sec(type, 0, code, ext_code);
		WLPARAM lwparam;
		on_get_ip(sid, sec._ip);
		on_get_bind(sid, bind_tag, p_bind);
		lwparam.obj = this;
		lwparam.tag = bind_tag;
		lwparam.wparam = (void *)p_bind;
		_safe_set_pointer_i(lwparam.lparam, sid);
		m_p_packet_attemper->post((FAttemperSink)on_atp_disconnected, &sec, sizeof(sec), 0, 0, 0, 0, &lwparam);
	}

	inline int __on_recv(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		return on_analy_packet(type, sid, p_data, size, bind_tag, p_bind);
	}

	inline int __on_wwj_recv(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		return on_analy_wwj_packet(type, sid, p_data, size, bind_tag, p_bind);
	}

	inline int __on_websocket_recv(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		return on_analy_websocket_packet(type, sid, p_data, size, bind_tag, p_bind);
	}

	inline int __analy_dbp_cmd(socket_id sid, const void *pdata, int size)
	{
		int offset = 0;
		do
		{
			const char *phead = (const char *)pdata + offset;
			int headlen = __get_http_head_size(phead, size - offset); //>0- 成功  0 - 数据不完整  -1-错误数据
			if (0 == headlen)
				return offset;
			if (headlen < 0)
			{
				char msg[1024] = {0};
				__sprintf(msg, sizeof(msg), "sid=%d error http packet! size=%d data:%s", sid, size, __safe_log_string(phead));
				on_base_log(LOG_TYPE_ERROR, msg);
				return ERROR_PACKET; // error ask close
			}

			int lenlinelen = 0;
			int cmdlineoff = 0;
			int cmdlinelen = 0;
			const char *pline = __get_http_section_line(phead, headlen, "length:", &lenlinelen);
			const char *pcmd = __get_http_section_line(phead, headlen, "cmd:", &cmdlinelen);
			int dlen = pline ? atoi(pline + 7) : 0;
			if (!pline || !pcmd)
			{
				char msg[1024] = {0};
				char str[512] = {0};
				__sprintf(msg, sizeof(msg), "sid=%d error http packet! size=%d data:%s", sid, size, __safe_log_string(phead));
				on_base_log(LOG_TYPE_ERROR, msg);
				return ERROR_PACKET; // error ask close
			}

			if (headlen + dlen > size - offset)
				return offset;

			WLPARAM lwparam;
			lwparam.obj = this;
			lwparam.tag = (((_uint64)headlen) << 32) + cmdlinelen;
			cmdlineoff = (int)(pcmd - phead);
			_safe_set_pointer_i(lwparam.lparam, sid);
			_safe_set_pointer_i(lwparam.wparam, cmdlineoff);
			m_p_packet_attemper->post((FAttemperSink)on_atp_db_recv, (void *)phead, headlen + dlen, (void *)"\0", 1, 0, 0, &lwparam);

			offset += (headlen + dlen);
		} while (offset < size);

		return -1;
	}

	inline int __analy_http(socket_id sid, const void *pdata, int size)
	{
		int offset = 0;
		do
		{
			char *phead = (char *)pdata + offset;
			int head_size = 0;
			int content_size = 0;
			int packet_size = 0;
			const char *content = 0;
			// 分析http数据是否完整 0- 完整数据 1-未完整数据 -1错误数据
			int ret = __str_array_http_data(phead, size - offset, &head_size, &content, &content_size, &packet_size);
			if (ret > 0)
				return offset;
			if (ret < 0)
			{
				char msg[1024] = {0};
				__sprintf(msg, sizeof(msg), "sid=%d error http packet! size=%d data:%s", sid, size, __safe_log_string(phead));
				on_base_log(LOG_TYPE_ERROR, msg);
				return ERROR_PACKET; // error ask close
			}

			WLPARAM lwparam;
			lwparam.obj = this;
			lwparam.tag = (((_uint64)head_size) << 32) + content_size;
			_safe_set_pointer_i(lwparam.lparam, sid);
			m_p_packet_attemper->post((FAttemperSink)on_atp_http_recv, (void *)phead, head_size, (void *)"\0", 1, (void *)content, content_size, &lwparam);

			offset += packet_size;
		} while (offset < size);

		return -1;
	}

private:
	static FTYPE(void) on_atp_connecting(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(PATPHEAD))
			return;
		PATPHEAD *phead = (PATPHEAD *)pData;
		socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		((IPacketBase *)pParam->obj)->on_attemper_connecting(phead->_type, sid);
	}

	static FTYPE(void) on_atp_connect_fail(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(PATPHEAD))
			return;
		PATPHEAD *phead = (PATPHEAD *)pData;
		socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		((IPacketBase *)pParam->obj)->on_attemper_connect_fail(phead->_type, sid, (SEVTCODE)phead->_code);
	}

	static FTYPE(void) on_atp_connected(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(PATPHEAD))
			return;
		PATPHEAD *phead = (PATPHEAD *)pData;
		socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		((IPacketBase *)pParam->obj)->on_attemper_connected(phead->_type, sid, phead->_ip);
	}

	static FTYPE(void) on_atp_disconnected(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(PATPHEAD))
			return;
		PATPHEAD *phead = (PATPHEAD *)pData;
		socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		((IPacketBase *)pParam->obj)->on_attemper_disconnected(phead->_type, sid, phead->_ip, phead->_code, phead->_ex_code, pParam->tag, pParam->wparam);
	}

	static FTYPE(void) on_atp_recv(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(_uint32) + sizeof(CNETHEAD))
			return;
		socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		_uint32 &type = *((_uint32 *)pData);
		CNETHEAD *phead = (CNETHEAD *)((char *)pData + sizeof(_uint32));

		// 判断是否校验版本
		int off_len = check_key_ver((phead->size - sizeof(CNETHEAD) > 0 ? (char *)phead + sizeof(CNETHEAD) : 0), phead->size - sizeof(CNETHEAD));

		int len = phead->size - sizeof(CNETHEAD) - off_len;
		char *data = size <= 0 ? 0 : (char *)phead + sizeof(CNETHEAD) + off_len;
		//((IPacketBase*)pParam->obj)->on_attemper_recv(type,sid,phead,data,len,pParam->tag,pParam->wparam);
		_uint64 tag = 0;
		void *pbind = 0;
		((IPacketBase *)pParam->obj)->on_get_bind(sid, tag, pbind);
		((IPacketBase *)pParam->obj)->on_attemper_recv(type, sid, phead, data, len, tag, pbind);
	}

	static FTYPE(void) on_atp_wwj_recv(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(_uint32) + sizeof(WWJCNETHEAD))
			return;
		socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		_uint32 &type = *((_uint32 *)pData);
		WWJCNETHEAD *phead = (WWJCNETHEAD *)((char *)pData + sizeof(_uint32));
		int len = phead->size - sizeof(WWJCNETHEAD);
		char *data = size <= 0 ? 0 : (char *)phead + sizeof(WWJCNETHEAD);
		//((IPacketBase*)pParam->obj)->on_attemper_recv(type,sid,phead,data,len,pParam->tag,pParam->wparam);
		CNETHEAD head_temp;
		_uint64 tag = 0;
		void *pbind = 0;
		((IPacketBase *)pParam->obj)->on_get_bind(sid, tag, pbind);
		((IPacketBase *)pParam->obj)->on_attemper_recv(type, sid, &head_temp, data, len, tag, pbind);
	}

	static FTYPE(void) on_atp_db_recv(const void *pData, int size, const WLPARAM *pParam)
	{
		char cmd[128] = {0};
		char head[1024 * 8] = {0};
		char logbuf[1024 * 2] = {0};
		socket_id sid = 0;
		int cmdlineoff = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		_safe_get_pointer_i(pParam->wparam, cmdlineoff);
		int headlen = (int)(pParam->tag >> 32);
		int cmdlen = (int)pParam->tag;
		int datelen = size - headlen;
		const char *phead = (const char *)pData;
		const char *pdata = datelen > 0 ? phead + headlen : 0;
		__memcpy_s(cmd, sizeof(cmd) - 1, phead + cmdlineoff + 4, cmdlen - 6);
		__memcpy_s(head, sizeof(head) - 1, phead, headlen);
		__sprintf(logbuf, sizeof(logbuf), "sid=%d web cmd(%s) data:%s", sid, cmd, __safe_log_string(phead));
		((IPacketBase *)pParam->obj)->on_base_log(LOG_TYPE_DEBUG, logbuf);
		((IPacketBase *)pParam->obj)->on_attemper_dbp_packet(sid, cmd, head, headlen, pdata, datelen > 0 ? datelen + 1 : 0);
	}

	static FTYPE(void) on_atp_http_recv(const void *pData, int size, const WLPARAM *pParam)
	{

	#if 0	

		socket_id sid = 0;
		char logbuf[1024 * 2] = {0};
		char url[1024 * 16] = {0};
		_safe_get_pointer_i(pParam->lparam, sid);
		int headlen = (int)(pParam->tag >> 32);
		int contentlen = (int)pParam->tag;
		const char *szhead = (const char *)pData;
		const char *szcontent = contentlen ? szhead + headlen + 1 : 0;
		const char *szurl_begin = is_http(szhead) == 1 ? szhead + 4 : szhead + 5;
		const char *szurl_end = strstr(szurl_begin, "HTTP/");
		const char *szparam = 0;
		std::string origin_url = "";
		if (szurl_end)
		{
			__memcpy_s(url, sizeof(url) - 1, szurl_begin, szurl_end - szurl_begin);
			origin_url = std::string(url);
			szparam = strstr(url, "?");
			if (szparam)
			{
				*((char *)szparam) = '\0';
				szparam++;
			}
		}

		__sprintf(logbuf, sizeof(logbuf), "sid=%d HTTP Head:%s", sid, __safe_log_string(szhead));
		((IPacketBase *)pParam->obj)->on_base_log(LOG_TYPE_DEBUG, logbuf);
		if (((IPacketBase *)pParam->obj)->on_attemper_http_packet(sid, szhead, headlen, szcontent, contentlen, url, szparam))
		{
			return;
		}

		if (url && szcontent)
		{
			std::string strHead = szhead;
			int pos1 = strHead.find("X-Real-IP: ");

			std::string strRealIp;
			if (pos1 != std::string::npos)
			{
				int pos2 = strHead.find("\r\n", pos1);

				strRealIp = strHead.substr(pos1 + 11, pos2 - pos1 - 11);
			}

			((IPacketBase *)pParam->obj)->on_attemper_http_recv(sid,strRealIp, origin_url, szcontent);
		}
    #endif
 
        socket_id sid = 0;
		_safe_get_pointer_i(pParam->lparam, sid);
		int headlen = (int)(pParam->tag >> 32);
		int contentlen = (int)pParam->tag;
	    tnet::HttpParser parser(http_parser_type::HTTP_REQUEST, sid, IPacketBase::http_request_parse_handle);

		int code = parser.execute(static_cast<const char*>(pData), headlen + contentlen);
		if (code != 0) {
			MY_LOG_ERROR("parse http request failed with code : %d, sid: %d", code, sid);
		}

        tnet::HttpRequest *request = parser.get_request();
		((IPacketBase *)pParam->obj)->on_base_log(LOG_TYPE_DEBUG, request->dump().c_str());
		if (request->upgrade) {
            if (((IPacketBase *)pParam->obj)->on_attemper_http_packet(sid, request)) {
			    return;
		    }
		} else {
            ((IPacketBase *)pParam->obj)->on_attemper_http_recv(sid, request);
		}
	}

	static void http_request_parse_handle(svrlib::socket_id sid, const tnet::HttpRequest& request, tnet::RequestEvent ev, const void* p) {

	}

	// 分析逻辑数据包
	inline int on_analy_packet(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		int offset = 0;
		while (size > 0)
		{
			if (size < sizeof(CNETHEAD))
				return offset;
			CNETHEAD *phead = (CNETHEAD *)((char *)p_data + offset);
			if (phead->size > DEF_MAX_CLIENT_PACK_SIZE || phead->size < sizeof(CNETHEAD))
			{
				char msg[128] = {0};
				__sprintf(msg, sizeof(msg), "sid=%d recv error packet! DLen=%d PLen=%d", sid, size, phead->size);
				on_base_log(LOG_TYPE_ERROR, msg);
				return ERROR_PACKET;
				// error ask close
			}
			if (phead->size > (_uint32)size)
				return offset;

			// 判断是否校验版本
			int off_len = check_key_ver((phead->size - sizeof(CNETHEAD) > 0 ? (char *)phead + sizeof(CNETHEAD) : 0), phead->size - sizeof(CNETHEAD));

			offset += phead->size;
			size -= phead->size;
			if (!on_net_recv(type, sid, phead, (phead->size - sizeof(CNETHEAD) - off_len > 0 ? (char *)phead + sizeof(CNETHEAD) + off_len : 0), phead->size - sizeof(CNETHEAD) - off_len, bind_tag, p_bind))
				continue;

			WLPARAM lwparam;
			lwparam.obj = this;
			lwparam.tag = bind_tag;
			lwparam.wparam = (void *)p_bind;
			_safe_set_pointer_i(lwparam.lparam, sid);
			m_p_packet_attemper->post((FAttemperSink)on_atp_recv, &type, sizeof(_uint32), phead, phead->size, 0, 0, &lwparam);
		}
		return -1;
	}

	inline int on_analy_wwj_packet(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		int offset = 0;
		while (size > 0)
		{
			if (size < sizeof(WWJCNETHEAD))
				return offset;
			WWJCNETHEAD *phead = (WWJCNETHEAD *)((char *)p_data + offset);
			if (phead->size > DEF_MAX_CLIENT_PACK_SIZE || phead->size < sizeof(WWJCNETHEAD))
			{
				char msg[128] = {0};
				__sprintf(msg, sizeof(msg), "sid=%d recv error packet! DLen=%d PLen=%d", sid, size, phead->size);
				on_base_log(LOG_TYPE_ERROR, msg);
				return ERROR_PACKET; // error ask close
			}
			if (phead->size > (_uint32)size)
				return offset;

			offset += phead->size;
			size -= phead->size;

			WLPARAM lwparam;
			lwparam.obj = this;
			lwparam.tag = bind_tag;
			lwparam.wparam = (void *)p_bind;
			_safe_set_pointer_i(lwparam.lparam, sid);
			m_p_packet_attemper->post((FAttemperSink)on_atp_wwj_recv, &type, sizeof(_uint32), phead, phead->size, 0, 0, &lwparam);
		}
		return -1;
	}

	inline int on_analy_websocket_packet(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		int offset = 0;
		while (size > 0)
		{
			if (size < sizeof(CNETHEAD))
				return offset;

			char out_buff[MAX_WEBSOCKET_DATA_BUFF] = {0};
			WEBSOCKET_PACKER packer((char *)p_data + offset, size, out_buff);

			// 过滤相关操作(非结束帧)
			if (packer.fin == 0 || packer.oppcode == PING)
			{
				offset += packer.paload_len + packer.index_len;
				size -= (packer.paload_len + packer.index_len);
				continue;
			}

			if (packer.oppcode == CLOSE)
			{
				offset += packer.paload_len + packer.index_len;
				size -= (packer.paload_len + packer.index_len);
				on_net_recv(type, sid, 0, 0, 0, bind_tag, p_bind);
				return ERROR_PACKET;
			}

			offset += packer.paload_len + packer.index_len;
			size -= (packer.paload_len + packer.index_len);

			CNETHEAD phead;
			memcpy(&phead, out_buff, sizeof(CNETHEAD));
			if (phead.size > DEF_MAX_CLIENT_PACK_SIZE || phead.size < sizeof(CNETHEAD))
			{
				char msg[128] = {0};
				__sprintf(msg, sizeof(msg), "sid=%d recv error packet! DLen=%d PLen=%d", sid, size, phead.size);
				on_base_log(LOG_TYPE_ERROR, msg);
				return ERROR_PACKET; // error ask close
			}
			if (phead.size - sizeof(CNETHEAD) == 0)
			{
				// 只处理心跳
				if (!on_net_recv(type, sid, &phead, 0, 0, bind_tag, p_bind))
					continue;
			}

			_uint32 len = packer.paload_len;
			memcpy(out_buff, &len, sizeof(_uint32));

			WLPARAM lwparam;
			lwparam.obj = this;
			lwparam.tag = bind_tag;
			lwparam.wparam = (void *)p_bind;
			_safe_set_pointer_i(lwparam.lparam, sid);
			m_p_packet_attemper->post((FAttemperSink)on_atp_recv, &type, sizeof(_uint32), out_buff, packer.paload_len, 0, 0, &lwparam);
		}
		return -1;
	}
};

// 客户端数据解包接口
class IPacket : public ISockSink, public IPacketBase
{
public:
	virtual void on_base_log(TLOGTYPE type, const char *msg) { on_log(type, msg); }
	virtual void on_attemper_connecting(_uint32 type, socket_id sid) {}
	virtual void on_attemper_connect_fail(_uint32 type, socket_id sid, SEVTCODE code) {}

	// 网络回调
	virtual FTYPE(void) on_connected(_uint32 type, socket_id sid) { __on_connected(type, sid); }
	virtual FTYPE(void) on_disconnected(_uint32 type, socket_id sid, int code, int ext_code) { __on_disconnected(type, sid, code, ext_code); }
	virtual FTYPE(void) on_event(_uint32 type, socket_id sid, SEVTCODE code, _uint32 lparam, _uint32 wparam){};
	virtual FTYPE(int) on_recv(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind) { return __on_recv(type, sid, p_data, size, bind_tag, p_bind); }
};

// 服务器包解析
class IPacketSvr : public ISockSink, public IPacketBase
{
public:
	virtual void on_base_log(TLOGTYPE type, const char *msg) { on_log(type, msg); }
	virtual void on_attemper_connecting(_uint32 type, socket_id sid) {}
	virtual void on_attemper_connect_fail(_uint32 type, socket_id sid, SEVTCODE code) {}

	virtual void on_attemper_recv_ex(_uint32 type, socket_id sid, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data, _uint64 bind_tag, const void *p_bind) = 0;

	virtual void on_attemper_recv(_uint32 type, socket_id sid, CNETHEAD *p_head, const void *p_data, int dsize, _uint64 bind_tag, const void *p_bind)
	{
		SNETHEAD *pexhead = (SNETHEAD *)p_head;
		if (p_head->size < sizeof(SNETHEAD) || pexhead->size - sizeof(SNETHEAD) < pexhead->ex_size || pexhead->ex_size < 0)
		{
			char msg[128] = {0};
			__sprintf(msg, sizeof(msg), "sid=%d recv error packet! DLen=%d ExDLen=%d", sid,
					  pexhead ? pexhead->size : 0, pexhead ? pexhead->ex_size : 0);
			on_base_log(LOG_TYPE_ERROR, msg);
			return;
		}

		int dlen = pexhead->size - sizeof(SNETHEAD) - pexhead->ex_size;
		void *data = dlen > 0 ? (char *)p_head + sizeof(SNETHEAD) : 0;
		void *ex_data = pexhead->ex_size > 0 ? (char *)p_head + sizeof(SNETHEAD) + dlen : 0;

		on_attemper_recv_ex(type, sid, pexhead, data, dlen, ex_data, bind_tag, p_bind);
	}
	virtual void on_attemper_http_recv(socket_id sid, const std::string &strIP, const std::string &strUrl, const std::string &strContent) {}

	// 网络回调
	virtual FTYPE(void) on_connected(_uint32 type, socket_id sid) { __on_connected(type, sid); }
	virtual FTYPE(void) on_disconnected(_uint32 type, socket_id sid, int code, int ext_code) { __on_disconnected(type, sid, code, ext_code); }
	virtual FTYPE(void) on_event(_uint32 type, socket_id sid, SEVTCODE code, _uint32 lparam, _uint32 wparam){};
	virtual FTYPE(int) on_recv(_uint32 type, socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind) { return __on_recv(type, sid, p_data, size, bind_tag, p_bind); }
};

// 服务器包解析
class IPacketExSvr : public ISockSinkEx, public IPacketBase
{
public:
	virtual void on_base_log(TLOGTYPE type, const char *msg) { on_log(type, msg); }

	virtual void on_attemper_recv_ex(_uint32 type, socket_id sid, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data, _uint64 bind_tag, const void *p_bind) = 0;
	virtual void on_attemper_recv(_uint32 type, socket_id sid, CNETHEAD *p_head, const void *p_data, int dsize, _uint64 bind_tag, const void *p_bind)
	{
		SNETHEAD *pexhead = (SNETHEAD *)p_head;
		if (p_head->size < sizeof(SNETHEAD) || pexhead->size - sizeof(SNETHEAD) < pexhead->ex_size || pexhead->ex_size < 0)
		{
			char msg[128] = {0};
			__sprintf(msg, sizeof(msg), "sid=%d recv error packet! DLen=%d ExDLen=%d", sid,
					  pexhead ? pexhead->size : 0, pexhead ? pexhead->ex_size : 0);
			on_base_log(LOG_TYPE_ERROR, msg);
			return;
		}

		int dlen = pexhead->size - sizeof(SNETHEAD) - pexhead->ex_size;
		void *data = dlen > 0 ? (char *)p_head + sizeof(SNETHEAD) : 0;
		void *ex_data = pexhead->ex_size > 0 ? (char *)p_head + sizeof(SNETHEAD) + dlen : 0;

		on_attemper_recv_ex(type, sid, pexhead, data, dlen, ex_data, bind_tag, p_bind);
	}

	// 网络回调
	virtual FTYPE(void) on_connecting(_uint32 type, socket_id sid) { __on_connecting(type, sid); }
	virtual FTYPE(void) on_connect_fail(_uint32 type, socket_id sid, SEVTCODE code) { __on_connect_fail(type, sid, code); }
	virtual FTYPE(void) on_connected(_uint32 type, socket_id sid) { __on_connected(type, sid); }
	virtual FTYPE(void) on_disconnected(_uint32 type, socket_id sid, int code, int ext_code) { __on_disconnected(type, sid, code, ext_code); }
	virtual FTYPE(void) on_event(_uint32 type, socket_id sid, SEVTCODE code, _uint32 lparam, _uint32 wparam){};
	virtual FTYPE(int) on_recv(_uint32 type, socket_id sid, const void *p_data, int size) { return __on_recv(type, sid, p_data, size, 0, 0); }
};

// 虚拟服务端回调接口
class IPacketVMClient : public IVMCliSink
{
public:
	IAttemper *m_p_packet_attemper; // 调度器（把网络线程转成调度器线程）
	IVMClient *m_p_vm_client;
	ILog *m_p_log;

	// 应用层回调
	virtual void on_attemper_pipe_connected(const _pipesec *psec) = 0;
	virtual void on_attemper_pipe_disconnected(const _pipesec *psec, int code, int ext_code, _uint32 ip) = 0;
	virtual void on_attemper_pipe_speed(const _pipesec *psec, _uint32 time_msec, _uint32 ip) = 0;
	virtual void on_attemper_pipe_recv(const _pipesec *psec, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data) = 0;
	virtual void on_attemper_vm_connect_fail(const _vmsec *psec, int code) = 0;
	virtual void on_attemper_vm_connected(const _vmsec *psec) = 0;
	virtual void on_attemper_vm_disconnected(const _vmsec *psec, int code, int ext_code) = 0;
	virtual void on_attemper_vm_recv(const _vmsec *psec, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data) = 0;

	// 底层网络回调
	virtual FTYPE(void) on_pipe_connected(const _pipesec *psec)
	{
		WLPARAM param;
		param.obj = this;
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_connected, (void *)psec, sizeof(_pipesec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_pipe_disconnected(const _pipesec *psec, int code, int ext_code)
	{
		_uint32 ip = 0;
		WLPARAM param;
		m_p_vm_client->pipe_addr(psec->pipe_sid, &ip, 0);
		param.obj = this;
		param.tag = (((_uint64)code) << 32) + (_uint32)ext_code;
		_safe_set_pointer_i(param.wparam, ip);
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_disconnected, (void *)psec, sizeof(_pipesec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_pipe_speed(const _pipesec *psec, _uint32 time_msec)
	{
		_uint32 ip = 0;
		WLPARAM param;
		m_p_vm_client->pipe_addr(psec->pipe_sid, &ip, 0);
		param.obj = this;
		param.tag = (((_uint64)ip) << 32) + time_msec;
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_speed, (void *)psec, sizeof(_pipesec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_pipe_recv(const _pipesec *psec, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		WLPARAM param;
		param.obj = this;
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_recv, (void *)psec, sizeof(_pipesec), (void *)p_data, size, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_connect_fail(const _vmsec *psec, int code)
	{
		WLPARAM param;
		param.obj = this;
		param.tag = code;
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_connect_fail, (void *)psec, sizeof(_vmsec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_connected(const _vmsec *psec)
	{
		WLPARAM param;
		param.obj = this;
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_connected, (void *)psec, sizeof(_vmsec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_disconnected(const _vmsec *psec, int code, int ext_code)
	{
		WLPARAM param;
		param.obj = this;
		param.tag = (((_uint64)code) << 32) + (_uint32)ext_code;
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_disconnected, (void *)psec, sizeof(_vmsec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_recv(const _vmsec *psec, const void *p_data, int size)
	{
		WLPARAM param;
		param.obj = this;
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_recv, (void *)psec, sizeof(_vmsec), (void *)p_data, size, 0, 0, &param);
	}

private:
	static FTYPE(void) on_atp_pipe_connected(void *pData, int size, const WLPARAM *pParam)
	{
		((IPacketVMClient *)pParam->obj)->on_attemper_pipe_connected((_pipesec *)pData);
	}

	static FTYPE(void) on_atp_pipe_disconnected(void *pData, int size, const WLPARAM *pParam)
	{
		_uint32 ip = 0;
		int code = int(pParam->tag >> 32);
		int excode = int(pParam->tag);
		_safe_get_pointer_i(pParam->wparam, ip);
		((IPacketVMClient *)pParam->obj)->on_attemper_pipe_disconnected((_pipesec *)pData, code, excode, ip);
	}

	static FTYPE(void) on_atp_pipe_speed(void *pData, int size, const WLPARAM *pParam)
	{
		_uint32 ip = _uint32(pParam->tag >> 32);
		_uint32 time_msec = _uint32(pParam->tag);
		_safe_get_pointer_i(pParam->wparam, ip);
		((IPacketVMClient *)pParam->obj)->on_attemper_pipe_speed((_pipesec *)pData, time_msec, ip);
	}

	static FTYPE(void) on_atp_pipe_recv(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(_pipesec) + sizeof(SNETHEAD))
			return;
		_pipesec *psec = (_pipesec *)((char *)pData);
		SNETHEAD *phead = (SNETHEAD *)((char *)pData + sizeof(_pipesec));
		size -= sizeof(_pipesec) + sizeof(SNETHEAD);
		if (phead->ex_size > (_uint32)size)
			return;
		size -= phead->ex_size;
		void *data = size > 0 ? (char *)pData + sizeof(_pipesec) + sizeof(SNETHEAD) : 0;
		void *ex_data = phead->ex_size ? (char *)pData + sizeof(_pipesec) + sizeof(SNETHEAD) + size : 0;

		((IPacketVMClient *)pParam->obj)->m_p_log->write_log(LOG_TYPE_INFO, "【%s】PIPE Recv: pid=%d svrid=%lld cmd(%d,%d) DL=%d ET=%d EL=%d", __vm_name(psec->server_type), psec->pipe_sid, psec->server_id, phead->mcmd, phead->scmd, size, phead->ex_type, phead->ex_size);

		((IPacketVMClient *)pParam->obj)->on_attemper_pipe_recv(psec, phead, data, size, ex_data);
	}

	static FTYPE(void) on_atp_vm_connect_fail(void *pData, int size, const WLPARAM *pParam)
	{
		((IPacketVMClient *)pParam->obj)->on_attemper_vm_connect_fail((_vmsec *)pData, int(pParam->tag));
	}

	static FTYPE(void) on_atp_vm_connected(void *pData, int size, const WLPARAM *pParam)
	{
		((IPacketVMClient *)pParam->obj)->on_attemper_vm_connected((_vmsec *)pData);
	}

	static FTYPE(void) on_atp_vm_disconnected(void *pData, int size, const WLPARAM *pParam)
	{
		int code = int(pParam->tag >> 32);
		int excode = int(pParam->tag);
		((IPacketVMClient *)pParam->obj)->on_attemper_vm_disconnected((_vmsec *)pData, code, excode);
	}

	static FTYPE(void) on_atp_vm_recv(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(_vmsec) + sizeof(SNETHEAD))
			return;
		_vmsec *psec = (_vmsec *)((char *)pData);
		SNETHEAD *phead = (SNETHEAD *)((char *)pData + sizeof(_vmsec));
		size -= sizeof(_vmsec) + sizeof(SNETHEAD);
		if (phead->ex_size > (_uint32)size)
			return;
		size -= phead->ex_size;
		void *data = size > 0 ? (char *)pData + sizeof(_vmsec) + sizeof(SNETHEAD) : 0;
		void *ex_data = phead->ex_size ? (char *)pData + sizeof(_vmsec) + sizeof(SNETHEAD) + size : 0;

		if (!(cmd_net::CMD_SYS == phead->mcmd && (cmd_net::CMD_SYS_HEART_ASK == phead->scmd || cmd_net::CMD_SYS_HEART_ACK == phead->scmd)))
		{
			((IPacketVMClient *)pParam->obj)->m_p_log->write_log(LOG_TYPE_INFO, "【%s】VM Recv: sid=%d svrid=%lld cmd(%d,%d) DL=%d ET=%d EL=%d", __vm_name(psec->server_type), psec->local_sid, psec->server_id, phead->mcmd, phead->scmd, size, phead->ex_type, phead->ex_size);
		}

		((IPacketVMClient *)pParam->obj)->on_attemper_vm_recv(psec, phead, data, size, ex_data);
	}
};

// 虚拟服务端回调接口
class IPacketVMServer : public IVMSrvSink
{
public:
	IAttemper *m_p_packet_attemper; // 调度器（把网络线程转成调度器线程）
	IVMServer *m_p_vm_server;

	virtual void on_attemper_pipe_connecting(socket_id pid, _uint32 ip, _uint32 port) = 0;
	virtual void on_attemper_pipe_connect_fail(socket_id pid, int code, _uint32 ip, _uint32 port) = 0;
	virtual void on_attemper_pipe_connected(socket_id pid, _uint32 ip, _uint32 port) = 0;
	virtual void on_attemper_pipe_disconnected(socket_id pid, int code, int ext_code, _uint32 ip, _uint32 port) = 0;
	virtual void on_attemper_pipe_speed(socket_id pid, _uint32 time_msec, _uint32 ip, _uint32 port) = 0;
	virtual void on_attemper_pipe_recv(socket_id pid, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data) = 0;
	virtual void on_attemper_vm_connected(socket_id sid) = 0;
	virtual void on_attemper_vm_disconnected(socket_id sid, int code, int ext_code, _uint32 ip, _uint64 bind_tag, const void *p_bind) = 0;
	virtual void on_attemper_vm_recv(socket_id sid, SNETHEAD *phead, const void *pdata, int dsize, const void *ex_data, _uint64 bind_tag, const void *p_bind) = 0;

	// 网络回调
	virtual FTYPE(void) on_pipe_connecting(socket_id pid)
	{
		ATPSEC sec(this, pid, 0, 0, 0, 0);
		m_p_vm_server->pipe_addr(pid, &sec._ip, &sec._port);
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_connecting, &sec, sizeof(sec), 0, 0, 0, 0, 0);
	}

	virtual FTYPE(void) on_pipe_connect_fail(socket_id pid, int code)
	{
		ATPSEC sec(this, pid, 0, 0, code, 0);
		m_p_vm_server->pipe_addr(pid, &sec._ip, &sec._port);
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_connect_fail, &sec, sizeof(sec), 0, 0, 0, 0, 0);
	}

	virtual FTYPE(void) on_pipe_connected(socket_id pid)
	{
		ATPSEC sec(this, pid, 0, 0, 0, 0);
		m_p_vm_server->pipe_addr(pid, &sec._ip, &sec._port);
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_connected, &sec, sizeof(sec), 0, 0, 0, 0, 0);
	}

	virtual FTYPE(void) on_pipe_disconnected(socket_id pid, int code, int ext_code)
	{
		ATPSEC sec(this, pid, 0, 0, code, ext_code);
		m_p_vm_server->pipe_addr(pid, &sec._ip, &sec._port);
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_disconnected, &sec, sizeof(sec), 0, 0, 0, 0, 0);
	}

	virtual FTYPE(void) on_pipe_speed(socket_id pid, _uint32 time_msec)
	{
		ATPSEC sec(this, pid, 0, 0, time_msec, 0);
		m_p_vm_server->pipe_addr(pid, &sec._ip, &sec._port);
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_speed, &sec, sizeof(sec), 0, 0, 0, 0, 0);
	}

	virtual FTYPE(void) on_pipe_recv(socket_id pid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		WLPARAM param;
		param.obj = this;
		param.tag = pid;
		m_p_packet_attemper->post((FAttemperSink)on_atp_pipe_recv, (void *)p_data, size, 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_connected(socket_id sid)
	{
		WLPARAM param;
		param.obj = this;
		param.tag = sid;
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_connected, 0, 0, 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_disconnected(socket_id sid, int code, int ext_code)
	{
		ATPSEC sec(this, sid, 0, 0, code, ext_code);
		WLPARAM param;
		m_p_vm_server->get_addr(sid, &sec._ip, &sec._port);
		m_p_vm_server->get_bind_tag(sid, param.tag);
		m_p_vm_server->get_bind_data(sid, param.wparam);
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_disconnected, &sec, sizeof(sec), 0, 0, 0, 0, &param);
	}

	virtual FTYPE(void) on_vm_recv(socket_id sid, const void *p_data, int size, _uint64 bind_tag, const void *p_bind)
	{
		WLPARAM param;
		param.obj = this;
		param.tag = bind_tag;
		param.wparam = (void *)p_bind;
		_safe_set_pointer_i(param.lparam, sid);
		m_p_packet_attemper->post((FAttemperSink)on_atp_vm_recv, (void *)p_data, size, 0, 0, 0, 0, &param);
	}

protected:
	struct ATPSEC
	{
		void *_this;
		socket_id _sid;
		_uint32 _ip;
		_uint16 _port;
		int _code;
		int _ex_code;
		ATPSEC(void *pthis, socket_id sid, _uint32 ip, _uint32 port, int code, int ex_code)
		{
			_this = pthis;
			_sid = sid;
			_ip = ip;
			_port = port;
			_code = code;
			_ex_code = ex_code;
		}
	};
	static FTYPE(void) on_atp_pipe_connecting(void *pData, int size, const WLPARAM *pParam)
	{
		ATPSEC *psec = (ATPSEC *)pData;
		((IPacketVMServer *)psec->_this)->on_attemper_pipe_connecting(psec->_sid, psec->_ip, psec->_port);
	}

	static FTYPE(void) on_atp_pipe_connect_fail(void *pData, int size, const WLPARAM *pParam)
	{
		ATPSEC *psec = (ATPSEC *)pData;
		((IPacketVMServer *)psec->_this)->on_attemper_pipe_connect_fail(psec->_sid, psec->_code, psec->_ip, psec->_port);
	}

	static FTYPE(void) on_atp_pipe_connected(void *pData, int size, const WLPARAM *pParam)
	{
		ATPSEC *psec = (ATPSEC *)pData;
		((IPacketVMServer *)psec->_this)->on_attemper_pipe_connected(psec->_sid, psec->_ip, psec->_port);
	}

	static FTYPE(void) on_atp_pipe_disconnected(void *pData, int size, const WLPARAM *pParam)
	{
		ATPSEC *psec = (ATPSEC *)pData;
		((IPacketVMServer *)psec->_this)->on_attemper_pipe_disconnected(psec->_sid, psec->_code, psec->_ex_code, psec->_ip, psec->_port);
	}

	static FTYPE(void) on_atp_pipe_speed(void *pData, int size, const WLPARAM *pParam)
	{
		ATPSEC *psec = (ATPSEC *)pData;
		((IPacketVMServer *)psec->_this)->on_attemper_pipe_speed(psec->_sid, psec->_code, psec->_ip, psec->_port);
	}

	static FTYPE(void) on_atp_pipe_recv(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(SNETHEAD))
			return;
		SNETHEAD *phead = (SNETHEAD *)((char *)pData);
		size -= sizeof(SNETHEAD);
		if (phead->ex_size > (_uint32)size)
			return;
		size -= phead->ex_size;
		void *data = size > 0 ? (char *)pData + sizeof(SNETHEAD) : 0;
		void *ex_data = phead->ex_size ? (char *)pData + sizeof(SNETHEAD) + size : 0;
		((IPacketVMServer *)pParam->obj)->on_attemper_pipe_recv((socket_id)pParam->tag, phead, data, size, ex_data);
	}

	static FTYPE(void) on_atp_vm_connected(void *pData, int size, const WLPARAM *pParam)
	{
		((IPacketVMServer *)pParam->obj)->on_attemper_vm_connected((socket_id)pParam->tag);
	}

	static FTYPE(void) on_atp_vm_disconnected(void *pData, int size, const WLPARAM *pParam)
	{
		ATPSEC *psec = (ATPSEC *)pData;
		((IPacketVMServer *)psec->_this)->on_attemper_vm_disconnected(psec->_sid, psec->_code, psec->_ex_code, psec->_ip, pParam->tag, pParam->wparam);
	}

	static FTYPE(void) on_atp_vm_recv(void *pData, int size, const WLPARAM *pParam)
	{
		if (size < sizeof(SNETHEAD))
			return;
		SNETHEAD *phead = (SNETHEAD *)((char *)pData);
		socket_id sid;
		_safe_get_pointer_i(pParam->lparam, sid);
		size -= sizeof(SNETHEAD);
		if (phead->ex_size > (_uint32)size)
			return;
		size -= phead->ex_size;
		void *data = size > 0 ? (char *)pData + sizeof(SNETHEAD) : 0;
		void *ex_data = phead->ex_size ? (char *)pData + sizeof(SNETHEAD) + size : 0;

		//((IPacketVMServer*)pParam->obj)->on_attemper_vm_recv(sid,phead,data,size,ex_data,pParam->tag,pParam->wparam);

		_uint64 tag = pParam->tag;
		void *pbind = pParam->wparam;
		if (0 == tag)
			((IPacketVMServer *)pParam->obj)->m_p_vm_server->get_bind_tag(sid, tag);
		if (0 == pbind)
			((IPacketVMServer *)pParam->obj)->m_p_vm_server->get_bind_data(sid, pbind);
		((IPacketVMServer *)pParam->obj)->on_attemper_vm_recv(sid, phead, data, size, ex_data, tag, pbind);
	}
};

#endif //__NET_DEF_H_______

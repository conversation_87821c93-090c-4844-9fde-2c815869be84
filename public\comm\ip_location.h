#pragma once
#include "../lib/json/json.h"
#include "dbproxy_interface.h"
#include "log_manager.h"
#include <vector>

struct db_ip_locationinfo
{
	_tint64 beginip;
	_tint64 endip;
	char    szdistinct[64];
	char   szcity[64];
};
#define IP_ONETIME_RECORD_SIZE 1000

class iplocation
{
public:
	iplocation();
	~iplocation();

	static iplocation *GetInstance()
	{
		static iplocation st_ipipg;
		return &st_ipipg;
	}

	static FTYPE(void) on_user_ip_location_dbproxy(void*obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection &section);
	void _on_user_ip_location_dbproxy(void*obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection &section);
	bool load_ip_location_info(const char* pcountry_code="TH", _tint64 startip=0);
	bool  get_ip_location(const char* pip, db_ip_locationinfo& info);
	bool  get_rand_location(db_ip_locationinfo& info);
private:
	std::vector<db_ip_locationinfo> m_iplocation;
	_tint64  m_get_start_ip;
	char     m_szcountrycode[32];

};

#define iplocation_inst iplocation::GetInstance()

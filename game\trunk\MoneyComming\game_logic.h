#ifndef _GAME_LOGIC_
#define _GAME_LOGIC_

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
using namespace std;

#include "logic_def.h"
#include "comm_fun.h"

#define  MAX_BET_CONFIG_COUNT  5       	//下注选项数目
#define  WHEEL_CONFIG_COUNT 8       	//转盘中奖分布

/* 牌面值 */
enum EMUN_MC_JL_CRAD
{
	BOX_LOCK = 0,//锁状态
	BOX_NONE = 1,// 不中
	BOX_0 = 2,//0
	BOX_00 = 3,//00
	BOX_1 = 4,//1
	BOX_5 = 5,//5
	BOX_10 = 6,//10
	BOX_SCATTER = 7, //绿scatter
	BOX_SCATTER_H = 8, //红scatter
	BOX_ALL= 11,//ALL SPIN
	BOX_X2 = 12,//X2
	BOX_X5 = 13,//X5
	BOX_X10 = 14,//X10
	BOX_MAX = 15
};

const int whell_config[MAX_BET_CONFIG_COUNT][WHEEL_CONFIG_COUNT] = {
	{0, 0, 0, 0, 0, 0, 0, 0},							 // 1
	{25, 50, 75, 100, 150, 250, 500, 1000},				 // 5
	{50, 100, 150, 200, 300, 500, 1000, 2000},			 // 10
	{500, 1000, 1500, 2000, 3000, 5000, 10000, 20000},	 // 50
	{1000, 2000, 3000, 4000, 6000, 10000, 20000, 40000}, // 100
};
const int bet_config_count[MAX_BET_CONFIG_COUNT] = {
	1,5,10,50,100
};


const int wheel_weights[WHEEL_CONFIG_COUNT] = {10,10,10,10,10,10,10,10};

struct user_game_info 
{
    int uid;
    int tax;  // 税收
    int time; // 时间
    int bet;  // 下注数目
    int result;  // 总奖励
    int pay_out; // 实际输赢
	int record_id;//下注单号
	int cur_mode;//当前模式 0-bet1 1-bet5 2-bet10 3-bet50 4-bet100
	_uint64  balance_before_round; //下注前余额
	_uint64  round_index;
	_uint64  round_index_v2;
	time_t _last_kick_user_offline_tm_sec;

    int normal_data[5];
	int normal_platewin;
	int respin_data[4];
	int respin_platewin;
	int wheel_win;
	bool hasRespin;

	string token;
	string web_token;
	int user_type;
	int client_id;
	int sub_client_id;
	user_game_info();
	void reset() 
	{
		normal_platewin = 0;
		respin_platewin = 0;
		wheel_win = 0;
		result = 0;
        tax = 0;
        pay_out = 0; 
		record_id = 0;
		hasRespin = false;
	}

	void clear() 
	{
		result = 0;
        pay_out = 0;
	}
};

typedef std::map<_uint32, user_game_info>  map_user_game_info;
typedef map_user_game_info::iterator iter_user_game_info;


class game_logic
{
public:
    game_logic();
    ~game_logic();

public:
    void init();
   
	//检查牌
	void check_card(user_game_info &game_info);

	//从图形库获取提取数据
	bool init_static_graph_data_reply(const string& grah);
	bool gen_game_data_from_graph_data(user_game_info &game_info, const string& graph);

	_uint64 gen_new_round_index();


private:
	
};

#endif

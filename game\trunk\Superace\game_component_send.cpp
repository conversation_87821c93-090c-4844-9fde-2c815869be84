#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "server.pb.h"
#include "custom.pb.h"
#include "google/protobuf/text_format.h"
#include "google/protobuf/util/json_util.h"
#include "google/protobuf/util/message_differencer.h"
#include "google/protobuf/message.h"
#include "google/protobuf/descriptor.h"

#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <fstream>
#include <cmath>
#include <array>

#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/buffer.h>
// AES 密钥长度（256-bit = 32 字节）
constexpr size_t AES_KEY_SIZE = 32;
constexpr size_t AES_BLOCK_SIZE = 16;

std::string base64Encode(const std::vector<unsigned char>& data) 
{
    BIO* bio, * b64;
    BUF_MEM* bufferPtr;
    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);
    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);  // 不加换行符
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);
    std::string encoded(bufferPtr->data, bufferPtr->length);
    BIO_free_all(bio);
    return encoded;
}

// AES-CBC 加密函数
std::vector<unsigned char> encryptAESCBC(const std::string& plaintext, const std::string& token) 
{
    std::vector<unsigned char> iv(AES_BLOCK_SIZE);
    RAND_bytes(iv.data(), AES_BLOCK_SIZE);  // 生成随机 IV

    // 截取 token 前 32 字节作为 key
    std::string keyString = token.substr(0, AES_KEY_SIZE);
    std::vector<unsigned char> key(keyString.begin(), keyString.end());

    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) throw std::runtime_error("Failed to create context");

    if (1 != EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key.data(), iv.data()))
        throw std::runtime_error("EncryptInit failed");

    std::vector<unsigned char> ciphertext(plaintext.size() + AES_BLOCK_SIZE);
    int len = 0;
    int ciphertext_len = 0;

    if (1 != EVP_EncryptUpdate(ctx, ciphertext.data(), &len,
                               reinterpret_cast<const unsigned char*>(plaintext.data()),
                               plaintext.size()))
        throw std::runtime_error("EncryptUpdate failed");

    ciphertext_len += len;

    if (1 != EVP_EncryptFinal_ex(ctx, ciphertext.data() + ciphertext_len, &len))
        throw std::runtime_error("EncryptFinal failed");

    ciphertext_len += len;
    ciphertext.resize(ciphertext_len);  // 截取实际密文长度

    EVP_CIPHER_CTX_free(ctx);

    // 合并 IV + 密文
    std::vector<unsigned char> result;
    result.insert(result.end(), iv.begin(), iv.end());
    result.insert(result.end(), ciphertext.begin(), ciphertext.end());

    return result;
}

std::string base64_decode(const std::string &encoded_string)
{
	// Base64字符集
	static const std::string base64_chars =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		"abcdefghijklmnopqrstuvwxyz"
		"0123456789+/";

	auto is_base64 = [](unsigned char c) -> bool
	{
		return (isalnum(c) || (c == '+') || (c == '/'));
	};

	// 清除空格等非法字符
	std::string clean_input;
	for (auto c : encoded_string)
	{
		if (is_base64(c) || c == '=')
		{
			clean_input += c;
		}
	}

	int in_len = clean_input.size();
	int i = 0;
	int j = 0;
	int in_ = 0;
	unsigned char char_array_4[4], char_array_3[3];
	std::string ret;

	while (in_len-- && (clean_input[in_] != '=') && is_base64(clean_input[in_]))
	{
		char_array_4[i++] = clean_input[in_];
		in_++;
		if (i == 4)
		{
			for (i = 0; i < 4; i++)
			{
				char_array_4[i] = base64_chars.find(char_array_4[i]);
			}

			char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
			char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
			char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

			for (i = 0; i < 3; i++)
			{
				ret += char_array_3[i];
			}
			i = 0;
		}
	}

	if (i)
	{
		for (j = i; j < 4; j++)
		{
			char_array_4[j] = 0;
		}

		for (j = 0; j < 4; j++)
		{
			char_array_4[j] = base64_chars.find(char_array_4[j]);
		}

		char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
		char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
		char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

		for (j = 0; j < i - 1; j++)
		{
			ret += char_array_3[j];
		}
	}

	return ret;
}

bool compare_graph()
{
	bool equal = true;

	// google::protobuf::util::MessageDifferencer differencer;
	// differencer.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
	// // differencer.set_treat_nan_as_equal();

	// const google::protobuf::FieldDescriptor *field = mcProto::SpinAck::descriptor()->FindFieldByName("NextRow");
	// if (field != nullptr)
	// {
	// 	differencer.IgnoreField(field);
	// }

	// std::string d = "";
	// differencer.ReportDifferencesToString(&diff);
	// if (!differencer.Compare(g1, g2))
	// {
	// 	equal = false;
	// 	return false;
	// }

	// diff = "equal";
	return equal;
}

void CGameComponent::send_data(IUser *pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *pData)
{

	if (pUser)
		m_table->send(pUser, mcmd, scmd, pData);
	else
		m_table->send_user_batch(mcmd, scmd, pData);
}

void CGameComponent::send_bet_roll_err_result(IUser *user, serverProto::AckType type, serverProto::Error err,
											  const std::string &token, const std::string &msg, void *data, size_t size)
{
	serverProto::GaiaResponse response;
	response.set_type(type);
	response.set_ret(err);
	response.set_errormsg(msg);
	response.set_token(token);
	auto encrypted = encryptAESCBC("", token);
	response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());

	std::string content = response.SerializeAsString();

	svrlib::socket_id sid = user->get_socket_id();
	if (m_table)
	{
		m_table->send_http(sid, content);
	}
	else
	{
		MY_LOG_ERROR("user[%d] invalid table", user->get_user_id());
	}
}

void encodeACKProto(customProto::AllPlate &ack, const user_game_info &game_info)
{
	for(int g=0;g<game_info.game_vec_remove.size();g++)
	{
		customProto::Plate *plate = ack.add_plate();
		for (int i = 0; i < MAX_LIST; i++)
		{
			customProto::ColumnData *col = plate->add_column();
			for (int j = 0; j < MAX_ROW; j++)
			{
				customProto::Symbol s = static_cast<customProto::Symbol>(game_info.game_vec_remove[g][0].vec_now_box[i][j].value);
				col->add_row(s);
				col->add_isgold(game_info.game_vec_remove[g][0].vec_now_box[i][j].is_gold);
			}
		}

		for (auto &rm : game_info.game_vec_remove[g])
		{
			if (rm.multer > 0)
			{
				customProto::Combo *combo = plate->add_combo();
				for (int i = 0; i < MAX_LIST; i++)
				{
					for (auto &f : rm.vec_fill_box[i])
					{
						customProto::Cell *cell = combo->add_change();
						customProto::Symbol s = static_cast<customProto::Symbol>(f.value);
						cell->set_symbol(s);
						cell->set_column(f.y);
						cell->set_row(f.x);
						cell->set_isgold(f.is_gold);
						cell->set_lastgold(f.last);
						if (s == BOX_WILD)
						{
							if (f.is_gold == 1)
							{
								cell->set_isgold(102);
							}
							else
							{
								cell->set_isgold(101);
							}
						}
					}
				}
				for (auto &r : rm.vec_reward)
				{
					customProto::AwardReel *aw = combo->add_award();
					customProto::Symbol s = static_cast<customProto::Symbol>(r.value);
					aw->set_symbol(s);
					for (auto &p : r.vec_pos)
					{
						customProto::Block *bl = aw->add_block();
						bl->set_row(p.x);
						bl->set_column(p.y);
					}
					// MY_LOG_PRINT("win = %f", (r.multer * game_info.bet) / 10000.0);
					aw->set_win((r.multer * game_info.bet) / 10000.0);
					aw->set_bonus(r.LineNum);
					aw->set_maxlen(r.count);
				}
				// MY_LOG_PRINT("rm.multer = %d", rm.multer);
				combo->set_win((rm.multer * game_info.bet) / 10000.0);
				combo->set_combobonus(rm.comboBonus);
			}
		}
		plate->set_win((game_info.game_multer[g]*game_info.bet) / 10000.0);
		plate->set_totalfreespin(10);
		if(game_info.is_free && g==0)
		{
			plate->set_isbonus(true);
		}
	}
}

void CGameComponent::send_bet_roll_result(IUser *user, std::string msg, bool is_suc)
{
	MY_LOG_DEBUG("send_bet_roll_result");	
	serverProto::SpinResponse spin;

	serverProto::ServiceData *service = spin.mutable_service();
	serverProto::freeSpinList *freeSpinList= service->mutable_freeremainv2();
	auto uid = user->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		MY_LOG_ERROR("user[%d] game info is empty", user->get_user_id());
		return;
	}

	auto gold = user->get_gold();
	spin.set_roundindexv2(iter->second.round_index_v2);
	spin.set_postmoney((gold) / 100.0);
	spin.set_totalwin((iter->second.result)/ 100.0);
	//spin.set_totalwin(std::round(iter->second.result)/ 100.0);
	spin.set_hasspin(true);
	spin.set_basebet((iter->second.bet) / 100.0);
	spin.set_realbet((iter->second.bet) / 100.0);
	serverProto::SpinReq *req = spin.mutable_spinreq();
	req->set_bet((iter->second.bet) / 100.0);
	req->mutable_special();
	customProto::AllPlate plates;
	encodeACKProto(plates, iter->second);
	//encodeACKProto_test(ack, iter->second);

	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = true;
	google::protobuf::util::MessageToJsonString(plates, &json_string, options);

	//MY_LOG_PRINT("%s", json_string.c_str());
	customProto::AllPlate demo;
	bool isTest = false;
	if(isTest)
	{
		static int i = 493;
		//static int i = 567;
		MY_LOG_PRINT("第%d个图形", i + 1);
		string dd;
		myredis::GetInstance()->get_graph_from_redis_by_index("fh_reply", dd, i, 3);
		// i++;
		// if (i > 1000)
		// {
		// 	i = 0;
		// }
		std::string decoded = base64_decode(dd);

		demo.ParseFromString(decoded);
	}

	std::string *d = spin.mutable_data();
	if(isTest)
		demo.SerializeToString(d);
	else
		plates.SerializeToString(d);
	//
	json_string.clear();
	// google::protobuf::util::MessageToJsonString(spin, &json_string, options);
	// MY_LOG_PRINT("spin = %s", json_string.c_str());

	string data;
	spin.SerializeToString(&data);

	serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::spin);
	if(iter->second.token.length() >= AES_KEY_SIZE)
	{
		std::vector<unsigned char> encrypted = encryptAESCBC(data, iter->second.token);
		// string str = base64Encode(encrypted);
		// MY_LOG_PRINT("str = %s", str.c_str());

		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
		json_string.clear();

		response.set_token(iter->second.token);
		//google::protobuf::util::MessageToJsonString(response, &json_string, options);
		//MY_LOG_PRINT("response = %s", json_string.c_str());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content = "";
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
	MY_LOG_DEBUG("user[%d] send spin response ok with content length: %d total win: %.3f post_money: %.3f",
				 uid, content.length(), spin.totalwin(), spin.postmoney());
}

void CGameComponent::test_bet_roll_result(const user_game_info &game_info, int i)
{
	// mcProto::SpinAck ack;
	// encodeACKProto(ack, game_info);

	// std::string json_string;
	// google::protobuf::util::JsonPrintOptions options;
	// options.add_whitespace = false;

	// google::protobuf::util::MessageToJsonString(ack, &json_string, options);

	// std::remove("test.txt");
	// std::ofstream ofs;
	// ofs.open("test.txt", ios::out | ios::app);
	// ofs << json_string;

	// string dd;
	// //myredis::GetInstance()->get_graph_from_redis_by_index("ge_reply", dd, i, 3);
	// std::string decoded = base64_decode(dd);
	// mcProto::SpinAck demo;
	// demo.ParseFromString(decoded);

	// string diff;
	// // if (!compare_graph(ack, demo, diff))
	// // {
	// // 	MY_LOG_PRINT("graph%d diff = %s", i + 1, diff.c_str());
	// // }
	// // else
	// // {
	// // 	MY_LOG_PRINT("graph%d equal", i + 1);
	// // }

	// ofs.close();
}

void CGameComponent::send_room_info(IUser *pUser)
{
}

void CGameComponent::send_my_user_info(IUser *pUser)
{
}

void CGameComponent::send_game_info_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::GameInfoAck ack;
	serverProto::WalletInfo *wallet = ack.add_walletinfo();
	int64_t gold = user->get_gold();
	wallet->set_coin((gold) / 100.0);

   	int base_bet = 1;
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end())
	{
		get_base_bet(iter->second.client_id,  iter->second.base_bet);
		if(iter->second.base_bet > 1)
		{
			base_bet = iter->second.base_bet;
		}
	}
    for (int32_t i = 0; i < MAX_BET_CONFIG_COUNT; i++)
	{
		wallet->add_bet(bet_config_count[i]*base_bet);
	}
	wallet->set_unit(1);
	wallet->set_ratio(1);
	wallet->set_rate(0.2);
	wallet->set_decimal(4);

	serverProto::MallInfo* mall = ack.mutable_mall();
	mall->set_priceodd(40.5);
	mall->set_maxbet(10000);
	mall->set_alterid(50);
	mall->set_show(2);

	serverProto::GameMallInfo* game_mall = ack.mutable_gamemall();
	game_mall->set_maxbet(10000);
	game_mall->add_priceodd(40.5);
	game_mall->add_alterid(50);

	ack.set_maxodd(10000);
	ack.set_freespintype(-1);
	serverProto::freeSpinList *freespin = ack.mutable_freespin();


    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::info);	
	string data;
	ack.SerializeToString(&data);

	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}


void CGameComponent::send_notice_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::NoticeInfo info;
	
	int64_t gold = user->get_gold();
	info.set_type(0);
	info.set_value(0);
	info.set_currencynumber(33);

    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::notice);	
	string data;
	info.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}

void CGameComponent::send_setting_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::JiliJpSetting setting;
	
    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::jilijpSetting);	
	string data;
	setting.SerializeToString(&data);
	MY_LOG_DEBUG("data = %s", data.c_str());

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}
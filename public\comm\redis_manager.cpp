#include "redis_manager.h"
#include "log_manager.h"
#ifdef WIN32
#pragma comment(lib,"ws2_32.lib")
#else
#include "arpa/inet.h"
#endif

DECLARE_SINGLETON_MEMBER(redis_manager);
redis_manager::redis_manager():m_maxcon(0) {
}

redis_manager::~redis_manager() {
}

struct manager_key
{
    union{
        struct {
            unsigned int ip;
            unsigned short port;
			unsigned short ndb;
        };
        _uint64 key;
    };
};

CDLLHelper<IRedis>* redis_manager::alloc_redis()
{
	CDLLHelper<IRedis>* redis_helper=new CDLLHelper<IRedis>();
	if(!redis_helper->GetInterface())
	{
		redis_helper->DLLInit(SVRLIB_NAME,"_redis_instance","_redis_free");
		if(!redis_helper->GetInterface())  
		{
			MY_LOG_ERROR("redis_manager::alloc_redis instance redis fail()");
			return NULL;
		}
	}
	return redis_helper;

}

int redis_manager::get_port_by_uid(_uint64 uid)
{
	return 0;
}

IRedis* redis_manager::get_redis(char* ip, int port, int ndb,char* pwd) {

	int retry = 0;
	CDLLHelper<IRedis>* redis = NULL;

	while (retry < 3) {

        redis = get_redis_internal(ip, port, ndb, pwd);
		if (redis && is_connection_broken(redis->GetInterface())) {
			for (redis_iterator iter = m_map_redis.begin(); iter != m_map_redis.end(); iter ++) {
				if (iter->second == redis) {
					m_map_redis.erase(iter);
					redis->DeleteInstance();
					delete redis;
					break;
				}
			}
		} else {
			break;
		}

		retry ++;
	}

    if (redis) {
		return redis->GetInterface();
	}

	return NULL;
}

void redis_manager::put(char* ip, int port)
{
	manager_key key;
	key.port = port;
	key.ip = inet_addr(ip);
	redis_iterator itor;
	//dj::scope_lock lock(m_redis_lock); // ����	
	itor = m_map_redis.find(key.key);
	if(itor != m_map_redis.end())
	{
		itor->second->GetInterface()->close();
		delete itor->second;
		itor->second = NULL;
		m_map_redis.erase(itor);
	}
}

void redis_manager::free_redis()
{
	redis_iterator itor = m_map_redis.begin();
	for (;itor != m_map_redis.end();) {
		itor->second->GetInterface()->close();
		delete itor->second;
		itor->second = NULL;
		m_map_redis.erase(itor++);		
	}
}

void redis_manager::keep_alive() {
    
	for (redis_iterator iter = m_map_redis.begin(); iter != m_map_redis.end();) {
		if (is_connection_broken(iter->second->GetInterface())) {
			iter->second->DeleteInstance();
			delete iter->second;
			iter = m_map_redis.erase(iter);
		} else {
			iter ++;
		}
	}
}

bool redis_manager::is_connection_broken(IRedis* redis) {

    if (redis) {
		return !redis->cmd_ping();
	}

	return false;
}

CDLLHelper<IRedis>* redis_manager::get_redis_internal(char* ip, int port, int ndb,char* pwd) {

	bool is_err = false;

	IRedis* redis = NULL;
    manager_key key;
    key.port = port;
    key.ip = inet_addr(ip);
	key.ndb = ndb;
    redis_iterator iter;
	//dj::scope_lock lock(m_redis_lock); // ����	
    iter = m_map_redis.find(key.key);
    if(iter == m_map_redis.end())//��ipû�н�������
    {
        CDLLHelper<IRedis>* helper= alloc_redis();
		redis = helper->GetInterface();
		if(!redis->init(ip, port, ndb))
		{
			MY_LOG_ERROR("redis_manager::get error redis init! ip:%s, port:%d, ndb:%d, pwd:%s", ip, port, ndb, pwd);
			return NULL;
		}
		if (strlen(pwd) != 0)
		{
			if(!redis->set_auth(pwd))
			{
				MY_LOG_ERROR("redis_manager::get error redis pass word!");
				return NULL;
			}
		}
		if(!redis->connect())
		{
			char* erro_str = redis->get_error_msg();
			MY_LOG_ERROR("redis_manager::get error redis init!-:%s", erro_str);
			return NULL;
		}

		m_map_redis.insert(make_pair(key.key, helper));
		return helper;
    }

    return iter->second;
}
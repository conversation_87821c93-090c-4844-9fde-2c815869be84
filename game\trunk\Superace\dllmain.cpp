
#include "log_manager.h"
#include "typedef.h"
#include "dll.h"

#ifdef WIN32

#include <windows.h>

BOOL APIENTRY DllMain( HMODULE hModule,
					  DWORD  ul_reason_for_call,
					  LPVOID lpReserved
					  )
{
	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
	case DLL_THREAD_ATTACH:
	case DLL_THREAD_DETACH:
	case DLL_PROCESS_DETACH:
		break;
	}
	return TRUE;
}
#else

#include "version.h"

#endif

#include "game_component.h"

#ifdef _WINNT_SYSTEM
IDBPClient*  g_dbproxy = NULL;
#else
#endif

/* �ϲ��ʼ������� */
LIBRET(DLLInstance *) _get_component_instance()
{
	static DLLInstance g_dllInstance = {g_pstLogManager /* ��־ */
		, g_pstGameConfig /* ���� */
		, &g_dbproxy      /* db�� */
		, {0}
	};

#if !defined(_WIN32) && !defined(_WIN64)
	mysprintf(g_dllInstance.szVersionInfo, sizeof(g_dllInstance.szVersionInfo), "SGAME_GameDll Version: %d.%d.%d.%d_%d_%d"
		, MAJOR_VERSION, MINOR_VERSION, REVISE_VERSION, BUILD_VERSION, SVNINFO_REVERSION, BUILD_DATE);
#else
	mysprintf(g_dllInstance.szVersionInfo, sizeof(g_dllInstance.szVersionInfo), "SUPERACE.dll %d.%d.%d.%d_%d_%d", 
	    MAJOR_VERSION, MINOR_VERSION, REVISE_VERSION, BUILD_VERSION, SVNINFO_REVERSION, BUILD_DATE);
#endif

	return &g_dllInstance;
}

LIBRET(IHundredGameComponent *) _create_component_module()
{   
	return new CGameComponent();
}

LIBRET(void) _free_component_module(IHundredGameComponent *& pComponent)
{
	CGameComponent * pGameComponent = dynamic_cast<CGameComponent *>(pComponent);
	if (pGameComponent)
	{
		delete pGameComponent;
		pGameComponent = 0;
	}
}

LIBRET(void) _free_component_instance(DLLInstance *& pInstance)
{

}


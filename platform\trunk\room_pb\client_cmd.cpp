﻿
#include "log_manager.h"

#include <time.h>
#include "client_cmd.h"
#include "cmd_net.h"
#include "user_token.h"
#include "game_frame.h"
#include "room_route_challenge.h"
#include "user_manager.h"
#include "game_frame.h"
#include "comm_def.h"
#include "dbproxy_data.h"
#include "table_manager.h"
#include "config_manager.h"
#include "table_Hundred.h"
#include "my_redis.h"
#include "bill_fun.h"
#include "observe_manager.h"
#include "robot_manager.h"
#include "pair_manager.h"
#include "rand_position.h"
#include "room_bill_fun.h"
#include "server.pb.h"
#include "web_request.h"

#include "google/protobuf/text_format.h"
#include "google/protobuf/util/json_util.h"


extern CRoomRouteChallenge _room_route_challenge;
static CClientCmd _client_cmd;

CClientCmd::CClientCmd(void)
{
	/*********************** 客户端消息 ****************************/

	/* 优先处理 */
	__reg_user_rcv(on_enter_challenge_room, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_ROOM_ASK);
	__reg_user_rcv(on_create_challenge_room, cmd_net::CMD_ROOM, cmd_net::SUB_CREATE_ROOM_ASK);
	__reg_user_rcv(on_offline_enter_room, cmd_net::CMD_ROOM, cmd_net::SUB_OFFLINE_ENTER_ROOM);

	__reg_user_rcv(on_enter_nomal_room, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_NOMAL_ROOM_REQ);

	__reg_user_rcv(on_leave_nomal_room, cmd_net::CMD_ROOM, cmd_net::SUB_LEAVE_NOMAL_ROOM_REQ);
	__reg_user_rcv(on_nomal_room_ready_start, cmd_net::CMD_ROOM, cmd_net::SUB_NOMAL_READY_START_REQ);

	__reg_user_rcv(on_enter_web_room, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_WEB_ROOM_REQ);
	__reg_user_rcv(on_leave_web_room, cmd_net::CMD_ROOM, cmd_net::SUB_LEAVE_WEB_ROOM_REQ);

	//	__reg_user_rcv(on_bag_message_record_startpage_req, cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_SEARCH_REQ);

	__reg_user_rcv(on_bag_message_record_del_req, cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_DEL_REQ);

	/* 通用处理 */
	__reg_user_rcv(on_game_frame_message, cmd_net::CMD_ROOM, 0);

	__reg_user_rcv(on_game_frame_message, cmd_net::CMD_GAME, 0);
	__reg_user_rcv(on_game_frame_http_message, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_HTTP_ROOM_REQ);

	__reg_action_sink(ACTION_USER_DISCONNECTED, on_user_disconnect, this);

	__reg_user_rcv(lobby_notice_action, cmd_net::CMD_ROOM, cmd_net::CMD_BUY_NOTICE_TO_ROOM_REQ);
}

CClientCmd::~CClientCmd(void)
{
}

FTYPE(void)
CClientCmd::lobby_notice_action(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	cmd_room::LobbyNoticeType req(data, size);
	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(req.get_uid());
	if (RoomUserInfo == NULL)
	{
		return;
	}

	if (RoomUserInfo->get_user_status() != cmd_room::USER_STATUS_STANDUP)
	{
		return;
	}

	CGameFrame::GetInstance()->change_table(req.get_uid());
}

// 发送进入普通房结果
FTYPE(void)
CClientCmd::send_enter_web_room_result(socket_id sid, int ret, const char *msg)
{
	cmd_room::UserEnterWebRoomResp result;
	result.set_Result(ret);
	result.set_Roomid(g_server_id);
	if (msg)
		result.set_Msg(msg, 0);
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_WEB_ROOM_RESP, &result);
}

// 发送离开房间结果
FTYPE(void)
CClientCmd::send_leave_web_room_result(socket_id sid, int ret, const char *msg)
{
	cmd_room::UserLeaveWebRoomResp result;
	result.set_Result(ret);
	if (msg)
		result.set_Msg(msg, 0);
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_LEAVE_WEB_ROOM_RESP, &result);
}

// 发送进入普通房结果
FTYPE(void)
CClientCmd::send_enter_nomal_room_result(socket_id sid, int ret, const char *msg)
{
	cmd_room::UserEnterNoamlRoomResp result;
	result.set_Result(ret);
	result.set_Roomid(g_server_id);
	if (msg)
		result.set_Msg(msg, 0);
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_NOMAL_ROOM_RESP, &result);
}

FTYPE(void)
CClientCmd::send_leave_nomal_room_result(socket_id sid, int ret, const char *msg)
{
	cmd_room::UserLeaveNoamlRoomResp result;
	result.set_Result(ret);
	if (msg)
		result.set_Msg(msg, 0);
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_LEAVE_NOMAL_ROOM_RESP, &result);
}

// 发送准备开始结果
FTYPE(void)
CClientCmd::send_nomal_room_ready_result(socket_id sid, int ret, const char *msg)
{
	g_log->write_log(LOG_TYPE_DEBUG, "send_nomal_room_ready_result  ret：%d", ret);
	cmd_room::UserNomalReadyStartResp result;
	result.set_Result(ret);
	if (msg)
		result.set_Msg(msg, 0);
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_NOMAL_READY_START_RESP, &result);
}

// 发送配桌结果
FTYPE(void)
CClientCmd::send_pair_table_result(socket_id sid, int ret, int country, const char *msg)
{
	int patch_time_len = config_manager::GetInstance()->get_patch_time();

	// 延迟3秒
	cmd_room::UserAddPairResult result;
	result.set_Result(ret);
	result.set_TimerLen(patch_time_len);
	result.set_GameID(config_manager::GetInstance()->get_game_id());
	if (msg)
	{
		result.set_Msg(msg, 0);
	}
	else
	{
		if (ret != 0)
			result.set_Msg(get_error_msg(MSG_ROOM_ENTER_FAILED, country), 0);
		// result.set_Msg("进入房间失败，请稍后再试", 0);
	}
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_ADD_PAIR_RESULT, &result);
}

FTYPE(bool)
CClientCmd::check_user_tiyantimes(int uid, int nodeid)
{
	stGoldNodeInfo *nodeinfo = config_manager::GetInstance()->get_node_info(nodeid);
	g_log->write_log(LOG_TYPE_DEBUG, "check_user_tiyantimes  uid:%d nodeid:%d", uid, nodeid);
	if (nodeinfo == NULL)
		return true;

	int gameid = config_manager::GetInstance()->get_game_id();
	char key[32] = {0};
	sprintf(key, CUR_TIYAN_TIMES, uid, gameid);
	int use_times = myredis::GetInstance()->get_user_temp_data(key);
	g_log->write_log(LOG_TYPE_DEBUG, "check_user_tiyantimes  uid:%d nodeid:%d limit:%d tiyan:%d",
					 uid, nodeid, nodeinfo->tiyan_limit_times, nodeinfo->tiyan);

	if (nodeinfo == NULL)
		return true;

	if (nodeinfo->tiyan != 1 || nodeinfo->tiyan_limit_times == 0)
		return true;

	if (use_times >= nodeinfo->tiyan_limit_times)
		return false;

	return true;
}

FTYPE(bool)
CClientCmd::check_user_gold(int gold, int uid, int nodeid, IUser *RoomUserInfo)
{
	if (config_manager::GetInstance()->get_room_type() != TYPE_NOAML ||
		RoomUserInfo->get_user_type() == ROBOT_TYPE)
	{
		return false;
	}

	stGoldNodeInfo *nodeinfo = config_manager::GetInstance()->get_node_info(nodeid);

	bool is_kick = false;
	const char *msg = NULL;
	if (nodeinfo != NULL && nodeinfo->tiyan == 1)
	{
		if (RoomUserInfo->get_score() <= TIYAN_CHECK_SCORE)
		{
			is_kick = true;
			msg = get_error_msg(MSG_ROOM_LOWER_GOLD_LIMIT, RoomUserInfo->get_user_country());
			CGameFrame::GetInstance()->send_comm_message(uid, cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
		}
	}
	else
	{
		int min_gold = config_manager::GetInstance()->get_min_gold(nodeid);
		int max_gold = config_manager::GetInstance()->get_max_gold(nodeid);
		if (gold > max_gold && max_gold != 0)
		{
			is_kick = true;
			msg = get_error_msg(MSG_ROOM_UPPER_GOLD_LIMIT, RoomUserInfo->get_user_country());
			CGameFrame::GetInstance()->send_comm_message(uid, cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
		}
		else if (gold <= 0 || (min_gold != 0 && gold < min_gold))
		{
			is_kick = true;
			msg = get_error_msg(MSG_ROOM_LOWER_GOLD_LIMIT, RoomUserInfo->get_user_country());
			CGameFrame::GetInstance()->send_comm_message(uid, cmd_room::ROOM_NOMAL_GOLD_LIMIT, msg);
		}
	}
	return is_kick;
}

// 普通房准备开始
FTYPE(void)
CClientCmd::on_nomal_room_ready_start(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	g_log->write_log(LOG_TYPE_DEBUG, "on_nomal_room_ready_start  uid:%llu", userid);
	if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE ||
		config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE ||
		config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED ||
		config_manager::GetInstance()->get_room_type() == TYPE_FMATCH)
	{
		g_log->write_log(LOG_TYPE_WARNING, "The room type is challenge.so return fail. uid：%llu", userid);
		return;
	}

	const char *msg = NULL;
	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(userid);
	if (!RoomUserInfo)
	{
		// 用户不存在房间里面
		msg = get_error_msg(MSG_ROOM_GOLD_PLAY_NOT_EXIST_ROOM, LANGUATE_EN);
		send_nomal_room_ready_result(sid, cmd_room::GOLD_ROOM_USER_NOT_IN_ROOM, msg);

		UserManager::GetInstance()->delete_user(userid);
		CRoomRouteChallenge::send_nomal_user_leave(userid);
		return;
	}
	/*else
	{
		//正在游戏，不处理
		if (RoomUserInfo->get_table_id() != INVALID_TABLE)
			return;
	}*/

	// 正在游戏，不处理
	if (RoomUserInfo->get_table_id() != INVALID_TABLE && RoomUserInfo->get_real_status() == cmd_room::USER_STATUS_PLAY)
	{
		return;
	}

	if (!check_user_tiyantimes(userid, RoomUserInfo->get_node_id()))
	{
		msg = get_error_msg(MSG_ROOM_TIYAN_LIMIT_TIMES, RoomUserInfo->get_user_country());
		CGameFrame::GetInstance()->send_comm_message(userid, cmd_room::ROOM_ENTER_ROOM_LIMIT, msg, cmd_room::COMM_TIPS_BOX);
		return;
	}

	_uint64 now_gold = myredis::GetInstance()->get_data_by_uid(userid, DB_COMM, CUR_GOLD);
	RoomUserInfo->set_gold(now_gold);
	if (check_user_gold(now_gold, userid, RoomUserInfo->get_node_id(), RoomUserInfo))
		return;

	_uint8 is_change_table = 0;
	if (size != 0)
	{
		cmd_room::ReadyStartReq req(data, size);
		is_change_table = req.get_is_change();
	}

	// 每局配桌模式先放入配桌队列
	if (config_manager::GetInstance()->get_patch_mode() == cmd_room::MODE_PATCH)
	{
		if (RoomUserInfo->get_table_id() != INVALID_TABLE)
		{
			CTable *itable = TableManager::GetInstance()->get_table(RoomUserInfo->get_table_id());
			if (itable)
				itable->standup_user(RoomUserInfo->get_chair_id());
		}

		int add_ret = pair_manager::GetInstance()->add_pair(userid, RoomUserInfo->get_node_id());
		send_pair_table_result(sid, add_ret, RoomUserInfo->get_user_country());
		return;
	}
	else // 单局配桌模式
		if (config_manager::GetInstance()->get_patch_mode() == cmd_room::MODE_PATCH_ONE)
		{
			if (RoomUserInfo->get_table_id() != INVALID_TABLE)
			{
				// 在桌子上，判断桌子上面是否有人离开了，是的话站起重新速配,否则直接准备游戏
				CTable *itable = TableManager::GetInstance()->get_table(RoomUserInfo->get_table_id());
				if (itable == NULL || is_change_table == 1)
				{
					itable->standup_user(RoomUserInfo->get_chair_id());
					int add_ret = pair_manager::GetInstance()->add_pair(userid, RoomUserInfo->get_node_id());
					send_pair_table_result(sid, add_ret, RoomUserInfo->get_user_country());
				}
				else
				{
					itable->on_user_action(RoomUserInfo, cmd_room::USER_ACTION_READY, true);
				}
			}
			else
			{
				int add_ret = pair_manager::GetInstance()->add_pair(userid, RoomUserInfo->get_node_id());
				send_pair_table_result(sid, add_ret, RoomUserInfo->get_user_country());
			}
		}
		else // 重新寻桌进入模式
		{
			int now_table_id = RoomUserInfo->get_table_id();
			if (RoomUserInfo->get_table_id() != INVALID_TABLE)
			{
				CTable *itable = TableManager::GetInstance()->get_table(now_table_id);
				if (itable)
					itable->standup_user(RoomUserInfo->get_chair_id());
			}

			// 寻找桌子
			CTable *table = NULL;
			if (config_manager::GetInstance()->get_choice_real() == 1)
			{
				table = TableManager::GetInstance()->find_nomal_real_table(now_table_id,
																		   RoomUserInfo->get_node_id());
			}
			else if (config_manager::GetInstance()->get_choice_one() == 1)
			{
				table = TableManager::GetInstance()->find_nomal_real_one(now_table_id,
																		 RoomUserInfo->get_node_id());
			}
			else
			{
				table = TableManager::GetInstance()->find_nomal_table(now_table_id,
																	  RoomUserInfo->get_node_id());
			}

			if (table == NULL)
			{
				msg = get_error_msg(MSG_ROOM_GOLD_PLAY_NOT_MATCH_ROOM, RoomUserInfo->get_user_country());
				send_nomal_room_ready_result(sid, cmd_room::GOLD_ALLOCATION_TABLE_FAIL, msg);

				UserManager::GetInstance()->delete_user(userid);
				CRoomRouteChallenge::send_nomal_user_leave(userid);
				return;
			}

			// 坐下桌子
			if (table->on_user_enter(RoomUserInfo) != 0)
			{
				msg = get_error_msg(MSG_CHALLENGE_OUT_GOLD_RANGE, RoomUserInfo->get_user_country());
				send_nomal_room_ready_result(sid, cmd_room::GOLD_STANDUP_FAIL, msg);

				UserManager::GetInstance()->delete_user(userid);
				CRoomRouteChallenge::send_nomal_user_leave(userid);
				return;
			}

			send_nomal_room_ready_result(sid, 0);
		}
}

FTYPE(bool)
CClientCmd::on_with_hold(bool is_suc, int uid, const void *data, int size, socket_id sid, int code)
{
	if (!is_suc)
	{
		// 预扣失败，提示没钱，并且清理出房间
		int user_country = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_COUNTRY_CODE);
		const char *msg = get_error_msg(MSG_ROOM_LOWER_GOLD_LIMIT, user_country);
		char tips[64] = {0};
		sprintf(tips, "%s code:%d", msg, code);
		send_enter_room_fail(sid, 0, 4, tips);
		UserManager::GetInstance()->delete_user(uid, true);
		return true;
	}

	cmd_room::UserEnterNoamlRoomReq req;
	req.unpack(data, size);
	stGoldNodeInfo *nodeinfo = config_manager::GetInstance()->get_node_info(req.get_NodeID());
	if (nodeinfo->with_hold != 0)
	{
		myredis::GetInstance()->update_data_by_uid(uid, DB_COMM, CUR_WHTH_GOLD, nodeinfo->with_hold);
	}

	return on_enter_namal_logic(sid, uid, data, size);
}

bool CClientCmd::on_enter_namal_logic(socket_id sid, _uint64 userID, const void *data, int size)
{
	int user_country = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_COUNTRY_CODE);
	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(userID);
	if (RoomUserInfo == NULL)
	{
		return false;
	}

	cmd_room::UserEnterNoamlRoomReq req;
	req.unpack(data, size);

	if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
	{
		// 配桌模式先放入配桌队列
		int add_ret = pair_manager::GetInstance()->add_pair(userID, req.get_NodeID());
		on_enter_room_succ(sid, userID);
		send_pair_table_result(sid, add_ret, user_country);
	}
	else
	{
		// 寻找桌子
		CTable *table = NULL;
		if (config_manager::GetInstance()->get_choice_real() == 1)
		{
			table = TableManager::GetInstance()->find_nomal_real_table(INVALID_TABLE, req.get_NodeID());
		}
		else if (config_manager::GetInstance()->get_choice_one() == 1)
		{
			table = TableManager::GetInstance()->find_nomal_real_one(INVALID_TABLE, req.get_NodeID());
		}
		else
		{
			table = TableManager::GetInstance()->find_nomal_table(INVALID_TABLE, req.get_NodeID());
		}
		if (table == NULL)
		{
			const char *msg = get_error_msg(MSG_ROOM_GOLD_PLAY_NOT_MATCH_ROOM, user_country);
			send_enter_nomal_room_result(sid, cmd_room::ROOM_NOMAL_NOR_FIND, msg);
			send_enter_room_fail(sid, 0, 4, "server erro");
			UserManager::GetInstance()->delete_user(userID, true);
			return false;
		}

		// 坐下桌子
		bool is_quick = (req.exist_IsQuick() && req.get_IsQuick() == 1);
		int ret = table->on_user_enter(RoomUserInfo, false, false, false, is_quick);
		if (ret != 0)
		{
			const char *msg = get_error_msg(MSG_ROOM_GOLD_PLAY_NOT_MATCH_ROOM, RoomUserInfo->get_user_country());
			send_enter_nomal_room_result(sid, ret);
			send_enter_room_fail(sid, 0, 5, "server erro");
			UserManager::GetInstance()->delete_user(userID, true);
			return false;
		}
	}

	// 通知网关用户进入
	on_enter_room_succ(sid, userID);

	// 通知约战服用户进入
	CRoomRouteChallenge::send_nomal_user_enter(userID);

	// 通知用户进入结果
	send_enter_nomal_room_result(sid, 0);

	return true;
}

// 进入普通房
FTYPE(void)
CClientCmd::on_enter_nomal_room(socket_id sid, _uint64 userid, SNETHEAD *head,
								const void *data, int size, const void *ex_data)
{
	cmd_gate::CGateUserInfo userInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;
	cmd_lobby::CLogonSuccessResp logonUserInfo;
	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		logonUserInfo.unpack(userInfo.get_LogonInfoBuffer(), userInfo.LogonInfoBuffer_length());
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (!userInfo.exist_UserID())
	{
		send_enter_room_fail(sid, 0, 1, "server erro");
		return;
	}

	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_enter_nomal_room  uid:%llu", userInfo.get_UserID());
	if (config_manager::GetInstance()->get_room_type() != TYPE_NOAML)
	{
		send_enter_room_fail(sid, 0, 2, "server erro");
		return;
	}

	// 在房间的用户直接断线重回
	_tint64 userID = userInfo.get_UserID();
	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(userID);
	if (RoomUserInfo)
	{
		_client_cmd.on_offline_enter_room(sid, userID, head, data, size, ex_data);
		return;
	}

	cmd_room::UserEnterNoamlRoomReq req;
	req.unpack(data, size);

	/*if (!room_check::GetInstance()->on_check_user_enter(userID))
	{
		const char *msg = msg_manager::GetInstance()->get_message_by_id(MSG_ROOM_LIMIT);
		send_enter_nomal_room_result(sid, cmd_room::ROOM_ENTER_ROOM_LIMIT, msg);
		return;
	}*/
	UidRedisInfo uid_info;
	myredis::GetInstance()->get_all_uid_info(userID, uid_info);
	_tint64 gold = uid_info.gold;
	MY_LOG_DEBUG("on_enter_nomal_room uid[%d] gold=%lld", userID, gold);

	RoomUserInfo = UserManager::GetInstance()->create_user(userID, req.get_NodeID(), logonUserInfo.get_UserType());
	if (RoomUserInfo)
	{
		char game_version[32] = {0};
		__soft_ver_to_str(softInfo.get_SoftVer(), game_version, sizeof(game_version));

		RoomUserInfo->set_user_version(game_version);
		RoomUserInfo->set_user_id(userID);
		RoomUserInfo->set_head_image(logonUserInfo.get_FaceID());
		RoomUserInfo->set_nick_name(logonUserInfo.get_NickName());
		RoomUserInfo->set_socket_id(sid);
		RoomUserInfo->set_client((_uint32)softInfo.get_MChannel());
		RoomUserInfo->set_user_type(logonUserInfo.get_UserType());
		RoomUserInfo->set_user_country(logonUserInfo.get_country_code());

		char *ip = myredis::GetInstance()->get_data_by_uid_for_string(userID, DB_COMM, CUR_LOGON_IP);
		RoomUserInfo->set_ip(ip);
		RoomUserInfo->set_room_card(gameinfo.get_RoomCard());
		RoomUserInfo->set_sys_type(softInfo.get_SysType());
		RoomUserInfo->set_last_gold(gold);
		RoomUserInfo->set_enter_time(time(0));
		RoomUserInfo->set_write_gameinfo_time(time(0));

		int plat = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_CHANNEL_ID);
		_uint64 now_gold = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_GOLD);
		RoomUserInfo->set_gold(now_gold);
		RoomUserInfo->set_plat_type(plat);
		g_server->bind_user(sid, RoomUserInfo);

		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_enter_nomal_room  bind_user uid:%llu  sid:%d usertype:%d",
						 userInfo.get_UserID(), sid, logonUserInfo.get_UserType());
	}
	else
	{
		const char *msg = get_error_msg(MSG_ROOM_GOLD_PLAY_NOT_MATCH_ROOM, LANGUATE_EN);
		send_enter_nomal_room_result(sid, cmd_room::ROOM_NOMAL_NOR_FIND, msg);
		send_enter_room_fail(sid, 0, 3, "server erro");
		return;
	}

	if (logonUserInfo.get_UserType() == ROBOT_TYPE)
	{
		robot_manager::GetInstance()->add_robot(logonUserInfo.get_UserID());
		send_enter_nomal_room_result(sid, 0);
		on_enter_room_succ(sid, userID);
	}
	else
	{
		if (!check_user_tiyantimes(userID, req.get_NodeID()))
		{
			const char *msg = get_error_msg(MSG_ROOM_TIYAN_LIMIT_TIMES, RoomUserInfo->get_user_country());
			send_enter_nomal_room_result(sid, cmd_room::ROOM_ENTER_ROOM_LIMIT, msg);
			send_enter_room_fail(sid, 0, 4, "server erro");
			UserManager::GetInstance()->delete_user(userID, true);
			return;
		}

		// 单一模式，需要预扣的游戏，走异步进入房间
		stGoldNodeInfo *nodeinfo = config_manager::GetInstance()->get_node_info(req.get_NodeID());
		int api_type = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_API_MODE);
		if (api_type == MODE_SINGLE && nodeinfo->with_hold != 0)
		{
			transfer_inout inout;
			get_guid(inout.gguid);
			inout.bill_type = GOLD_WITH_HOLD;
			inout.result = -nodeinfo->with_hold;
			inout.pay_out = -nodeinfo->with_hold;
			inout.uid = userID;
			inout.sid = sid;
			inout.write_db = true;
			inout.call_back = false;
			memcpy(inout.p_data.buff, data, size);
			inout.p_data.buff_len = size;
			room_bill_fun::GetInstance()->write_transfer_inout(inout);
			return;
		}

		on_enter_namal_logic(sid, userID, data, size);
	}
}

// 离开普通房
FTYPE(void)
CClientCmd::on_leave_nomal_room(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	cmd_gate::CGateUserInfo userInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;
	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (!userInfo.exist_UserID())
	{
		g_log->write_log(LOG_TYPE_WARNING, "The userid is not exist userinfo. userid:%llu", userid);
		return;
	}
	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_leave_nomal_room  uid:%llu, room_type(%d)", userInfo.get_UserID(), config_manager::GetInstance()->get_room_type());

	// 其他场接受到SUB_LEAVE_NOMAL_ROOM_REQ消息时，不做处理
	if (config_manager::GetInstance()->get_room_type() != TYPE_NOAML)
	{
		return;
	}

	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(userInfo.get_UserID());
	if (RoomUserInfo == NULL)
	{
		const char *msg = get_error_msg(MSG_ROOM_LEAVE_FAILE, LANGUATE_EN);
		send_leave_nomal_room_result(sid, cmd_room::ROOM_NOMAL_LEAVE_FAILE, msg);
		return;
	}

	CTable *table = TableManager::GetInstance()->get_table(RoomUserInfo->get_table_id());
	if (table == NULL)
	{
		send_leave_nomal_room_result(sid, 0);
		UserManager::GetInstance()->delete_user(RoomUserInfo);
		CRoomRouteChallenge::send_nomal_user_leave(userInfo.get_UserID());
		if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
			pair_manager::GetInstance()->delete_pair(userInfo.get_UserID());
	}
	else
	{
		int ret = table->on_user_leave(RoomUserInfo);
		CGameFrame::GetInstance()->send_action_result(userInfo.get_UserID(), ret, cmd_room::USER_ACTION_LEAVE);
		if (ret == 0)
		{
			send_leave_nomal_room_result(sid, 0);
			UserManager::GetInstance()->delete_user(RoomUserInfo);
			CRoomRouteChallenge::send_nomal_user_leave(userInfo.get_UserID());
		}
		else
		{
			// const char *msg = msg_manager::GetInstance()->get_message_by_id(MSG_ROOM_LEAVE_FAILE);
			send_leave_nomal_room_result(sid, 0);
		}
	}

	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_leave_nomal_room close uid：%llu  sid:%d", userInfo.get_UserID(), sid);
	// send_has_leave_room(sid, userInfo.get_UserID());
}

// 进入WEB百人游戏
FTYPE(void)
CClientCmd::on_enter_web_room(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	cmd_gate::CGateUserInfo userInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;
	cmd_lobby::CLogonSuccessResp logonUserInfo;
	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		logonUserInfo.unpack(userInfo.get_LogonInfoBuffer(), userInfo.LogonInfoBuffer_length());
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (!userInfo.exist_UserID())
	{
		send_enter_room_fail(sid, 0, 1, "server erro");
		g_log->write_log(LOG_TYPE_WARNING, "on_enter_web_room The userid is not exist userinfo. userid:%llu", userid);
		return;
	}

	// 在房间的用户直接断线重回
	MY_LOG_DEBUG("on_enter_web_room uid:%lld", userInfo.get_UserID());
	_tint64 userID = userInfo.get_UserID();
	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(userID);
	if (RoomUserInfo)
	{
		int node_id = RoomUserInfo->get_node_id();
		CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			ptable->on_user_reconn(RoomUserInfo);
			on_enter_room_succ(sid, userID);
		}
		else
		{
			send_enter_room_fail(sid, 0, 6, "server erro");
			// CRoomRouteChallenge::send_web_user_leave(userID);
		}
		return;
	}

	if (config_manager::GetInstance()->get_room_type() != TYPE_HUNDRED)
	{
		send_enter_room_fail(sid, 0, 7, "server erro");
		return;
	}

	cmd_room::UserEnterWebRoomReq req;
	req.unpack(data, size);
	int node_id = req.get_NodeID();

	// 根据逻辑给用户挑选适合的房间

	CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(node_id);
	if (!ptable)
	{
		MY_LOG_DEBUG("on_enter_web_room uid:%lld no talbe:nodeid:%d", userInfo.get_UserID(), node_id);
		send_enter_web_room_result(sid, cmd_room::ROOM_NOMAL_NOR_FIND);
		send_enter_room_fail(sid, 0, 8, "server erro");
		// CRoomRouteChallenge::send_web_user_leave(userID);
		return;
	}

	// 场次是否已经满了
	if (UserManager::GetInstance()->user_count() >= ptable->get_max_user())
	{
		MY_LOG_DEBUG("on_enter_web_room uid:%lld not seat :nodeid:%d user:%d", userInfo.get_UserID(), node_id, UserManager::GetInstance()->user_count(), ptable->get_max_user());
		send_enter_web_room_result(sid, cmd_room::ROOM_NOMAL_NOR_FIND);
		send_enter_room_fail(sid, 0, 9, "server erro");
		return;
	}

	// 进场条件是否满足 TODO
	UidRedisInfo uid_info;
	myredis::GetInstance()->get_all_uid_info(userID, uid_info);
	_tint64 gold = uid_info.gold;
	MY_LOG_DEBUG("on_enter_web_room uid[%d] gold=%lld contry_code=%lld", userID, gold, logonUserInfo.get_country_code());

	RoomUserInfo = UserManager::GetInstance()->create_user(userID, req.get_NodeID(), logonUserInfo.get_UserType());
	if (RoomUserInfo)
	{
		char game_version[32] = {0};
		__soft_ver_to_str(softInfo.get_SoftVer(), game_version, sizeof(game_version));

		RoomUserInfo->set_user_version(game_version);
		RoomUserInfo->set_user_id(userID);
		RoomUserInfo->set_head_image(logonUserInfo.get_FaceID());
		RoomUserInfo->set_nick_name(logonUserInfo.get_NickName());
		RoomUserInfo->set_socket_id(sid);
		RoomUserInfo->set_client((_uint32)softInfo.get_MChannel());
		RoomUserInfo->set_user_country(logonUserInfo.get_country_code());

		char *ip = myredis::GetInstance()->get_data_by_uid_for_string(userID, DB_COMM, CUR_LOGON_IP);
		RoomUserInfo->set_ip(ip);
		RoomUserInfo->set_room_card(gameinfo.get_RoomCard());

		RoomUserInfo->set_user_type(logonUserInfo.get_UserType());
		RoomUserInfo->set_node_id(node_id);
		RoomUserInfo->set_last_gold(gold);
		RoomUserInfo->set_enter_time(time(0));
		RoomUserInfo->set_write_gameinfo_time(time(0));

		_uint64 now_gold = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_GOLD);
		RoomUserInfo->set_gold(now_gold);
		int plat = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_CHANNEL_ID);
		RoomUserInfo->set_plat_type(plat);
		RoomUserInfo->set_sys_type(softInfo.get_SysType());
		g_server->bind_user(sid, RoomUserInfo);
	}
	else
	{
		MY_LOG_DEBUG("on_enter_web_room uid:%lld createuserfail :nodeid:%d ", userInfo.get_UserID(), node_id);
		send_enter_web_room_result(sid, cmd_room::ROOM_NOMAL_NOR_FIND);
		send_enter_room_fail(sid, 0, 10, "server erro");
		return;
	}

	// 通知用户进入结果
	send_enter_web_room_result(sid, 0);
	if (robot_manager::GetInstance()->get_robot_count() < config_manager::GetInstance()->get_backup_count() &&
		RoomUserInfo->get_user_type() == ROBOT_TYPE)
	{
		// 备份机器人数目不够，添加到队列
		robot_manager::GetInstance()->add_robot(userID);
		on_enter_room_succ(sid, userID);
		CRoomRouteChallenge::send_web_user_enter(userID);
		return;
	}

	if (ptable->on_user_enter(RoomUserInfo) == 0)
	{
		// 通知网关用户进入
		on_enter_room_succ(sid, userID);

		// 通知约战服用户进入
		CRoomRouteChallenge::send_web_user_enter(userID);

		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_enter_web_room  uid：%llu", userInfo.get_UserID());
	}
	else
	{
		MY_LOG_DEBUG("on_enter_web_room uid:%lld on_user_enter fail :nodeid:%d ", userInfo.get_UserID(), node_id);
		send_enter_web_room_result(sid, cmd_room::ROOM_NOMAL_NOR_FIND);
		send_enter_room_fail(sid, 0, 11, "server erro");
		UserManager::GetInstance()->delete_user(userID, true);
	}
}

FTYPE(void)
CClientCmd::create_vir_robot(int begin_uid, int end_uid)
{
	vector<int> node_list;
	TableManager::GetInstance()->get_all_hundred_node(node_list);

	int n = 0;
	for (vector<int>::iterator itr = node_list.begin(); itr != node_list.end(); itr++)
	{
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::create_vir_robot n:%d, nodeid:%d .", n, *itr);
		n++;
	}

	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
	{
		for (int i = begin_uid; i < end_uid; i++)
		{
			IUser *puser = UserManager::GetInstance()->create_user(i, 0, ROBOT_TYPE);
			if (puser)
			{
				// stRobotVirInfo info = { 0 };
				// config_manager::GetInstance()->pop_robot_vir_info(info);

				puser->set_user_version("1.0.0");
				puser->set_user_id(i);
				puser->set_head_image("");
				puser->set_socket_id(0);
				puser->set_sex(0);
				puser->set_client(0);
				puser->set_user_type(ROBOT_TYPE);
				puser->set_ip("");
				puser->set_room_card(0);
				puser->set_gold(0);
				puser->set_sys_type(0);
				puser->set_enter_time(time(0));
				puser->set_write_gameinfo_time(time(0));
				puser->set_node_id(0);
				puser->set_nick_name("");

				char rand_pos[128] = {0};
				get_rand_postion(rand_pos);
				puser->set_position_info(rand_pos, strlen(rand_pos));
				MY_LOG_DEBUG("create_vir_robot... uid:%d  rand_pos:%s", i, rand_pos);
				// g_server->bind_user(0, puser);
				myredis::GetInstance()->set_data_by_uid(i, DB_COMM, CUR_USER_TYPE, ROBOT_TYPE);

				robot_manager::GetInstance()->add_robot(i);
			}
		}
	}
	else if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int num = node_list.size();
		int nCount = 1;
		int nBegin = 0, nEnd = 0;
		int robot_backup_count = config_manager::GetInstance()->get_backup_count();
		int per = (end_uid - begin_uid - robot_backup_count) / (num > 0 ? num : 1);
		for (vector<int>::iterator itr = node_list.begin(); itr != node_list.end(); itr++)
		{
			if (num > 1 && per > 0)
			{
				if (nCount == 1)
				{
					// 如果多个节点，第一个节点分配的机器人要算备用的
					nBegin = begin_uid + (nCount - 1) * per;
					nEnd = begin_uid + nCount * per - 1 + robot_backup_count;
				}
				else
				{
					nBegin = begin_uid + (nCount - 1) * per + robot_backup_count;
					nEnd = begin_uid + nCount * per - 1 + robot_backup_count;
				}
			}
			else
			{
				// 一个节点
				nBegin = begin_uid;
				nEnd = end_uid;
			}

			g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::create_vir_robot all robot nCount:%d, num:%d, per:%d, nBegin:%d, nEnd:%d, robot_backup_count:%d  .",
							 nCount, num, per, nBegin, nEnd, robot_backup_count);

			for (int i = nBegin; i < nEnd; i++)
			{
				IUser *puser = UserManager::GetInstance()->create_user(i, 0, ROBOT_TYPE);
				if (puser)
				{
					stRobotVirInfo info = {0};
					config_manager::GetInstance()->rand_robot_vir_info(info);

					puser->set_user_version("1.0.0");
					puser->set_user_id(i);
					puser->set_head_image(info.face);
					puser->set_socket_id(0);
					puser->set_sex(0);
					puser->set_client(0);
					puser->set_user_type(ROBOT_TYPE);
					puser->set_ip("");
					puser->set_room_card(0);
					puser->set_gold(0);
					puser->set_sys_type(0);
					puser->set_enter_time(time(0));
					puser->set_write_gameinfo_time(time(0));
					puser->set_node_id(0);
					puser->set_nick_name(info.name);

					char rand_pos[128] = {0};
					get_rand_postion(rand_pos);
					puser->set_position_info(rand_pos, strlen(rand_pos));
					MY_LOG_DEBUG("create_vir_robot... uid:%d  face:%s rand_pos:%s", i, info.face, rand_pos);
					// g_server->bind_user(0, puser);
					myredis::GetInstance()->set_data_by_uid(i, DB_COMM, CUR_USER_TYPE, ROBOT_TYPE);

					// 放一部分进备用组
					if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
					{
						if (robot_manager::GetInstance()->get_robot_count() < config_manager::GetInstance()->get_backup_count())
						{
							robot_manager::GetInstance()->add_robot(i);
						}
						else
						{
							int node_id = *itr;
							CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(node_id);
							if (ptable)
							{
								g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::create_vir_robot create robot succe uid:%d, node_id:%d .", i, node_id);
								ptable->on_user_enter(puser);
							}
							else
							{
								g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::create_vir_robot create robot fail uid:%d, node_id:%d .", i, node_id);
							}
						}
					}
				}
			}
			nCount++;

			config_manager::GetInstance()->m_map_node_add_robot[*itr] = nEnd - nBegin;
			g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::create_vir_robot create robot info end_uid:%d, node_id:%d .", end_uid, *itr);
		}

		if (end_uid > config_manager::GetInstance()->last_vir_uid)
			config_manager::GetInstance()->last_vir_uid = end_uid;
	}
}

FTYPE(void)
CClientCmd::new_room_add_vir_robot()
{
	vector<int> node_list;
	TableManager::GetInstance()->get_all_hundred_node(node_list);

	if (config_manager::GetInstance()->get_room_type() != TYPE_HUNDRED)
		return;

	for (vector<int>::iterator itr = node_list.begin(); itr != node_list.end(); itr++)
	{
		int node_id = *itr;
		iter_node_add_robot iterAddRobot = config_manager::GetInstance()->m_map_node_add_robot.find(node_id);
		iter_node_info iterNodeInfo = config_manager::GetInstance()->m_map_node_info.find(node_id);
		if (iterAddRobot == config_manager::GetInstance()->m_map_node_add_robot.end() &&
			iterNodeInfo != config_manager::GetInstance()->m_map_node_info.end())
		{
			int begin_uid = config_manager::GetInstance()->last_vir_uid + 1;
			int end_uid = begin_uid + 40;
			for (int i = begin_uid; i < end_uid; i++)
			{
				IUser *puser = UserManager::GetInstance()->create_user(i, 0, ROBOT_TYPE);
				if (puser)
				{
					stRobotVirInfo info = {0};
					config_manager::GetInstance()->rand_robot_vir_info(info);

					puser->set_user_version("1.0.0");
					puser->set_user_id(i);
					puser->set_head_image(info.face);
					puser->set_socket_id(0);
					puser->set_sex(0);
					puser->set_client(0);
					puser->set_user_type(ROBOT_TYPE);
					puser->set_ip("");
					puser->set_room_card(0);
					puser->set_gold(0);
					puser->set_sys_type(0);
					puser->set_enter_time(time(0));
					puser->set_write_gameinfo_time(time(0));
					puser->set_node_id(0);
					puser->set_nick_name(info.name);

					char rand_pos[128] = {0};
					get_rand_postion(rand_pos);
					puser->set_position_info(rand_pos, strlen(rand_pos));
					MY_LOG_DEBUG("new_room_add_vir_robot... uid:%d  face:%s rand_pos:%s", i, info.face, rand_pos);
					// g_server->bind_user(0, puser);
					myredis::GetInstance()->set_data_by_uid(i, DB_COMM, CUR_USER_TYPE, ROBOT_TYPE);

					// 房间放入机器人
					CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(node_id);
					if (ptable)
					{
						g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::new_room_add_vir_robot create robot succe uid:%d, node_id:%d .", i, node_id);
						ptable->on_user_enter(puser);
					}
					else
					{
						g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::new_room_add_vir_robot create robot fail uid:%d, node_id:%d .", i, node_id);
					}
				}
			}

			config_manager::GetInstance()->m_map_node_add_robot[node_id] = end_uid - begin_uid;
			g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::new_room_add_vir_robot create robot info begin_uid:%d, end_uid:%d, node_id:%d, last_vir_uid:%d .", begin_uid, end_uid, node_id, config_manager::GetInstance()->last_vir_uid);

			if (end_uid > config_manager::GetInstance()->last_vir_uid)
				config_manager::GetInstance()->last_vir_uid = end_uid;
		}

		// g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd::new_room_add_vir_robot nodeid:%d .", node_id);
	}
}

// 离开WEB百人游戏
FTYPE(void)
CClientCmd::on_leave_web_room(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	cmd_gate::CGateUserInfo userInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;
	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (config_manager::GetInstance()->get_room_type() != TYPE_HUNDRED)
	{
		return;
	}

	if (!userInfo.exist_UserID())
	{
		g_log->write_log(LOG_TYPE_WARNING, "The userid is not exist userinfo. userid:%llu", userid);
		return;
	}

	IUser *RoomUserInfo = UserManager::GetInstance()->get_user(userInfo.get_UserID());
	if (RoomUserInfo == NULL)
	{
		send_leave_web_room_result(sid, cmd_room::ROOM_NOMAL_LEAVE_FAILE);
		return;
	}

	int node_id = RoomUserInfo->get_node_id();
	CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(node_id);
	if (!ptable)
	{
		send_leave_web_room_result(sid, 1);
		return;
	}

	int ret = ptable->on_user_leave(RoomUserInfo);
	CGameFrame::GetInstance()->send_action_result(userInfo.get_UserID(), ret, cmd_room::USER_ACTION_LEAVE);
	if (ret == 0)
	{
		UserManager::GetInstance()->delete_user(RoomUserInfo);
		CRoomRouteChallenge::send_web_user_leave(userInfo.get_UserID());
	}
	else
	{
		send_leave_web_room_result(sid, 1);
		return;
	}

	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_leave_web_room close uid：%llu  sid:%d", userInfo.get_UserID(), sid);
	send_leave_web_room_result(sid, 0);
	send_has_leave_room(sid, userInfo.get_UserID(), false);
	g_server->close(sid);
}

FTYPE(void)
CClientCmd::on_enter_challenge_room(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	g_log->write_log(LOG_TYPE_DEBUG, "on_enter_challenge_room");

	cmd_gate::CGateUserInfo userInfo;
	cmd_lobby::CLogonSuccessResp logonUserInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;

	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		logonUserInfo.unpack(userInfo.get_LogonInfoBuffer(), userInfo.LogonInfoBuffer_length());
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (!userInfo.exist_UserID())
		return;

	MY_LOG_DEBUG("on_enter_challenge_room  uid:%d", userInfo.get_UserID());
	if (config_manager::GetInstance()->get_room_type() != TYPE_CHALLENGE)
	{
		send_enter_room_fail(sid, 0, 14, "server erro");
		return;
	}

	_tint64 userID = userInfo.get_UserID();
	IUser *pUser = UserManager::GetInstance()->get_user(userID);
	if (pUser)
	{
		_client_cmd.on_offline_enter_room(sid, userID, head, data, size, ex_data);
		return; // 在房间的用户直接断线重回
	}

	cmd_room::EnterRoomReq req;
	req.unpack(data, size);
	if (!req.exist_Code())
	{
		MY_LOG_ERROR("uid(%lld)进入失败ret=%d！", userID, cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT);
		send_enter_room_fail(sid, 0, cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT);
		return;
	}

	CTable *pTable = TableManager::GetInstance()->find_table_from_num((_uint32)req.get_Code());
	if (!pTable)
	{
		MY_LOG_ERROR("uid(%lld)进入失败ret=%d！", userID, cmd_room::ROOM_ENTER_ROOM_EXPIRE_LIMIT);
		send_enter_room_fail(sid, 0, cmd_room::ROOM_ENTER_ROOM_EXPIRE_LIMIT);
		return;
	}

	if (!config_manager::GetInstance()->check_user_vesion(softInfo.get_SoftVer(), softInfo.get_MChannel(), softInfo.get_SChannel()))
	{
		send_enter_room_fail(sid, 0, cmd_room::ROOM_VERSION_NOT_MATCH);
		return;
	}

	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_enter_challenge_room  uid：%llu  tableid:%d", userInfo.get_UserID(), pTable->get_table_id());

	if (pTable->is_full())
	{
		if (ObserveManager::GetInstance()->get_max_observe_num() == 0) // 不允许旁观
		{
			// send_enter_room_fail(sid,0,cmd_room::ROOM_ENTER_ROOM_FULL_LIMIT, "该房间人数已满");
			send_enter_room_fail(sid, 0, cmd_room::ROOM_ENTER_ROOM_FULL_LIMIT,
								 get_error_msg(MSG_ROOM_FULL_USERS, pUser->get_user_country()));

			return;
		}
		if (ObserveManager::GetInstance()->get_observe_limit())
		{
			// send_enter_room_fail(sid,0,cmd_room::ROOM_ENTER_ROOM_OBSERVE_LIMIT, "本桌禁止旁观");
			send_enter_room_fail(sid, 0, cmd_room::ROOM_ENTER_ROOM_OBSERVE_LIMIT,
								 get_error_msg(MSG_ROOM_FORBID_ONLOOKER, pUser->get_user_country()));
			return;
		}
	}

	if (UserManager::GetInstance()->check_tick(userID, req.get_Code()))
	{
		send_enter_room_fail(sid, 0, cmd_room::ROOM_CHECK_TICK);
		return;
	}

	pUser = UserManager::GetInstance()->create_user(userID, 0, logonUserInfo.get_UserType());
	if (pTable->is_full())
	{
		if (pUser)
		{
			pUser->set_user_id(userID);
			ObserveManager::GetInstance()->add_user(pUser);
		}
	}

	if (pUser)
	{
		char game_version[32] = {0};
		__soft_ver_to_str(softInfo.get_SoftVer(), game_version, sizeof(game_version));

		pUser->set_user_version(game_version);
		pUser->set_user_id(userID);
		pUser->set_head_image(logonUserInfo.get_FaceID());
		pUser->set_nick_name(logonUserInfo.get_NickName());
		pUser->set_socket_id(sid);
		pUser->set_client((_uint32)softInfo.get_MChannel());
		pUser->set_user_country(logonUserInfo.get_country_code());

		char *ip = myredis::GetInstance()->get_data_by_uid_for_string(userID, DB_COMM, CUR_LOGON_IP);
		pUser->set_ip(ip);
		pUser->set_room_card(gameinfo.get_RoomCard());
		pUser->set_gold(gameinfo.get_Gold());
		pUser->set_sys_type(softInfo.get_SysType());
		pUser->set_last_gold(gameinfo.get_Gold());
		pUser->set_user_type(logonUserInfo.get_UserType());

		int plat = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_CHANNEL_ID);
		pUser->set_enter_time(time(0));
		pUser->set_write_gameinfo_time(time(0));
		pUser->set_plat_type(plat);
		g_server->bind_user(sid, pUser);
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_enter_challenge_room  bind_user uid：%llu  sid:%d", userID, sid);

		// 如果为旁观玩家，则直接进入房间
		if (ObserveManager::GetInstance()->is_observe_user(userID))
		{
			int ret = CGameFrame::GetInstance()->on_user_enter(pUser, (_uint32)req.get_Code(), true);
			if (ret != 0)
			{
				send_enter_room_fail(sid, req.exist_Code(), ret);
				UserManager::GetInstance()->delete_user(userID);
				return;
			}
			on_enter_room_succ(sid, userID);
			return;
		}

		bool bfree = pTable->get_cost_card() == 0 ? true : false;
		if (bfree)
		{
			int ret = CGameFrame::GetInstance()->on_user_enter(pUser, (_uint32)req.get_Code(), false);
			if (ret != 0)
			{
				const char *msg = get_error_msg(MSG_ROOM_ENTER_LIMIT_GOLD, pUser->get_user_country());
				send_enter_room_fail(sid, req.exist_Code(), ret, msg);
				UserManager::GetInstance()->delete_user(userID);
				return;
			}
			on_enter_room_succ(sid, userID);
		}

		// modify by hsl for AAPay
		else if (pTable->get_pay_mode() == e_PMT_AA && pTable->get_owner_uid() != pUser->get_user_id())
		{
			cmd_dbproxy::CDBProxyExceSection section;
			section.set_SID(sid);
			section.set_UserID(userID);
			section.set_lParamInt(pTable->get_cost_card());
			section.set_wParamInt(cmd_net::SUB_CREATE_ROOM_ASK);
			section.set_lParamBuffer((const char *)ex_data, head->ex_size);
			section.set_wParamBuffer((const char *)data, size);

			switch (config_manager::GetInstance()->get_create_pay_type())
			{
			case PAY_TYPE_DIAMOND:
			{
				if (pTable->get_cost_card() > pUser->get_room_card())
				{
					send_enter_room_fail(sid, req.get_Code(), cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT);
					UserManager::GetInstance()->delete_user(userID);
					return;
				}
				else
				{
					do_sub_room_card_dbproxy(section, pTable->get_room_num(), CREATE_ROOM_MONEY_BILL);
					return;
				}
			}
			break;
			case PAY_TYPE_GOLD:
			{
				int ret = do_sub_room_gold_redis(section, pTable->get_room_num(), CREATE_ROOM_MONEY_BILL);
				if (ret != 0)
				{
					send_enter_room_fail(sid, req.get_Code(), cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT);
					UserManager::GetInstance()->delete_user(userID);
					return;
				}
			}
			break;
			default:
				break;
			}
			if (pTable->get_cost_card() > pUser->get_room_card())
			{
				send_enter_room_fail(sid, req.get_Code(), cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT);
				UserManager::GetInstance()->delete_user(userID);
				return;
			}
			else
			{
				cmd_dbproxy::CDBProxyExceSection section;
				section.set_SID(sid);
				section.set_UserID(userID);
				section.set_lParamInt(pTable->get_cost_card());
				section.set_wParamInt(cmd_net::SUB_ENTER_ROOM_ASK);
				section.set_lParamBuffer((const char *)ex_data, head->ex_size);
				section.set_wParamBuffer((const char *)data, size);

				do_sub_room_card_dbproxy(section, pTable->get_room_num(), CREATE_ROOM_MONEY_BILL);
			}
		}
		else
		{
			do_enter_room(pUser->get_user_id(), sid, req, false);
		}
	}
}

FTYPE(void)
CClientCmd::on_create_challenge_room(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	g_log->write_log(LOG_TYPE_DEBUG, "on_create_challenge_room");

	cmd_gate::CGateUserInfo userInfo;
	cmd_lobby::CLogonSuccessResp logonUserInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;

	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		logonUserInfo.unpack(userInfo.get_LogonInfoBuffer(), userInfo.LogonInfoBuffer_length());
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (!userInfo.exist_UserID())
		return;

	MY_LOG_DEBUG("on_create_challenge_room  uid:%d", userInfo.get_UserID());
	if (config_manager::GetInstance()->get_room_type() != TYPE_CHALLENGE)
	{
		send_enter_room_fail(sid, 0, 15, "server erro");
		return;
	}

	_tint64 userID = userInfo.get_UserID();
	IUser *pUser = UserManager::GetInstance()->get_user(userID);
	if (pUser)
	{
		_client_cmd.on_offline_enter_room(sid, userID, head, data, size, ex_data);
		return; // 断线重回
	}

	cmd_room::EnterRoomReq req;
	req.unpack(data, size);

	if (!config_manager::GetInstance()->check_user_vesion(softInfo.get_SoftVer(), softInfo.get_MChannel(), softInfo.get_SChannel(), softInfo.get_SysType()))
	{
		CRoomRouteChallenge::send_challenge_user_leave(userID, req.get_Code(), 0, 1);
		send_enter_room_fail(sid, 0, cmd_room::ROOM_VERSION_NOT_MATCH);
		g_server->close(sid);
		return;
	}

	if (!req.exist_Code())
	{
		MY_LOG_ERROR("uid(%lld)进入失败ret=%d！", userID, cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT);
		CRoomRouteChallenge::send_challenge_user_leave(userID, req.get_Code(), 0, 1);
		send_enter_room_fail(sid, 0, cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT);
		g_server->close(sid);

		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_create_challenge_room closeA uid：%llu  sid:%d", userID, sid);
		return;
	}

	if (!req.exist_Rule())
	{
		CRoomRouteChallenge::send_challenge_user_leave(userID, req.get_Code(), 0, 1);
		send_enter_room_fail(sid, 0, cmd_room::ROOM_ENTER_ROOM_CFG_LIMIT);
		g_server->close(sid);

		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_create_challenge_room closeB uid：%llu  sid:%d", userID, sid);
		return;
	}

	UidRedisInfo uid_info;
	myredis::GetInstance()->get_all_uid_info(userID, uid_info);
	_tint64 gold = uid_info.gold;
	MY_LOG_DEBUG("on_create_challenge_room uid[%d] gold=%lld", userID, gold);

	pUser = UserManager::GetInstance()->create_user(userID, 0, logonUserInfo.get_UserType());
	if (pUser)
	{
		char game_version[32] = {0};
		__soft_ver_to_str(softInfo.get_SoftVer(), game_version, sizeof(game_version));

		pUser->set_user_version(game_version);
		pUser->set_user_id(userID);
		pUser->set_head_image(logonUserInfo.get_FaceID());
		pUser->set_nick_name(logonUserInfo.get_NickName());
		pUser->set_socket_id(sid);
		pUser->set_client((_uint32)softInfo.get_MChannel());
		pUser->set_user_country(logonUserInfo.get_country_code());

		char *ip = myredis::GetInstance()->get_data_by_uid_for_string(userID, DB_COMM, CUR_LOGON_IP);
		pUser->set_ip(ip);
		pUser->set_room_card(gameinfo.get_RoomCard());
		pUser->set_gold(gold);
		pUser->set_sys_type(softInfo.get_SysType());
		pUser->set_last_gold(gold);
		pUser->set_enter_time(time(0));
		pUser->set_write_gameinfo_time(time(0));
		pUser->set_user_type(logonUserInfo.get_UserType());
		g_server->bind_user(sid, pUser);
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_create_challenge_room  bind_user uid：%llu  sid:%d", userID, sid);

		CTable *pTable = NULL;
		int ret = 0;

		do
		{
			// 生成桌子
			ret = CGameFrame::GetInstance()->on_user_create(pUser, (_uint32)req.get_Code(), (void *)req.get_Rule(), req.Rule_length());
			if (ret != 0)
			{
				break;
			}

			// 找到桌子
			pTable = TableManager::GetInstance()->find_table_from_num((_uint32)req.get_Code());
			if (!pTable)
			{
				ret = cmd_room::ROOM_TABLE_NOT_ENOUGH;
				break;
			}

			g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_create_challenge_room  uid：%u  tableid:%d", pUser->get_user_id(), pTable->get_table_id());

			// add by hsl on 03/23/2017 桌子创建时间
			pTable->set_create_time();
			MY_LOG_DEBUG("set_create_time { %lld }", pTable->get_create_time());

			// 免费
			bool bfree = (pTable->get_cost_card() == 0);
			if (bfree)
			{
				if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
				{
					ret = pTable->on_user_enter(pUser, false, false, true);
				}
				else
				{
					ret = pTable->on_user_enter(pUser);
				}
				break;
			}
			else // 付费
			{
				cmd_dbproxy::CDBProxyExceSection section;
				section.set_SID(sid);
				section.set_UserID(userID);
				section.set_lParamInt(pTable->get_cost_card());
				section.set_wParamInt(cmd_net::SUB_CREATE_ROOM_ASK);
				section.set_lParamBuffer((const char *)ex_data, head->ex_size);
				section.set_wParamBuffer((const char *)data, size);

				switch (config_manager::GetInstance()->get_create_pay_type())
				{
				case PAY_TYPE_DIAMOND:
				{
					if (pTable->get_cost_card() > pUser->get_room_card())
					{
						g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_create_challenge_room  uid：%u  tableid:%d cost_card:%d room_card:%llu",
										 pUser->get_user_id(), pTable->get_table_id(), pTable->get_cost_card(), pUser->get_room_card());
						ret = cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT;
						break;
					}
					else
					{
						do_sub_room_card_dbproxy(section, pTable->get_room_num(), CREATE_ROOM_MONEY_BILL);
						return;
					}

					if (pTable->get_cost_card() > pUser->get_room_card())
					{
						g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_create_challenge_room  uid：%u  tableid:%d cost_card:%d room_card:%llu", pUser->get_user_id(), pTable->get_table_id(), pTable->get_cost_card(), pUser->get_room_card());
						ret = cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT;
						break;
					}
					else
					{
						cmd_dbproxy::CDBProxyExceSection section;
						section.set_SID(sid);
						section.set_UserID(userID);
						section.set_lParamInt(pTable->get_cost_card());
						section.set_wParamInt(cmd_net::SUB_CREATE_ROOM_ASK);
						section.set_lParamBuffer((const char *)ex_data, head->ex_size);
						section.set_wParamBuffer((const char *)data, size);

						do_sub_room_card_dbproxy(section, pTable->get_room_num(), CREATE_ROOM_MONEY_BILL);
						return;
					}
				}
				break;
				case PAY_TYPE_GOLD:
				{
					ret = do_sub_room_gold_redis(section, pTable->get_room_num(), CREATE_ROOM_MONEY_BILL);
				}
				break;
				default:
					break;
				}
			}

		} while (false);

		if (ret != 0)
		{
			MY_LOG_DEBUG("send_enter_room_fail(%lld)delete_table！", req.get_Code());

			const char *msg = get_error_msg(MSG_ROOM_CREATE_LIMIT_GOLD, pUser->get_user_country());
			send_enter_room_fail(sid, req.get_Code(), ret, msg);
			CRoomRouteChallenge::send_challenge_user_leave(userID, req.get_Code(), 0, 1);
			TableManager::GetInstance()->delete_table(pTable);
			UserManager::GetInstance()->delete_user(userID, true);
		}
		else
		{
			on_enter_room_succ(sid, userID);
		}

		_room_route_challenge.send_challenge_create_result(userID, req.get_Code(), ret, pTable);
	}
}

FTYPE(bool)
CClientCmd::create_user_info(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data, IUser *RoomUserInfo)
{
	cmd_gate::CGateUserInfo userInfo;
	cmd_lobby::CLogonSuccessResp logonUserInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;
	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		logonUserInfo.unpack(userInfo.get_LogonInfoBuffer(), userInfo.LogonInfoBuffer_length());
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (RoomUserInfo)
	{
		char game_version[32] = {0};
		__soft_ver_to_str(softInfo.get_SoftVer(), game_version, sizeof(game_version));

		RoomUserInfo->set_user_version(game_version);
		RoomUserInfo->set_user_id(userInfo.get_UserID());
		RoomUserInfo->set_head_image(logonUserInfo.get_FaceID());
		RoomUserInfo->set_nick_name(logonUserInfo.get_NickName());
		RoomUserInfo->set_socket_id(sid);
		RoomUserInfo->set_client((_uint32)softInfo.get_MChannel());
		RoomUserInfo->set_user_type(logonUserInfo.get_UserType());

		char *ip = myredis::GetInstance()->get_data_by_uid_for_string(userInfo.get_UserID(), DB_COMM, CUR_LOGON_IP);
		RoomUserInfo->set_ip(ip);
		RoomUserInfo->set_room_card(gameinfo.get_RoomCard());
		RoomUserInfo->set_sys_type(softInfo.get_SysType());

		_uint64 now_gold = myredis::GetInstance()->get_data_by_uid(userInfo.get_UserID(), DB_COMM, CUR_GOLD);
		RoomUserInfo->set_gold(now_gold);
		RoomUserInfo->set_last_gold(now_gold);
		RoomUserInfo->set_enter_time(time(0));
		RoomUserInfo->set_write_gameinfo_time(time(0));
		g_server->bind_user(sid, RoomUserInfo);
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd create_user_info  bind_user uid:%llu  sid:%d usertype:%d",
						 userInfo.get_UserID(), sid, logonUserInfo.get_UserType());

		return true;
	}
	return false;
}

FTYPE(void)
CClientCmd::on_offline_enter_room(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	g_log->write_log(LOG_TYPE_DEBUG, "on_offline_enter_room - sid=%d", sid);
	cmd_gate::CGateUserInfo userInfo;
	cmd_lobby::CLogonSuccessResp logonUserInfo;
	cmd_sys::CSysSoftInfo softInfo;
	cmd_gate::CUserGameInfo gameinfo;

	if (head->ex_type == SVREXTYPE_USERINFO)
	{
		userInfo.unpack(ex_data, head->ex_size);
		logonUserInfo.unpack(userInfo.get_LogonInfoBuffer(), userInfo.LogonInfoBuffer_length());
		softInfo.unpack(userInfo.get_SoftInfoBuffer(), userInfo.SoftInfoBuffer_length());
		gameinfo.unpack(userInfo.get_GameInfoBuffer(), userInfo.GameInfoBuffer_length());
	}

	if (!userInfo.exist_UserID())
	{
		send_enter_room_fail(sid, 0, 16, "server erro");
		return;
	}

	_tint64 userID = userInfo.get_UserID();
	IUser *pUser = UserManager::GetInstance()->get_user(userID);
	if (!pUser)
	{
		// 百人场防卡人，只用在百人场
		// if (config_manager::GetInstance()->get_room_type() != TYPE_HUNDRED)
		//{
		if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
			pair_manager::GetInstance()->delete_pair(userID);

		// 提示用户已经离开房间
		send_enter_room_fail(sid, 0, 17, "server erro");
		send_has_leave_room(sid, userID, false);
		g_server->close(sid);
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room close uid：%llu  sid:%d", userID, sid);
		return;
		//}

		/*int node_id = TableManager::GetInstance()->get_rand_hundred_node();
		if (node_id <= 0)
		{
			if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
				pair_manager::GetInstance()->delete_pair(userID);

			//提示用户已经离开房间
			send_enter_room_fail(sid, 0, 1, "server erro");
			send_has_leave_room(sid, userID, false);
			g_server->close(sid);
			g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room close uid：%llu  sid:%d", userID, sid);
			return;
		}

		//创建用户信息是否成功
		pUser = UserManager::GetInstance()->create_user(userInfo.get_UserID(), node_id, logonUserInfo.get_UserType());
		bool is_create = create_user_info(sid, userid, head, data, size, ex_data, pUser);
		if (!pUser || !is_create)
		{
			if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
				pair_manager::GetInstance()->delete_pair(userID);

			//提示用户已经离开房间
			send_enter_room_fail(sid, 0, 1, "server erro");
			send_has_leave_room(sid, userID, false);
			g_server->close(sid);
			g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room close uid：%llu  sid:%d", userID, sid);
			return;
		}
		else
		{
			//重新进入房间，和网关同步数据
			int node_id = pUser->get_node_id();
			CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
			if (!ptable)
			{
				send_enter_room_fail(sid, 0, 1, "server erro");
				UserManager::GetInstance()->delete_user(userID, true);
				g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room close uid：%llu  sid:%d", userID, sid);
				return;
			}
			if (ptable->on_user_enter(pUser) == 0)
			{
				//通知网关用户进入
				//通知约战服用户进入
				on_enter_room_succ(sid, userID);
				CRoomRouteChallenge::send_web_user_enter(userID);
				g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room  uid：%llu", userInfo.get_UserID());
			}
			else
			{
				send_enter_room_fail(sid, 0, 1, "server erro");
				UserManager::GetInstance()->delete_user(userID, true);
				g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room close uid：%llu  sid:%d", userID, sid);
			}
			return;
		}*/
	}
	g_log->write_log(LOG_TYPE_DEBUG, "on_offline_enter_room - sid=%d  uid:%llu", sid, pUser->get_user_id());

	pUser->set_room_card(gameinfo.get_RoomCard());
	pUser->set_client((_uint32)softInfo.get_MChannel());

	// 如果有预扣金额，需要给玩家金币加上
	stGoldNodeInfo *nodeinfo = config_manager::GetInstance()->get_node_info(pUser->get_node_id());
	int api_type = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_API_MODE);
	_uint64 with_gold = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_WHTH_GOLD);
	if (api_type == MODE_SINGLE && nodeinfo->with_hold != 0 && with_gold != 0)
	{
		myredis::GetInstance()->update_data_by_uid(userID, DB_COMM, CUR_GOLD, with_gold);
		_uint64 now_gold = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_GOLD);
		pUser->set_gold(now_gold);
	}

	if (!pUser->is_offline())
	{
		socket_id oldSid = pUser->get_socket_id();
		if (oldSid != sid)
			regain_logon(sid, userID);
	}

	pUser->set_socket_id(sid);
	char *ip = myredis::GetInstance()->get_data_by_uid_for_string(userID, DB_COMM, CUR_LOGON_IP);
	pUser->set_ip(ip);

	///* 金币 */
	// if (logonUserInfo.exist_Gold())
	//{
	//     _uint64 llGold = logonUserInfo.get_Gold();
	//     pUser->set_opt(cmd_game_comm::OPT_GOLD, 0, 0, &llGold, sizeof(_uint64));
	// }

	g_server->bind_user(sid, pUser);
	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_offline_enter_room  bind_user uid：%llu  sid:%d", userID, sid);

	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int node_id = pUser->get_node_id();
		CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			send_enter_web_room_result(sid, 0);
			ptable->on_user_reconn(pUser);
			on_enter_room_succ(sid, userID);
		}
		else
		{
			send_enter_web_room_result(sid, 1);
			send_enter_room_fail(sid, 0, 20, "server erro");
		}
	}
	else
	{
		// 如果为旁观玩家，则直接进入房间
		int is_observe = false;
		if (ObserveManager::GetInstance()->is_observe_user(userID))
		{
			is_observe = true;
		}

		if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
		{
			if (pUser->get_table_id() == INVALID_TABLE)
			{
				// 普通房没有桌子直接换桌(如果是机器人则加入机器人队列)
				if (pUser->get_user_type() == ROBOT_TYPE)
				{
					if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
					{
						if (!pair_manager::GetInstance()->check_in_pair(userID))
							robot_manager::GetInstance()->add_robot(pUser->get_user_id());
					}
					else
					{
						robot_manager::GetInstance()->add_robot(pUser->get_user_id());
					}
					on_enter_room_succ(sid, userID);
				}
				else
				{
					if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
					{
						// 配桌模式重新放回队列
						int add_ret = pair_manager::GetInstance()->add_pair(userID, pUser->get_node_id());
						on_enter_room_succ(sid, userID);
						send_pair_table_result(sid, add_ret, pUser->get_user_country());
					}
					else
					{
						on_enter_room_succ(sid, userID);
						CGameFrame::GetInstance()->change_table(userID);
					}
				}
			}
			else
			{
				int ret = CGameFrame::GetInstance()->on_user_enter(userID, is_observe);
				if (ret != 0)
				{
					send_enter_room_fail(sid, 0, ret);
					CRoomRouteChallenge::send_nomal_user_leave(userID);
					UserManager::GetInstance()->delete_user(userID);
					return;
				}
				on_enter_room_succ(sid, userID);
			}
		}
		else
		{
			int ret = CGameFrame::GetInstance()->on_user_enter(userID, is_observe);
			if (ret != 0)
			{
				send_enter_room_fail(sid, 0, ret);
				CRoomRouteChallenge::send_nomal_user_leave(userID);
				UserManager::GetInstance()->delete_user(userID);
				return;
			}
			on_enter_room_succ(sid, userID);
		}
	}
}

FTYPE(void)
CClientCmd::send_enter_room_fail(socket_id sid, int room_code, int ret, const char *msg)
{
	cmd_room::EnterRoomFail fail;
	fail.set_Ret(ret);
	if (room_code)
		fail.set_Code(room_code);

	// const char* msg = enter_fail_to_str(ret);
	if (msg)
		fail.set_Msg(msg, 0);
	g_server->send(sid, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_ROOM_FAIL, &fail);
	g_log->write_log(LOG_TYPE_WARNING, "send_enter_room_fail erro. sid=%d roomcode:%d ret:%d msg:%s", sid, room_code, ret, msg);
}

FTYPE(void)
CClientCmd::send_has_leave_room(socket_id sid, _tint64 userID, bool is_offline)
{
	g_log->write_log(LOG_TYPE_PRINT, "send user has leave room to gate. sid=%d uid=%ld", sid, userID);

	tagUserAction userAction(userID, sid);
	userAction.is_offlne = is_offline;
	__post_action(ACTION_USER_LEAVE, &userAction, sizeof(userAction));
}

FTYPE(void)
CClientCmd::on_enter_room_succ(socket_id sid, _tint64 userID)
{
	tagUserAction userAction(userID);
	__post_action(ACTION_USER_LOGON_SUCCEED, &userAction, sizeof(userAction));
}

FTYPE(void)
CClientCmd::regain_logon(socket_id sid, _tint64 userID)
{
	IUser *pUser = UserManager::GetInstance()->get_user(userID);
	if (pUser)
	{
		socket_id oldSid = pUser->get_socket_id();
		cmd_sys::CSysBombMsg bombMsg;
		bombMsg.set_Type(0);
		bombMsg.set_Attribute(0);
		// bombMsg.set_Title("提示", strlen("提示"));
		bombMsg.set_Title(get_error_msg(MSG_TYPE_TIPS, pUser->get_user_country()), 0);

		// const char * pMsg = "您已经在其它地方登录，请退出登录！";
		const char *pMsg = get_error_msg(MSG_USER_LOGON_IN_OTHER_AREA, pUser->get_user_country());
		bombMsg.set_Msg(pMsg, strlen(pMsg));
		g_server->send(oldSid, cmd_net::CMD_SYS, cmd_net::CMD_SYS_BOMB_MSG, &bombMsg);
		g_server->bind_user(oldSid, NULL);
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd regain_logon  bind_user uid：%llu  nsid:%d osid:%d", userID, sid, oldSid);

		g_server->close(oldSid);
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd regain_logon close uid：%llu  sid:%d", userID, oldSid);
	}
}

FTYPE(void)
CClientCmd::on_user_disconnect(void *obj, enAction action, const void *pdata, int size)
{
	const tagUserAction *pUserAction = (tagUserAction *)pdata;

	IUser *pUser = UserManager::GetInstance()->get_user(pUserAction->userID);
	if (pUser)
	{
		CGameFrame::GetInstance()->on_user_offline(pUser);
	}
}

FTYPE(void)
CClientCmd::on_game_frame_message(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *ex_data)
{
	IUser *user = UserManager::GetInstance()->get_user(userid);
	if (!head || !user)
	{
		MY_LOG_ERROR("head or user is null. user=0x%p head=0x%p size=%d", user, head, size);
		return;
	}

	// 刷新货币
	_tint64 now_gold = myredis::GetInstance()->get_gold_by_uid(userid);
	if (now_gold != -1)
		user->set_gold(now_gold);

	MY_LOG_DEBUG("game frame recv message. uid=%u mcmd=%d scmd=%d", (_uint32)user->get_user_id(), head->mcmd, head->scmd);
	bool ret = CGameFrame::GetInstance()->on_recv(head->mcmd, head->scmd, data, size, user);
	if (!ret)
	{
		MY_LOG_ERROR("game frame recv data fail.");
	}
}

// modify by hsl on 03/14/2017
FTYPE(int)
CClientCmd::do_enter_room(_tint64 n64UserID, _tint64 n64SID, cmd_room::EnterRoomReq &reqEnterRoom, bool bCreate)
{
	g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd do_enter_room - uid:%llu", n64UserID);

	int ret = cmd_room::ROOM_NUM_NOT_FIND;
	IUser *pUser = NULL;
	CTable *pTable = NULL;

	do
	{
		pTable = TableManager::GetInstance()->find_table_from_num(reqEnterRoom.get_Code());
		if (!pTable)
		{
			ret = cmd_room::ROOM_NUM_NOT_FIND; // 属于异常
			break;
		}

		pUser = UserManager::GetInstance()->get_user(n64UserID);
		if (!pUser)
		{
			ret = cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT; // 扣费过程中掉线
			break;
		}

		ret = pTable->on_user_enter(pUser);

	} while (false);

	if (bCreate)
	{
		_room_route_challenge.send_challenge_create_result(n64UserID, reqEnterRoom.get_Code(), ret, pTable);
	}

	if (ret == 0)
	{
		MY_LOG_DEBUG("uid(%lld)进入成功ret=%d！", n64UserID, ret);
		on_enter_room_succ(n64SID, n64UserID);
	}
	else
	{
		send_enter_room_fail(n64SID, reqEnterRoom.get_Code(), ret);
	}

	return ret;
}

// modify by hsl on 03/14/2017
FTYPE(void)
CClientCmd::do_update_user_vcoin(_tint64 n64UserID, _tint64 n64CurCard, _tint64 n64AddCoin,
								 cmd_sys::HEALTHE_CHANGE_TYPE change_type, cmd_sys::GAME_MONEY_TYPE money_type)
{
	MY_LOG_DEBUG("uid(%lld)更新房卡 cur_card=%lld！", n64UserID, n64CurCard);

	IUser *puser = UserManager::GetInstance()->get_user(n64UserID);
	if (puser && puser->get_user_type() != ROBOT_TYPE)
	{
		// 通知客户端更新房卡
		cmd_sys::CSysUpdateUserWealth update_user_vcoin;
		update_user_vcoin.set_UserID(n64UserID);
		update_user_vcoin.set_AddCoin(n64AddCoin);
		update_user_vcoin.set_CurCoin(n64CurCard);
		update_user_vcoin.set_Reason(change_type);
		update_user_vcoin.set_Type(money_type); // todo
		g_server->send_leave_user(n64UserID, cmd_net::CMD_SYS, cmd_net::CMD_SYS_UPDATE_USER_YUANBAO, &update_user_vcoin);
	}

	cmd_gate::CUserGameInfo gameinfo;
	gameinfo.set_UserID(n64UserID);
	switch (money_type)
	{
	case GAME_ROOM_CARD:
		gameinfo.set_RoomCard(n64CurCard);
		break;
	case GAME_ROOM_GOLD:
		gameinfo.set_Gold(n64CurCard);
		break;
	default:
		break;
	}
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_UPDATE_USER_GAME_INFO, &gameinfo);
}

FTYPE(void)
CClientCmd::do_update_user_to_server(_tint64 n64UserID, _tint64 n64CurCard, cmd_sys::GAME_MONEY_TYPE type)
{
	cmd_gate::CUserGameInfo gameinfo;
	gameinfo.set_UserID(n64UserID);
	switch (type)
	{
	case cmd_sys::GAME_ROOM_CARD:
		gameinfo.set_RoomCard(n64CurCard);
		break;
	case cmd_sys::GAME_ROOM_GOLD:
		gameinfo.set_Gold(n64CurCard);
		break;
	default:
		break;
	}
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_GATE, cmd_net_svr::CMD_ROUTE_GATE_UPDATE_USER_GAME_INFO, &gameinfo);
}

FTYPE(void)
CClientCmd::do_add_room_gold_redis(_tint64 userID, int room_num, int room_gold, int bill_type)
{
	IUser *user = UserManager::GetInstance()->get_user(userID);
	if (!user)
		return;

	// 更新内存
	user->update_gold(room_gold, true, false);
	int now_gold = user->get_gold();
	bool bstatistics = user->get_user_type() != ROBOT_TYPE ? true : false;
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		user->get_client_id(),
		room_num,
		user->get_version(),
		userID,
		now_gold,
		room_gold,
		now_gold - room_gold,
		(GOLD_BILL_TYPE)bill_type,
		0,
		user->get_user_type(),
		bstatistics);
}

FTYPE(int)
CClientCmd::do_sub_room_gold_redis(cmd_dbproxy::CDBProxyExceSection &section, int room_num, int bill_type)
{
	IUser *user = UserManager::GetInstance()->get_user(section.get_UserID());
	if (!user)
	{
		g_server->close(section.get_SID());
		return 1;
	}

	if (user->get_gold() < section.get_lParamInt())
	{
		return 1;
	}

	// 更新内存
	user->update_gold(-section.get_lParamInt(), true, false);
	int now_gold = user->get_gold();
	bool bstatistics = user->get_user_type() != ROBOT_TYPE ? true : false;
	BILL->write_gold_bill(
		config_manager::GetInstance()->get_game_id(),
		user->get_client_id(),
		room_num,
		user->get_version(),
		section.get_SID(),
		now_gold,
		section.get_lParamInt(),
		now_gold - section.get_lParamInt(),
		(GOLD_BILL_TYPE)bill_type,
		0,
		user->get_user_type(),
		bstatistics);

	int ret = 0;
	if (section.get_wParamInt() == cmd_net::SUB_CREATE_ROOM_ASK || section.get_wParamInt() == cmd_net::SUB_ENTER_ROOM_ASK)
	{
		cmd_room::EnterRoomReq req;
		req.unpack(section.get_wParamBuffer(), section.wParamBuffer_length());

		bool bCreate = (section.get_wParamInt() == cmd_net::SUB_CREATE_ROOM_ASK);

		ret = do_enter_room(section.get_UserID(), section.get_SID(), req, bCreate);
		if (ret != 0)
		{
			// 退还
			_client_cmd.do_add_room_gold_redis(section.get_UserID(), req.get_Code(), -section.get_lParamInt(), ROOM_RETURN_MONEY_BILL);
			CTable *pTable = TableManager::GetInstance()->find_table_from_num(req.get_Code());
			;
			if (bCreate)
			{
				MY_LOG_ERROR("uid(%lld)delete_table！", section.get_UserID());
				TableManager::GetInstance()->delete_table(pTable);
			}
		}
	}

	if (ret == 0)
	{
		do_update_user_vcoin(section.get_UserID(), now_gold, -section.get_lParamInt(), cmd_sys::RETURN_HEALTH_CHANGE, cmd_sys::GAME_ROOM_GOLD);
	}
	else
	{
		// 异步失败返回 所有不删除用户 等网络连接断开再清除用户信息
		g_server->close(section.get_SID());
	}
	return ret;
}

FTYPE(void)
CClientCmd::do_sub_room_card_dbproxy(cmd_dbproxy::CDBProxyExceSection &section, int room_num, int bill_type)
{
	cmd_gate::CGateUserInfo userinfo;
	userinfo.unpack(section.get_lParamBuffer(), section.lParamBuffer_length());
	cmd_sys::CSysSoftInfo softinfo;
	softinfo.unpack(userinfo.get_SoftInfoBuffer(), userinfo.SoftInfoBuffer_length());
	char game_version[32] = {0};
	__soft_ver_to_str(softinfo.get_SoftVer(), game_version, sizeof(game_version));

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	//_tint64 expiryTime = 0;
	// g_com->_time_t_stoi("2037-12-31", &expiryTime);

	json_req_value["uid"] = Json::Value(section.get_UserID());
	json_req_value["post_time"] = Json::Value(g_com->_get_time32()); // unix时间戳
	json_req_value["type_id"] = Json::Value(bill_type);				 //
	json_req_value["type_id_sub"] = Json::Value(0);					 // 没有特别要求填0
	json_req_value["game_id"] = Json::Value(userinfo.get_GameID());
	// json_req_value["expiry_time"] = Json::Value(expiryTime);
	json_req_value["quantity"] = Json::Value(section.get_lParamInt());				   // 数量
	json_req_value["room_id"] = Json::Value(CGameFrame::GetInstance()->get_room_id()); // 没有或者取不到填0
	json_req_value["version"] = Json::Value(game_version);
	json_req_value["kind_id"] = Json::Value(0);

	std::string json_req_data = json_writer.write(json_req_value);
	MY_LOG_DEBUG("更新用户(%lld)扣除房卡db请求:%s,json_req_cmd:%s,json_req_data:%s", section.get_UserID(), __FUNCTION__, "'mjq_del_yuanbao", json_req_data.c_str());
	g_dbproxy_namager->post_exec(0, on_sub_room_card_dbproxy, section.get_UserID(), "lobby_del_yuanbao", json_req_data.c_str(), section);
}

FTYPE(void)
CClientCmd::do_add_room_card_dbproxy(_tint64 userID, int room_num, int room_card, int bill_type)
{
	cmd_dbproxy::CDBProxyExceSection section;
	section.set_UserID(userID);
	section.set_lParamInt(room_card);

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	//_tint64 expiryTime = 0;
	// g_com->_time_t_stoi("2037-12-31", &expiryTime);

	json_req_value["uid"] = Json::Value(section.get_UserID());
	json_req_value["post_time"] = Json::Value(g_com->_get_time32()); // unix时间戳
	json_req_value["type_id"] = Json::Value(bill_type);				 // ROOM_RETURN_YUANBALL_BILL);//
	json_req_value["type_id_sub"] = Json::Value(room_num);			 // 没有特别要求填0
	json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
	// json_req_value["expiry_time"] = Json::Value(expiryTime);
	json_req_value["quantity"] = Json::Value(section.get_lParamInt());				   // 数量
	json_req_value["room_id"] = Json::Value(CGameFrame::GetInstance()->get_room_id()); // 没有或者取不到填0
	json_req_value["version"] = Json::Value(0);
	json_req_value["kind_id"] = Json::Value(0);
	std::string json_req_data = json_writer.write(json_req_value);
	MY_LOG_DEBUG("更新用户(%lld)返还房卡db请求:%s,json_req_cmd:%s,json_req_data:%s", section.get_UserID(), __FUNCTION__, "'mjq_add_yuanbao", json_req_data.c_str());
	g_dbproxy_namager->post_exec(0, on_add_room_card_dbproxy, section.get_UserID(), "lobby_add_yuanbao", json_req_data.c_str(), section);
}

FTYPE(void)
CClientCmd::on_add_room_card_dbproxy(void *obj, const char *data_json, int size, cmd_dbproxy::CDBProxyExceSection &section)
{
	if (data_json)
	{
		MY_LOG_DEBUG("用户(%lld)添加房卡db返回:%s", section.get_UserID(), data_json);
	}
	if (!section.get_UserID())
		return;

	Json::Value json_value;
	if (!get_dbproxy_resp_value(data_json, size, json_value, NULL))
	{
		MY_LOG_ERROR("uid(%lld)添加房卡失败！", section.get_UserID());
		return;
	}
	else
	{
		_uint64 cur_coin = json_value["recordset0"][0]["quantity"].asInt64();

		/*
		//通知客户端更新房卡
		cmd_sys::CSysUpdateUserWealth update_user_vcoin;
		update_user_vcoin.set_UserID(section.get_UserID());
		update_user_vcoin.set_AddCoin(section.get_lParamInt());
		update_user_vcoin.set_CurCoin(cur_coin);
		update_user_vcoin.set_Reason(cmd_sys::RETURN_HEALTH_CHANGE);
		g_server->send_leave_user(section.get_UserID(),cmd_net::CMD_SYS,cmd_net::CMD_SYS_UPDATE_USER_YUANBAO,&update_user_vcoin);

		cmd_gate::CUserGameInfo gameinfo;
		gameinfo.set_UserID(section.get_UserID());
		gameinfo.set_RoomCard(cur_coin);
		g_server->send_route_server_random(cmd_net::CMD_ROUTE_GATE,cmd_net_svr::CMD_ROUTE_GATE_UPDATE_USER_GAME_INFO,&gameinfo);
		*/
		do_update_user_vcoin(section.get_UserID(), cur_coin, section.get_lParamInt(), cmd_sys::RETURN_HEALTH_CHANGE, cmd_sys::GAME_ROOM_CARD);

		IUser *pUser = UserManager::GetInstance()->get_user(section.get_UserID());
		if (pUser)
			pUser->set_room_card(cur_coin);
	}
}

FTYPE(void)
CClientCmd::on_sub_room_card_dbproxy(void *obj, const char *data_json, int size, cmd_dbproxy::CDBProxyExceSection &section)
{
	if (data_json)
	{
		MY_LOG_DEBUG("用户(%lld)扣除房卡db返回:%s", section.get_UserID(), data_json);
	}
	if (!section.get_UserID())
		return;

	int ret = 0;
	int cur_card = 0;

	Json::Value json_value;
	if (!get_dbproxy_resp_value(data_json, size, json_value, NULL))
	{
		MY_LOG_ERROR("uid(%lld)扣房卡失败！", section.get_UserID());
		ret = cmd_room::ROOM_ENTER_ROOM_SERVER_LIMIT;
	}
	else
	{
		cur_card = json_value["recordset0"][0]["quantity"].asInt64();
	}
	// 约战场
	if ((section.get_wParamInt() == cmd_net::SUB_CREATE_ROOM_ASK || section.get_wParamInt() == cmd_net::SUB_ENTER_ROOM_ASK) && !ret)
	{
		cmd_room::EnterRoomReq req;
		req.unpack(section.get_wParamBuffer(), section.wParamBuffer_length());

		bool bCreate = (section.get_wParamInt() == cmd_net::SUB_CREATE_ROOM_ASK);

		ret = do_enter_room(section.get_UserID(), section.get_SID(), req, bCreate);
		if (ret == 0)
		{
			IUser *pUser = UserManager::GetInstance()->get_user(section.get_UserID());
			if (pUser)
				pUser->set_room_card(cur_card);
		}
		else
		{
			MY_LOG_ERROR("uid(%lld)进入失败ret=%d！", section.get_UserID(), ret);
			if (ret != cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT)
			{
				// 退还
				_client_cmd.do_add_room_card_dbproxy(section.get_UserID(), req.get_Code(), section.get_lParamInt(), ROOM_RETURN_MONEY_BILL);
			}

			CTable *pTable = TableManager::GetInstance()->find_table_from_num(req.get_Code());
			;
			if (bCreate)
			{
				MY_LOG_ERROR("uid(%lld)delete_table！", section.get_UserID());
				TableManager::GetInstance()->delete_table(pTable);
			}
		}
	}

	if (ret == 0)
	{
		/*MY_LOG_DEBUG("uid(%lld)更新房卡 cur_card=%d！",section.get_UserID(),cur_card);
		//通知客户端更新房卡
		cmd_sys::CSysUpdateUserWealth update_user_vcoin;
		update_user_vcoin.set_UserID(section.get_UserID());
		update_user_vcoin.set_AddCoin(section.get_lParamInt());
		update_user_vcoin.set_CurCoin(cur_card);
		update_user_vcoin.set_Reason(cmd_sys::RETURN_HEALTH_CHANGE);
		g_server->send_leave_user(section.get_UserID(),cmd_net::CMD_SYS,cmd_net::CMD_SYS_UPDATE_USER_YUANBAO,&update_user_vcoin);

		cmd_gate::CUserGameInfo gameinfo;
		gameinfo.set_UserID(section.get_UserID());
		gameinfo.set_RoomCard(cur_card);
		g_server->send_route_server_random(cmd_net::CMD_ROUTE_GATE,cmd_net_svr::CMD_ROUTE_GATE_UPDATE_USER_GAME_INFO,&gameinfo);
		*/
		do_update_user_vcoin(section.get_UserID(), cur_card, section.get_lParamInt(), cmd_sys::RETURN_HEALTH_CHANGE, cmd_sys::GAME_ROOM_CARD);
	}
	else
	{
		g_server->close(section.get_SID()); // 异步失败返回 所有不删除用户 等网络连接断开再清除用户信息
		g_log->write_log(LOG_TYPE_DEBUG, "CClientCmd on_sub_room_card_dbproxy close sid:%d", section.get_SID());
	}
}

/*FTYPE(void) CClientCmd::do_sub_room_card_dbproxy(cmd_dbproxy::CDBProxyExceSection &section,int room_num,int bill_type)
{
	bool ret =  myredis::GetInstance()->update_data_by_uid(section.get_UserID(), DB_COMM, CUR_DIAMOND, -section.get_lParamInt());

	if( (section.get_wParamInt() == cmd_net::SUB_CREATE_ROOM_ASK
		|| section.get_wParamInt() == cmd_net::SUB_ENTER_ROOM_ASK)
		&& ret )
	{
		_tint64 cur_card = myredis::GetInstance()->get_data_by_uid(section.get_UserID(), DB_COMM, CUR_DIAMOND);

		cmd_room::EnterRoomReq req;
		req.unpack(section.get_wParamBuffer(),section.wParamBuffer_length());

		bool bCreate = (section.get_wParamInt() == cmd_net::SUB_CREATE_ROOM_ASK);

		int rete = do_enter_room(section.get_UserID(), section.get_SID(), req, bCreate);
		if(rete == 0)
		{
			IUser* pUser = UserManager::GetInstance()->get_user(section.get_UserID());
			if(pUser)
				pUser->set_room_card(cur_card);
		}
		else
		{
			MY_LOG_ERROR("uid(%lld)进入失败ret=%d！",section.get_UserID(),ret);
			if(rete != cmd_room::ROOM_ENTER_ROOM_CARD_LIMIT)
			{
				//退还
				_client_cmd.do_add_room_card_dbproxy(section.get_UserID(),req.get_Code(),section.get_lParamInt(), ROOM_RETURN_MONEY_BILL);
			}

			CTable* pTable = TableManager::GetInstance()->find_table_from_num(req.get_Code());;
			if(bCreate)
			{
				MY_LOG_ERROR("uid(%lld)delete_table！",section.get_UserID());
				TableManager::GetInstance()->delete_table(pTable);
			}
		}
	}

	if(ret)
	{
		_tint64 cur_card = myredis::GetInstance()->get_data_by_uid(section.get_UserID(), DB_COMM, CUR_DIAMOND);
		do_update_user_vcoin(section.get_UserID(), cur_card, section.get_lParamInt());

		//钻石流水
		BILL->write_diamond_bill(section.get_UserID(), cur_card, -section.get_lParamInt(), cur_card+section.get_lParamInt(), (YUANBAO_BILL_TYPE)bill_type);
	}
	else
	{
		g_server->close(section.get_SID()); //异步失败返回 所有不删除用户 等网络连接断开再清除用户信息
		g_log->write_log(LOG_TYPE_DEBUG,"CClientCmd on_sub_room_card_dbproxy close sid:%d", section.get_SID());
	}
}

FTYPE(void) CClientCmd::do_add_room_card_dbproxy(_tint64 userID,int room_num,int room_card, int bill_type)
{
	bool ret = myredis::GetInstance()->update_data_by_uid(userID, DB_COMM, CUR_DIAMOND, room_card);
	if(ret)
	{
		//通知用户更新
		_uint64 cur_coin = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_DIAMOND);
		do_update_user_vcoin(userID, cur_coin, room_card);

		IUser* pUser = UserManager::GetInstance()->get_user(userID);
		if(pUser)
			pUser->set_room_card(cur_coin);

		//钻石流水
		BILL->write_diamond_bill(userID, cur_coin, room_card, cur_coin-room_card, (YUANBAO_BILL_TYPE)bill_type);
	}
}*/
FTYPE(void)
CClientCmd::on_bag_message_record_startpage_req(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *exdata)
{
	cmd_room::UserBagMessageSearchReq req;
	req.unpack(data, size);

	cmd_dbproxy::CDBProxyExceSection db_section;
	db_section.set_SID(sid);
	db_section.set_UserID(userid);

	IUser *pUser = UserManager::GetInstance()->get_user(userid);
	if (pUser)
	{
		MY_LOG_DEBUG("on_bag_message_record_startpage_req pUser null uid(%d) sid(%d).", userid, sid);
		return;
	}

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	json_req_value["i_uid"] = Json::Value(userid);
	json_req_value["i_gameid"] = Json::Value(req.get_game_id());
	json_req_value["i_page"] = Json::Value(req.get_Page());
	json_req_value["i_page_size"] = Json::Value(req.get_Page_size());
	json_req_value["i_max_count"] = Json::Value(100);

	MY_LOG_DEBUG("on_bag_message_record_startpage_req uid(%d) sid(%d)  gameid:%d,page:%d,pagesize:%d ", userid, req.get_game_id(), req.get_Page(), req.get_Page_size());

	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, on_bag_message_record_startpage_dbproxy, userid, "bag_message_record_startpage_req", json_req_data.c_str(), db_section);
}

FTYPE(void)
CClientCmd::on_bag_message_record_startpage_dbproxy(void *obj, const char *data_json, int size, cmd_dbproxy::CDBProxyExceSection &section)
{
	if (!data_json || size == 0)
	{
		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy data_json null.");
		return;
	}

	_tint64 uid = section.get_UserID();
	_uint32 sid = section.get_SID();

	IUser *pUser = UserManager::GetInstance()->get_user(uid);
	if (pUser)
	{
		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy pUser null uid(%d) sid(%d).", uid, sid);
		return;
	}

	cmd_room::UserBagMessageSearchRsp resp;

	MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy data_json:%s uid(%d) sid(%d) lau(%d)", data_json, uid, sid, pUser->get_user_country());
	Json::Value json_value;
	if (!get_dbproxy_resp_value(data_json, size, json_value, NULL))
	{
		MY_LOG_ERROR("on_bag_message_record_startpage_dbproxy data_json(%s) uid(%d)", data_json, uid);
		g_server->send((socket_id)section.get_SID(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_SEARCH_RSP, &resp);
		return;
	}

	int recordsetcount = json_value["recordsetcount"].asInt();
	int nTotalNum = 0;
	if (!json_value["recordset0"].isNull())
	{
		nTotalNum = json_value["recordset0"][0]["total_num"].asInt();
	}
	resp.set_total_num(nTotalNum);
	if (nTotalNum > 0)
	{
		int nRecorCount = json_value["recordset1"].size();
		for (int i = 0; i < nRecorCount; i++)
		{
			string message_param = json_value["recordset1"][i]["param"].asString();
			_uint64 id = json_value["recordset1"][i]["id"].asInt();
			int message_id = json_value["recordset1"][i]["message_id"].asInt();
			int game_id = json_value["recordset1"][i]["game_id"].asInt();
			int props_id = json_value["recordset1"][i]["props_id"].asInt();
			int post_time = json_value["recordset1"][i]["post_time"].asInt();

			vector<string> vecParam;
			split_string_str(message_param, vecParam, ",");

			const char *game_name = get_error_msg(game_id, pUser->get_user_country());
			const char *format_str = get_error_msg(message_id, pUser->get_user_country());

			char msg[1024] = {0};

			MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy start msg:[%s] format_str:[%s] vecParam:%d", msg, format_str, vecParam.size());

			if (message_id == MSG_PROPS_MESSAGE_REWARD && vecParam.size() > 1)
			{
				sprintf(msg, format_str, game_name, atoi(vecParam[0].c_str()), atoi(vecParam[1].c_str()));
			}
			else if (message_id == MSG_PROPS_MESSAGE_SCORE_BY_USE && vecParam.size() > 0)
			{
				sprintf(msg, format_str, game_name, atoll(vecParam[0].c_str()));
			}
			else if (message_id == MSG_PROPS_MESSAGE_COMPOUND_CARD && vecParam.size() > 1)
			{
				sprintf(msg, format_str, atoi(vecParam[0].c_str()), game_name, atoi(vecParam[1].c_str()));
			}

			MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy msg:[%s] format_str:[%s]", msg, format_str);

			cmd_room::BagMessageDetailsInfo *temp = resp.bag_message_details_list_item(i);
			temp->set_id(id);
			temp->set_msg(msg, 0);
			temp->set_create_time(post_time);
			resp.set_bag_message_details_list_item_count(i + 1);
		}

		MY_LOG_DEBUG("on_bag_message_record_startpage_dbproxy end nTotalNum:%d nRecorCount:%d", nTotalNum, nRecorCount);
	}

	g_server->send((socket_id)section.get_SID(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_SEARCH_RSP, &resp);
}

// 背包消息删除请求
FTYPE(void)
CClientCmd::on_bag_message_record_del_req(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *exdata)
{
	cmd_room::UserBagMessageDelReq req;
	req.unpack(data, size);

	cmd_dbproxy::CDBProxyExceSection db_section;
	db_section.set_SID(sid);
	db_section.set_UserID(userid);
	db_section.set_wParamInt(req.get_id());

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	json_req_value["i_uid"] = Json::Value(userid);
	json_req_value["i_id"] = Json::Value(req.get_id());

	MY_LOG_DEBUG("on_bag_message_record_del_req uid(%d)  id:%d ", userid, req.get_id());

	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, on_bag_message_record_del_dbproxy, userid, "bag_message_record_del_req", json_req_data.c_str(), db_section);
}

FTYPE(void)
CClientCmd::on_bag_message_record_del_dbproxy(void *obj, const char *data_json, int size, cmd_dbproxy::CDBProxyExceSection &section)
{
	if (!data_json || size == 0)
	{
		return;
	}

	cmd_room::UserBagMessageDelRsp resp;

	MY_LOG_DEBUG("on_bag_message_record_del_dbproxy data_json:%s", data_json);
	Json::Value json_value;
	if (!get_dbproxy_resp_value(data_json, size, json_value, NULL))
	{
		MY_LOG_ERROR("on_bag_message_record_startpage_dbproxy data_json(%s) uid(%d)", data_json, section.get_UserID());
		g_server->send((socket_id)section.get_SID(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_DEL_RSP, &resp);
		return;
	}

	int result = json_value["result"].asInt();
	string msg = json_value["msg"].asString();

	resp.set_message_id(section.get_wParamInt());
	resp.set_Result(result);
	resp.set_Msg(msg.c_str(), 0);

	g_server->send((socket_id)section.get_SID(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_BAG_MESSAGE_DEL_RSP, &resp);
}

// 处理http请求
FTYPE(void)
CClientCmd::on_game_frame_http_message(socket_id sid, _uint64 userid, SNETHEAD *head, const void *data, int size, const void *exdata)
{
	cmd_room::UserEnterHttpRoomReq req;
	req.unpack(data, size);

	int gameid = req.get_GameID();
	int nodeid = req.get_NodeID();
	int opid = req.get_OPID();
	int uid = req.get_UID();
	std::string strParam = req.get_OPParam();

	MY_LOG_DEBUG("recv http request gameid: %d nodeid: %d opid:%d uid:%d param:%s", gameid, nodeid, opid, uid, strParam.c_str());
	CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(nodeid);
	if (!ptable) {
		MY_LOG_ERROR("cannot found table with nodeid: %d", nodeid);
		return;
	}

	//_tint64 ugold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);

    // 调用gameinfo 视为用户登录, 需在用户管理模块创建用户，因为游戏的so模块需要引用
	IUser *user = UserManager::GetInstance()->get_user(uid);
	if (!user) {
		user = UserManager::GetInstance()->create_user(uid, req.get_NodeID(), REAL_USER);
		if (user) {
			int client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
			user->set_client(client_id);
			user->set_user_type(REAL_USER);
			user->set_node_id(nodeid);
			int api_type = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_API_MODE);
			user->set_api_type(api_type);
			g_server->bind_user(sid, user);
		} else {
			MY_LOG_ERROR("on_game_frame_http_message uid:%d create user fail :nodeid:%d ", uid, nodeid);
			return;
		}
		MY_LOG_DEBUG("user[%d] create user success socket_id: %d", uid, sid);
	 } 
	//else {
	// 	_tint64 memery_gold = user->get_gold();
	// 	if (ugold != memery_gold) {
	// 		MY_LOG_ERROR("user[%d] begin maybe_money add memery gold: %lld not equal cache gold: %lld ", uid, memery_gold, ugold);
	// 	}
	// }
	user->set_socket_id(sid);

    // gameinfo时，从redis读取gold的值，更新用户user内存的m_gold
    //user->set_gold(ugold);

    // 刷新用户最近活跃时间
	if (user) {
		user->set_enter_time(time(0));
	}

    int length = 0;
    const char* buffer = req.get_OPParam(&length);

//     if (serverProto::AckType::spin == opid) {
//         // todo : add error process 
//         serverProto::Request pb_request;
//         if (!pb_request.ParseFromString(std::string(buffer, length))) {
//             MY_LOG_ERROR("failed to parse pb request, uid:%d", user->get_user_id());
//             return;
//         }

//         serverProto::SpinReq sreq;
//         if (!sreq.ParseFromString(pb_request.req())) {
//             MY_LOG_ERROR("failed to parse pb spin request, uid:%d", user->get_user_id());
//             return;
//         }

// #ifndef TEST_MODE
//         std::string json_string;
//         google::protobuf::util::JsonPrintOptions options;
//         options.add_whitespace = true;
//         google::protobuf::util::MessageToJsonString(sreq, &json_string, options);
//         MY_LOG_DEBUG("user[%d] recv spin request %s", uid, json_string.c_str());

//         if (sreq.itemid() > 0 && sreq.itemindex() > 0) {
//            web_request::GetInstance()->post_use_user_item(uid, gameid, 0, sreq.itemid(), sreq.itemindex(), buffer, length);
//         } else {
//             ptable->on_http_recv(sid, uid, req.get_OPID(), std::string(buffer, length));
//         }

// #endif

//     } else
	    ptable->on_http_recv(sid, uid, req.get_OPID(), std::string(buffer, length));
}

﻿/*
-----------------------------------------------------------------------------

File name        :   game_config.cpp
Author           :   hwl
Version          :   1.0
Date             :   2018.1.15
Description      :   百人牛牛组件配置;

-----------------------------------------------------------------------------
*/

#include "game_config.h"
#include "log_manager.h"
#include <algorithm>

#ifndef USE_GAME_DLL
IGameConfig *g_pstGameConfig = GameConfig::GetInstance();
#endif

GameConfig::GameConfig()
{
	memset(m_conf_file, 0x00, sizeof(m_conf_file)); /* 配置文件; */
	strcat(get_app_path(m_conf_file, sizeof(m_conf_file) - 1), "game_config.xml");
}

void GameConfig::set_dll_name(const char *dll_name)
{
	if (dll_name)
		mysprintf(m_dll_name, sizeof(m_dll_name), "%s", dll_name);
}

int GameConfig::init(const char *conf_file)
{
	if (conf_file)
	{
		memset(m_conf_file, 0x00, sizeof(m_conf_file)); /* 配置文件; */
		strcat(get_app_path(m_conf_file, sizeof(m_conf_file) - 1), conf_file);
	}

	reload();
	return 0;
}

void GameConfig::clear()
{
	m_MapStrInfo.clear();
}

int GameConfig::reload()
{
	clear();
	TiXmlDocument xmlcfg;
	TiXmlElement *pRoot = 0;
	TiXmlElement *pXmlItem = 0;

	if (!xmlcfg.LoadFile(m_conf_file))
	{
		MY_LOG_ERROR("load xml file fail.[error: %s]", xmlcfg.ErrorDesc());
		return -1;
	}

	pRoot = xmlcfg.FirstChildElement("root");
	if (!pRoot)
	{
		MY_LOG_ERROR("not find root name[root]");
		return -2;
	}

	string str = "0";
	pXmlItem = pRoot->FirstChildElement("game_config");
	if (pXmlItem)
	{
		string pst_value;
		const char *pst_text = 0;
		TiXmlElement *pItem = pXmlItem->FirstChildElement();
		while (pItem)
		{
			pst_value = pItem->Value();
			pst_text = pItem->GetText();

			if (pst_text)
			{
				int n_value = atoi(pst_text);
				if (n_value == 0 && str.find(pst_text) == str.npos)
				{
					m_MapStrToStrInfo[pst_value] = pst_text;
				}
				if (pst_value == "bet_level")
				{
					m_MapStrToStrInfo[pst_value] = pst_text;
					MY_LOG_DEBUG("bet_level = %s", m_MapStrToStrInfo[pst_value].c_str());
				}
				else if (pst_value == "bet_size")
				{
					m_MapStrToStrInfo[pst_value] = pst_text;
					MY_LOG_DEBUG("bet_size = %s", m_MapStrToStrInfo[pst_value].c_str());
				}
				else if (pst_value == "lottery_ip")
				{
					m_MapStrToStrInfo[pst_value] = pst_text;
					MY_LOG_DEBUG("lottery_ip = %s", m_MapStrToStrInfo[pst_value].c_str());
				}
				else
				{
					m_MapStrInfo[pst_value] = n_value;
				}
			}

			pItem = pItem->NextSiblingElement();
		}
	}
	return 0;
}

/*初始化int数组;*/
void GameConfig::init_vec_int_info(TiXmlElement *pXmlItem, VEC_INTINFO &vecInfo)
{
	const char *pst_text = 0;
	TiXmlElement *pItem = pXmlItem->FirstChildElement();
	while (pItem)
	{
		pst_text = pItem->GetText();
		if (pst_text)
		{
			int n_value = atoi(pst_text);
			vecInfo.push_back(n_value);
		}

		pItem = pItem->NextSiblingElement();
	}
}

/*例如：將字符串“1,31,4123”解析成一个数组;*/
VEC_INTINFO GameConfig::stringtoarray(string source, string seperator)
{
	VEC_INTINFO vec;

	if (!source.empty())
	{
		string::size_type begin = 0;
		string::size_type end = 0;
		unsigned int sepSize = seperator.size();
		while ((end = source.find_first_of(seperator, begin)) != string::npos)
		{
			string item = source.substr(begin, end - begin);
			int num = atoi(item.c_str());
			vec.push_back(num);
			begin = end + sepSize;
		}
		// last item,注意如果最后是分割符，则最后的元素为空字符串
		if (begin <= source.size())
		{
			string item = source.substr(begin, source.size() - begin);
			if (item.size() > 0)
			{
				int num = atoi(item.c_str());
				vec.push_back(num);
			}
		}
	}

	return vec;
}

/*判断是否跨天;*/
bool GameConfig::isNewDay(int nTime)
{
	return isNewDay(nTime, static_cast<int>(time(NULL)));
}

/** 检测输入两个的时间是否为同一天;
@param
@param
@param  offset[0-23]，如果为0，将每日的00：00当作新的一天的开始；如果为2，将每日的22：00当作新的一天的开始;...如果为18，将每日的06：00当作新的一天的开始;
@return
*/
bool GameConfig::isNewDay(int nFirstTime, int nSecondTime, int offset)
{
	int nInterval = offset * 3600;

	// 比较时间;
	nFirstTime += nInterval;
	time_t oldTime = (time_t)nFirstTime;

	// 当前时间;
	nSecondTime += nInterval;
	time_t curTime = (time_t)nSecondTime;

	tm tmTime1;
	tm tmTime2;

	tm *temp = localtime(&oldTime);
	if (temp == NULL)
	{
		return true;
	}
	memcpy(&tmTime1, temp, sizeof(tm));

	temp = localtime(&curTime);
	if (temp == NULL)
	{
		return true;
	}
	memcpy(&tmTime2, temp, sizeof(tm));

	if (tmTime1.tm_year != tmTime2.tm_year || tmTime1.tm_mon != tmTime2.tm_mon || tmTime1.tm_mday != tmTime2.tm_mday)
	{
		return true;
	}
	return false;
}

// 获得比例计算值
int GameConfig::get_ratio(string str)
{
	/*< RobotMinGold>50000 < / RobotMinGold >
	< RobotMaxGold>500000 < / RobotMaxGold >
	< BankerUp>50000 < / BankerUp >
	< BankerRobotMaxGold>6000000 < / BankerRobotMaxGold >
	< HonorGuestMin>10000 < / HonorGuestMin >
	< RobotLeave>10000 < / RobotLeave >
	< SysBankerUp>******** < / SysBankerUp >*/

	if (str == "RobotMinGold" ||
		str == "RobotMaxGold" ||
		str == "BankerUp" ||
		str == "BankerRobotMaxGold" ||
		str == "HonorGuestMin" ||
		str == "RobotLeave" ||
		str == "SysBankerUp")
	{
		return 100;
	}
	return 1;
}

/*通过字符串获取单个int配置;*/
int GameConfig::getValueByStr(string str)
{
	MAP_STRINFO::iterator itr = m_MapStrInfo.find(str.c_str());
	if (itr != m_MapStrInfo.end())
	{
		return itr->second * get_ratio(str);
	}

	MY_LOG_ERROR("通过字符串获取单个int配置错误.  %s", str.c_str());
	return 0;
}

/*通过字符串获取单个string配置;*/
const char *GameConfig::getStrByStr(string str)
{
	MAP_STRSTRINFO::iterator itr = m_MapStrToStrInfo.find(str.c_str());
	if (itr != m_MapStrToStrInfo.end())
		return itr->second.c_str();
	memset(m_szValue, 0, sizeof(m_szValue));
	MAP_STRINFO::iterator itr2 = m_MapStrInfo.find(str.c_str());
	if (itr2 != m_MapStrInfo.end())
	{
		sprintf(m_szValue, "%d", itr2->second);
		return m_szValue;
	}
	MY_LOG_ERROR("通过字符串获取单个string配置错误.  %s", str.c_str());
	return "";
}

void GameConfig::field_list_split(std::vector<int> &vec_list, const std::string str, std::string sep)
{
	if (str.length() <= 0)
	{
		return;
	}
	vec_list.clear();
	std::string tmp;
	std::string::size_type pos_begin = str.find_first_not_of(sep);
	std::string::size_type comma_pos = 0;
	int nCont = 0;
	while (pos_begin != std::string::npos)
	{
		comma_pos = str.find(sep, pos_begin);
		if (comma_pos != string::npos)
		{
			tmp = str.substr(pos_begin, comma_pos - pos_begin);
			pos_begin = comma_pos + sep.length();
		}
		else
		{
			tmp = str.substr(pos_begin);
			pos_begin = comma_pos;
		}

		if (!tmp.empty())
		{
			vec_list.push_back(atoi(tmp.c_str()));
			tmp.clear();
		}
	}
}
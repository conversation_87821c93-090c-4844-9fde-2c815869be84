﻿#include <stdlib.h>
#include <time.h>
#include <string.h>
#include "log_manager.h"
#include "game_frame.h"
#include "user.h"
#include "user_manager.h"
#include "table.h"
#include "table_manager.h"
#include "i_game_config.h"
#include "room_route_challenge.h"
#include "config_manager.h"
#include "table_Hundred.h"
#include "kw.h"
#include "msg_manager.h"
#include "cmd_net.h"
#include "observe_manager.h"
#include "robot_manager.h"
#include "pair_manager.h"
#include "client_cmd.h"
#include "room_bill_fun.h"

#include "common.h"


#ifdef USE_GAME_DLL
IGameConfig *g_pstGameConfig = 0;
#endif
#ifndef WIN32
#define _atoi64(val)  strtoll(val, NULL, 10);
#endif

// redis keep alive
#define REDIS_TCP_KEEP_ALIVE_TIMER_ID   0xFFF0
#define REDIS_TCP_KEEP_ALIVE_DURATION   5*1000

CGameFrame::CGameFrame()
{
    m_room_id = 0;
    m_dll_instance = 0;
	memset(m_conf_name, 0x0, sizeof(m_conf_name));
}

CGameFrame::~CGameFrame()
{
    
}

IGameConfig *CGameFrame::get_config()
{
    return g_pstGameConfig;
}

int CGameFrame::get_max_player()
{
    if (g_pstGameConfig)
    {
        return g_pstGameConfig->get_table_count() * g_pstGameConfig->get_game_player();
    }
    else
    {
        MY_LOG_DEBUG("g_pstGameConfig is NULL!");
    }

    return 0;
}

/*
int CGameFrame::get_game_id()
{
    if (g_pstGameConfig)
    {
        return g_pstGameConfig->get_game_id();
    }
    else
    {
        MY_LOG_WARN("g_pstGameConfig is NULL!");
    }
    return 0;
}
*/

//int CGameFrame::set_xml_config_game(const char* xml_config_game)
//{
//	strcpy(m_xml_config_game, xml_config_game);
//	return 0;
//}
int CGameFrame::init(int roomID, const char *dll_name)
{   
	MY_LOG_DEBUG("CGameFrame::init rooid:%d dll_name:%s", roomID, dll_name);
    int iRet = 0;
    //char szConfName[255] = {0};
    m_room_id = roomID;
	
#ifdef USE_GAME_DLL 
    DLL_MOBULE_INIT(m_game_instance, dll_name, "_get_component_instance", "_free_component_instance");
    m_dll_instance = m_game_instance.GetInterface();

    g_pstGameConfig = m_dll_instance->conf;
    if (!g_pstGameConfig)
    {
        MY_LOG_DEBUG("g_pstGameConfig is NULL!");
        return -13;
    }
	g_pstGameConfig->m_b_web_active = false;
    /* 初始化日志 */
    m_dll_instance->log->SetLog(g_pstLogManager->GetLog());

    /* 加载配置 */    
    const char *p = dll_name;
	memset(m_conf_name, 0x0, sizeof(m_conf_name));
    while (*p && *p++ != '.');
    if (*p)
    {
        strncat(m_conf_name, dll_name, (size_t)p - (size_t)dll_name - 1);
        strncat(m_conf_name, "_", 1);
        strncat(m_conf_name, GAME_CONFIG_NAME, sizeof(m_conf_name) - 1);
    }
	
	MY_LOG_PRINT("CGameFrame::init  =======================================, %s", m_conf_name);

	iRet = g_pstGameConfig->init(m_conf_name);
	if (iRet)
    {
        MY_LOG_ERROR("Init config game_config.xml error. %d", iRet);
        return -10;
    }

    g_pstGameConfig->set_dll_name(dll_name);         

    *(m_dll_instance->dbproxy) = g_dbproxy;

    MY_LOG_PRINT("CGameFrame::init %s", m_dll_instance->szVersionInfo);    
#else
    if (!g_pstGameConfig)
    {
        MY_LOG_DEBUG("g_pstGameConfig is NULL!");
        return -14;
    }
	MY_LOG_ERROR("222");
    iRet = g_pstGameConfig->init(GAME_CONFIG_NAME);
#endif

    if (iRet)
    {
        MY_LOG_ERROR("Game config init fail. %d", iRet);
        return -11;
    }

	if (config_manager::GetInstance()->m_room_type != TYPE_HUNDRED )
    {
		iRet = TableManager::GetInstance()->init(get_config()->get_table_count());
    }

    if (iRet)
    {
        MY_LOG_ERROR("Table init fail. ret=%d table_count=%d", iRet, get_config()->get_table_count());
        return -12;
    }

    iRet = kw_init("filterwords.txt");
    if (iRet)
    {
        MY_LOG_WARN("Load keywords fail. %d", iRet);
    }

    // test code
    /*int iconvLen = 0;
    char szKeyWord[100] = {"逗逼"};

    MY_LOG_DEBUG("Convert before %s", szKeyWord);
    kw_hexie(szKeyWord, &iconvLen);
    MY_LOG_DEBUG("Convert end %s", szKeyWord);*/

	// keep redis connection alive with ping cmd
	// bool bRet = g_timer_mgr->set_timer(this
	// 	, on_timer
	// 	, REDIS_TCP_KEEP_ALIVE_TIMER_ID
	// 	, REDIS_TCP_KEEP_ALIVE_DURATION // + CGameFrame::GetInstance()->get_config()->get_net_delay() * 1000
	// 	, 1
	// 	, 0);

	// if (!bRet) {
	// 	MY_LOG_WARN("create redis keep alive timer failed");
	// }

    return 0;
}

void CGameFrame::on_timer(void*obj,_uint32 timerid,_uint64 tag,_uint32 Elapse,_uint32 iRepeat,_uint32 delay) {

    if (REDIS_TCP_KEEP_ALIVE_TIMER_ID == timerid) {
		sRedisMgr->keep_alive();
	}
}

int CGameFrame::init_ex(const char * game_config_xml)
{       
	MY_LOG_DEBUG("CGameFrame::init_ex game_config_xml:%s", game_config_xml);
    m_dll_instance = m_game_instance.GetInterface();
    g_pstGameConfig = m_dll_instance->conf;
    if (!g_pstGameConfig)
    {
        MY_LOG_DEBUG("g_pstGameConfig is NULL!");
        return -13;
    }        

	// 直接替换本地config.xml文件，再重新reload

	char _conf_file[256];
	memset(_conf_file, 0x00, sizeof(_conf_file));            /* 配置文件 */
	strcat(get_app_path(_conf_file, sizeof(_conf_file) - 1), m_conf_name);       

	FILE * file = fopen(_conf_file, "w+b");
	if (!file)
	{
		MY_LOG_DEBUG("init_ex fopen %s error!", CONFIG);
		return -12;
	}

	fseek(file, 0, SEEK_SET);
	if( !fwrite(game_config_xml, strlen(game_config_xml), 1, file) )
	{
		MY_LOG_DEBUG("init_ex fwrite %s error!", CONFIG);
		return -11;
	}

	fclose(file);
	g_pstGameConfig->reload();
	/*/		
	int iRet = g_pstGameConfig->init_ex(game_config_xml);
	if (iRet)
    {
        MY_LOG_WARN("init_ex game config error. %d so set local game config file.", iRet);	
		//如果web推送的游戏配置内容有错误，则采用本地的文件内容进行覆盖
		iRet = g_pstGameConfig->init(m_conf_name);
		if (iRet)
		{
			MY_LOG_ERROR("Init config game_config.xml error. %d", iRet);
			return -10;
		}
	}
	/*/
    return 0;
}

bool CGameFrame::on_recv(_uint8 mcmd, _uint8 scmd, const void* pData, int size, IUser* pUser)
{
    if (NULL == pUser)
    {
        MY_LOG_WARN("User is null. m_cmd=%d s_cmd=%d size=%d", mcmd, scmd, size);
        return false;
    }

    m_scmd = scmd;
    switch(mcmd)
    {    
    case cmd_net::CMD_ROOM:    
        on_cmd_room(scmd, pData, size, pUser);
        break;

    case cmd_net::CMD_GAME:
        on_cmd_game(scmd, pData, size, pUser);
        break;
    default:
        MY_LOG_ERROR("On recv packet error. mcmd=%d scmd=%d", mcmd, scmd);
        break;
    }

    return true;
}

int CGameFrame::on_cmd_room(_uint8 scmd, const void *pdata, int size, IUser *pUser)
{
    int ret = 0;

    m_pUser = pUser;
    m_recv_data = pdata;
    m_recv_len = size;

    switch(scmd)
    {
    case cmd_net::SUB_USER_ACTION:				   /* 用户行为请求(坐下、站起、准备、离开) */
        on_user_action();
        break;
	case cmd_net::SUB_ENTER_GAME_IS_START:         /* 房主确定点击游戏开始 */
		on_table_action(cmd_room::USER_ACTION_READY);
		break;
    default:
        on_table_msg();
        break;
    }

    return 0;
}

int CGameFrame::on_cmd_game(_uint8 scmd, const void *pdata, int size, IUser *pUser)
{
	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int node_id = pUser->get_node_id();
		CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			ptable->on_game_recv(scmd, pdata, size, pUser);
		}
	}
	else
	{
		CTable *pTable = TableManager::GetInstance()->get_table(pUser->get_table_id());
		if (!pTable)
		{
			MY_LOG_ERROR("Table game msg, but user table is null. uid=%u table_id=%d", pUser->get_user_id(), pUser->get_table_id());
			return -1;
		}

		int iret = pTable->on_game_recv(scmd, pdata, size, pUser);
		if (iret)
		{
			MY_LOG_ERROR("Table recv game data error. %d", iret);
			return -2;
		}
	}
    return 0;
}

int CGameFrame::on_table_msg()
{
	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int node_id = m_pUser->get_node_id();
		CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			ptable->on_recv(m_scmd, m_recv_data, m_recv_len, m_pUser);
		}
	}
	else
	{
		CTable *pTable = TableManager::GetInstance()->get_table(m_pUser->get_table_id());
		if (!pTable)
		{
			MY_LOG_ERROR("Table msg, but user table is null. uid=%u table_id=%d", m_pUser->get_user_id(), m_pUser->get_table_id());
			return -1;
		}

		int iret = pTable->on_recv(m_scmd, m_recv_data, m_recv_len, m_pUser);
		if (iret)
		{
			MY_LOG_ERROR("Table recv data error. %d", iret);
			return -2;
		}
	}
    return 0;
}

int CGameFrame::on_user_action()
{
    cmd_room::RoomChairAction stAction;

    if (!stAction.unpack(m_recv_data, m_recv_len))
        return -1;

    MY_LOG_DEBUG("User action. type=%d", (int)stAction.get_ActionType());

    switch (stAction.get_ActionType())
    {
    case cmd_room::USER_ACTION_SIT_DOWN:
        /* 进入房间后服务器将用户坐下 */
        break;

	case cmd_room::USER_TEMP_LEAVE:
	case cmd_room::USER_COME_BACK:
    case cmd_room::USER_ACTION_READY:
        on_table_action((_uint8)stAction.get_ActionType());
        break;

    case cmd_room::USER_ACTION_STANDUP:        
    case cmd_room::USER_ACTION_LEAVE:
        on_leave();
        break;
	case cmd_room::USER_CHANGE_TABLE:
		change_table();
		break;

    default:
        MY_LOG_ERROR("User action type is error. %d", stAction.get_ActionType());
        return -99;
    }

    return 0;
}

int CGameFrame::on_table_action(_uint8 type)
{
    CTable *pTable = TableManager::GetInstance()->get_table(m_pUser->get_table_id());
    if (!pTable)
    {
		if (m_pUser->get_user_type() != ROBOT_TYPE)
		{
			MY_LOG_ERROR("on_table_action Table is null. %d", m_pUser->get_table_id());
			TableManager::GetInstance()->print_table_info();
		}
        return -1;
    }

	pTable->on_user_action(m_pUser, type);
    return 0;
}


int CGameFrame::on_user_create(IUser *pUser, _uint32 room_num, void *rule, int rule_len)
{
	if (!pUser)
	{
		MY_LOG_ERROR("User is null. room_num=%u create=%d", room_num);
		return -1;
	}

	CTable *pUserTable = TableManager::GetInstance()->get_table(pUser->get_table_id());
	CTable *pTable = TableManager::GetInstance()->find_table_from_num(room_num);

	MY_LOG_DEBUG("User create room. uid=%u num=%u", pUser->get_user_id(), room_num);

	if (pTable)
	{   
		MY_LOG_ERROR("Create room, but room is exists. %u", room_num);
		return cmd_room::ROOM_NUM_IS_EXISTS;
	}      

	if (pUserTable)
	{
		MY_LOG_ERROR("Create table, user is already in table. uid=%u table=%d"
			, pUser->get_user_id(), pUserTable->get_table_id());

		return cmd_room::ROOM_USER_IN_TABLE;
	}

	pTable = TableManager::GetInstance()->get_table_from_num(room_num, rule, rule_len);
	if (!pTable)
	{
		MY_LOG_ERROR("Create room fail with num. %u", room_num);
		return cmd_room::ROOM_TABLE_NOT_ENOUGH;
	}

	pTable->set_owner(pUser);        

	return 0;
}

/* web创建房间 */
int CGameFrame::on_web_create(int uid, _uint32 room_num, void *rule, int rule_len)
{
	CTable* pTable = TableManager::GetInstance()->get_table_from_num(room_num, rule, rule_len, 1);
	if (!pTable)
	{
		MY_LOG_ERROR("on_web_create room fail with num. %u", room_num);
		return cmd_room::ROOM_TABLE_NOT_ENOUGH;
	}

	pTable->set_owner(uid);     
	return 0;
}

int CGameFrame::on_user_enter(IUser *pUser, _uint32 room_num, bool is_observe)
{
    if (!pUser)
    {
        MY_LOG_ERROR("User is null. room_num=%u", room_num);
        return -1;
    }

    CTable *pUserTable = TableManager::GetInstance()->get_table(pUser->get_table_id());
    CTable *pTable = TableManager::GetInstance()->find_table_from_num(room_num);

	MY_LOG_DEBUG("User enter room. uid=%u num=%u", pUser->get_user_id(), room_num);

	if (!pTable)
	{   
		MY_LOG_ERROR("Enter room, but room is not exists. %u", room_num);
		return cmd_room::ROOM_NUM_NOT_FIND;
	}

	if (pUserTable && pTable != pUserTable)
	{
		MY_LOG_ERROR("Enter room, user enter other table. uid=%u in_table=%d other_table=%d"
			, pUser->get_user_id(), pUserTable->get_table_id(), pTable->get_table_id());

		return cmd_room::ROOM_USER_IN_TABLE;
	} 

    int iret = pTable->on_user_enter(pUser, is_observe);
    if (iret)
    {
        MY_LOG_ERROR("User enter table fail. %d", iret);
        return iret;
    }

    return 0;
}

int CGameFrame::on_user_enter(_uint64 uid, bool is_observe)
{
    IUser *puser = UserManager::GetInstance()->get_user(uid);
    if (!puser)
    {
        MY_LOG_ERROR("user is null. uid=%zu", uid);
        return -1;
    }

	CTable *ptable = NULL;
	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
	{
		ptable = TableManager::GetInstance()->get_table(puser->get_table_id());
		if (!ptable)
		{
			if(puser->get_user_type() == ROBOT_TYPE)
			{
				robot_manager::GetInstance()->add_robot(uid);
				return 0;
			}

			//寻找桌子
			/*ptable = TableManager::GetInstance()->find_nomal_table();
			if (ptable->on_user_enter(puser) != 0)
			{
				return -2;
			}*/
		}
	}
	else
	{
		ptable = TableManager::GetInstance()->get_table(puser->get_table_id());
		if (!ptable)
		{
			MY_LOG_ERROR("user table is null. uid=%zu table_id=%d", uid, puser->get_table_id());
			return -2;
		}
	}

	if(ptable == NULL)
	{
		return -2;
	}

	if (ptable->on_user_enter(puser, is_observe))
	{
		MY_LOG_ERROR("user enter table fail.");
		return -3;
	}
    return 0;
}

int CGameFrame::on_user_offline(IUser *user)
{
    if (!user)
    {
        return -1;
    }

    MY_LOG_DEBUG("User offline uid=%u", user->get_user_id());
	if(config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int node_id = user->get_node_id();
		CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			ptable->on_user_offline(user);
		}
	}
	else
	{
		robot_manager::GetInstance()->robot_offline(user->get_user_id());
		CTable *table = TableManager::GetInstance()->get_table(user->get_table_id());
		MY_LOG_DEBUG("====on_user_offline uid=%u table_id:%d=====",user->get_user_id(),user->get_table_id());
		if (!table)
		{   
			if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
				pair_manager::GetInstance()->delete_pair(user->get_user_id());

			if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
				CRoomRouteChallenge::send_nomal_user_leave(user->get_user_id());

			if (user->get_user_type() == ROBOT_TYPE)
			{
				robot_manager::GetInstance()->push_robot(user->get_user_id());
			}
			else
			{
				UserManager::GetInstance()->delete_user(user->get_user_id());
			}
		}
		else
		{   
			//如果为旁观玩家，则直接离开房间
			if(ObserveManager::GetInstance()->is_observe_user(user->get_user_id()))
			{			
				UserManager::GetInstance()->delete_user(user->get_user_id());
				return 0;
			}
			else
			{
				table->on_user_offline(user);
			}
		}
	}
    return 0;
}

int CGameFrame::on_free_table(_uint32 room_num)
{
    CTable *table = TableManager::GetInstance()->find_table_from_num(room_num);
    if (!table) 
    {
        MY_LOG_ERROR("Table is not exists. %u", room_num);
        return -1;
    }

    MY_LOG_PRINT("Free table game. %u", room_num);
	table->force_free_table();

    return 0;
}

int CGameFrame::change_table(_uint64 uid)
{
	IUser* puser = m_pUser;
	if (uid != 0)
	{
		puser = UserManager::GetInstance()->get_user(uid);
	}
	if (puser == NULL)
	{
		return 0;
	}

	if(config_manager::GetInstance()->get_room_type() != TYPE_NOAML)
	{
		send_action_result(puser->get_user_id(), 1, cmd_room::USER_CHANGE_TABLE);
		return 0;
	}

	int now_table_id = puser->get_table_id();
	CTable *pTable = TableManager::GetInstance()->get_table(puser->get_table_id());
	if (pTable)
	{
		//校验金币，金币不足，先弹快捷购买
		if (pTable->check_user_bankupt(puser->get_user_id()))
		{
			send_action_result(puser->get_user_id(), 1, cmd_room::USER_CHANGE_TABLE);
			return 0;
		}

		//如果有桌子，先要离开
		//if (pTable->on_user_stanup(puser) != 0)
		MY_LOG_DEBUG("CGameFrame::change_table on_user_change_table_standup uid:%d, now_table_id:%d .", puser->get_user_id(), now_table_id);
		if (pTable->on_user_change_table_standup(puser) != 0)
		{
			MY_LOG_ERROR("change_table on_user_stanup error. uid: %d  tableid:%d", puser->get_user_id(), pTable->get_table_id());
			send_action_result(puser->get_user_id(), 1, cmd_room::USER_CHANGE_TABLE);
			return 0;
		}
	}

	//配桌模式直接放入队列
	if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
	{
		int add_ret = pair_manager::GetInstance()->add_pair(puser->get_user_id(), puser->get_node_id());
		CClientCmd::send_pair_table_result(puser->get_socket_id(), add_ret, puser->get_user_country());
		return 0;
	}

	//挑选新桌子
	//寻找桌子
	int fit_node = puser->get_node_id();
	if (fit_node == 0)
	{
		MY_LOG_ERROR("change_table fit_node error. uid: %d  fit_node:%d", puser->get_user_id(), fit_node);
		send_action_result(puser->get_user_id(), 1, cmd_room::USER_CHANGE_TABLE);
		return 0;
	}

	if (config_manager::GetInstance()->get_choice_real() == 1)
	{
		pTable = TableManager::GetInstance()->find_nomal_real_table(now_table_id, fit_node);
	}
	else
	if (config_manager::GetInstance()->get_choice_one() == 1)
	{
		pTable = TableManager::GetInstance()->find_nomal_real_one(now_table_id, fit_node);
	}
	else
	{
		pTable = TableManager::GetInstance()->find_nomal_table(now_table_id, fit_node);
	}
	if (pTable == NULL)
	{
		MY_LOG_ERROR("change_table pTable error. uid: %d ", puser->get_user_id());
		send_action_result(puser->get_user_id(), 1, cmd_room::USER_CHANGE_TABLE);
		return 0;
	}

	//坐下桌子
	int ret  = pTable->on_user_enter(puser, false, true);
	if(ret != 0)
	{
		MY_LOG_ERROR("change_table on_user_enter error. uid: %d ret:%d", puser->get_user_id(), ret);
		send_action_result(puser->get_user_id(), 1, cmd_room::USER_CHANGE_TABLE);
		return 0;
	}

	send_action_result(puser->get_user_id(), 0, cmd_room::USER_CHANGE_TABLE);
	return 0;
}

int CGameFrame::on_leave()
{
	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int node_id = m_pUser->get_node_id();
		CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			ptable->on_user_leave(m_pUser);
		}
	}
	else
	{
		MY_LOG_DEBUG("====uid:%u,table_id:%d====",m_pUser->get_user_id(),m_pUser->get_table_id());
		CTable *pTable = TableManager::GetInstance()->get_table(m_pUser->get_table_id());
		if (pTable)
		{
			//如果为旁观玩家，则直接离开房间
			if(ObserveManager::GetInstance()->is_observe_user(m_pUser->get_user_id()))
			{			
				UserManager::GetInstance()->delete_user(m_pUser->get_user_id());
				return 0;
			}

			int iret = pTable->on_user_leave(m_pUser);
			CGameFrame::GetInstance()->send_action_result(m_pUser->get_user_id(), iret, cmd_room::USER_ACTION_LEAVE);
			if (iret)
			{
				MY_LOG_INFO("User leave fail. %d", iret);
				return 1;
			}

			if (config_manager::GetInstance()->get_room_type() == TYPE_CHALLENGE)
			{
				// modify by hsl on 03/14/2017 AA支付，非房主离开房间，返钻
				_uint64 u64CostCard = 0;
				if ( pTable->get_pay_mode() == e_PMT_AA && pTable->get_owner_uid() != m_pUser->get_user_id() )
				{
					MY_LOG_ERROR("hsl debug version[%s] not allow owner[%u] leave room. add room card {UID:%d,TabID:%d,RoomCard:%d,RoomID:%d}", 
						m_pUser->get_version(), m_pUser->get_user_id(), m_pUser->get_user_id(), m_pUser->get_table_id(), m_pUser->get_room_card(), pTable->get_room_num() );
					u64CostCard = pTable->get_cost_card();
				}
				CRoomRouteChallenge::send_challenge_user_leave(m_pUser->get_user_id(),pTable->get_room_num(), u64CostCard, 0);
			}

			if (m_pUser->get_user_type() != ROBOT_TYPE)
			{
				UserManager::GetInstance()->delete_user(m_pUser->get_user_id());
			}
			return 0;
		}
		else
		{
			if (config_manager::GetInstance()->get_patch_mode() >= cmd_room::MODE_PATCH)
				pair_manager::GetInstance()->delete_pair(m_pUser->get_user_id());

			if (m_pUser->get_user_type() != ROBOT_TYPE)
			{
				if (config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
					CRoomRouteChallenge::send_challenge_user_leave(m_pUser->get_user_id(),0, 0, 0);
				else
					CRoomRouteChallenge::send_nomal_user_leave(m_pUser->get_user_id());

				UserManager::GetInstance()->delete_user(m_pUser->get_user_id());
			}
			else
			{
				robot_manager::GetInstance()->push_robot(m_pUser->get_user_id());
			}
		}
	}
    return -1;
}

//发送动作结果
void CGameFrame::send_action_result(_uint64 uid, int result, int action)
{
	IUser *user = UserManager::GetInstance()->get_user(uid);
	if (!user)
	{
		MY_LOG_WARN("User is not exists. %zu", uid);
		return;
	}

	cmd_room::UserActionResult data;
	data.set_Result(result);
	data.set_Action(action);
	if (!g_server->send(user->get_socket_id(), cmd_net::CMD_ROOM, cmd_net::SUB_USER_ACTION_RESULT, &data))
	{
		MY_LOG_ERROR("server send_action_result to user fail.");
	}
}

int CGameFrame::send_comm_message(_uint64 uid, int code, const char *msg, int type)
{
	if (uid == 0)
	{
		return 0;
	}

	IUser *user = UserManager::GetInstance()->get_user(uid);
	if (!user)
	{
		MY_LOG_WARN("User is not exists. %zu", uid);
		return -1;
	}

	if (user->is_offline())
	{
		MY_LOG_DEBUG("User is offline.uid=%u", user->get_user_id());
		return 0;
	}


	cmd_room::CommMessage stmsg;
	stmsg.set_Code(code);
	stmsg.set_Msg(msg, 0); 
	stmsg.set_Type(type);
#ifdef WIN32
	char utf8[256] = { 0 };
	_gbk_to_utf8(msg, strlen(msg), utf8, sizeof(utf8));
	stmsg.set_Msg(utf8, 0); 
#endif
	
	if (!g_server->send(user->get_socket_id(), cmd_net::CMD_ROOM, cmd_net::SUB_COMM_MESSAGE, &stmsg))
	{
		MY_LOG_ERROR("server send message to user fail.");
		return -2;
	}
	return 0;
}

int CGameFrame::send_comm_message_ex(_uint64 uid, int code, int msgid, int type)
{
	
	IUser *user = UserManager::GetInstance()->get_user(uid);
	if (!user)
	{
		MY_LOG_WARN("User is not exists. %zu", uid);
		return -1;
	}


	if (user->is_offline())
	{
		MY_LOG_DEBUG("User is offline.uid=%u", user->get_user_id());
		return 0;
	}

	const char *msg = get_error_msg(msgid, user->get_user_country());
	cmd_room::CommMessage stmsg;
	stmsg.set_Code(code);
	stmsg.set_Msg(msg, 0); 
	stmsg.set_Type(type);
#ifdef WIN32
	char utf8[256] = { 0 };
	_gbk_to_utf8(msg, strlen(msg), utf8, sizeof(utf8));
	stmsg.set_Msg(utf8, 0); 
#endif

	if (!g_server->send(user->get_socket_id(), cmd_net::CMD_ROOM, cmd_net::SUB_COMM_MESSAGE, &stmsg))
	{
		MY_LOG_ERROR("server send message to user fail.");
		return -2;
	}
	return 0;
}

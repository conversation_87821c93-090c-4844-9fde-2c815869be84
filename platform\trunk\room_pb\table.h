﻿#ifndef _TABLE_H_
#define _TABLE_H_

#include "room_common.h"
#include "i_game_component.h"
#include "LocationLogic.h"
#include "table_interface.h"
#include <map>
#include <time.h>
#include "config_manager.h"
#include "observe_manager.h"
#include "data_pool.h"

class CTable : public ITable
{
public:
    CTable();
    virtual ~CTable();

public:
	static CTable *GetInstance()
	{
		static CTable stMgr;
		return &stMgr;
	}

	void print_table_info();
    void free(int code); /* 设置释放桌子：code==0通知客户端,并返还房卡 */    
	void release(); /*私人房释放桌子*/

    int on_recv(_uint8 scmd, const void* pData, int size, IUser* pUser);
    int on_game_recv(_uint8 scmd, const void* pData, int size, IUser* pUser);

	inline void set_create_time(){ m_n64CreateTime = time(NULL); }
	inline _tint64 get_create_time(){ return m_n64CreateTime; }
private:
    int on_chat(IUser *pUser, const void *pdata, int size);
	int on_gps_recv(IUser *pUser, const void *pdata, int size);
	int on_master(IUser *pUser, const void *pdata, int size);
	void on_look_avatar(IUser *pUser, const void *pdata, int size);
	void on_text_radio(IUser *pUser, const void *pdata, int size);
	int on_upload_gps(const void *pdata, int size);

public: 
	const stGameCfg* get_game_config(int game_id)
	{
		return config_manager::GetInstance()->get_game_config(game_id, m_node_id);
	}

	void reset_game_rule(string rule);

	stGoldLimitNodeInfo* get_gold_node_info() { return &m_stGoldNodeInfo;  }
	data_pool_interface* get_data_pool() { return data_pool::GetInstance(); }

	void set_cost_card(int cost_card)
	{
		m_cost_card = cost_card;
	}

	void set_node_id(int node)
	{
		m_node_id = node;
	}

	int get_node_id() { return m_node_id; }

	int get_cost_card()
	{
		return m_cost_card;
	}

	int get_room_mode()
	{
		return config_manager::GetInstance()->get_room_type();
	}

	const char* get_node_name()
	{
		return config_manager::GetInstance()->get_node_name(m_node_id);
	}

    void  set_room_num(_uint32 room_num)
    {
        m_room_num = room_num;
    }
    int get_room_num()
    {
        return m_room_num;
    }    
    int  get_table_id()
    {
        return m_table_id;
    }

	int get_high_bit(int today_result)
    {
        char str[100] = "0";
		sprintf(str, "%d", today_result);
		int temp = str[0] - '0';
		return temp;
    }

    int get_max_player();
    int get_player_count(); 

	int get_robot_count(); 
	int get_real_count();

    int get_max_count();
    int get_play_count();

    int get_room_id();
    int get_user_chair(_uint64 uid);    

    int is_full();
    int on_user_enter(IUser *pUser, bool is_observe=false, bool is_change_table=false, 
		bool is_create=false, bool is_quick=false); 
    int on_user_action(IUser *pUser, _uint8 type, bool is_patch=false); /* 行为：准备 */
    int on_user_leave(IUser *pUser);
    int on_user_offline(IUser *pUser, bool is_leave=false);
	int on_user_stanup(IUser *pUser);
	int on_user_change_table_standup(IUser *pUser);

	virtual void user_action(IUser *pUser, _uint8 type) { on_user_action(pUser, type); };

    bool is_playing()
    {
        return m_playing;
    }    

    /* 设置房主 */
    void set_owner(IUser *pUser);
	void set_owner(int uid);
    _uint64 get_owner_uid()
    {
        return m_owner;
    }
    _uint32 get_owner_ver()
    {
        return m_version;
    }

	const char* get_owner_nickname()
	{
		return m_owner_name;
	}

	VECOBSERVE* get_observe_list()
	{
		return ObserveManager::GetInstance()->get_observe_list();
	}

    /* 设置桌子游戏规则 */
    //void set_rule(const void *pstTableRule, int rule_len = 0);   

    IUser* create_user(_tint64 uid);
    /**
     *  @brief: 获得用户指针
     *  @userID: IN 用户ID
     *  @return: 返回用户指针，没有用户则返回NULL
    **/
    IUser *get_user_from_uid(_tint64 userID);

    /**
     *  @brief: 获得用户指针
     *  @chairID: IN 用户椅子ID
     *  @return: 返回用户指针，没有则返回NULL
    **/
    IUser *get_user_from_chair(int chairID);

    /**
     *  @brief: 获得椅子上用户ID
     *  @chairID: IN 用户椅子ID
     *  @return: 返回用户ID
    **/
    _uint64 get_uid_from_chair(int chairID);

    /**
     *  @brief: 设置定时器
     *  @timerID: IN 定时器ID
     *  @timeout_msec: IN 定时器超时时间（单位毫秒）
     *  @param: IN 定时器参数
     *  @repeat: 执行次数
     *  @return: 成功返回true, 失败返回false
    **/
    bool set_timer(_uint8 time_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat = 1);

    /**
     *  @brief: 关闭定时器
     *  @timerID: IN 定时器ID
     *  @return: 成功返回true, 失败返回false
    **/
    bool kill_timer(_uint8 time_id);

    /**
     *  @brief: 发送游戏数据
     *  @pUsr: IN 用户指针
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 发送数据
     *  @size: IN 数据长度
     *  @return: 发送成功返回true, 失败返回false
    **/
    bool send(IUser * pUser, _uint8 mcmd, _uint8 scmd, const void * pData, int size);

    /**
     *  @brief: 发送游戏数据
     *  @pUsr: IN 用户指针
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 发送数据
     *  @return: 发送成功返回true, 失败返回false
    **/
    bool send(IUser * pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);

    /**
     *  @brief: 发送游戏数据
     *  @chairID: IN 用户椅子ID
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 发送数据
     *  @size: IN 数据长度
     *  @return: 发送成功返回true, 失败返回false
    **/
    bool send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, const void * pData, int size);

    /**
     *  @brief: 发送游戏数据
     *  @chairID: IN 用户椅子ID
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 发送数据
     *  @return: 发送成功返回true, 失败返回false
    **/
    bool send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);    
    bool send(socket_id sid, const string &str);
    /**
     *  @brief: 发送用户广播
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 发送数据
     *  @size: IN 数据长度
     *  @return: 成功返回true, 失败返回false
    **/
    bool send_user_batch(_uint8 mcmd, _uint8 scmd, const void * pdata, int size);

    /**
     *  @brief: 发送用户广播
     *  @mcmd: IN 主命令码
     *  @scmd: IN 子命令码
     *  @pData: IN 发送数据
     *  @return: 成功返回true, 失败返回false
    **/
    bool send_user_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);   
    
    
    /**
     *  @brief: 游戏开始    
     *  @return: 成功返回true, 失败返回false
    **/
    bool on_start_game();

    /**
     *  @brief: 游戏结束     
     *  @return: 成功返回true, 失败返回false
    **/
    bool on_end_game();

    /* @bref 强制结束游戏
     * @return 0 成功 !0 失败
    */
    int on_forced_end(int uid);
	
	void on_user_game_info(IUser *pUser);

    void broadcast_user_info(IUser *pUser, bool is_start=false);

	void broadcat_all_users_info();

	bool is_free_status();

	void send_common_msg(_uint8 chair_id, const char* msg, int len, int type, int tips_type=cmd_room::COMM_TIPS_TEXT);

    /* @bref: 交换两个玩家椅子 
     * @param chair_a A玩家椅子
     * @param chair_b B玩家椅子
     * @return 0成功 !0失败
    */ 
    int swap_chair(_uint8 chair_a, _uint8 chair_b);

	//游戏写金币
	void end_write_result(end_result result);
	//游戏写钻石
	void end_write_diamond_result(end_result result);
	//游戏结束后加钻石---目前只有切磋场用到
	void end_write_add_diamond_result(end_result result);

	/* 获取桌子规则描述 */
	const char* get_table_rule() {return m_table_rule.c_str();}

	/* 设置桌子规则描述 */
	void set_table_rule(const char* rule){m_table_rule = rule;};

	int get_pay_mode();
	void play_game_end(MAPSCORE &inMapScore);

	virtual void set_playing_method(int nPlayMethod);
	virtual int get_playing_method();
	virtual void set_default_score(int score) {m_nDefaultScore = score;}
	virtual int get_default_score(){ return m_nDefaultScore;}
	virtual void set_avatar_switch(int switch_data) { m_avatar_switch = switch_data; };


	virtual void update_super_control_info(const char* key, const char * data, int len) {}
	virtual void query_room_gold_statistic(int gameid, int nodeid) {}       //查询房间输赢
	virtual _tint64 get_room_gold_statistic(int gameid, int nodeid) { return 0;  }      //获取房间输赢
	virtual void update_game_config(const char *op_type, _tint64 data);

	virtual _uint64 get_game_config_special(const char *op_type);
	virtual int get_curr_room_status();                //获取当前房间状态         (0运行状态     1维护状态)
	void force_free_table();					//强制解散桌子


	virtual void send_direct_tips(_uint8 chairid);

public:
	/* 每局写流水 */
	virtual void write_versus_bill(const char * data, int len);
	//写三方投付
	virtual bool write_transfer_inout(transfer_inout inout);
	//统计真实玩家房间输赢
	virtual void write_room_gold_statistic(int gold, int type_id); 
	//投递投付结果
	virtual bool on_post_transfer_inout(transfer_inout_result_info inout_result);

    /* 写录像流水 */
    void write_video_room(const char* data);
	/* 每一局结束 */
    void write_video_bill(const char *data); 

	//写用户金币流水
	void write_gold_bill(IUser * pUser, int gold, int type_id, int lose_rate=0, bool agent=true);
	void write_gold_bill(int uid, int gold, _uint64 now_gold, int client_id, const char version[16], int type_id, int lose_rate=0, bool agent=true);

    //写普通房下注金币流水
    void write_normal_room_bet_gold_bill(IUser * pUser, int gold, int type_id, int lose_rate=0);
	//写金币对局流水
	void write_gold_versus(end_result result, int now_gold[MAX_TABLE_USER_COUNT]);

	//写玩法用户信息
	void write_user_info(const char * data, int len);
	//写玩法信息
	void write_game_rule(const char * data, int len);
	//写战绩局数信息
	void write_versus_relation(const char * data, int len);

public:
	virtual void game_db_push_data(const char* type, const char *data, int len);
public:
	//配牌
	void patch_card(const char * data, int len) { m_game_com->set_patch_card(data, len); }
	//修改牌堆数据
	void patch_table_card(const char * data, int len){ m_game_com->set_patch_table_card(data, len); }
	
public:
    /**
     *  @brief: 初始化数据
     *  @return: 成功返回0, 失败返回!0
    **/
    int init(const void * rule, int rule_len = 0, int is_web=0);   

    /**
     *  @brief: 设置桌子ID
     *  @tableID: IN 桌子ID
     *  @return: 无返回值
    **/
    void set_table_id(int table_id);
    
    void clear();   /* 分配时调用 */
    
    /**
     *  @brief: 添加用户到桌子
     *  @userID: IN 用户ID
     *  @return: 成功返回椅子ID, 失败返回0
    **/
    int add_user(_tint64 userID, char chChairID = -1);
	
	void write_user_roominfo(IUser *puser, int type);

	//强制用户
	virtual void force_user_action(int chair_id, int action);
	virtual const char* get_game_web_rule(int nodeid);

public:    
    void on_time_out(_uint8 timer_id, _uint64 param);

    /**
     *  @brief: 桌子定时器处理函数
     *  @return: 无返回值
    **/
    static FTYPE(void) on_timer(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);
	static FTYPE(void) on_robot_timer(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);

	bool check_all_robot();

	void check_robot_leave();

	//改变房主信息
	void change_owner_info(_uint8 chairid);
	//踢出用户
	void tick_user(_uint8 chairid);
	//站起用户
	void standup_user(_uint8 chairid);

	int	get_end_game_time()	{return m_end_game_time;}

	bool check_user_bankupt(int uid, bool is_check_tiyan = false, bool is_check_ready = false);

public:

	int get_next_id(int nId);

	void _free_chair(_uint8 chair_id);

private:
    int on_user_reconn(IUser *pUser);    
   
	bool all_player_ready(); 

private:
    /**
     *  @brief: 重置数据
     *  @return: 无返回值
    **/
    void _reset_data();


	//发送GPS定位信息
	void send_gps_info();

private:
	//解散返钻
	void free_diamond(int code);
	//解散返金币
	void free_gold(int code);

private:
	bool							m_ready_start;
	bool							m_bankup_check;
	bool							m_btiyan;

private:
    _uint8                          m_need_free;

    CDLLHelper<BaseGameComponent>   m_game_dll;
    BaseGameComponent              *m_game_com;                                             /* 游戏组件 */
	stPrivateUserLimit				m_private_limit;

    /* 临时数据：仅限内部同步接口使用 */
    IUser                          *m_puser;
    const void                     *m_recv_data;
    int                             m_recv_len;
    
	int								m_start_time;											/* 比赛开始时间 */
    _uint32                         m_room_num;                                             /* 房间号 */

    bool                            m_playing;                                              /* 游戏标记 */
    int                             m_table_id;                                             /* 桌子号 */
	char							m_owner_name[64];										
    _uint32                         m_owner;                                                /* 房主 */
    _uint32                         m_version;                                              /* 房间版本号 */
	_uint32							m_cost_card;											/* 消耗的房卡 */
    
    int                             m_max_player;                                           /* 最多人数 */
    int                             m_player_count;                                         /* 已有人数 */
    _uint64                         m_game_users[MAX_GAME_PLAY_USER_EX];                    /* 椅子上对应的用户id */
	int								m_play_count;											/* 完成局数 */
	string                          m_table_rule;										    /* 桌子规则描述 */
	int								m_node_id;												/* 桌子对应节点ID */
	stGoldLimitNodeInfo				m_stGoldNodeInfo;										/* 金币房节点配置 */

	// GPS相关 add by hsl on 3/8/2017
	CLocationLogic					m_gpsLogic;												/* GPS相关数据保存及逻辑处理 */
	int								m_nPlayMethod;											/* 玩法 */
	int								m_nDefaultScore;										/* 底分 */
	_tint64							m_n64CreateTime;										/* 桌子创建时间 */
	int								m_avatar_switch;										/* 互动表情开关 */

	int								m_end_game_time;										/* 记录游戏结束时间 */
};

#endif

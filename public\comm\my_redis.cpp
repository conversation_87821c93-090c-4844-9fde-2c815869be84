#include "my_redis.h"
#include "log_manager.h"
#include <algorithm>
#include "comm_fun.h"
#include "make_pptr.h"
myredis::myredis()
{
	m_ptr = NULL;
	memset(m_pass_word, 0, sizeof(m_pass_word));

	//sprintf(m_pass_word, "%s", "bvNW+Fcw7jvIys-");
	//sprintf(m_redis_ip, "%s", "*************");
	//m_port = 6379;
}

myredis::~myredis()
{
	free(m_ptr);
	m_ptr = NULL;
}

void myredis::init(const char *ip, int len, int port, char *pass_word, int pass_len)
{ 
	memcpy(m_redis_ip, ip, len); 
	memcpy(m_pass_word, pass_word, pass_len);
	m_port = port;
}

//????????????
bool myredis::is_exits_data_by_uid(_uint64 uid, int db_type, const char *op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	int ret = redis->cmd_hexists(key, op_type);
	return (ret == 1);
}

_tint64 myredis::get_data_by_uid(_uint64 uid, int db_type, const char *op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	char *ret = redis->cmd_hget(key, op_type);
	if (ret)
	{
		_tint64 data= atoll(ret);
		MY_LOG_DEBUG("redis get data by uid. uid:%lld db_type:%d op_type:%s data:%lld", uid, db_type, op_type, data);
		return data;
	}
	return 0;
}

_tint64 myredis::get_gold_by_uid(_uint64 uid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	char *ret = redis->cmd_hget(key, CUR_GOLD);
	if (ret)
	{
		_tint64 data = atoll(ret);
		MY_LOG_DEBUG("get_gold_by_uid. uid:%lld data:%lld", uid, data);
		return data;
	}
	return -1;
}

_tint64 myredis::get_data_by_uid_optime(_uint64 uid, int db_type, const char *op_type, _tint64& optime)
{
	optime = 0;
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	char *ret = redis->cmd_hget(key, op_type);
	if (ret)
	{
		_tint64 data = atoll(ret);
		const char*p = strchr(ret,'|');
		if (p != NULL)
		{
			optime = atoll(p + 1);
		}
		MY_LOG_DEBUG("redis get_data_by_uid_optime. uid:%lld db_type:%d op_type:%s data:%lld optime:%lld", uid, db_type, op_type, data, optime);
		return data;
	}
	return 0;
}

//?????????��????
bool myredis::get_all_uid_info(_uint64 uid, UidRedisInfo &info)
{
	MY_LOG_DEBUG("get_all_uid_info user_id(%llu)", uid);

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	memset(&info, 0, sizeof(UidRedisInfo));
	info.gold =  get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	info.user_type = get_data_by_uid(uid, DB_COMM, CUR_USER_TYPE);
	info.uid = uid;
	return true;
}

char* myredis::get_data_by_uid_for_string(_uint64 uid, int db_type, const char *op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return NULL;
	}
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	
	char *ret = redis->cmd_hget(key, op_type);
	if (ret)
	{ 
		MY_LOG_DEBUG("get_data_by_uid_for_string key:%s, ret:%s  op_type:%s", 
			key, 
			ret,
			op_type);

		return ret;
	}
	
	return "";
}

void myredis::get_data_by_uid_for_s_string(_uint64 uid, int db_type, const char *op_type, string &s)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	s = "";
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);

	char *ret = redis->cmd_hget(key, op_type);
	if (ret != NULL && strlen(ret) > 0)
		s = ret;
}

//??????????TOKEN
char* myredis::get_token_by_uid(_uint64 uid)
{
	char key[64] = { 0 };
	sprintf(key, "%s:%lld", CUR_UNIQUE_STRING, uid);

	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return NULL;
	}
	return redis->cmd_get(key);
}

bool myredis::update_data_by_uid(_uint64 uid, int db_type, const char *op_type, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	char key[32] = { 0 };
	sprintf(key, "%lld", uid);

	_tint64 end_data = 0;
	char *rets = redis->cmd_hincrby(key, op_type, data);

	if (rets)
	{
		end_data = atoll(rets);
	}
	
	MY_LOG_DEBUG("redis update data by uid. uid:%lld db_type:%d op_type:%s data:%lld, end_data:%lld .", uid, db_type, op_type, data, end_data);
	return true;
}

//??????????????(??)
void myredis::set_expire_time(int db_type, const char *op_type, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	redis->cmd_expire(op_type, data);
}

//???????ID???
bool myredis::get_auto_uid(int begin_id, int &result_uid)
{
	//?????????ID??????????REDIS????????????????????
	if (begin_id == 0)
	{
		result_uid = 0;
		return true;
	}
	
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get_auto_uid instance is fail.");
		result_uid = 0;
		return false;
	}

	char* ret = redis->cmd_incr(CUR_AUTO_UID);
	if (ret == NULL || strlen(ret) == 0)
	{
		result_uid = 0;
		return false;
	}
	
	int now_uid = atoi(ret);
	if (now_uid < begin_id)
	{
		char v[32] = { 0 };
		sprintf(v, "%d", begin_id);
		redis->cmd_set(CUR_AUTO_UID, v);
		result_uid = begin_id;
	}
	else
	{
		result_uid = now_uid;
	}
	return true;
}

//?????????????
_tint64 myredis::get_gold_lock(int uid)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return GOLD_LOCK_ING;
	}
	char key[32] = { 0 };
	sprintf(key, "%d", uid);
	char* ret = redis->cmd_hincrby(key, CUR_GOLD_LOCK, 1);
	if (ret == NULL)
		return GOLD_LOCK_ING;

	return atoll(ret);
}

//?????????????
void myredis::release_gold_lock(int uid)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char key[32] = { 0 };
	sprintf(key, "%d", uid);
	char val[32] = { 0 };
	sprintf(val, "%d", 0);
	redis->cmd_hset(key, CUR_GOLD_LOCK, val);
}

//???��??????
void myredis::set_stock_info(int gameid, int nodeid, stStockInfo info)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char temp_info[256] = { 0 };
	memcpy(temp_info, &info, sizeof(info));

	int bulk_len = 0;
	char key_val[CUR_STOCK_KEY_COUNT*2][128]   = { 0 };
	char* str_data[CUR_STOCK_KEY_COUNT*2 + 10] = { 0 };
	for (int i = 0; i < CUR_STOCK_KEY_COUNT; i++)
	{
		//KEY
		sprintf(key_val[bulk_len], "%s", CUR_STOCK_STR[i]);
		str_data[bulk_len] = key_val[bulk_len];
		bulk_len++;

		//VALUE
		_tint64 data_num = 0;
		memcpy(&data_num, temp_info + i * sizeof(_tint64), sizeof(_tint64));
		sprintf(key_val[bulk_len], "%lld", data_num);

		str_data[bulk_len] = key_val[bulk_len];
		bulk_len++;
	}
	str_data[bulk_len] = NULL;

	char key[32] = { 0 };
	sprintf(key, CUR_STOCK, gameid, nodeid);
	redis->cmd_hmset(key, (const char**)str_data);
}

//??????????
void myredis::get_stock_info(int gameid, int nodeid, stStockInfo &info)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char key[32] = { 0 };
	sprintf(key, CUR_STOCK, gameid, nodeid);

	_uint64 v_value = 0;
	int len = redis->cmd_hgetall(key, &m_ptr);
	for (int i = 0; i < len; )
	{
		v_value = atoi(m_ptr[i+1].bulk);

		if (strcmp(CUR_STOCK_TAX, m_ptr[i].bulk) == 0)  info.tax = v_value;
		else
		if (strcmp(CUR_STOCK_ACTUAL_TAX, m_ptr[i].bulk) == 0) info.actual_tax = v_value;
		else
		if (strcmp(CUR_STOCK_EXTRACT, m_ptr[i].bulk) == 0) info.pool_extract = v_value;
		else
		if (strcmp(CUR_STOCK_ACTUAL_POOL, m_ptr[i].bulk) == 0) info.actual_pool = v_value;
		else
		if (strcmp(CUR_STOCK_POOL_RATE, m_ptr[i].bulk) == 0) info.pool_rate = v_value;
		else
		if (strcmp(CUR_STOCK_MIN_TOUCH, m_ptr[i].bulk) == 0) info.min_touch = v_value;
		else
		if (strcmp(CUR_ACTUAL_NUM, m_ptr[i].bulk) == 0) info.actual_num = v_value;
		else
		if (strcmp(CUR_ACTUAL_STOCK, m_ptr[i].bulk) == 0) info.actual_stock = v_value;
		else
		if (strcmp(CUR_STOCK_MIN, m_ptr[i].bulk) == 0) info.stock_min = v_value;
		else
		if (strcmp(CUR_STOCK_MAX, m_ptr[i].bulk) == 0) info.stock_max = v_value;
		else
		if (strcmp(CUR_STOCK_SWITCH, m_ptr[i].bulk) == 0) info.stock_switch = v_value;
		else
		if (strcmp(CUR_POOL_MAX_LOSE, m_ptr[i].bulk) == 0) info.stock_pool_lose = v_value;

		i += 2;
		if (i >= len )
			break;
	}
}

//??????????
void myredis::set_actual_data(int gameid, int node,int data, const char *op_type)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char key[32] = { 0 };
	sprintf(key, CUR_STOCK, gameid, node);

	char val[32] = { 0 };
	sprintf(val, "%d", data);
	redis->cmd_hset(key, op_type, val);
}

//?????????
int myredis::get_actual_data(int gameid, int nodeid, const char *op_type)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK, gameid, nodeid);
	char *ret = redis->cmd_hget(key, op_type);
	if (ret)
	{
		_tint64 data= atol(ret);
		return data;
	}	
	return 0;
}

//?????????
char* myredis::get_stock_config(int plat, int gameid, int nodeid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK_CFG, plat, gameid, nodeid);
	return redis->cmd_get(key);
}

//???????????
_tint64 myredis::get_update_stock_time(int plat, int gameid, int nodeid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK_CFG_TIME, plat, gameid, nodeid);
	char *ret = redis->cmd_get(key);
	if (ret)
	{
		_tint64 data = atol(ret);
		return data;
	}
	return 0;
}

//??????
_tint64 myredis::get_stock_value(int plat, int gameid, int nodeid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK_VALUE, plat, gameid, nodeid);
	char *ret = redis->cmd_get(key);
	if (ret)
	{
		_tint64 data = atol(ret);
		return data;
	}
	return 0;
}

//??????��????
void myredis::set_imp_stock_value(int plat, int gameid, int nodeid, _tint64 value)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK_IMP_VALUE, plat, gameid, nodeid);
	char val[32] = { 0 };
	sprintf(val, "%lld", value);
	redis->cmd_set(key, val);
}

//?????��???
_tint64 myredis::get_imp_stock_value(int plat, int gameid, int nodeid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK_IMP_VALUE, plat, gameid, nodeid);
	char *ret = redis->cmd_get(key);
	if (ret)
	{
		_tint64 data = atol(ret);
		return data;
	}
	return 0;
}

//???????
_tint64 myredis::get_round_id(int gameid, int nodeid, string ext)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[128] = { 0 };
	sprintf(key, CUR_ROUND_ID, gameid, nodeid, ext.c_str());
	char* ret = redis->cmd_incr(key);

	if (ret != NULL && strlen(ret) != 0)
	{
		_tint64 data = atol(ret);
		return data;
	}
	return 0;
}

//????????????
int myredis::set_round_id_expire(int gameid, int nodeid, int left_time, string ext)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[128] = { 0 };
	sprintf(key, CUR_ROUND_ID, gameid, nodeid, ext.c_str());
	redis->cmd_expire(key, left_time);
	return 0;
}






//?????????????????
string myredis::get_video_round_info(int gameid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return "";
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_ID, gameid);
	char* ret = redis->cmd_get(key);
	if (ret != NULL && strlen(ret) != 0)
		return ret;
	
	return "";
}

//?????????????
string myredis::get_video_game_result(int gameid, string roundid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return "";
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_RESULT, gameid, roundid.c_str());
	char* ret = redis->cmd_get(key);
	if (ret != NULL && strlen(ret) != 0)
		return ret;

	return "";
}

//??????????????
void myredis::set_video_game_status(int gameid, string roundid, int status)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_STATUS, gameid, roundid.c_str());
	char value[32] = { 0 };
	sprintf(value, "%d", status);
	redis->cmd_set(key, value);
	redis->cmd_expire(key, 3 * 86400);
}

//??????????????
int myredis::get_video_game_status(int gameid, string roundid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_STATUS, gameid, roundid.c_str());
	char* ret = redis->cmd_get(key);
	if (ret != NULL && strlen(ret) != 0)
		return atoi(ret);

	return 0;
}

//?????????????????
void myredis::set_video_game_start_time(int gameid, string roundid, int start_time)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_START_TIME, gameid, roundid.c_str());
	char value[32] = { 0 };
	sprintf(value, "%d", start_time);
	redis->cmd_set(key, value);
	redis->cmd_expire(key, 3 * 86400);
	MY_LOG_DEBUG("set_video_game_start_time key:%s value:%s", key, value);
}

//??????????????????
void myredis::set_video_game_end_time(int gameid, string roundid, int end_time)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_END_TIME, gameid, roundid.c_str());
	char value[32] = { 0 };
	sprintf(value, "%d", end_time);
	redis->cmd_set(key, value);
	redis->cmd_expire(key, 3 * 86400);
	MY_LOG_DEBUG("set_video_game_end_time key:%s value:%s", key, value);
}

//?????????????????
int myredis::get_video_game_start_time(int gameid, string roundid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_START_TIME, gameid, roundid.c_str());
	char* ret = redis->cmd_get(key);
	if (ret != NULL && strlen(ret) != 0)
	{
		MY_LOG_DEBUG("get_video_game_start_time key:%s ret:%s", key, ret);
		return atoi(ret);
	}
		
	return 0;
}

//??????????????????
int myredis::get_video_game_end_time(int gameid, string roundid)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[128] = { 0 };
	sprintf(key, LOTTERY_PERIOD_END_TIME, gameid, roundid.c_str());
	char* ret = redis->cmd_get(key);
	if (ret != NULL && strlen(ret) != 0)
	{
		MY_LOG_DEBUG("get_video_game_end_time key:%s ret:%s", key, ret);
		return atoi(ret);
	}
		
	return 0;
}







//???gm???????????
char* myredis::get_gm_now_round(int gameid, int nodeid, string ext)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_STATUS, gameid, nodeid, ext.c_str());
	return redis->cmd_get(key);
}

//???gm?????????????
char* myredis::get_gm_control_info(int gameid, int nodeid, string ext)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_INFO, gameid, nodeid, ext.c_str());
	return redis->cmd_get(key);

}

//????gm???????
bool myredis::set_gm_control_lock_info(int gameid, int nodeid, int left_time, string ext /*= ""*/)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_LOCK, gameid, nodeid, ext.c_str());
	bool result = redis->cmd_setnx(key, "1");
	if (result)
	{
		return redis->cmd_expire(key, left_time);
	}
	else
	{
		return false;
	}	
}

// ???gm?????????
bool myredis::del_gm_control_lock_info(int gameid, int nodeid, string ext /*= ""*/)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_LOCK, gameid, nodeid, ext.c_str());
	int ret = redis->cmd_del(key);
	if (ret <= 0)
	{
		MY_LOG_ERROR("redis cmd_del() failed !! key:%s", key);
		return false;
	}
	return true;
}

// ?????????????????????
bool myredis::add_user_bet_info_set(string rid, int uid, _tint64 total_score, int left_time)
{
	if (rid.empty())
	{
		MY_LOG_ERROR("redis add_user_bet_info_set() failed !! rid is empty.");
		return false;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[128] = { 0 };
	sprintf(key, CUR_GM_CONTROL_ZSET, rid.c_str());

	char score[32] = { 0 };
	sprintf(score, "%lld", total_score);

	char members[32] = { 0 };
	sprintf(members, "%d", uid);

	int ret = redis->cmd_zadd(key, score, members);
	if (ret <= 0)
	{
		MY_LOG_ERROR("redis cmd_del() failed !! key:%s", key);
		return false;
	}

	redis->cmd_expire(key, left_time);
	return true;
}

// ???????????????????
bool myredis::zrem_user_bet_info_set(string rid, int uid)
{
	if (rid.empty())
	{
		MY_LOG_ERROR("redis add_user_bet_info_set() failed !! rid is empty.");
		return false;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[128] = { 0 };
	sprintf(key, CUR_GM_CONTROL_ZSET, rid.c_str());

	char members[32] = { 0 };
	sprintf(members, "%d", uid);
	redis->cmd_zrem(key, members);

	return true;
}

// ??????????????
void myredis::set_round_user_bet_info(const char* key, char** data_str, int left_time)
{
	if (!key || !data_str)
	{
		MY_LOG_ERROR("invalid key.");
		return;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

//	MY_LOG_DEBUG("set_round_user_bet_info cmd_hmset key:%s data_str:[%s %s]", key, data_str[0], data_str[1]);

	redis->cmd_hmset(key, (const char**)data_str);

//	MY_LOG_DEBUG("set_round_user_bet_info cmd_expire key:%s", key);

	redis->cmd_expire(key, left_time);
}

// ???????????????????
void myredis::set_round_result_info(string rid, const char* value, int left_time)
{
	if (rid.empty() || NULL == value)
	{
		MY_LOG_ERROR("redis set_round_result_info() failed !! rid is empty.");
		return;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char key[128] = { 0 };
	sprintf(key, CUR_GM_CONTROL_RESULT, rid.c_str());
	redis->cmd_set(key, value);
	redis->cmd_expire(key, left_time);
}

//???????????
void myredis::set_now_round_info(int gameid, int nodeid, string info, string ext)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_STATUS, gameid, nodeid, ext.c_str());
	redis->cmd_set(key, info.c_str());
}

//?????????????
void myredis::set_control_round_info(int gameid, int nodeid, string info, string ext)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_INFO, gameid, nodeid, ext.c_str());
	MY_LOG_DEBUG("set_control_round_info = %s", info.c_str());
	MY_LOG_DEBUG("set_control_round_info len= %d", info.length());
	if (!redis->cmd_set(key, info.c_str()))
	{
		MY_LOG_ERROR("redis cmd_set failed");
	}
}

/////////////////////////////////////????????/////////////////////////////////////////////////////////////
void myredis::get_control_round_info(int gameid, int nodeid, string ext, int nstart, int nstop, vector<string>& vec_str)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get_user_history_game_record_data get redis instance is fail.");
		return;
	}
	vec_str.clear();
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_INFO, gameid, nodeid, ext.c_str());
	bulk_t* vallst;
	int count = redis->cmd_lrange(key, nstart, nstop, &vallst);
	for (int i = 0; i < count; ++i)
	{
		string strData = vallst->bulk;
		if (strData.length() > 0)
		{
			vec_str.push_back(strData);
		}
		else
		{
			MY_LOG_WARN("get_user_history_game_record_data illegal data is null");
		}
		vallst++;
	}
}

void myredis::add_control_round_info(int gameid, int nodeid, string ext, const vector<string>& vec_str, const int& length)
{
	//for (size_t i = 0; i < vec_str.size(); i++)
	//{
	//	MY_LOG_DEBUG("add_control_round_info i:%d, size%d,str:%s", i, vec_str.size(),vec_str[i].c_str());
	//}
	
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("add_user_history_game_record_data get redis instance is fail.");
		return;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_INFO, gameid, nodeid, ext.c_str());
	MY_LOG_DEBUG("add_control_round_info 2");

	//const char* szbuf[] = { "1","2","3", "4", "5"};
	//const char** szbuf = new char* [length];
	MY_LOG_DEBUG("add_control_round_info 3");

	//for (int i = 0; i < vec_str.size() && i < 20; i++)
	//{
	//	szbuf[i] = vec_str[i].c_str();
	//	MY_LOG_DEBUG("add_control_round_info i:%d, str:%s", i, szbuf[i]);
	//}
	redis->cmd_del(key);
	if (redis->is_error())
	{
		MY_LOG_WARN("add_control_round_info(%s)", redis->get_error_msg());
		return;
	}
	MY_LOG_DEBUG("add_control_round_info 4");
	const char* szbuf[] = { "1","2","3", "4", "5" };

	redis->cmd_lpush(key, szbuf);
	if (redis->is_error())
	{
		MY_LOG_WARN("add_control_round_info(%s)", redis->get_error_msg());
		return;
	}
	MY_LOG_DEBUG("add_control_round_info 5");

}

void myredis::update_control_round_info(int gameid, int nodeid, string ext, const string& vec_str)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("add_user_history_game_record_data get redis instance is fail.");
		return;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_GM_CONTROL_INFO, gameid, nodeid, ext.c_str());

	redis->cmd_lset(key, 0, vec_str.c_str());
	if (redis->is_error())
	{
		MY_LOG_WARN("update_control_round_info(%s)", redis->get_error_msg());
		return;
	}
}

//////////////////////////////////////////////////////////////////////////////////////////////////

//????????
_tint64 myredis::update_stock_value(int plat, int gameid, int nodeid, int value)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	char key[32] = { 0 };
	sprintf(key, CUR_STOCK_VALUE, plat, gameid, nodeid);

	_tint64 end_data = 0;
	char *rets = redis->cmd_incrby(key, value);

	if (rets)
	{
		end_data = atoll(rets);
	}
	return end_data;
}

//???��??????????
void myredis::reset_user_times(int gameid, int nodeid, int uid, int bet)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char key[128] = { 0 };
	sprintf(key, CUR_BET_LOSE_TIMES, uid, gameid, nodeid, bet);

	char val[128] = { 0 };
	sprintf(val, "%d", 0);
	redis->cmd_set(key, val);
}

//?????????????
void myredis::update_user_times(int gameid, int nodeid, int uid, int bet, int times)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	char key[128] = { 0 };
	sprintf(key, CUR_BET_LOSE_TIMES, uid, gameid, nodeid, bet);
	redis->cmd_incrby(key, times);
}

//????????��?????
_tint64 myredis::get_user_times(int gameid, int nodeid, int uid, int bet)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char key[128] = { 0 };
	sprintf(key, CUR_BET_LOSE_TIMES, uid, gameid, nodeid, bet);
	char *ret = redis->cmd_get(key);
	if (ret)
	{
		_tint64 data = atol(ret);
		return data;
	}
	return 0;
}

//?????????????????
void myredis::set_user_temp_expire_time(const char* key, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	redis->cmd_expire(key, data);
}

//??????????????
bool myredis::set_user_temp_data_for_string(const char* key, const char* value, int len)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	return redis->cmd_set(key, value);
}

//?????????????
char* myredis::get_user_temp_data_for_string(const char* key)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return NULL;
	}
	return redis->cmd_get(key);
}

//?��????????????????
int myredis::check_exit_user_temp_data(const char* key)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	return redis->cmd_exists(key);
}

bool myredis::del_key(int db_type, const char *op_key)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("del_key get redis instance is fail.");
		return false;
	}
	int ret = redis->cmd_del(op_key);
	if (ret <= 0)
	{
		MY_LOG_ERROR("redis cmd_del() failed !! key:%s", op_key);
		return false;
	}
	return true;
}

//??????????????
bool myredis::set_user_temp_data(const char* key, _tint64 value)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char val[32] = { 0 };
	sprintf(val, "%lld", value);
	return redis->cmd_set(key, val);
}

//??????????????
bool myredis::update_user_temp_data(const char* key, _tint64 value)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	return redis->cmd_incrby(key, value);
}

//?????????????
_tint64 myredis::get_user_temp_data(const char* key)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char *ret = redis->cmd_get(key);
	if (ret)
	{
		_tint64 data = atoll(ret);
		return data;
	}
	return 0LL;
}

//???????????
bool myredis::set_data_by_uid(_uint64 uid, int db_type, const char *op_type, _tint64 data)
{
	MY_LOG_DEBUG("redis set_data_by_uid uid. uid:%lld db_type:%d op_type:%s data:%lld", uid, db_type, op_type, data);

	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	char val[32] = { 0 };
	sprintf(val, "%lld", data);
	MY_LOG_DEBUG("redis set data by uid. uid:%lld db_type:%d op_type:%s data:%lld", uid, db_type, op_type, data);
	redis->cmd_hset(key, op_type, val);
	return true;
}

bool myredis::set_data_by_uid_optime(_uint64 uid, int db_type, const char *op_type, _tint64 data, _tint64 data_time)
{
	MY_LOG_DEBUG("redis set_data_by_uid_optime uid. uid:%lld db_type:%d op_type:%s data:%lld data_time:%lld", uid, db_type, op_type, data, data_time);
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	char val[32] = { 0 };
	sprintf(val, "%lld|%lld", data, data_time);
	MY_LOG_DEBUG("redis set data by uid. uid:%lld db_type:%d op_type:%s data:%lld data_time:%lld", uid, db_type, op_type, data, data_time);
	redis->cmd_hset(key, op_type, val);
	return true;
}

//data???????+??-?? result??????????
bool myredis::safe_incr_data_by_uid(_uint64 uid, int db_type, const char *op_type, int data, _tint64& result)
{
	MY_LOG_DEBUG("redis safe_incr_data_by_uid uid. uid:%lld db_type:%d op_type:%s data:%lld", uid, db_type, op_type, data);
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	MY_LOG_DEBUG("redis safe_incr_data_by_uid (cmd_hincrby)uid:%lld db_type:%d op_type:%s data:%d", uid, db_type, op_type, data);
	char* pres = redis->cmd_hincrby(key, op_type, data);
	if (redis->is_error() || pres == NULL)
	{
		MY_LOG_ERROR("redis safe_incr_data_by_uid err %s (uid:%lld db_type:%d op_type:%s data:%d), ", redis->get_error_msg(), uid, db_type, op_type, data);
		return false;
	}
	result = atoll(pres);
	return true;
}

bool myredis::set_data_by_uid(_uint64 uid, int db_type, const char *op_type, const char* data, int len)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[32] = { 0 };
	sprintf(key, "%lld", uid);
	char val[128] = { 0 };
	snprintf(val, sizeof(val), "%s", data);
	MY_LOG_DEBUG("redis set data by uid. uid:%lld db_type:%d op_type:%s data:%s", uid, db_type, op_type, data);
	redis->cmd_hset(key, op_type, val);
	return true;
}

//??��?????????
int myredis::get_pool_data_count(const char* key, int uid)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}

	if (uid == 0)
	{
		char *ret = redis->cmd_get(key);
		if (ret)
		{
			_tint64 data= atol(ret);
			return data;
		}
	}
	else
	{
		char uid_key[32] = { 0 };
		sprintf(uid_key, "%d", uid);
		char *ret = redis->cmd_hget(uid_key, key);
		if (ret)
		{
			_tint64 data= atol(ret);
			return data;
		}
	}
	return 0;
}

//???��??????
int myredis::set_pool_data_count(const char* key, int count, int uid)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}

	char val[32] = { 0 };
	sprintf(val, "%d", count);
	if (uid == 0)
	{
		return (redis->cmd_set(key, val) ? 0 : -1);
	}
	else
	{
		char uid_key[32] = { 0 };
		sprintf(uid_key, "%d", uid);
		return redis->cmd_hset(uid_key, key, val);
	}
	return 0;
}

//??????????
int myredis::update_pool_data_count(const char* key, int count, int uid)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_POOL, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	
	if (uid == 0)
	{
		redis->cmd_incrby(key, count);
	}
	else
	{
		char uid_key[32] = { 0 };
		sprintf(uid_key, "%d", uid);
		redis->cmd_hincrby(uid_key, key, count);
	}
	return 0;
}

//???????????????????????
void myredis::set_user_jetton_info(_tint64 uid, int gameId, int nodeId, vector<int> jettonIndex)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("set redis set_user_jetton_info is fail.");
		return;
	}

	//???????????
	char skey[32] = { 0 };
	sprintf(skey, "%d_%d", gameId, nodeId);

	char key[32] = { 0 };
	sprintf(key, "%lld", uid);

	char data[128] = { 0 };
	int offset = 0;
	for (int i = 0; i<jettonIndex.size(); i++)
	{
		sprintf(data + offset, "%d,", jettonIndex[i]);
		while (data[offset] != ',')
			offset++;
		offset++;
	}
	data[offset - 1] = 0;

	MY_LOG_DEBUG("redis set_user_jetton_info  key(%s), skey(%s), data(%s)", key, skey, data);
	redis->cmd_hset(key, skey, data);
}

//??????????????????????
void myredis::get_user_jetton_info(_tint64 uid, int gameId, int nodeId, vector<int> &jettonIndex)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis get_user_jetton_info is fail.");
		return;
	}

	//???????????
	char skey[32] = { 0 };
	sprintf(skey, "%d_%d", gameId, nodeId);

	char key[32] = { 0 };
	sprintf(key, "%lld", uid);

	char *ret = redis->cmd_hget(key, skey);
	if (ret)
	{
		MY_LOG_DEBUG("get redis get_user_jetton_info  key(%s)  skey(%s)  data(%s)", key, skey, ret);
		int index[5] = { 0 };
		split_string(ret, index, ",");

		for (int i = 0; i < 5; i++)
			jettonIndex.push_back(index[i]);
	}
}

bool myredis::set_super_control_info_for_string(const char* key, const char* value)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("myredis::set_super_control_info_for_string get redis instance is fail.");
		return false;
	}
	return redis->cmd_set(key, value);
}

bool myredis::set_user_comm_data(int db_type, const char *op_type, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("myredis::set_user_comm_data get redis instance is fail.");
		return false;
	}

	char val[32] = { 0 };
	sprintf(val, "%lld", data);
	redis->cmd_set(op_type, val);
	MY_LOG_DEBUG("myredis::set_user_comm_data. op_type:%s,   data:%lld  .", op_type, data);
	return true;
}

bool myredis::set_game_config_for_room(const char *op_type, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char val[32] = { 0 };
	sprintf(val, "%lld", data);
	redis->cmd_set(op_type, val);
	MY_LOG_DEBUG("myredis::set_game_config_for_room. op_type:%s,   data:%lld  .", op_type, data);
	return true;
}

int myredis::get_game_config_for_room(const char *op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	MY_LOG_DEBUG("myredis::get_game_config_for_room   op_type(%s)", op_type);
	char *ret = redis->cmd_get(op_type);
	if (ret)
	{
		MY_LOG_DEBUG("myredis::get_game_config_for_room   op_type(%s), ret(%s)", op_type, ret);
		int count = atoi(ret);
		return count;
	}
	return 0;
}

bool myredis::update_data_by_comm(int db_type, const char* op_type, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("myredis::update_data_by_comm update redis instance is fail.");
		return false;
	}

	redis->cmd_incrby(op_type,  data);
	MY_LOG_DEBUG("myredis::update_data_by_comm. op_type:%s,   data:%lld  .", op_type, data);
	
	return true;
}

_tint64 myredis::get_user_comm_data(int db_type, const char *op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("myredis::get_user_comm_data get redis instance is fail.");
		return false;
	}

	char *ret = redis->cmd_get(op_type);
	if (ret)
	{
		MY_LOG_DEBUG("myredis::get_user_comm_data   op_type(%s), ret(%s)", op_type, ret);
		_tint64 data = atoll(ret);
		return data;
	}
	return 0;
}

//?????????????
void myredis::save_user_bet_model(const char* str, int len, int type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_LUCKY_USER, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("save_user_bet_model redis instance is fail.");
		return;
	}

	char *ret = NULL;
	const char* key_str = NULL;
	const char* key_index_str = NULL;
	switch (type)
	{
	case bet_s: 
		ret = redis->cmd_get(COPY_S_BET_INDEX); 
		key_str = COPY_USER_S_BET;
		key_index_str = COPY_S_BET_INDEX;
		break;
	case bet_m: 
		ret = redis->cmd_get(COPY_M_BET_INDEX);  
		key_str = COPY_USER_M_BET;
		key_index_str = COPY_M_BET_INDEX;
		break;
	case bet_b: 
		ret = redis->cmd_get(COPY_B_BET_INDEX);  
		key_str = COPY_USER_B_BET;
		key_index_str = COPY_B_BET_INDEX;
		break;
	default:
		break;
	}
	
	int control_index = 0;
	if (ret)
		control_index = atoi(ret);

	char data_key[32] = { 0 };
	sprintf(data_key, key_str, control_index);
	redis->cmd_set(data_key, str);

	char val[32] = { 0 };
	sprintf(val, "%d", control_index+1);
	redis->cmd_set(key_index_str, val);
}

//????????????????
char* myredis::get_user_bet_model(int type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_LUCKY_USER, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("save_user_bet_model redis instance is fail.");
		return NULL;
	}

	char *ret = NULL;
	const char* key_str = NULL;
	switch (type)
	{
	case bet_s:
		ret = redis->cmd_get(COPY_S_BET_INDEX);
		key_str = COPY_USER_S_BET;
		break;
	case bet_m:
		ret = redis->cmd_get(COPY_M_BET_INDEX);
		key_str = COPY_USER_M_BET;
		break;
	case bet_b:
		ret = redis->cmd_get(COPY_B_BET_INDEX);
		key_str = COPY_USER_B_BET;
		break;
	default:
		break;
	}

	int control_index = 0;
	if (ret)
		control_index = atoi(ret);

	//?????????????????
	int choice_index = 0;
	if (control_index > 0)
		choice_index = rand() % control_index;
	
	char data_key[32] = { 0 };
	sprintf(data_key, key_str, choice_index);
	return redis->cmd_get(data_key);
}

void myredis::add_user_history_game_record_data(const char* key, const char* value, int nLimitLen)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("add_user_history_game_record_data get redis instance is fail.");
		return;
	}
	char szbuf[128] = { 0 };
	char* pbf = szbuf;
	__sprintf(szbuf, sizeof(szbuf), CUR_GAME_HISTORY_RECORD_DATA, value);
	redis->cmd_lpush(key, pbf);
	if (redis->is_error())
	{
		MY_LOG_WARN("add_world_charge_record(%s)", redis->get_error_msg());
		return;
	}
	//??????????????
	int len = redis->cmd_llen(key);
	if (len > nLimitLen)
	{
		//?????????????????
		redis->cmd_rpop(key);
	}
}


void myredis::get_user_history_game_record_data(const char*key, int nstart, int nstop, vector<string> &vec_str)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get_user_history_game_record_data get redis instance is fail.");
		return;
	}
	vec_str.clear();
	bulk_t *vallst;
	int count = redis->cmd_lrange(key, nstart, nstop, &vallst);
	for (int i = 0; i < count; ++i)
	{  
		string strData = vallst->bulk;
		if (strData.length()>0)
		{
			vec_str.push_back(strData);
		}
		else
		{
			MY_LOG_WARN("get_user_history_game_record_data illegal data is null");
		}
		vallst++;
	}
}

int myredis::get_user_history_game_record_data_num(const char*key)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get_user_history_game_record_data_num get redis instance is fail.");
		return 0;
	}

	int len = redis->cmd_llen(key);
	MY_LOG_DEBUG("get_user_history_game_record_data_num len is :%d",len);
	
	return len;
}

void myredis::get_color_game_probability_data(int nodeid, stColorGameProbability& info)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_USER_DATA_RECORD, m_pass_word);
	if(NULL==redis)
	{
		MY_LOG_ERROR("get_color_game_probability_data get redis instance is fail.");
		return;
	}

	char key[64] = { 0 };
	sprintf(key, COLOR_GAME_PROBABILITY, nodeid);

	_uint64 v_value = 0;
	int len = redis->cmd_hgetall(key, &m_ptr);
	for (int i = 0; i < len; )
	{
		v_value = atoi(m_ptr[i+1].bulk);

		if (strcmp(CUR_COLOR_YELLOW, m_ptr[i].bulk) == 0)  info.color_yellow = v_value;
		else
		if (strcmp(CUR_COLOR_WHITE, m_ptr[i].bulk) == 0) info.color_white = v_value;
		else
		if (strcmp(CUR_COLOR_POWDER, m_ptr[i].bulk) == 0) info.color_powder = v_value;
		else
		if (strcmp(CUR_COLOR_BLUE, m_ptr[i].bulk) == 0) info.color_blue = v_value;
		else
		if (strcmp(CUR_COLOR_GREEN, m_ptr[i].bulk) == 0) info.color_green = v_value;
		else
		if (strcmp(CUR_COLOR_RED, m_ptr[i].bulk) == 0) info.color_red = v_value;

		i += 2;
		if (i >= len )
			break;
	}
}

bool myredis::update_color_game_probability_data(int nodeid, const char* op_type, _tint64 data)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_USER_DATA_RECORD, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("update_color_game_probability_data get redis instance is fail.");
		return false;
	}
	char key[64] = { 0 };
	sprintf(key, COLOR_GAME_PROBABILITY, nodeid);

	return redis->cmd_hincrby(key, op_type, data);
}


//??????????????
bool myredis::set_user_temp_data(const char* key, const char* op_type, _tint64 data)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char val[32] = { 0 };
	sprintf(val, "%lld", data);
	MY_LOG_DEBUG("redis set_user_temp_data. key:%s op_type:%s data:%lld", key, op_type, data);
	redis->cmd_hset(key, op_type, val);
	return true;
}

bool myredis::set_user_temp_data(const char* key, const char* op_type, const char* val)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}
	MY_LOG_DEBUG("redis set_user_temp_data. key:%s op_type:%s val:%s", key, op_type, val);
	redis->cmd_hset(key, op_type, val);
	return true;
}

// ?????????????????
_tint64 myredis::update_common_data(const char* key, int db_type, const char* op_type, _tint64 data)
{
	if (!key || !op_type)
	{
		MY_LOG_ERROR("invalid key.");
		return -1;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -2;
	}

	char* ret = redis->cmd_hincrby(key, op_type, data);

	if (ret != NULL && strlen(ret) != 0)
	{
		_tint64 data = atoll(ret);
		return data;
	}
	return 0;
}

// ???????????????
_tint64 myredis::get_common_data(const char* key, int db_type, const char* op_type)
{
	if (!key || !op_type)
	{
		MY_LOG_ERROR("invalid key.");
		return -1;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -2;
	}
	char* ret = redis->cmd_hget(key, op_type);
	if (ret)
	{
		_tint64 data = atoll(ret);
		return data;
	}
	return -3;
}

// ??????????????????
_tint64 myredis::sadd_common_data(const char* key, int db_type, const char* member)
{
	if (!key || !member)
	{
		MY_LOG_ERROR("invalid key.");
		return -1;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -2;
	}

	MY_LOG_DEBUG("sadd_common_data key:%s member:%s", key, member);

	return redis->cmd_sadd(key, member);
}


// ����Ԫ�ؼ��ϵ�ָ���ļ�����
_tint64 myredis::sadd_common_data_list(const char* key, int db_type, vector<User_list_info>& members, _tint32 data)
{
	if (!key || members.empty())
	{
		MY_LOG_ERROR("invalid key.");
		return -1;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -2;
	}

	int num_members = members.size();
	int ret = 0;
	char buff[128] = { 0 };
	for (int i = 0; i < num_members; i++)
	{
		memset(buff, 0, sizeof(buff));
		sprintf(buff, "%d,%d,%d", members[i].uid, members[i].gid, members[i].rid);
		ret = redis->cmd_sadd(key, buff);
	}

	return ret;
}

// ????????��?????��?
void myredis::get_set_data(const char* key, int db_type, vector<int>& members)
{
	if (!key)
	{
		MY_LOG_ERROR("invalid key.");
		return;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	bulk_t* vallst;
	int count = redis->cmd_smembers(key, &vallst);
	for (int i = 0; i < count; ++i)
	{
		string strData = vallst->bulk;
		if (strData.length() > 0)
		{
			members.push_back(atoi(strData.c_str()));
		}
		else
		{
			MY_LOG_WARN("get_set_data illegal data is null");
		}
		vallst++;
	}	
}

void myredis::get_set_data(const char* key, int db_type, vector<string>& members)
{
	if (!key)
	{
		MY_LOG_ERROR("invalid key.");
		return;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	bulk_t* vallst;
	int count = redis->cmd_smembers(key, &vallst);
	for (int i = 0; i < count; ++i)
	{
		string strData = vallst->bulk;
		if (strData.length() > 0)
		{
			members.push_back(strData);
		}
		else
		{
			MY_LOG_WARN("get_set_data illegal data is null");
		}
		vallst++;
	}	
}

// 删除集合中的某个元素
bool myredis::srem_set_data(const char* key, int db_type, const char* member)
{
	if (!key || !member)
	{
		MY_LOG_ERROR("invalid key.");
		return false;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	MY_LOG_DEBUG("srem_set_data key:%s member:%s", key, member);

	return redis->cmd_srem(key, member);
}

// ?????????????
void myredis::set_common_data(const char* key, int db_type, const char* op_type, _tint32 data)
{
	if (!key || !op_type)
	{
		MY_LOG_ERROR("invalid key.");
		return;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}

	string num = std::to_string(data);

	redis->cmd_hset(key, op_type, num.c_str());
}

// ?��????key????????
int myredis::check_common_exist_key(const char* key, int db_type)
{
	if (!key)
	{
		MY_LOG_ERROR("invalid key.");
		return 0;
	}

	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db_type, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return 0;
	}
	return redis->cmd_exists(key);
}

_tint64 myredis::get_user_temp_data(const char* key, const char* op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char* ret = redis->cmd_hget(key, op_type);
	if (ret)
	{
		_tint64 data = atoll(ret);
		return data;
	}
	return 0LL;
}

char* myredis::get_user_temp_data_for_string(const char* key, const char* op_type)
{
	int port = m_port;
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, port, DB_SAVE_DATA, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return "";
	}
	char* ret = redis->cmd_hget(key, op_type);
	if (ret)
		return ret;

	return "";
}


bool myredis::add_freegame_info(int uid, int score)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[128] = { 0 };
	sprintf(key, USER_lOTTERY_SET, uid);


	char score_key[32] = { 0 };
	sprintf(score_key, "%d", score);

	int ret = redis->cmd_rpush(key, score_key);
	if (ret <= 0)
	{
		MY_LOG_ERROR("redis cmd_del() failed !! key:%s", key);
		return false;
	}

	return true;
}

void myredis::get_freegame_info(int uid, vector<int>& members)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[128] = { 0 };
	sprintf(key, USER_lOTTERY_SET, uid);

	int count = redis->cmd_llen(key);
	for (int i = 0; i < count; ++i)
	{
		char *test = redis->cmd_lindex(key, i);

		if (test != nullptr)
		{
			members.push_back(atoi(test));
		}
		else
		{
			MY_LOG_WARN("get_set_data illegal data is null");
		}
	}
}

bool myredis::get_graph_from_redis(const string&key, string& ret, int& r,int db)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	int count = redis->cmd_llen(key.c_str());

	if (count <= 0)
	{
		return false;
	}
	r = getNewRand(0, count-1);

	char *test = redis->cmd_lindex(key.c_str(), r);
	if (test != nullptr)
	{
		ret = std::string(test);
	}
	else
	{
		return false;
	}
	
	return true;
}

/*
* 获取某key下，第r个图形组
*/
bool myredis::get_graph_from_redis_by_index(const string& key, string& ret, int r, int db) {
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, db, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	int count = redis->cmd_llen(key.c_str());
	if (count <= 0)
	{
		return false;
	}
	char* test = redis->cmd_lindex(key.c_str(), r % count);
	if (test != nullptr)
	{
		ret = test;
	}
	else
	{
		return false;
	}

	return true;
}

int myredis::add_freeGame_index(const string&key)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return -1;
	}
	char* ret = redis->cmd_incr(key.c_str());
	if (ret == NULL || strlen(ret) == 0)
	{
		MY_LOG_ERROR("add_freeGame_index fail.");
		return -1;
	}
	
	return atoi(ret);
}

void myredis::del_freeGame_index(const string&key)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	int ret = redis->cmd_del(key.c_str());
	if (ret <= 0)
	{
		MY_LOG_ERROR("redis cmd_del() failed !! key:%s", key.c_str());
		return;
	}
}

bool myredis::add_freegame_scatter_info(int uid, int count)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char key[128] = { 0 };
	sprintf(key, USER_SCATTER_SET, uid);


	char score_key[32] = { 0 };
	sprintf(score_key, "%d", count);

	int ret = redis->cmd_rpush(key, score_key);
	if (ret <= 0)
	{
		MY_LOG_ERROR("redis cmd_del() failed !! key:%s", key);
		return false;
	}

	return true;
}

void myredis::get_freegame_scatter_info(int uid, vector<int>& members)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return;
	}
	char key[128] = { 0 };
	sprintf(key, USER_SCATTER_SET, uid);

	int count = redis->cmd_llen(key);
	for (int i = 0; i < count; ++i)
	{
		char *test = redis->cmd_lindex(key, i);

		if (test != nullptr)
		{
			members.push_back(atoi(test));
		}
		else
		{
			MY_LOG_WARN("get_set_data illegal data is null");
		}
	}
}

char* myredis::get_str(const char* key)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_COMM, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return NULL;
	}
	return redis->cmd_get(key);
}

bool myredis::set_sss_jp_pool_info(sssPoolInfo poolInfo)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}

	char temp_info[512] = { 0 };
	memcpy(temp_info, &poolInfo, sizeof(poolInfo));

	int bulk_len = 0;
	char key_val[6 * 2][128] = { 0 };
	char* str_data[6 * 2 + 10] = { 0 };
	for (int i = 0; i < 6; i++)
	{
		//KEY
		sprintf(key_val[bulk_len], "%s", CUR_SSS_JP_POOL_STR[i]);
		str_data[bulk_len] = key_val[bulk_len];
		bulk_len++;

		//VALUE
		_tint64 data_num = 0;
		memcpy(&data_num, temp_info + i * sizeof(_tint64), sizeof(_tint64));
		sprintf(key_val[bulk_len], "%lld", data_num);

		str_data[bulk_len] = key_val[bulk_len];
		bulk_len++;
	}
	str_data[bulk_len] = NULL;
	redis->cmd_hmset(CUR_SSS_JP_POOL,(const char**)str_data);

	return true;
}
bool myredis::get_sss_jp_pool_info(sssPoolInfo& poolInfo)
{
	IRedis* redis = sRedisMgr->get_redis(m_redis_ip, m_port, DB_POOL, m_pass_word);
	if (NULL == redis)
	{
		MY_LOG_ERROR("get redis instance is fail.");
		return false;
	}


	_uint64 v_value = 0;
	int len = redis->cmd_hgetall(CUR_SSS_JP_POOL, &m_ptr);
	for (int i = 0; i < len; )
	{
		v_value = atoi(m_ptr[i + 1].bulk);

		if (strcmp(CUR_SSS_JP_POOL_F_1, m_ptr[i].bulk) == 0) poolInfo.sss_jp_pool_f_1 = v_value;
		else if (strcmp(CUR_SSS_JP_POOL_B_1, m_ptr[i].bulk) == 0) poolInfo.sss_jp_pool_b_1 = v_value;
		else if (strcmp(CUR_SSS_JP_POOL_F_2, m_ptr[i].bulk) == 0) poolInfo.sss_jp_pool_f_2 = v_value;
		else if (strcmp(CUR_SSS_JP_POOL_B_2, m_ptr[i].bulk) == 0) poolInfo.sss_jp_pool_b_2 = v_value;
		else if (strcmp(CUR_SSS_JP_POOL_F_3, m_ptr[i].bulk) == 0) poolInfo.sss_jp_pool_f_3 = v_value;
		else if (strcmp(CUR_SSS_JP_POOL_B_3, m_ptr[i].bulk) == 0) poolInfo.sss_jp_pool_b_3 = v_value;
		i += 2;
		if (i >= len)
			break;
	}

	return true;
}
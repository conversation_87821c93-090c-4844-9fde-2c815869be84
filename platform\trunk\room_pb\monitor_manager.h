﻿

#ifndef _MONITOR_MANAGER_H_
#define _MONITOR_MANAGER_H_

#include "room_common.h"

class CMonitorManager : public IMonitorManager
{
public:
    CMonitorManager();
    virtual ~CMonitorManager();

public:
    /**
     *  @brief: 初始化监控管理
     *  @return: 成功返回true, 失败返回false
    **/
    virtual bool init();
    /**
     *  @brief: 接收监控命令
     *  @return: 无返回值
    **/
    virtual void on_recv_cmd(_uint64 sid, int argc, const char ** argv, const char * cmd);

private:
    void on_nonsupport(_uint64 sid, const char * cmd);

private:
    bool on_info(_uint64 sid, int argc, const char ** argv);
    bool on_table_list(_uint64 sid, int argc, const char ** argv);
    bool on_log(_uint64 sid, int argc, const char ** argv);
    bool on_exit(_uint64 sid, int argc, const char ** argv);
    bool on_update_config(_uint64 sid, int argc, const char ** argv);
    bool on_room_update(_uint64 sid, int argc, const char ** argv);
    bool on_room_normal(_uint64 sid, int argc, const char ** argv);
    bool on_dismiss_room(_uint64 sid, int argc, const char ** argv);
    bool on_load_keywords(_uint64 sid, int argc, const char ** argv);
	bool on_load_node_info(_uint64 sid, int argc, const char ** argv);

};

#endif //_MONITOR_MANAGER_H_

﻿#include "user_manager.h"
#include <time.h>
#include "client_cmd.h"
#include "robot_manager.h"
#include "config_manager.h"
#include "room_route_challenge.h"
#include "bill_fun.h"
#include "game_frame.h"
#include "room_bill_fun.h"
extern CClientCmd _client_cmd;

TickInfo::TickInfo()
{
	len = 0;
	memset(uid_list, 0, sizeof(uid_list));
	memset(time_list, 0, sizeof(time_list));
}

UserManager::UserManager()
{
#if DEBUG_VALUE
    DEBUG_LOG("UserManager");
#endif
    m_user_list.clear();
}

UserManager::~UserManager()
{

}

bool UserManager::init()
{    
	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML ||
		config_manager::GetInstance()->get_room_type() == TYPE_PRIVATE)
	{
		g_timer_mgr->set_timer(this, clear_offline_user, CHECK_USER_TIMEID, CHECK_USER_LEN, 0, 0);
	}

	g_timer_mgr->set_timer(this, update_user_date, CHECK_UPDATE_USER_DATA, CHECK_UPDATE_LEN, 0, 0);
    return true;
}

void UserManager::check_offline_user()
{
	vector<_uint64>  clear_user_list;
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (!pUser)
			continue;
		
		if (pUser->get_standup_time() != 0)
		{
			if (time(0) - pUser->get_standup_time() >= CHECK_OFFLINE_USER && pUser->get_user_type() != ROBOT_TYPE)
				clear_user_list.push_back(pUser->get_user_id());
		}
	}

	//统一清除
	for (int i = 0; i < clear_user_list.size(); i++)
	{
		if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
			CRoomRouteChallenge::send_nomal_user_leave(clear_user_list[i]);

		delete_user(clear_user_list[i]);
	}
}

FTYPE(void) UserManager::clear_offline_user(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{
	UserManager::GetInstance()->check_offline_user();
}

FTYPE(void) UserManager::update_user_date(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{

}

bool UserManager::final()
{
    return true;
}

int UserManager::user_count()
{
    return m_user_list.count();
}

int UserManager::play_count()
{
    return 0;
}

IUser * UserManager::get_user(_tint64 userID)
{
    IUser * pUser = NULL;
    m_user_list.get_key_data(userID, pUser);

    return pUser;
}

bool UserManager::delete_user(IUser*& pUser, bool is_need_send )
{
    if (pUser)
    {
        _tint64 userID = pUser->get_user_id();
		g_log->write_log(LOG_TYPE_DEBUG,"UserManager delete_user -  userID:%lld", userID);

        if (m_user_list.exists(userID))
        {
			//更新用户输赢数据
			update_user_result(userID, pUser->get_add_num());

			//如果有预扣款,退还给用户
			int api_type = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_API_MODE);
			int with_gold = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_WHTH_GOLD);
			if (with_gold > 0 && api_type == MODE_SINGLE)
			{
				transfer_inout inout;
				get_guid(inout.gguid);
				inout.bill_type = GOLD_BACK_WITH_HOLD;
				inout.result = with_gold;
				inout.pay_out = with_gold;
				inout.uid = userID;
				inout.sid = 0;
				inout.write_db = true;
				inout.call_back = false;
				room_bill_fun::GetInstance()->write_transfer_inout(inout);
			}

			//清除用户所在房间信息
			room_bill_fun::GetInstance()->write_user_roominfo(userID, true, 0, 
				pUser->get_user_type(), pUser->get_add_num(), pUser->get_client_id(),
				pUser->get_add_bill());

			myredis::GetInstance()->set_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_ROOM_NODE, 0);
			myredis::GetInstance()->set_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_GAME_ID, 0);
			myredis::GetInstance()->set_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_ROOM_ID, 0);
			myredis::GetInstance()->set_data_by_uid(pUser->get_user_id(), DB_COMM, CUR_GAME_STATUS, 0);

			//离开房间输赢
			if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML)
			{
				room_bill_fun::GetInstance()->write_normal_room_bill(pUser, RBT_LOGON_OUT,
					CGameFrame::GetInstance()->get_room_id(), 0, pUser->get_node_id());
			}

            /* 通知其他服务器用户离开房间 */
			if (is_need_send)
			{
				_client_cmd.send_has_leave_room(pUser->get_socket_id(), pUser->get_user_id(), pUser->is_offline());
			}
           
			if (pUser->get_user_type() == ROBOT_TYPE)
			{
				robot_manager::GetInstance()->delete_robot(pUser->get_user_id());
			}

            m_user_list.remove_key(userID);
            pUser->reset_data();
            CUser * pU = dynamic_cast<CUser *>(pUser);
            m_user_pool.push(pU);
            pUser = NULL;
            return true;
        }
    }

    return false;
}

void UserManager::update_user_result(_tint64 userID, int result)
{
	int t = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_TODAY_RESULT_TIME);
	if (is_in_today(t))
	{
		myredis::GetInstance()->update_data_by_uid(userID, DB_COMM, CUR_TODAY_RESULT, result);
	}
	else
	{
		myredis::GetInstance()->set_data_by_uid(userID, DB_COMM, CUR_TODAY_RESULT, result);
		myredis::GetInstance()->set_data_by_uid(userID, DB_COMM, CUR_TODAY_RESULT_TIME, time(0));
	}
	myredis::GetInstance()->update_data_by_uid(userID, DB_COMM, CUR_HISTORY_RESULT, result);
}

bool UserManager::delete_user(_tint64 userID,  bool is_need_send)
{
	g_log->write_log(LOG_TYPE_DEBUG,"UserManager delete_user -  userID:%lld", userID);


    if (userID != 0)
    {
        IUser * pUser = NULL;
        m_user_list.get_key_data(userID, pUser);

        return delete_user(pUser, is_need_send);
    }

    return false;
}

IUser * UserManager::enum_user(int index)
{
    IUser * pUser = NULL;
    m_user_list.get_index_data(index, pUser);
    return pUser;
}

void UserManager::leave_user(_tint64 userID)
{
    
}

IUser * UserManager::create_user(_tint64 userID, int nodeid, int user_type)
{
	g_log->write_log(LOG_TYPE_DEBUG,"UserManager create_user -  userID:%lld", userID);

    if (userID == 0)
    {
        return NULL;
    }

    IUser * pUser = NULL;
    m_user_list.get_key_data(userID, pUser);
    if (!pUser)
    {
        pUser = m_user_pool.pop();
        if (!pUser)
        {
            g_log->write_log(LOG_TYPE_DEBUG, "创建用户指针失败，内存或许不足");
            return NULL;
        }
        pUser->reset_data();
		pUser->set_user_id(userID);
        m_user_list.add(userID, pUser);

		g_log->write_log(LOG_TYPE_DEBUG,"UserManager create_user m_user_list.count():%d", m_user_list.count());
    }

	room_bill_fun::GetInstance()->write_user_roominfo(userID, false, nodeid, user_type, 
		0, pUser->get_chair_id(), pUser->get_add_bill());

	int gameid = config_manager::GetInstance()->get_game_id();
	int roomid = CGameFrame::GetInstance()->get_room_id();
	myredis::GetInstance()->set_data_by_uid(userID, DB_COMM, CUR_GAME_ID, gameid);
	myredis::GetInstance()->set_data_by_uid(userID, DB_COMM, CUR_ROOM_ID, roomid);
    return pUser;
}

int UserManager::user_count(int & onlineCount, int & offlineCount)
{
    int userCount = m_user_list.count();
    onlineCount = 0;
    offlineCount = 0;

    for (int i = 0; i < userCount; ++i)
    {
        IUser * pUser = NULL;
        m_user_list.get_index_data(i, pUser);
        if (pUser)
        {
			if (pUser->get_user_type() == ROBOT_TYPE)
				continue;
			
            if (pUser->is_offline())
            {
                ++offlineCount;
            }
            else
            {
                ++onlineCount;
            }
        }
    }

    return userCount;
}

//获得虚拟UID
int UserManager::get_vir_uid()
{
	int userCount = m_user_list.count();
	int real_min_uid = 0;
	int real_max_uid = 0;
	int real_uid = 0;
	if (userCount > 0)
	{
		for (int i = 0; i < userCount; i++)
		{
			IUser * pUser = NULL;
			m_user_list.get_index_data(i, pUser);
			if (pUser && pUser->get_user_type() != ROBOT_TYPE)
			{
				if (real_min_uid == 0 || pUser->get_user_id() < real_min_uid)
					real_min_uid = pUser->get_user_id();
				if (real_max_uid == 0 || pUser->get_user_id() > real_max_uid)
					real_max_uid = pUser->get_user_id();
			}
		}
	}
	real_uid = (real_min_uid + real_max_uid) / 2;
	g_log->write_log(LOG_TYPE_DEBUG, "get_vir_uid ... real_uid:%d  robot_uid:%d", real_uid,
		config_manager::GetInstance()->get_vir_uid());

	//机器人是14开头的，过滤小于这个的
	if (real_uid <= config_manager::GetInstance()->get_vir_uid())
		real_uid = config_manager::GetInstance()->get_vir_uid();

	int vir_dis = config_manager::GetInstance()->get_vir_dis();
	if (vir_dis < 200)
		vir_dis = 200;
	
	//否则模拟50个假玩家ID，并且不在房间内的,没20个ID做一个间隔
	int vir_uid[50] = { 0 };
	int vir_count = 0;
	for (int i = 0; i < 50; i++)
	{
		int rand_add = rand() % vir_dis;
		real_uid = real_uid + rand_add;
		if (is_use_virid(real_uid))
			continue;
		
		vir_uid[vir_count] = real_uid;
		vir_count++;
	}

	int index = rand() % vir_count;
	g_log->write_log(LOG_TYPE_DEBUG, "get_vir_uid ... real_uid:%d  vir_dis:%d  vir_uid:%d", 
		real_uid, vir_dis, vir_uid[index]);

	return vir_uid[index];
}

//检查虚拟ID是否被占用
bool UserManager::is_use_virid(int vir_id)
{
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (!pUser)
			continue;

		if(pUser->get_user_id() == vir_id || pUser->get_vir_uid() == vir_id)
			return true;
	}
	return false;
}

//打印机器人信息
void UserManager::print_robot_info()
{
	if (config_manager::GetInstance()->get_room_type() != TYPE_NOAML)
		return;
	
	g_log->write_log(LOG_TYPE_INFO, "print_robot_info begin......");
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser)
		{
			if (pUser->get_user_type() == ROBOT_TYPE)
			{
				g_log->write_log(LOG_TYPE_INFO, "print_robot_info uid:%d, nodeid:%d, tableid:%d status:%d",
					pUser->get_user_id(), pUser->get_node_id(), pUser->get_table_id(), pUser->get_real_status());

				//没有桌子的机器人,异常的机器人为发恢复状态
				if (pUser->get_table_id() == INVALID_TABLE)
				{
					if ( !robot_manager::GetInstance()->check_robot_status(pUser->get_user_id()) )
					{
						robot_manager::GetInstance()->add_robot(pUser->get_user_id());
					}
				}
			}
		}
	}
	g_log->write_log(LOG_TYPE_INFO, "print_robot_info end......");
}


//统计场次人数
void UserManager::get_node_count(map<int,int> &count)
{
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser && pUser->get_user_type() != ROBOT_TYPE)
		{
			int nodeid = pUser->get_node_id();
			map<int,int>::iterator iter = count.find(nodeid);
			if (iter == count.end())
			{
				count[nodeid] = 1;
			}
			else
			{
				iter->second++;
			}
		}
	}
}

//添加用户被T时间
void UserManager::add_check_tick(_uint32 code, _uint64 uid)
{
 	if(m_tick_list.exists(code))
 	{
 		TickInfo *info;
 		m_tick_list.get_key_data(code, info);
		if (info)
		{
			info->update_info(uid);
		}
 	}
 	else
 	{
		TickInfo* info = m_tick_pool.pop();
		info->add_info(uid);
 		m_tick_list.add(code, info);
 	}
}

//删除本场被T时间
void UserManager::delete_check_tick(_uint32 code)
{
	if(m_tick_list.exists(code))
	{
		TickInfo * info;
		m_tick_list.get_key_data(code, info);
		m_tick_pool.push(info);
		m_tick_list.remove_key(code);
	}
}

//用户是否在限定时间内
bool UserManager::check_tick(_tint64 userid, _uint32 code)
{
 	if(m_tick_list.exists(code))
 	{
 		TickInfo *info;
		m_tick_list.get_key_data(code, info);
 		int user_time = info->get_time(userid);
 		g_log->write_log(LOG_TYPE_DEBUG, "check_tick user time uid:%lld, tick_time:%d now_time:%d", userid, user_time, time(0));
		return (time(0) - user_time < config_manager::GetInstance()->get_tick_limit());
 	}
	return false;
}


//添加用户文字语音时间
void UserManager::add_text_radio(_uint32 code, _uint64 uid)
{
	if(m_text_radio_list.exists(code))
	{
		TickInfo *info;
		m_text_radio_list.get_key_data(code, info);
		if (info)
		{
			info->update_info(uid);
		}
	}
	else
	{
		TickInfo* info = m_text_radio_pool.pop();
		info->add_info(uid);
		m_text_radio_list.add(code, info);
	}
}

//删除本场文字语音时间
void UserManager::delete_text_radio(_uint32 code)
{
	if(m_text_radio_list.exists(code))
	{
		TickInfo * info;
		m_text_radio_list.get_key_data(code, info);
		m_text_radio_pool.push(info);
		m_text_radio_list.remove_key(code);
	}
}

//用户是否在限定时间内
bool UserManager::check_text_radio(_tint64 userid, _uint32 code)
{
	if(m_text_radio_list.exists(code))
	{
		TickInfo *info;
		m_text_radio_list.get_key_data(code, info);
		int user_time = info->get_time(userid);
		g_log->write_log(LOG_TYPE_DEBUG, "check_tick user time uid:%lld, tick_time:%d now_time:%d", userid, user_time, time(0));
		return (time(0) - user_time < CHECK_TEXT_RADIO);
	}
	return false;
}


//添加互动表情时间
void UserManager::add_avatar(_uint32 code, _uint64 uid)
{
	if(m_avatar_list.exists(code))
	{
		TickInfo *info;
		m_avatar_list.get_key_data(code, info);
		if (info)
		{
			info->update_info(uid);
		}
	}
	else
	{
		TickInfo* info = m_avatar_pool.pop();
		info->add_info(uid);
		m_avatar_list.add(code, info);
	}
}

//删除互动表情时间
void UserManager::delete_avatar(_uint32 code)
{
	if(m_avatar_list.exists(code))
	{
		TickInfo * info;
		m_avatar_list.get_key_data(code, info);
		m_avatar_pool.push(info);
		m_avatar_list.remove_key(code);
	}
}

//用户是否在限定时间内
bool UserManager::check_avatar(_tint64 userid, _uint32 code)
{
	if(m_avatar_list.exists(code))
	{
		TickInfo *info;
		m_avatar_list.get_key_data(code, info);
		int user_time = info->get_time(userid);
		g_log->write_log(LOG_TYPE_DEBUG, "check_tick user time uid:%lld, tick_time:%d now_time:%d", userid, user_time, time(0));
		return (time(0) - user_time < CHECK_AVATAR);
	}
	return false;
}

//统计房间内各平台人满比赛人数
void UserManager::match_user_count_from_plat(map_match_count_info &match_count, int match_type)
{
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser)
		{
			if (pUser->get_user_type() == ROBOT_TYPE)
				continue;

			if (pUser->get_match_type() == match_type) 
			{
				int plat = pUser->get_plat_type();

				iter_match_count_info iter = match_count.find(plat);
				if (iter == match_count.end())
				{
					stMatchCountInfo info = { 0 };
					if (pUser->is_offline())
						info.matchFullOfflineCount += 1;
					else
						info.matchFullOnlineCount += 1;
				}
				else
				{
					if (pUser->is_offline())
						iter->second.matchFullOfflineCount += 1;
					else
						iter->second.matchFullOnlineCount += 1;
				}
			}
		}
	}
}

//统计房间内各平台房间人数
void UserManager::get_node_count_from_plat(map_plat_user_count &node_count)
{
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser && pUser->get_user_type() != ROBOT_TYPE)
		{
			int plat = pUser->get_plat_type();
			int nodeid = pUser->get_node_id();

			iter_plat_user_count iter_plat = node_count.find(plat);
			if (iter_plat == node_count.end())
			{
				stNodeCountInfo info_count;
				iter_node_user_count iter = info_count.node_user_count.find(nodeid);
				if (iter == info_count.node_user_count.end())
					info_count.node_user_count[nodeid] = 1;
				else
					iter->second++;

				node_count[plat] = info_count;
			}
			else
			{
				iter_node_user_count iter = iter_plat->second.node_user_count.find(nodeid);
				if (iter == iter_plat->second.node_user_count.end())
					iter_plat->second.node_user_count[nodeid] = 1;
				else
					iter->second++;
			}
		}
	}
}

/*
*    根据系统类型获取场次人数
*/
void UserManager::get_node_count_for_sys(map_plat_sys_room_user_count &count)
{
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser && pUser->get_user_type() != ROBOT_TYPE)
		{
			int plat = pUser->get_plat_type();
			int nodeid = pUser->get_node_id();
			int sys_type = pUser->get_sys_type();
			iter_plat_sys_room_user_count iter_plat = count.find(plat);
			if (iter_plat == count.end())
			{
				stPlatSysRoomUserCount info_count;
				stSysRoomUserCount n = { 0 };
				if (sys_type == cmd_sys::SYSTYPE_ANDROID)
					n.android_count = 1;
				else
					n.ios_count = 1;

				info_count.count[nodeid] = n;
				count[plat] = info_count;
			}
			else
			{
				iter_sys_room_user_count iter = iter_plat->second.count.find(nodeid);
				if (iter == iter_plat->second.count.end())
				{
					stSysRoomUserCount n = { 0 };
					if (sys_type == cmd_sys::SYSTYPE_ANDROID)
						n.android_count = 1;
					else
						n.ios_count = 1;

					iter_plat->second.count[nodeid] = n;
				}
				else
				{
					if (sys_type == cmd_sys::SYSTYPE_ANDROID)
						iter->second.android_count++;
					else
						iter->second.ios_count++;
				}
			}
		}
	}
}

//统计房间内各平台人数
void UserManager::get_user_count_from_plat(map_room_user_count &room_user_count)
{
	int userCount = m_user_list.count();
	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser && pUser->get_user_type() != ROBOT_TYPE)
		{
			int plat = pUser->get_plat_type();
			iter_room_user_count iter_plat = room_user_count.find(plat);
			if (iter_plat == room_user_count.end())
			{
				stRoomUserCount info = { 0 };
				if (pUser->is_offline())
					info.OfflineCount++;
				else
					info.OnlineCount++;

				room_user_count[plat] = info;
			}
			else
			{
				if (pUser->is_offline())
					iter_plat->second.OfflineCount++;
				else
					iter_plat->second.OnlineCount++;
			}
		}
	}
}

//人满赛比赛场用户人数
void UserManager::match_full_user_count(int &matchFullOnlineCount, int &matchFullOfflineCount)
{
	int userCount = m_user_list.count();
	matchFullOnlineCount = 0;
	matchFullOfflineCount = 0;

	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser)
		{
			if (pUser->get_user_type() == ROBOT_TYPE)
				continue;

			if (pUser->get_match_type() == cmd_match::MATCH_FULL)   //人满赛
			{
				if (pUser->is_offline())
				{
					++matchFullOfflineCount;
				}
				else
				{
					++matchFullOnlineCount;
				}
			}
		}
	}
}

//定时赛比赛场用户人数
void UserManager::match_time_user_count(int &matchTimeOnlineCount, int &matchTimeOfflineCount)
{
	int userCount = m_user_list.count();
	matchTimeOnlineCount = 0;
	matchTimeOfflineCount = 0;

	for (int i = 0; i < userCount; ++i)
	{
		IUser * pUser = NULL;
		m_user_list.get_index_data(i, pUser);
		if (pUser)
		{
			if (pUser->get_user_type() == ROBOT_TYPE)
				continue;

			if (pUser->get_match_type() == cmd_match::MATCH_TIME)   //定时赛
			{
				if (pUser->is_offline())
				{
					++matchTimeOfflineCount;
				}
				else
				{
					++matchTimeOnlineCount;
				}
			}
		}
	}
}
#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <iomanip>
#include <cstring>

// 完全独立的测试程序，不依赖任何外部文件

struct GameResult {
    double totalwin;
    int award;
    int bet;
    int icons[3][3];
    
    GameResult() {
        totalwin = 0.0;
        award = 0;
        bet = 1000;
        memset(icons, 0, sizeof(icons));
    }
};

// 简单的游戏逻辑模拟
GameResult calculateGameResult(const std::vector<int>& input_icons) {
    GameResult result;
    result.bet = 1000;
    
    // 填充图标矩阵
    int idx = 0;
    for (int i = 0; i < 3 && idx < input_icons.size(); i++) {
        for (int j = 0; j < 3 && idx < input_icons.size(); j++) {
            result.icons[i][j] = input_icons[idx++];
        }
    }
    
    // 简单的计算逻辑
    int bonus_count = 0;
    int wild_count = 0;
    int same_count = 0;
    
    // 统计特殊图标
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            if (result.icons[i][j] == 5) bonus_count++;  // BONUS
            if (result.icons[i][j] == 11) wild_count++;  // WILD
        }
    }
    
    // 检查连线
    for (int i = 0; i < 3; i++) {
        if (result.icons[i][0] == result.icons[i][1] && 
            result.icons[i][1] == result.icons[i][2]) {
            same_count++;
        }
    }
    
    // 计算奖励
    if (bonus_count >= 3) {
        result.totalwin = 50.0;
        result.award = 3;
    } else if (wild_count >= 2) {
        result.totalwin = 25.0;
        result.award = 2;
    } else if (same_count >= 1) {
        result.totalwin = 10.0;
        result.award = 1;
    } else {
        result.totalwin = 0.0;
        result.award = 0;
    }
    
    return result;
}

// 保存结果到文件
void saveResultToFile(const GameResult& result, const std::string& filename) {
    std::ofstream file(filename);
    if (file.is_open()) {
        file << "=== 游戏计算结果 ===" << std::endl;
        file << "totalwin: " << std::fixed << std::setprecision(2) << result.totalwin << std::endl;
        file << "award: " << result.award << std::endl;
        file << "bet: " << result.bet << std::endl;
        file << "图标矩阵:" << std::endl;
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                file << std::setw(3) << result.icons[i][j] << " ";
            }
            file << std::endl;
        }
        
        // JSON 格式
        file << std::endl << "JSON 格式:" << std::endl;
        file << "{" << std::endl;
        file << "  \"totalwin\": " << result.totalwin << "," << std::endl;
        file << "  \"award\": " << result.award << "," << std::endl;
        file << "  \"bet\": " << result.bet << "," << std::endl;
        file << "  \"ackqueue\": [" << std::endl;
        for (int i = 0; i < 3; i++) {
            file << "    {" << std::endl;
            file << "      \"platesymbol\": {" << std::endl;
            file << "        \"col\": [";
            for (int j = 0; j < 3; j++) {
                file << result.icons[i][j];
                if (j < 2) file << ", ";
            }
            file << "]" << std::endl;
            file << "      }" << std::endl;
            file << "    }";
            if (i < 2) file << ",";
            file << std::endl;
        }
        file << "  ]" << std::endl;
        file << "}" << std::endl;
        
        file.close();
    }
}

// 读取参考文件
void readReferenceFile(const std::string& filename) {
    std::ifstream file(filename);
    if (file.is_open()) {
        std::string line;
        std::cout << filename << " 内容:" << std::endl;
        std::cout << "----------------------------------------" << std::endl;
        while (std::getline(file, line)) {
            std::cout << line << std::endl;
        }
        std::cout << "----------------------------------------" << std::endl;
        file.close();
    } else {
        std::cout << "无法打开 " << filename << " 文件" << std::endl;
    }
}

// 创建示例参考文件
void createSampleReferenceFile() {
    std::ofstream file("test.txt");
    if (file.is_open()) {
        file << "{" << std::endl;
        file << "  \"totalwin\": 15.5," << std::endl;
        file << "  \"award\": 1," << std::endl;
        file << "  \"bet\": 1000," << std::endl;
        file << "  \"ackqueue\": [" << std::endl;
        file << "    {\"platesymbol\": {\"col\": [5, 0, 4]}}," << std::endl;
        file << "    {\"platesymbol\": {\"col\": [0, 5, 0]}}," << std::endl;
        file << "    {\"platesymbol\": {\"col\": [5, 0, 1]}}" << std::endl;
        file << "  ]" << std::endl;
        file << "}" << std::endl;
        file.close();
        std::cout << "已创建示例参考文件 test.txt" << std::endl;
    }
}

int main() {
    std::cout << "GoldenBank 独立测试程序" << std::endl;
    std::cout << "========================" << std::endl;
    std::cout << "这是一个完全独立的测试程序，不依赖任何外部库" << std::endl;
    std::cout << std::endl;
    
    try {
        // 测试数据
        std::vector<int> icons = {5,0,4,0,5,0,5,0,1};
        
        std::cout << "--- 步骤1: 使用测试数据计算结果 ---" << std::endl;
        std::cout << "输入图标: ";
        for (int icon : icons) {
            std::cout << icon << " ";
        }
        std::cout << std::endl;
        
        // 计算结果
        GameResult result = calculateGameResult(icons);
        
        std::cout << "计算结果:" << std::endl;
        std::cout << "  totalwin: " << std::fixed << std::setprecision(2) << result.totalwin << std::endl;
        std::cout << "  award: " << result.award << std::endl;
        std::cout << "  bet: " << result.bet << std::endl;
        
        std::cout << "图标矩阵:" << std::endl;
        for (int i = 0; i < 3; i++) {
            std::cout << "  ";
            for (int j = 0; j < 3; j++) {
                std::cout << std::setw(3) << result.icons[i][j] << " ";
            }
            std::cout << std::endl;
        }
        
        // 保存结果
        std::cout << "\n--- 步骤2: 保存结果到文件 ---" << std::endl;
        saveResultToFile(result, "standalone_result.txt");
        std::cout << "结果已保存到 standalone_result.txt" << std::endl;
        
        // 读取或创建参考文件
        std::cout << "\n--- 步骤3: 处理参考数据 ---" << std::endl;
        std::ifstream test_file("test.txt");
        if (!test_file.is_open()) {
            std::cout << "未找到 test.txt，创建示例文件..." << std::endl;
            createSampleReferenceFile();
        } else {
            test_file.close();
        }
        
        readReferenceFile("test.txt");
        
        // 比较说明
        std::cout << "\n--- 步骤4: 比较说明 ---" << std::endl;
        std::cout << "✅ 当前结果已保存到 standalone_result.txt" << std::endl;
        std::cout << "✅ 参考数据在 test.txt 中" << std::endl;
        std::cout << "📊 主要数据:" << std::endl;
        std::cout << "   - 总赢分: " << result.totalwin << std::endl;
        std::cout << "   - 奖励类型: " << result.award << std::endl;
        std::cout << "   - 下注金额: " << result.bet << std::endl;
        std::cout << "💡 请手动比较两个文件的内容差异" << std::endl;
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
        std::cout << "这个程序演示了基本的比较测试流程" << std::endl;
        std::cout << "如需真正的 protobuf 比较，请解决 protobuf 库依赖问题" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n按回车键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}

.\" **************************************************************************
.\" *                                  _   _ ____  _
.\" *  Project                     ___| | | |  _ \| |
.\" *                             / __| | | | |_) | |
.\" *                            | (__| |_| |  _ <| |___
.\" *                             \___|\___/|_| \_\_____|
.\" *
.\" * Copyright (C) 1998 - 2017, <PERSON>, <<EMAIL>>, et al.
.\" *
.\" * This software is licensed as described in the file COPYING, which
.\" * you should have received as part of this distribution. The terms
.\" * are also available at https://curl.haxx.se/docs/copyright.html.
.\" *
.\" * You may opt to use, copy, modify, merge, publish, distribute and/or sell
.\" * copies of the Software, and permit persons to whom the Software is
.\" * furnished to do so, under the terms of the COPYING file.
.\" *
.\" * This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
.\" * KIND, either express or implied.
.\" *
.\" **************************************************************************
.\"
.\" DO NOT EDIT. Generated by the curl project gen.pl man page generator.
.\"
.TH curl 1 "16 Dec 2016" "Curl 7.52.0" "Curl Manual"
.SH NAME
curl \- transfer a URL
.SH SYNOPSIS
.B curl [options]
.I [URL...]
.SH DESCRIPTION
.B curl
is a tool to transfer data from or to a server, using one of the supported
protocols (DICT, FILE, FTP, FTPS, GOPHER, HTTP, HTTPS, IMAP, IMAPS, LDAP,
LDAPS, POP3, POP3S, RTMP, RTSP, SCP, SFTP, SMB, SMBS, SMTP, SMTPS, TELNET
and TFTP). The command is designed to work without user interaction.

curl offers a busload of useful tricks like proxy support, user
authentication, FTP upload, HTTP post, SSL connections, cookies, file transfer
resume, Metalink, and more. As you will see below, the number of features will
make your head spin!

curl is powered by libcurl for all transfer-related features. See
\fIlibcurl(3)\fP for details.
.SH URL
The URL syntax is protocol-dependent. You'll find a detailed description in
RFC 3986.

You can specify multiple URLs or parts of URLs by writing part sets within
braces as in:

  http://site.{one,two,three}.com

or you can get sequences of alphanumeric series by using [] as in:

  ftp://ftp.example.com/file[1-100].txt

  ftp://ftp.example.com/file[001-100].txt    (with leading zeros)

  ftp://ftp.example.com/file[a-z].txt

Nested sequences are not supported, but you can use several ones next to each
other:

  http://example.com/archive[1996-1999]/vol[1-4]/part{a,b,c}.html

You can specify any amount of URLs on the command line. They will be fetched
in a sequential manner in the specified order.

You can specify a step counter for the ranges to get every Nth number or
letter:

  http://example.com/file[1-100:10].txt

  http://example.com/file[a-z:2].txt

When using [] or {} sequences when invoked from a command line prompt, you
probably have to put the full URL within double quotes to avoid the shell from
interfering with it. This also goes for other characters treated special, like
for example '&', '?' and '*'.

Provide the IPv6 zone index in the URL with an escaped percentage sign and the
interface name. Like in

  http://[fe80::3%25eth0]/

If you specify URL without protocol:// prefix, curl will attempt to guess what
protocol you might want. It will then default to HTTP but try other protocols
based on often-used host name prefixes. For example, for host names starting
with "ftp." curl will assume you want to speak FTP.

curl will do its best to use what you pass to it as a URL. It is not trying to
validate it as a syntactically correct URL by any means but is instead
\fBvery\fP liberal with what it accepts.

curl will attempt to re-use connections for multiple file transfers, so that
getting many files from the same server will not do multiple connects /
handshakes. This improves speed. Of course this is only done on files
specified on a single command line and cannot be used between separate curl
invokes.
.SH "PROGRESS METER"
curl normally displays a progress meter during operations, indicating the
amount of transferred data, transfer speeds and estimated time left, etc. The
progress meter displays number of bytes and the speeds are in bytes per
second. The suffixes (k, M, G, T, P) are 1024 based. For example 1k is 1024
bytes. 1M is 1048576 bytes.

curl displays this data to the terminal by default, so if you invoke curl to
do an operation and it is about to write data to the terminal, it
\fIdisables\fP the progress meter as otherwise it would mess up the output
mixing progress meter and response data.

If you want a progress meter for HTTP POST or PUT requests, you need to
redirect the response output to a file, using shell redirect (>), --output or
similar.

It is not the same case for FTP upload as that operation does not spit out
any response data to the terminal.

If you prefer a progress "bar" instead of the regular meter, --progress-bar is
your friend. You can also disable the progress meter completely with the
--silent option.
.SH OPTIONS
Options start with one or two dashes. Many of the options require an
additional value next to them.

The short "single-dash" form of the options, -d for example, may be used with
or without a space between it and its value, although a space is a recommended
separator. The long "double-dash" form, --data for example, requires a space
between it and its value.

Short version options that don't need any additional values can be used
immediately next to each other, like for example you can specify all the
options -O, -L and -v at once as -OLv.

In general, all boolean options are enabled with --\fBoption\fP and yet again
disabled with --\fBno-\fPoption. That is, you use the exact same option name
but prefix it with "no-". However, in this list we mostly only list and show
the --option version of them. (This concept with --no options was added in
7.19.0. Previously most options were toggled on/off on repeated use of the
same command line option.)

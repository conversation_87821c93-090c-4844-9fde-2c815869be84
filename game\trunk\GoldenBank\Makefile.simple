# GoldenBank 简化测试 Makefile
# 使用方法: make -f Makefile.simple

CXX = g++
CXXFLAGS = -std=c++11 -Wall -g -O0

# 包含路径
INCLUDES = -I. \
           -I../../../public \
           -I../../../public/comm

# 源文件
SOURCES = simple_test.cpp \
          game_logic.cpp \
          game_config.cpp

# 目标文件
OBJECTS = $(SOURCES:.cpp=.o)

# 可执行文件名
TARGET = simple_test

# 默认目标
all: $(TARGET)

# 编译可执行文件
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET)
	@echo "编译完成: $(TARGET)"

# 编译源文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 运行测试
run: $(TARGET)
	@echo "运行简化测试..."
	./$(TARGET)

# 清理
clean:
	rm -f $(OBJECTS) $(TARGET)
	@echo "清理完成"

# 快速测试（编译并运行）
test: clean all run

# 帮助信息
help:
	@echo "可用的目标:"
	@echo "  all     - 编译测试程序"
	@echo "  run     - 运行测试程序"
	@echo "  test    - 清理、编译并运行测试"
	@echo "  clean   - 清理编译文件"
	@echo "  help    - 显示此帮助信息"

.PHONY: all run clean test help

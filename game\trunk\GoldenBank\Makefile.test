# GoldenBank 测试单元 Makefile
# 使用方法: make -f Makefile.test

CXX = g++
CXXFLAGS = -std=c++11 -Wall -g -O0

# 包含路径
INCLUDES = -I. \
           -I../../.. \
           -I../../../public \
           -I../../../public/comm \
           -I../../../public/lib/protobuf/include \
           -I../../../public/lib/nlohmann

# 库路径
LIBDIRS = -L../../../public/lib/protobuf/lib

# 链接库
LIBS = -lprotobuf -lpthread

# 源文件
SOURCES = test_bet_roll_unit.cpp \
          game_logic.cpp \
          game_config.cpp

# 目标文件
OBJECTS = $(SOURCES:.cpp=.o)

# 可执行文件名
TARGET = test_bet_roll

# 默认目标
all: $(TARGET)

# 编译可执行文件
$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) $(LIBDIRS) $(LIBS) -o $(TARGET)
	@echo "编译完成: $(TARGET)"

# 编译源文件
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 运行测试
run: $(TARGET)
	@echo "运行测试..."
	./$(TARGET)

# 清理
clean:
	rm -f $(OBJECTS) $(TARGET) test.txt
	@echo "清理完成"

# 调试编译
debug: CXXFLAGS += -DDEBUG -g
debug: $(TARGET)

# 帮助信息
help:
	@echo "可用的目标:"
	@echo "  all     - 编译测试程序"
	@echo "  run     - 编译并运行测试"
	@echo "  clean   - 清理编译文件"
	@echo "  debug   - 调试模式编译"
	@echo "  help    - 显示此帮助信息"

.PHONY: all run clean debug help

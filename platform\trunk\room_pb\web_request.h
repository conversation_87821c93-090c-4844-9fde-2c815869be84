#ifndef _WEB_REQUEST_
#define _WEB_REQUEST_
#include "room_common.h"
#include "http_interface.h"
#include "http_curl.h"

#define HTTP_OUT_TIME 15
//#define MAX_REQ_URL_LEN  2048

#define GOLD_ROOM_CONFIG_PARM  "conf/getNodeByRoomID?page=%d&roomid=%d&_t=%d&_s=%s"
#define ROOM_STATUS_PARM       "conf/getRoom?room_id=%d&_t=%d&_s=%s"
#define GAME_PARM			   "conf/getConfigGameList?game_type=%d&gameid=%d&page=%d&_t=%d&_s=%s"
#define TRANSFER_INOUT		   "cash/transferInOut?_t=%d&_s=%s"
#define TRANSFER_INOUT_BATCH   "cash/transferInOutBatch?_t=%d&_s=%s"
#define GAME_PROPS_CONFIG_PARM "conf/getPropsList?_t=%d&_s=%s"
#define GET_USER_CASH          "cash/get?uid=%d&game_id=%d&_t=%d&_s=%s"  //刷新三方用户金币
#define USE_USER_ITEM          "user/item/use?user_id=%d&game_id=%d&_t=%d&_s=%s&item_id=%d&user_item_id=%d"  //使用道具
#define RETURN_USER_ITEM       "user/item/return?user_id=%d&_t=%d&_s=%s&record_id=%d"  //归还道具道具

enum enHttpAction
{
	enAction_GoldRoomConfig  = 1,
	enGame_cfg               = 2,
	enRoom_RoomStatus        = 8,
	enTransferInout          = 9,
	enTransferInoutBatch     = 10, 
	enAction_GamePropsConfig = 11, 
	enUser_cash              = 12,
    enUser_item_use          = 13, // 使用道具
    enUser_item_return       = 14, // 归还道具
};

struct stBindData {

	int action_type;
	char sign[33];
    std::string traceid;
	int uid;
	int result;
	string transfer_data;
	socket_id sid;
	penetrte_data p_data;
	bool write_db;
	bool call_back;
	bool step_transfer;
	string md5key;
	int time;
	int game_id;
	
	stBindData() {
		step_transfer = false;
		uid         = 0;
		sid         = 0;
		time        = 0;
		action_type = 0;
		game_id     = 0;
		memset(sign, 0, 0);
	}
};

class web_request : public IHttpCurlCallback
{
public:
	web_request(void);
	~web_request(void);

public:
	static web_request *GetInstance()
	{
		static web_request st_web_request;
		return &st_web_request;
	}

public:

	bool init(ICommon* pcom,ILog* plog,IAttemper*pattemper,
		string web_client_version_api, string web_room_config_api, string h5_api);

	void load_data();
	bool load_local_config();

public:
	virtual void on_atp_http_fail(_uint32 sockid, char* purl, void* pbinddata, _uint16 nerrcode, char* perror);
	virtual void on_atp_http_recv_data(_uint32 sockid, void* pdata, _uint16 size, void* pbinddata, char* purl);

public:

	void send_get_gold_room_config_req(int roomid, int page);
	void send_get_game_props_config_req();
	bool get_game_config(int page);
    void get_room_status_req(int roomid, int gameid);

public:

	bool post_transfer_inout_normal(string json, transfer_inout inout);
	bool post_transfer_inout_hundred(string json, hundred_transfer_inout_one inout);
	void post_transfer_inout_batch(string json);

	bool get_flush_user_gold(int uid, int gameid, int tableid);
    bool post_return_user_item(int32_t uid, int32_t game_id, int32_t table_id, int32_t record_id);
    bool post_use_user_item(int32_t uid, int32_t game_id, int32_t table_id, int32_t item_id, int32_t user_item_id, const char *buffer, size_t len);

private:

	void on_transfer_inout_faile(stBindData bind_data);
	void on_transfer_inout_suc(const char* data_json, int size, stBindData bind_data);

	void on_transfer_inout_normal(stBindData bind_data, Json::Value jsonValue, bool is_suc, int code, _uint64 balance_amount);
	void on_transfer_inout_hunred(stBindData bind_data, Json::Value jsonValue, bool is_suc, int code, _uint64 balance_amount);

	void on_transfer_inout_batch(const char* data_json, int size, string transfer_data);

private:

	void on_gold_room_config_resp(void* pdata,_uint16 size);
	bool on_game_config(const char* data_json,int size);
	void on_get_room_status(void* pdata, _uint16 size);
	void on_game_props_config_resp(void* pdata, int size);

    void on_use_user_item(void *pdata, int size, int64_t uid, int64_t game_id, int table_id, char *buffer, size_t len);
    void on_return_user_item(void *pdata, int size, int64_t uid, int64_t record_id);

private:

	void produce_hashids_s(string &sign, int _t, stBindData* info, int action_type);

private:

	void add_re_request(stBindData data);
	void pop_re_request();
    

private:
	static FTYPE(void) on_req_post_time(void*obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);

private:
	//CDLLHelper<IHttpCliSocket> m_IHttpCliSocket;
	CDLLHelper<IBlockPoolEx>   m_bfpool;

private:
	ICommon*                   m_com;
	ILog*                      m_log;
	IAttemper*                 m_pattemper;
	string					   m_web_room_config_api;
	string					   m_h5_api;
	int						   m_robot_nodeid;	//���������û�ȡnodeid

	CObjectPool<stBindData>           m_bind_pool;                  //͸������
	key_vector<char*, stBindData*>    m_bind_list;					//͸������

	CObjectPool<stBindData>           m_re_bind_pool;               //�ش�����
	key_vector<char*, stBindData*>    m_re_bind_list;				//�ش�����
};

#endif

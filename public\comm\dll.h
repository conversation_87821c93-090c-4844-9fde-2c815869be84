
#ifndef __DLL_H__
#define __DLL_H__

#include "log_manager.h"
#include "i_game_config.h"
#include "i_game_component.h"

struct tagDLLInstance;
typedef struct tagDLLInstance DLLInstance;
typedef struct tagDLLInstance *LPDLLINSTANCE;

class IDBPClient;

#pragma pack(1)

struct tagDLLInstance
{
    IMYLogManager *log;
    IGameConfig   *conf;
    IDBPClient   **dbproxy;

    char szVersionInfo[64];
};

#pragma pack()

#endif
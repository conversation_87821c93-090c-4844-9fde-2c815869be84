#include "http_router.h"
#include "log_manager.h"

namespace tnet {

int HttpRouter::add_route(const std::string& s, routeHandleFunc func) {
  
    auto it = routes.find(s);
    if(it == routes.end()) {
        routes.insert({s, func});
    } else {
        routes.at(s) = func;
    }

    return 0;
}

routeHandleFunc HttpRouter::get_route(const std::string&s ) {

    for(auto x: routes) {
        std::regex pattern(x.first);
        try {
            bool isTrue = std::regex_match(s, pattern);
            if(isTrue) {
                return x.second;
            }
        } catch (const std::regex_error& e) {
            MY_LOG_ERROR("get route with error : %s ,path: %s, pattern: %s", e.what(), s.c_str(), x.first.c_str());
            return nullptr;
        }
    }

    return nullptr;
}

}

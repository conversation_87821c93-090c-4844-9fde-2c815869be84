#ifndef __REDIS_POOL_H__
#define __REDIS_POOL_H__
#pragma once

#include "typedef.h"

#include <stdio.h>
#include <stdlib.h>
#ifdef _WINNT_SYSTEM
#include <stdarg.h>
#include <wtypes.h>
//#include "..\lib\svrlib\include\object_pool.h"
#define  _path_sign '\\' 
#else
#include <string.h>
#include <ctype.h>
#define  _path_sign      '/'
#include "object_pool.h"
#endif


//#include "object_queue.h"
#include "log_interface.h"
#include "common_interface.h"
#include "dll_model_helper.h"
#include "queue_interface.h"
#include "net_def.h"
#include "monitor_interface.h"
#include "../lib/json/json.h"
#include "../lib/tinyxml/tinyxml.h"
#include <vector>
#include <string>
using namespace std;

//#include "stack_object.h"
#include "redis_interface.h"
#include "singleton.h"
#include <map>
//#include "mutex.h"
using namespace std;

#define REDIS_DB_MAX 2		// redis db�����

typedef map<_uint64, CDLLHelper<IRedis>* > redis_map;
typedef redis_map::iterator redis_iterator;

class redis_manager:public Singleton<redis_manager>
{
public:
	redis_manager();
	~redis_manager();
	int get_port_by_uid(_uint64 uid);
	IRedis* get_redis(char* ip, int port, int ndb, char* pwd);
    void put(char* ip, int port);
	void free_redis();
	CDLLHelper<IRedis>* alloc_redis();
    void keep_alive();
	bool is_connection_broken(IRedis* redis);

private:
    CDLLHelper<IRedis>* get_redis_internal(char* ip, int port, int ndb,char* pwd);

private:
	
    redis_map m_map_redis;
	//dj::mutex m_redis_lock;						 // �߳���
    int  m_maxcon;
};
#define sRedisMgr redis_manager::instance()
#endif//__REDIS_POOL_H__
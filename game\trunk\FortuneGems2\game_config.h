/*
-----------------------------------------------------------------------------

File name        :   game_config.h
Author           :   
Version          :   1.0
Date             :   2018.1.15
Description      :   百人牛牛组件配置;

-----------------------------------------------------------------------------
*/

#ifndef __GAME_CONFIG_H__
#define __GAME_CONFIG_H__

#include "i_game_config.h"
#include "game_rule.h"
#include "tinyxml.h"
#include "room_route.h"
#include "logic_def.h"
#include "game_logic.h"

typedef std::vector<int> VEC_INTINFO;
typedef std::map<string, int> MAP_STRINFO;
typedef std::map<string, string> MAP_STRSTRINFO;

class GameConfig : public IGameConfig
{
public:
   static GameConfig *GetInstance()
   {
       static GameConfig stGameCfg;
       return &stGameCfg;
   }

protected:
	GameConfig();
	~GameConfig(){};

public:

	virtual int init(const char *conf_file);
	virtual int reload();

	virtual int get_table_count() { return getValueByStr("max_table"); }
	virtual int get_game_player() { return getValueByStr("max_user"); }
	virtual int get_net_delay() { return getValueByStr("net_delay"); }
	virtual int get_game_id() { return getValueByStr("game_id"); }

	virtual const char *get_dll_name() { return m_dll_name; }
	virtual void set_dll_name(const char *dll_name);
	virtual void set_rule(void* game_rule) {};

public:
	void clear();

	/*判断是否跨天;
	*/static bool isNewDay(int nTime);
	/*
	*/static bool isNewDay(int nFirstTime, int nSecondTime, int offset = 0);
	
	/*例如：將字符串“1,31,4123”解析成一个数组;
	*/VEC_INTINFO stringtoarray(string str, string seperator=",");

public:

	/*通过字符串获取单个int配置;
	*/int getValueByStr(string str);
	/*通过字符串获取单个string配置;
	*/const char *  getStrByStr(string str);

	/*初始化int数组;
	*/void init_vec_int_info(TiXmlElement *pXmlItem, VEC_INTINFO& vecInfo);
	void field_list_split(std::vector<int> &vec_list, const std::string str, std::string sep = ",");

public:
	//获得比例计算值
	int get_ratio(string str);

public:
	
public:

private:

	char m_dll_name[64];
	char m_conf_file[256];            /* 配置文件; */
	char m_szValue[128];
private:

	MAP_STRINFO m_MapStrInfo;			// 配置int值信息
	MAP_STRSTRINFO m_MapStrToStrInfo;	// 配置字符串信息
};

#define GET_INT_CONFIG(x) GameConfig::GetInstance()->getValueByStr(x)
#define GET_STR_CONFIG(x) GameConfig::GetInstance()->getStrByStr(x)
#define RAND_NUM_URT 100  //部分数据*100下发
#define MAX_COMBO	8	  //最多连消
#define RAND_RATE 999     //千分比

#endif

#include "block_timeout_socket.h"

int block_timeout_connect (int sockfd, struct sockaddr_in *servaddr, int nsec, int usec) {

    int ret = 0;
    struct timeval timeout;
    timeout.tv_sec = nsec;
    timeout.tv_usec = usec;

    socklen_t len = sizeof(timeout);
    ret = setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, len);
    if (ret == -1) {
        return -1;
    }

    if ((ret = connect(sockfd, (struct sockaddr*)servaddr, sizeof(struct sockaddr_in))) < 0) {
        if (errno == EINPROGRESS) {
            return -2;
        }

        return -3;
    }

    return 0;
}

int block_timeout_recv(int sockfd, char *buf, int len,  int nsec, int usec) {

    struct timeval timeout;
    timeout.tv_sec = nsec;
    timeout.tv_usec = usec;

    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        return -1;
    }

    int n = recv(sockfd, buf, len, 0);

    return n;
}

int block_connection_is_closed_by_remote(int sockfd, int ms) {

    struct timeval tval;
    tval.tv_sec = ms / 1000;
    tval.tv_usec = (ms % 1000) * 1000;

    char buffer[1024] = { 0 };

    fd_set fdSet;
    FD_ZERO(&fdSet);
    FD_SET(sockfd, &fdSet);

    if (select(sockfd + 1, &fdSet, NULL, NULL, &tval) <= 0) {
        return -1;
    }

    if (recv(sockfd, buffer, 1024, 0) <= 0) {
        return -1;
    }

    return 0;
}

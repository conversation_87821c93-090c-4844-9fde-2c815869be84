﻿/*
-----------------------------------------------------------------------------

File name        :   logic_def.h
Author           :   
Version          :   1.0
Date             :   2017.9.1
Description      :   游戏定义;

-----------------------------------------------------------------------------
*/

#ifndef __LOGIC_DEF_H__
#define __LOGIC_DEF_H__

#include "typedef.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
#include "comm_fun.h"
using namespace std;

//////////////////////////////////////////////////////////////////////////////////////////
//定义

#define INVALID_CHAIRID				0x7F								// 无效椅子号


//////////////////////////////////////////////////////////////////////////

/*随机函数
*/static _uint32 RandUInt()
{
	_uint32	inta = (rand() & 0x00000FFF);
	_uint32	intb = ((rand() & 0x00000FFF) << 12);
	_uint32  intc = ((rand() & 0x00000FFF) << 24);
	_uint32  result = inta + intb + intc;

	return result;
}

/*获取范围内随机值(>=nMin <=nMax);*/
static int getRand(int nMin, int nMax)
{
//#if 0
	int num = nMin;
	nMin = nMax > nMin ? nMin : nMax;
	nMax = nMax > num ? nMax : num;

	int nDiff = abs(nMax - nMin) + 1;
	return (RandUInt() % nDiff) + nMin;
//#endif
//	return getTureRand(nMin, nMax);
}


#endif

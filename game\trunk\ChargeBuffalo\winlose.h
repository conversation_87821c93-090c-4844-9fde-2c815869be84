#ifndef _SUPERACE_WONLOSE_CONTROL_
#define _SUPERACE_WONLOSE_CONTROL_

#include "typedef.h"
#include "redis_manager.h"
#include "my_redis.h"
using namespace svrlib;
#ifndef WIN32
#include <stdlib.h>
#endif
#include <map>
#include <vector>
#include <set>
#include <math.h>
#include <string>
#include "user_interface.h"
#include "game_logic.h"
struct ResultItem
{
	int nResult;
	int nIndex;
	ResultItem()
	{
		nResult = 0;
		nIndex = 0;
	}
};
namespace FG {

    //*************************slot输赢控制公共类**************************
struct form_item
{
    int multer;     // 倍数
    int touch;      // 触发权重
    form_item()
    {
        multer = 0;
        touch = 0;
    }
};
struct vec_from_item 
{
	int bet_form_win_total;
	int bonus_bet_form_win_total;
	vector<form_item> vec_bet_form_win;
	vector<form_item> vec_bonus_bet_form_win;
	vec_from_item()
	{
		bet_form_win_total = 0;
		bonus_bet_form_win_total = 0;
		vec_bet_form_win.clear();
		vec_bonus_bet_form_win.clear();
	}
};
struct stock_cfg_item
{
    int kill_rate; // 杀数
    int update_time;  //刷新时间
    int base_bet;  //下注金额基数
    map<int, vec_from_item> map_bet_form_win;
    stock_cfg_item()
	{
	    kill_rate = 0;
		update_time = 0;
		base_bet = 0;
	}
  
};


typedef std::map<string, stock_cfg_item>  map_stock_cfg;
typedef map_stock_cfg::iterator iter_stock_cfg;
    class winlose_control
    {
    public:
        winlose_control();
        ~winlose_control();


    public:
        static winlose_control* GetInstance()
        {
            static winlose_control stGameCfg;
            return &stGameCfg;
        }

    public:
        bool get_lottery_ctr_result(int plat_id, int gameid, int nodeid, int type, ResultItem& result, bool &bRespin, bool bBuyBonus = false);
		//更加index获取下一个中奖金额
		bool get_result_by_index(int plat_id, int gameid, int nodeid, int type, ResultItem& rItem, bool buy_bonus = false);
		int get_base_bet(int plat_id, int gameid, int nodeid);
    private:
        //初始化配置
		void init_config(int plat_id, int gameid, int nodeid);
		//加载配置
		void load_config(int plat_id, int gameid, int nodeid, int update_time);
        //获得配置
		iter_stock_cfg get_config(int plat_id, int gameid, int nodeid);
        //加载表权重
		void load_form_weight(Json::Value json_value, const char* str, vector<form_item>& form, int& total_weight);
        bool get_ctrl_result(iter_stock_cfg iter, int type, ResultItem &result, bool &bRespin);
		bool get_ctrl_buy_bonus_result(iter_stock_cfg iter, int type, ResultItem &result, bool &bRespin);
    private:
        map_stock_cfg  m_map_stock_cfg;
    };
}
#endif


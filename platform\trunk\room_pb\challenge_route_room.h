﻿
#ifndef _CHALLENGE_ROUTE_ROOM_H_
#define _CHALLENGE_ROUTE_ROOM_H_

#include "room_common.h"

class CChallengeRouteRoom
{
public:
    CChallengeRouteRoom();
    virtual ~CChallengeRouteRoom();

private:
    static FTYPE(void) on_challenge_ack(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_challenge_ack_ex(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
    static FTYPE(void) on_challenge_dismiss_table(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_challenge_userlist_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_challenge_web_userlist_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);

	static FTYPE(void) on_patch_card_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_patch_table_card_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);

    static void send_dismiss_table_fail(_tint64 sid, int errorCode, const char *msg);
	static void send_dismiss_table_success(_tint64 sid);

	static FTYPE(void) on_flush_client_version_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_update_node_data_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_update_limit_data_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_web_create_room(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_update_room_game_config_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_dissmiss_room(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data);
	static FTYPE(void) on_check_user(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) on_update_room_status(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) on_reset_update_time(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) on_gm_control(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) on_update_props_config_req(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);

	static FTYPE(void) on_update_new_stock(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) on_update_stock_robot_stock(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
	static FTYPE(void) on_test_person_srategy(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);

	static FTYPE(void) on_web_get_user_room_card_dbproxy(cmd_dbproxy::CDBProxyExceSection &section);
	static FTYPE(void) on_web_sub_user_room_card_dbproxy(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);
	static FTYPE(void) on_web_sub_user_room_card_result(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);
};

#endif //_CHALLENGE_ROUTE_ROOM_H_

﻿
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <string.h>

#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "user_token.h"
#include "block_timeout_socket.h"
#include <iostream>
#include <fstream>
#include <chrono>
#include "winlose.h"
#include "google/protobuf/text_format.h"
#include "mc.pb.h"
#include "server.pb.h"

CDLLHelper<ICommon> g_com(SVRLIB_NAME, "_common_instance", "_common_free");

std::string &replace_all(string &str, const std::string &old_value, const std::string &new_value, int off)
{

	std::string::size_type pos(0);
	while (true)
	{
		pos = str.find(old_value, pos);
		if (pos != string::npos)
		{
			str.replace(pos, old_value.length(), new_value);
			pos += off;
		}
		else
		{
			break;
		}
	}
	return str;
}

int char2bits(char ch)
{

	int bits = 0;
	if (ch >= 'a' && ch <= 'z')
	{
		bits = ch - 'a' + 10;
	}
	else if (ch >= 'A' && ch <= 'Z')
	{
		bits = ch - 'A' + 10;
	}
	else if (ch >= '0' && ch <= '9')
	{
		bits = ch - '0';
	}
	else
	{
		bits = -1;
	}
	return bits;
}

_uint64 _parseInt(const char *a, int len)
{

	std::vector<int> numArr;
	int radix = 16;
	_uint64 result = 0;
	for (int i = 0; i < len; i++)
	{
		int num = char2bits(a[i]);
		numArr.push_back(num);
	}
	for (int i = 0; i < numArr.size(); i++)
	{
		result += numArr[i] * pow(radix, numArr.size() - i - 1);
	}
	return result;
}
void writeGraph(const user_game_info &info)
{
	// std::remove("graph.txt");
	std::ofstream ofs;
	ofs.open("graph.txt", ios::out | ios::app);

	Json::Value all_data;
	Json::Value graph_root;

	for(int g=0;g<info.game_vec_remove.size();g++)
	{
		Json::Value item;
		
		for (int i = 0; i < MAX_LIST; i++)
		{
			for (int j = 0; j < MAX_ROW; j++)
			{
				card c = info.game_vec_remove[g][0].vec_now_box[i][j];

				int value = c.value;
				item.append(value);
			}
		}

		for (auto &rm : info.game_vec_remove[g])
		{
			for (int i = 0; i < MAX_LIST; i++)
			{
				for (auto &f : rm.vec_fill_box[i])
				{
					int value = f.value;
					item.append(value);
				}
			}
		}

		{
			// graph_root["data"] = item;
			// graph_root["multer"] = info.game_multer[g];
			all_data.append(item);
			// int size = info.vec_remove.size();
			// graph_root["combo"] = size;
		}
	}
	graph_root["multer"] = info.all_total_multer;
	graph_root["data"] = all_data;
	
	Json::FastWriter json_writer;
		string detail = json_writer.write(graph_root);
		ofs << detail << "\n";
	ofs.close();
}

//#define TEST_BET
//#define GEN_GRAPH
//#define GEN_FREE_GRAPH
//#define CHECK_GRAPH
int test_multer[] = {
//5,10,15,20,25,30,35,40,45,50,55,60,65,70,75,80,85,90,95,100,105,110,115,120,125,130,135,140,145,150,155,160,165,170,175,180,185,190,195,200,205,210,215,220,225,230,235,240,245,250,255,260,265,270,275,280,285,290,295,300,305,310,315,320,325,330,335,340,345,350,355,360,365,370,375,380,385,390,395,400,405,410,415,420,425,430,435,440,445,450,455,460,465,470,475,480,485,490,495,500,505,510,515,520,525,530,535,540,545,550,555,560,565,570,575,580,585,590,595,600,605,610,615,620,625,630,635,640,645,650,655,660,665,670,675,680,685,690,695,700,705,710,715,720,725,730,735,740,745,750,755,760,765,770,775,780,785,790,795,800,805,810,815,820,825,830,835,840,845,850,855,860,865,870,875,880,885,890,895,900,905,910,915,920,925,930,935,940,945,950,955,960,965,970,975,980,985,990,995,1000
// 1005, 1010, 1015, 1020, 1025, 1030, 1035, 1040, 1045, 1050, 1055, 1060, 1065, 1070, 1075, 1080, 1085, 1090, 1095, 1100, 1105, 1110, 1115, 1120, 1125, 1130, 1135, 1140, 1145, 1150, 1155, 1160, 1165, 1170, 1175, 1180, 1185, 1190, 1195, 1200, 1205, 1210, 1215, 1220, 1225, 1230, 1235, 1240, 1245, 1250, 1255, 1260, 1265, 1270, 1275, 1280, 1285, 1290, 1295, 1300, 1305, 1310, 1315, 1320, 1325, 1330, 1335, 1340, 1345, 1350, 1355, 1360, 1365, 1370, 1375, 1380, 1385, 1390, 1395, 1400, 1405, 1410, 1415, 1420, 1425, 1430, 1435, 1440, 1445, 1450, 1455, 1460, 1465, 1470, 1475, 1480, 1485, 1490, 1495, 1500, 1505, 1510, 1515, 1520, 1525, 1530, 1535, 1540, 1545, 1550, 1555, 1560, 1565, 1570, 1575, 1580, 1585, 1590, 1595, 1600, 1605, 1610, 1615, 1620, 1625, 1630, 1635, 1640, 1645, 1650, 1655, 1660, 1665, 1670, 1675, 1680, 1685, 1690, 1695, 1700, 1705, 1710, 1715, 1720, 1725, 1730, 1735, 1740, 1745, 1750, 1755, 1760, 1765, 1770, 1775, 1780, 1785, 1790, 1795, 1800, 1805, 1810, 1815, 1820, 1825, 1830, 1835, 1840, 1845, 1850, 1855, 1860, 1865, 1870, 1875, 1880, 1885, 1890, 1895, 1900, 1905, 1910, 1915, 1920, 1925, 1930, 1935, 1940, 1945, 1950, 1955, 1960, 1965, 1970, 1975, 1980, 1985, 1990, 1995, 2000, 2005, 2010, 2015, 2020, 2025, 2030, 2035, 2040, 2045, 2050, 2055, 2060, 2065, 2070, 2075, 2080, 2085, 2090, 2095, 2100, 2105, 2110, 2115, 2120, 2125, 2130, 2135, 2140, 2145, 2150, 2155, 2160, 2165, 2170, 2175, 2180, 2185, 2190, 2195, 2200, 2205, 2210, 2215, 2220, 2225, 2230, 2235, 2240, 2245, 2250, 2255, 2260, 2265, 2270, 2275, 2280, 2285, 2290, 2295, 2300, 2305, 2310, 2315, 2320, 2325, 2330, 2335, 2340, 2345, 2350, 2355, 2360, 2365, 2370, 2375, 2380, 2385, 2390, 2395, 2400, 2405, 2410, 2415, 2420, 2425, 2430, 2435, 2440, 2445, 2450, 2455, 2460, 2465, 2470, 2475, 2480, 2485, 2490, 2495, 2500, 2505, 2510, 2515, 2520, 2525, 2530, 2535, 2540, 2545, 2550, 2555, 2560, 2565, 2570, 2575, 2580, 2585, 2590, 2595, 2600, 2605, 2610, 2615, 2620, 2625, 2630, 2635, 2640, 2645, 2650, 2655, 2660, 2665, 2670, 2675, 2680, 2685, 2690, 2695, 2700, 2705, 2710, 2715, 2720, 2725, 2730, 2735, 2740, 2745, 2750, 2755, 2760, 2765, 2770, 2775, 2780, 2785, 2790, 2795, 2800, 2805, 2810, 2815, 2820, 2825, 2830, 2835, 2840, 2845, 2850, 2855, 2860, 2865, 2870, 2875, 2880, 2885, 2890, 2895, 2900, 2905, 2910, 2915, 2920,
// 2925,2930,2935,2940,2945,2950,2955,2960,2965,2970,2975,2980,2985,2990,2995,3000,3005,3010,3015,3020,3025,3030,3035,3040,3045,3050,3055,3060,3065,3070,3075,3080,3085,3090,3095,3100,3105,3110,3115,3120,3125,3130,3135,3140,3145,3150,3155,3160,3165,3170,3175,3180,3185,3190,3195,3200,3205,3210,3215,3220,3225,3230,3235,3240,3245,3250,3255,3260,3265,3270,3275,3280,3285,3290,3295,3300,3305,3310,3315,3320,3325,3330,3335,3340,3345,3350,3355,3360,3365,3370,3375,3380,3385,3390,3395,3400,3405,3410,3415,3420,3425,3430,3435,3440,3445,3450,3455,3460,3465,3470,3475,3480,3485,3490,3495,3500,3505,3510,3515,3520,3525,3530,3535,3540,3545,3550,3555,3560,3565,3570,3575,3580,3585,3590,3595,3600,3605,3610,3615,3620,3625,3630,3635,3640,3645,3650,3655,3660,3665,3670,3675,3680,3685,3690,3695,3700,3705,3710,3715,3720,3725,3730,3735,3740,3745,3750,3755,3760,3765,3770,3775,3780,3785,3790,3795,3800,3805,3810,3815,3820,3825,3830,
// 3835, 3840, 3845, 3850, 3855, 3860, 3865, 3870, 3875, 3880, 3885, 3890, 3895, 3900, 3905, 3910, 3915, 3920,
// 3925, 3930, 3935, 3940, 3945, 3950, 3955, 3960, 3965, 3970, 3975, 3980, 3985, 3990, 3995, 4000, 4005, 4010, 4015, 4020,
// 4025, 4030, 4035, 4040, 4045, 4050, 4055, 4060, 4065, 4070, 4075, 4080, 4085, 4090, 4095, 4100, 4105, 4110, 4115, 4120,
// 4125, 4130, 4135, 4140, 4145, 4150, 4155, 4160, 4165, 4170, 4175, 4180, 4185, 4190, 4195, 4200, 4205, 4210, 4215, 4220,
// 4225, 4230, 4235, 4240, 4245, 4250, 4255, 4260, 4265, 4270, 4275, 4280, 4285, 4290, 4295, 4300, 4305, 4310, 4315, 4320,
// 4325, 4330, 4335, 4340, 4345, 4350, 4355, 4360, 4365, 4370, 4375, 4380, 4385, 4390, 4395, 4400, 4405, 4410, 4415, 4420,
// 4425, 4430, 4435, 4440, 4445, 4450, 4455, 4460, 4465, 4470, 4480, 4485, 4490, 4495, 4500, 4505, 4510, 4515, 4520, 4525,
// 4530, 4535, 4540, 4545, 4550, 4555, 4560, 4565, 4570, 4575, 4580, 4585, 4590, 4595, 4600, 4605, 4610, 4615, 4620, 4625,
// 4630, 4635, 4640, 4645, 4650, 4655, 4660, 4665, 4670, 4680, 4685, 4690, 4695, 4705, 4710, 4715, 4720, 4725, 4730, 4735,
// 4740, 4745, 4750, 4755, 4760, 4765, 4770, 4775, 4780, 4785, 4790, 4795, 4800, 4805, 4810, 4815, 4820, 4825, 4830, 4835,
// 4840, 4845, 4850, 4855, 4860, 4865, 4870, 4875, 4880, 4885, 4890, 4895, 4900, 4905, 4910, 4915, 4920, 4925, 4930, 4935,
// 4940, 4950, 4955, 4960, 4965, 4970, 4975, 4980, 4985, 4990, 4995, 5000,
// 5005, 5015, 5020, 5025, 5030, 5035, 5040, 5045, 5050, 5055, 5060, 5065, 5070, 5075, 5080, 5085, 5090, 5095, 5100, 5105, 5110, 5120, 5130, 5135, 5140, 5145, 5150,5155,5160,5165,5175,5180,5185,5195,5200,5205,5210,5215,5220,5225,5230,5235,5240,5245,5250,5255,5260,5265,5270,5275,5280,5285,5295,5300,5305,5310,5315,5320,5325,5330,5335,5340,5345,5350,5355,5360,5365,5370,5375,5380,5385,5395,5400,5405,5410,5415,5420,5425,5430,5435,5440,5450,5455,5460,5465,5470,5475,5480,5485,5490,5495,5500,5505,5510,5515,5520,5525,5530,5535,5540,5545,5550,5555,5560,5565,5570,5575,5580,5585,5590,5600,5605,5610,5615,5625,5630,5635,5640,5645,5650,5655,5660,5665,5670,5675,5680,5685,5690,5695,5700,5705,5710,5715,5720,5725,5730,5735,5740,5745,5750,5755,5760,5765,5770,5775,5780,5785,5790,5795,5800,5805,5810,5815,5820,5825,5830,5835,5840,5845,5850,5855,5860,5865,5870,5875,5880,5885,5890,5895,5900,5905,5910,5915,5925,5930,5935,5940,5945,5950,5955,5960,5965,5970,5975,5980,5985,5990,5995,6000,6005,6010,6015,6020,6025,6030,6035,6040,6045,6050,6055,6060,6070,6075,6080,6085,6090,6100,6105,6110,6115,6120,6125,6130,6135,6140,6145,6150,6155,6160,6165,6170,6175,6180,6185,6190,6200,6205,6210,6215,6220,6225,6230,6235,6240,6245,6250,6260,6270,6275,6285,6290,6295,6300,6305,6310,6315,6320,6325,6335,6340,6345,6350,6355,6360,6365,6370,6375,6380,6385,6390,6395,6400,6405,6410,6415,6420,6425,6430,6435,6440,6445,6450,6455,6460,6465,6470,6475,6480,6485,6490,6495,6500,6505,6515,6520,6525,6530,6540,6555,6560,6565,6570,6575,6580,6590,6595,6600,6605,6610,6615,6620,6625,6630,6635,6640,6645,6655,6660,6670,6675,6680,6685,6690,6695,6700,6705,6710,6715,6720,6725,6730,6735,6740,6745,6750,6755,6760,6765,6775,6785,6790,6795,6800,6805,6810,6815,6820,6825,6830,6835,6840,6845,6850,6855,6860,6865,6870,6875,6880,6890,6895,6900,6905,6910,6920,6935,6940,6955,6960,6965,6985,6995,7000,7005,7010,7020,7025,7030,7040,7045,7050,7055,7060,7065,7070,7080,7085,7090,7095,7100,7105,7110,7120,7125,7130,7140,7150,7155,7160,7165,7170,7180,7185,7200,7205,7210,7215,7220,7225,7230,7235,7245,7260,7265,7275,7285,7295,7305,7310,7315,7330,7335,7340,7345,7350,7355,7360,7365,7370,7375,7380,7385,7390,7395,7400,7405,7415,7420,7425,7430,7435,7440,7445,7455,7460,7465,7470,7475,7480,7485,7495,7510,7515,7520,7525,7530,7535,7540,7545,7550,7560,7575,7580,7585,7630,7645,7650,7655,7660,7680,7685,7695,7705,7710,7735,7740,7750,7755,7760,7765,7770,7780,7785,7790,7795,7800,7805,7810,7815,7825,7835,7840,7845,7865,7870,7875,7880,7885,7890,7900,7905,7910,7920,7930,7935,7945,7955,7960,7970,7975,7985,7990,7995,8005,8010,8015,8020,8025,8045,8050,8055,8065,8075,8085,8090,8095,8110,8135,8140,8145,8150,8155,8165,8170,8180,8185,8195,8200,8210,8215,8220,8230,8235,8240,8245,8250,8255,8265,8275,8280,8295,8305,8310,8315,8320,8325,8330,8340,8345,8355,8360,8365,8370,8395,8400,8415,8425,8445,8450,8455,8465,8470,8475,8480,8490,8495,8510,8515,8520,8525,8530,8540,8545,8550,8555,8560,8565,8570,8585,8590,8595,8600,8605,8610,8615,8635,8640,8645,8650,8660,8665,8670,8675,8690,8695,8700,8710,8725,8730,8745,8750,8755,8760,8765,8770,8785,8790,8800,8805,8810,8820,8825,8840,8845,8850,8855,8860,8865,8890,8895,8900,8905,8915,8930,8935,8940,8945,8955,8970,8975,8980,9000,9010,9015,9020,9035,9040,9045,9060,9065,9070,9075,9085,9090,9115,9120,9125,9145,9155,9170,9175,9180,9210,9215,9220,9230,9235,9240,9255,9260,9280,9285,9290,9295,9305,9320,9325,9330,9350,9355,9365,9375,9385,9410,9415,9420,9435,9440,9460,9470,9475,9485,9500,9510,9515,9570,9575,9590,9595,9610,9620,9630,9635,9645,9650,9655,9665,9685,9690,9705,9710,9730,9735,9745,9750,9755,9760,9770,9775,9780,9800,9810,9820,9840,9850,9855,9860,9880,9900,9910,9915,9920,9925,9935,9945,9950,9955,9975,9980,9985,9990,9995,
// 10010, 10020, 10025, 10030, 10045, 10055, 10070, 10090, 10110, 10115, 10125, 10130, 10135, 10145, 10150, 10155, 10160, 10170, 10195, 10200, 10210, 10215, 10220, 10230, 10240, 10250, 10255, 10265, 10270, 10275, 10290, 10300, 10305, 10310, 10315, 10320, 10350, 10380, 10395, 10425, 10440, 10445, 10450, 10455, 10475, 10485, 10490, 10500, 10510, 10525, 10530, 10535, 10545, 10550, 10600, 10605, 10615, 10620, 10625, 10630, 10640, 10645, 10650, 10685, 10690, 10695, 10745, 10755, 10765, 10775, 10780, 10785, 10800, 10805, 10810, 10815, 10820, 10825, 10835, 10860, 10870, 10875, 10880, 10885, 10895, 10905, 10910, 10930, 10945, 10950, 10955, 10960, 10965, 10970, 10975, 10985, 10990, 10995, 11015, 11030, 11060, 11075, 11090, 11100, 11105, 11110, 11115, 11120, 11135, 11140, 11145, 11150, 11155, 11160, 11170, 11180, 11200, 11220, 11230, 11240, 11245, 11250, 11255, 11260, 11275, 11290, 11295, 11305, 11310, 11315, 11320, 11325, 11350, 11375, 11390, 11405, 11415, 11420, 11430, 11450, 11480, 11485, 11490, 11510, 11530, 11540, 11545, 11560, 11585, 11595, 11610, 11615, 11630, 11635, 11640, 11660, 11705, 11710, 11725, 11730, 11745, 11750, 11775, 11785, 11790, 11845, 11850, 11855, 11875, 11885, 11895, 11900, 11905, 11910, 11920, 11930, 11935, 11940, 11950, 11960, 12020, 12040, 12050, 12060, 12065, 12070, 12090, 12115, 12120, 12140, 12150, 12160, 12165, 12170, 12190, 12210, 12220, 12230, 12245, 12265, 12300, 12330, 12355, 12370, 12375, 12400, 12410, 12420, 12425, 12435, 12440, 12445, 12455, 12465, 12500, 12520, 12530, 12535, 12540, 12550, 12560, 12575, 12585, 12590, 12605, 12650, 12660, 12665, 12700, 12715, 12720, 12730, 12750, 12825, 12830, 12845, 12850, 12865, 12870, 12880, 12895, 12920, 12925, 12940, 12965, 12985, 13005, 13030, 13050, 13075, 13085, 13105, 13130, 13145, 13150, 13160, 13175, 13180, 13205, 13215, 13220, 13265, 13310, 13315, 13330, 13345, 13360, 13365, 13370, 13380, 13390, 13395, 13420, 13450, 13470, 13490, 13495, 13515, 13535, 13570, 13575, 13595, 13675, 13680, 13685, 13690, 13720, 13735, 13755, 13760, 13780, 13790, 13830, 13845, 13855, 13890, 13915, 13950, 14060, 14085, 14145, 14195, 14220, 14250, 14260, 14290, 14320, 14340, 14370, 14380, 14395, 14405, 14425, 14435, 14445, 14450, 14465, 14505, 14515, 14520, 14535, 14545, 14585, 14625, 14635, 14655, 14660, 14665, 14675, 14685, 14725, 14730, 14830, 14860, 14865, 14875, 14935, 14960, 14965, 15050, 15060, 15095, 15110, 15115, 15130, 15185, 15210, 15215, 15230, 15245, 15250, 15285, 15290, 15410, 15470, 15475, 15485, 15530, 15550, 15590, 15595, 15625, 15630, 15655, 15660, 15720, 15735, 15745, 15750, 15765, 15785,
// 15850, 15885, 15890, 15900, 15920, 15925, 15940, 15980, 15995, 16025, 16035, 16080, 16110, 16140, 16150, 16160, 16230, 16275, 16295, 16350, 16380, 16395, 16415, 16505, 16625, 16650, 16685, 16695, 16705, 16725, 16750, 16770, 16855, 16880, 16925, 16960, 16970, 16980, 17025, 17040, 17110, 17150, 17165, 17230, 17280, 17405, 17410, 17460, 17465, 17470, 17480, 17520, 17525, 17600, 17610, 17630, 17640, 17700, 17715, 17725, 17760, 17765, 17780, 17805, 17865, 17915, 17925, 17965, 18015, 18045, 18160, 18165, 18210, 18235, 18255, 18270, 18470, 18475, 18480, 18535, 18555, 18710, 18720, 18825, 18875, 18910, 18945, 18960, 19005, 19065, 19085, 19115, 19160, 19180, 19230, 19420, 19515, 19555, 19630, 19655, 19730, 19775, 19820, 19835, 19845, 19860, 19880, 19945, 19955, 19960, 20115, 20150, 20215, 20225, 20435, 20580, 20590, 20765, 20910, 21165, 21215, 21270, 21740, 21745, 21835, 21870, 21895, 22050, 22595, 22665, 22740, 23130, 23340, 23575, 23695, 23970, 24190, 24785, 24950, 25560, 25940, 26100, 26105, 26510, 26550, 26765, 27120, 27150, 27250, 27430, 27450, 28510, 28770, 29195, 30185, 30310, 30755, 31220, 33435, 34115, 34535, 35080, 35965, 36030, 36400, 36790, 37070, 38325, 38330, 38940, 40675, 42020, 43135, 43195, 43430, 43570, 44515, 44770, 45930, 49270, 49720, 49860, 51555, 52980, 54715, 55225, 55320, 57465, 58140, 60685, 67080, 71640, 72595, 72895, 78735
// 15210, 15250, 15290, 16350, 17480, 17525, 18255, 18475, 19005, 19180, 19420, 19655, 19820, 19835, 19845, 19955, 19960, 20150, 20225, 20435, 20765, 21165, 21215, 21740, 21835, 22665, 22740, 23575, 23695, 24785, 24950, 26105, 26510, 26550, 27120, 27250, 27430, 27450,
// 28770, 29195, 30185, 30310, 30755, 31220, 33435, 34115, 34535, 35080, 35965, 36030, 36400, 36790, 37070, 38325, 38330, 38940, 40675, 42020, 43135, 43195, 43430, 43570, 44515, 44770, 45930, 49270, 49720, 49860, 51555, 52980, 54715, 55225, 55320, 57465, 58140, 60685, 67080, 71640, 72595, 72895, 78735
//205,210,215,220,225,230,235,240,245,250,255,260,265,270,275,280,285,290,295,300,305,310,315,320,325,330,335,340,345,350,355,360,365,370,375,380,385,390,395,400,405,410,415,420,425,430,435,440,445,450,455,460,465,470,475,480,485,490,495,500,505,510,515,520,525,530,535,540,545,550,555,560,565,570,575,580,585,590,595,600,605,610,615,620,625,630,635,640,645,650,655,660,665,670,675,680,685,690,695,700,705,710,715,720,725,730,735,740,745,750,755,760,765,770,775,780,785,790,795,800,805,810,815,820,825,830,835,840,845,850,855,860,865,870,875,880,885,890,895,900,905,910,915,920,925,930,935,940,945,950,955,960,965,970,975,980,985,990,995,1000,1005,1010,1015,1020,1025,1030,1035,1040,1045,1050,1055,1060,1065,1070,1075,1080,1085,1090,1095,1100,1105,1110,1115,1120,1125,1130,1135,1140,1145,1150,1155,1160,1165,1170,1175,1180,1185,1190,1195,1200,1205,1210,1215,1220,1225,1230,1235,1240,1245,1250,1255,1260,1265,1270,1275,1280,1285,1290,1295,1300,1305,1310,1315,1320,1325,1330,1335,1340,1345,1350,1355,1360,1365,1370,1375,1380,1385,1390,1395,1400,1405,1410,1415,1420,1425,1430,1435,1440,1445,1450,1455,1460,1465,1470,1475,1480,1485,1490,1495,1500,1505,1510,1515,1520,1530,1535,1540,1545,1555,1560,1565,1570,1575,1580,1585,1590,1595,1600,1605,1610,1615,1620,1625,1630,1635,1640,1645,1650,1655,1660,1665,1670,1675,1680,1685,1690,1695,1700,1705,1710,1715,1720,1725,1730,1735,1740,1745,1750,1755,1760,1765,1770,1775,1780,1785,1790,1795,1800,1805,1810,1820,1825,1830,1835,1840,1845,1850,1855,1860,1865,1870,1875,1880,1885,1890,1900,1905,1915,1920,1925,1930,1940,1945,1950,1955,1960,1965,1970,1975,1980,1985,1990,1995,2000,2005,2010,2015,2020,2025,2030,2035,2040,2045,2050,2055,2060,2065,2070,2075,2080,2085,2090,2095,2100,2105,2110,2115,2125,2130,2135,2140,2145,2150,2160,2165,2170,2175,2180,2185,2190,2195,2205,2210,2220,2230,2235,2240,2245,2250,2255,2260,2265,2270,2275,2285,2290,2295,2300,2305,2315,2320,2325,2330,2340,2345,2350,2355,2360,2365,2370,2375,2380,2385,2390,2395,2400,2405,2410,2415,2420,2425,2430,2435,2440,2455,2460,2465,2470,2480,2500,2505,2510,2515,2520,2525,2535,2540,2545,2550,2555,2560,2565,2575,2580,2585,2590,2600,2605,2610,2615,2620,2630,2635,2640,2645,2650,2655,2660,2665,2675,2680,2685,2690,2695,2705,2710,2715,2720,2725,2730,2735,2740,2745,2750,2755,2760,2765,2770,2775,2780,2785,2790,2800,2805,2810,2820,2825,2830,2835,2845,2855,2860,2865,2870,2875,2880,2885,2895,2900,2910,2915,2920,2940,2945,2950,2955,2960,2965,2970,2975,2985,2995,3000,3005,3010,3020,3030,3035,3040,3050,3060,3065,3070,3080,3085,3090,3095,3100,3110,3115,3120,3130,3135,3140,3145,3150,3155,3160,3165,3175,3185,3190,3195,3200,3205,3210,3215,3220,3225,3230,3235,3240,3245,3255,3260,3265,3270,3280,3290,3300,3310,3320,3325,3330,3335,3340,3350,3355,3360,3370,3375,3380,3385,3400,3405,3410,3415,3420,3425,3430,3435,3445,3450,3455,3460,3465,3470,3475,3480,3490,3495,3505,3545,3560,3565,3575,3580,3585,3590,3595,3600,3610,3620,3630,3635,3640,3650,3670,3685,3690,3695,3700,3705,3720,3730,3750,3755,3765,3775,3785,3790,3795,3800,3815,3820,3825,3835,3845,3855,3865,3885,3890,3895,3900,3910,3915,3925,3930,3940,3945,3950,3955,3960,3965,3975,3985,3990,3995,4015,4020,4025,4040,4050,4060,4075,4080,4090,4095,4100,4110,4115,4120,4125,4130,4135,4140,4145,4170,4175,4180,4190,4195,4200,4210,4215,4220,4250,4260,4275,4280,4285,4290,4300,4305,4320,4325,4335,4345,4350,4355,4360,4365,4375,4425,4430,4435,4440,4455,4470,4485,4510,4525,4540,4545,4550,4560,4565,4575,4585,4590,4595,4600,4610,4625,4630,4635,4645,4650,4655,4660,4665,4680,4685,4690,4715,4730,4740,4755,4760,4765,4770,4775,4780,4800,4805,4825,4840,4845,4850,4855,4860,4865,4870,4875,4880,4885,4900,4905,4910,4915,4920,4925,4935,4940,4945,4950,4965,4970,4975,4980,4985,4990,5000,5005,5015,5030,5035,5040,5060,5065,5070,5085,5090,5105,5110,5125,5130,5155,5165,5175,5190,5200,5205,5210,5220,5235,5250,5260,5265,5290,5300,5305,5315,5325,5335,5345,5360,5375,5420,5425,5445,5455,5460,5470,5485,5490,5495,5510,5530,5615,5620,5655,5670,5675,5680,5705,5715,5730,5760,5770,5790,5795,5805,5835,5850,5865,5880,5885,5890,5900,5905,5910,5915,5920,5925,5935,5950,5955,5975,5985,6015,6025,6055,6060,6070,6090,6115,6125,6140,6145,6150,6155,6165,6175,6195,6200,6215,6225,6230,6235,6240,6265,6295,6305,6345,6355,6365,6380,6385,6395,6425,6435,6440,6445,6460,6465,6475,6485,6500,6515,6535,6545,6565,6590,6620,6625,6630,6635,6645,6660,6700,6710,6725,6735,6740,6750,6785,6815,6825,6830,6840,6885,6910,6945,6965,6970,6990,7040,7045,7135,7155,7170,7185,7215,7250,7285,7320,7335,7350,7365,7370,7375,7390,7415,7445,7460,7470,7485,7495,7520,7535,7540,7555,7565,7570,7615,7620,7720,7780,7815,7835,7840,7845,7850,7875,7900,7920,7940,7950,7970,7975,8065,8080,8085,8115,8130,8135,8180,8185,8200,8270,8285,8295,8325,8355,8410,8415,8485,8555,8570,8585,8600,8630,8675,8790,8850,8870,8875,8925,8940,9035,9040,9055,9065,9075,9115,9165,9185,9215,9265,9275,9285,9290,9320,9415,9420,9425,9460,9470,9485,9495,9500,9585,9610,9660,9665,9800,9850,9855,9945,9955,10035,10075,10105,10110,10290,10315,10325,10330,10335,10385,10410,10465,10510,10630,10685,10710,10805,10870,10910,11015,11020,11050,11070,11195,11225,11240,11250,11260,11380,11390,11445,11450,11455,11480,11500,11570,11620,11655,11670,11695,11750,11760,11815,11820,11890,11920,11925,11945,12045,12100,12150,12155,12165,12170,12190,12205,12245,12325,12330,12380,12540,12615,12650,12670,12725,12760,12835,12900,12925,13185,13345,13385,13425,13445,13470,13545,13550,13595,13600,13790,13810,13965,13975,14080,14085,14580,14595,14610,14625,14730,14760,14810,14840,14845,14875,14885,15085,15190,15385,15570,15630,15755,15765,15950,15970,16015,16280,16315,16425,16490,16865,17395,17455,17595,17770,17870,18130,18155,18590,18655,19180,19395,19610,20065,20235,20275,20375,20700,20765,20955,21490,22895,23085,23255,23665,25610,26110,26615,26700,28325,28595,30345,30885,44885,45475
//2600, 3965, 3995, 5885, 7485, 11750
9010, 9705, 10535, 11110
};

int CGameComponent::init(ITableHunded *table, data_pool_interface *data_pool, gamecontrol_interface *game_control, const void *rule, int rule_len)
{
	if (!table || !data_pool)
		return -1;

	m_table = table;
	m_data_pool = data_pool;
	m_game_control = game_control;
	m_tax = 0;
	m_game_logic.init();
	analy_game_rule(rule, rule_len);
	srand(time(0));
#ifdef CHECK_GRAPH
	for(int i =0; i<2094;i++)
	{
		string key = "BKF_";
		key = key + to_string(test_multer[i]);
		string graph;
		int index;
		if (!myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
		{
			MY_LOG_PRINT("%d not exist", test_multer[i]);		
		}
	}
#endif
#ifdef GEN_GRAPH
	std::remove("graph.txt");
	user_game_info info;
	info.bet = 100;
	info.cur_mode = 4;
	info.client_id = 31;
	int test_count = 100000;

	for(int i=150; i<200;i++)
	{		
		int gen_num = 0;
		int g_num = 1;
		if (test_multer[i] <= 2000) 
		{
			g_num = 50;
		}else if (test_multer[i] <= 5000) 
		{
			g_num = 50;
		}
		else if (test_multer[i] <= 10000) 
		{
			g_num = 20;
		}
		else
			g_num = 5;
		for (int j = 0; j < test_count; j++)
		{
			//MY_LOG_PRINT("isfree = %d", info.is_free);
			calcu_graph(info);
			//MY_LOG_PRINT("all_total_multer = %d", info.all_total_multer);
			if(info.all_total_multer == test_multer[i])
			{
				int wild_num = 0;
				int len = info.vec_remove.size();

				if (len >= 5 && len <= 10)
				{
					gen_num++;
					writeGraph(info);
					if (gen_num >= g_num)
					{
						MY_LOG_PRINT("%d ok", test_multer[i]);
						break;
					}
				}
			}
		}
		if(gen_num <= 0)
		{
			MY_LOG_PRINT("%d fail", test_multer[i]);
		}
		else if(gen_num < g_num)
		{
			MY_LOG_PRINT("%d not enough", test_multer[i]);
		}
	}
	
#endif

#ifdef GEN_FREE_GRAPH
	std::remove("graph.txt");
	user_game_info info;
	info.bet = 100;
	info.cur_mode = 0;
	info.client_id = 31;
	int test_count = 100000;

	for(int i=0; i<4;i++)
	{		
		int gen_num = 0;
		int g_num = 1;
		// if (test_multer[i] <= 2000) 
		// {
		// 	g_num = 1;
		// }else if (test_multer[i] <= 5000) 
		// {
		// 	g_num = 1;
		// }
		// else if (test_multer[i] <= 10000) 
		// {
		// 	g_num = 1;
		// }
		// else
		// 	g_num = 1;
		for (int j = 0; j < test_count; j++)
		{			
			calcu_graph(info,0);
			//MY_LOG_PRINT("all_total_multer = %d", info.all_total_multer);
			if(info.all_total_multer == (test_multer[i]))
			{
				if (info.game_vec_remove.size() <= 13)
				{
					gen_num++;
					writeGraph(info);
					if (gen_num >= g_num)
					{
						MY_LOG_PRINT("%d ok", test_multer[i]);
						break;
					}
				}
			}
		}
		if(gen_num <= 0)
		{
			MY_LOG_PRINT("%d fail", test_multer[i]);
		}
		else if(gen_num < g_num)
		{
			MY_LOG_PRINT("%d not enough", test_multer[i]);
		}
	}

	// for (int j = 0; j < test_count; j++)
	// {
	// 	calcu_graph(info);
	// 	if (info.game_vec_remove.size() <= 13)
	// 	{
	// 		writeGraph(info);
	// 		MY_LOG_PRINT("multer = %d", info.all_total_multer+20);
	// 	}
	// }
#endif
#ifdef TEST_BET
	std::remove("data.txt");
	std::ofstream ofs;
	ofs.open("data.txt", ios::out | ios::app);
	user_game_info info;
	info.bet = 100;
	info.cur_mode = 0;
	info.client_id = 31;

	int total = 0;
	int winCount = 0;
	int lianzhong = 0;
	int lianbuzhong = 0;
	int maxlianzhong = 0;
	int maxlianbuzhong = 0;
	int multer1 = 0;
	int multer2 = 0;
	int multer3 = 0;
	int multer4 = 0;
	int multer5 = 0;
	int multer6 = 0;
	int multer7 = 0;
	int multer8 = 0;
	int multer9 = 0;
	int test_count = 100000;
	int free = 0;

	for (int i = 0; i < test_count; i++)
	{
		MY_LOG_PRINT("The %d time", i+1);
		calcu_graph(info);
		calcu_result(info);
		//MY_LOG_PRINT("result = %d", info.result);		
		total += info.result;
		if(info.result > 0)
		{
			lianzhong++;
			winCount++;
			lianbuzhong = 0;
			if(lianzhong > maxlianzhong)
				maxlianzhong = lianzhong;							
		}
		else
		{
			lianzhong = 0;
			lianbuzhong++;
			if(lianbuzhong > maxlianbuzhong)
				maxlianbuzhong = lianbuzhong;
		}

		int multer = info.result;
		if (multer <= 0)
			multer9++;
		else if (multer <= 100)
			multer1++;
		else if (multer > 100 && multer <= 300)
			multer2++;
		else if (multer > 300 && multer <= 500)
			multer3++;
		else if (multer > 500 && multer <= 1000)
			multer4++;
		else if (multer > 1000 && multer <= 2000)
			multer5++;
		else if (multer > 2000 && multer <= 5000)
			multer6++;
		else if (multer > 5000 && multer <= 10000)
			multer7++;
		else
			{
				multer8++;
			}
		if(info.is_free)
			{
				free++;
			}
	}

	float rtp = float(total) / (test_count*100);
	float winRate = float(winCount) / test_count;	
	float freeRate = float(free) / test_count;
	ofs << "中奖概率: " << winRate * 100 << "%" << endl;
	ofs << "RTP: " << rtp * 100 << "%" << endl;
	ofs << "最大连中: " << maxlianzhong << endl;
	ofs << "freegame概率: " << freeRate * 100 << "%" << endl;
	//ofs << "最大连不中: " << maxlianbuzhong<< endl;

	rtp = float(multer9) / test_count;
	ofs << "0倍占比" << rtp * 100 << "%" << endl;
	rtp = float(multer1) / test_count;
	ofs << "0~1倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer2) / test_count;
	ofs << "1~3倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer3) / test_count;
	ofs << "3~5倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer4) / test_count;
	ofs << "5~10倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer5) / test_count;
	ofs << "10~20倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer6) / test_count;
	ofs << "20~50倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer7) / test_count;
	ofs << "50~100倍占比: " << rtp * 100 << "%" << endl;
	rtp = float(multer8) / test_count;
	ofs << "100以上倍占比: " << rtp * 100 << "%" << endl;

	ofs.close();
#endif
	set_timer(TID_CHECK_USER, 60 * 60 * 1000, 0, 0);
	srand(time(0));
	return 0;
}

bool CGameComponent::analy_game_rule(const void *rule, int rule_len)
{
	if (rule == NULL || rule_len == 0)
	{
		MY_LOG_WARN("%s,rule is null", __FUNCTION__);
		return false;
	}
	Json::Reader json_reader;
	Json::Value json_value;
	Json::Value va;
	if (!json_reader.parse((const char *)rule, json_value))
	{
		MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
		return false;
	}

	va = json_value["rule"];
	m_tax = va["tax"].asInt();
	MY_LOG_DEBUG("analy_game_rule tax(%d)", m_tax);
	return true;
}

void CGameComponent::reset_game_rule(const void *rule, int rule_len)
{
	if (rule == NULL)
	{
		MY_LOG_WARN("%s,rule is null", __FUNCTION__);
		return;
	}
	Json::Reader json_reader;
	Json::Value json_value;
	Json::Value va;
	if (!json_reader.parse((const char *)rule, json_value))
	{
		MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
		return;
	}

	va = json_value["rule"];
	m_tax = va["tax"].asInt();
	MY_LOG_DEBUG("reset_game_rule tax(%d)", m_tax);
	return;
}

void CGameComponent::clear()
{

	reset();
}

void CGameComponent::reset()
{
}

//**********************************定时器***************************************\\

bool CGameComponent::set_timer(_uint8 time_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat /* = 1 */)
{

	bool ret = m_table->set_timer(time_id, timeout_msec, param, repeat);
	if (!ret)
	{
		MY_LOG_ERROR("set timer fail. time_id=%d", time_id);
		return false;
	}

	return true;
}

bool CGameComponent::kill_timer(_uint8 time_id)
{

	bool ret = m_table->kill_timer(time_id);
	if (!ret)
	{
		MY_LOG_ERROR("kill timer fail. time_id=%d", time_id);
		return false;
	}

	return true;
}

void CGameComponent::on_time(_uint8 time_id, _uint64 param)
{
	time_t current = time(NULL);
	int kicked_total_count = 0;

	if (time_id == TID_CHECK_USER)
	{
		for (auto &info : m_map_user_game_info)
		{
			if (current - info.second._last_kick_user_offline_tm_sec > KICK_USER_OFFLINE_DURATION)
			{
				IUser *user = m_table->get_user_from_uid(info.first);
				if (current - user->get_enter_time() > KICK_USER_OFFLINE_DURATION)
				{
					m_table->on_user_leave(user);
					m_map_user_game_info.erase(info.first);
					kicked_total_count++;
				}
			}
		}
	}
}

//**********************************客户端消息处理***************************************\\

// 接收客户端消息;
int CGameComponent::on_recv(_uint8 mcmd, _uint8 scmd, const void *pData, int size, IUser *pUser)
{

	return 0;
}

void CGameComponent::on_http_recv(socket_id sid, int uid, int opid, const std::string &str_param)
{
	MY_LOG_DEBUG("on_http_recv");
	IUser *user = m_table->get_user_from_uid(uid);
	if (user == nullptr)
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::internal, "", "invalid user", nullptr, 0);
		MY_LOG_ERROR("user[%d] sid[%d] invalid user", uid, sid);
		return;
	}
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		user_game_info game_info;
		game_info.uid = uid;
		game_info.token = myredis::GetInstance()->get_data_by_uid_for_string(uid, DB_COMM, "current_token");
		game_info.user_type = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_USER_TYPE);
		game_info.web_token = myredis::GetInstance()->get_token_by_uid(uid);
		game_info.sub_client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_SUB_CHANNEL_ID);
		game_info.client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
		m_map_user_game_info[uid] = game_info;
		iter = m_map_user_game_info.find(uid);
	}
	iter->second._last_kick_user_offline_tm_sec = time(NULL);

	switch (opid)
	{
	case serverProto::AckType::spin:
	{
		_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
		user->set_gold(gold);
		on_user_bet_roll(user, str_param, iter->second);
	}
	break;

	case serverProto::AckType::info:
	{
		on_user_game_info(user, str_param, str_param.length(), iter->second);
	}
	break;

	case serverProto::AckType::jilijpSetting:
	{
		send_setting_result(user);
	}
	break;

	case serverProto::AckType::fullJpInfoAll:
	{
	}
	break;
	case serverProto::AckType::notice:
	{
		send_notice_result(user);
	}
	break;

	default:
		break;
	}
}

//**********************************房间消息处理***************************************\\

void CGameComponent::on_user_game_info(IUser *user, const std::string &data, int size, user_game_info &game_info)
{
	auto uid = user->get_user_id();
	// todo : add error process
	serverProto::Request pb_request;
	if (!pb_request.ParseFromString(data))
	{
		MY_LOG_ERROR("failed to parse pb request, uid:%d", uid);
		return;
	}

	// todo : add error process
	serverProto::InfoReq req;
	if (!req.ParseFromString(pb_request.req()))
	{
		MY_LOG_ERROR("user[%d] failed to parse pb spin request", uid);
		return;
	}

	std::string debugstr;
	using namespace google::protobuf;
	TextFormat::PrintToString(req, &debugstr);
	MY_LOG_DEBUG("user[%d] on_user_game_info recved spin request: %s", uid, debugstr.c_str());

	// 单一钱包请求第三方刷新金币
	int api_type = user->get_api_type();
	if (api_type == MODE_SINGLE)
	{
		m_table->get_flush_user_gold(uid, GameConfig::GetInstance()->get_game_id());
		return;
	}
	_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	user->set_gold(gold);
	send_game_info_result(user);
}

void CGameComponent::handle_user_notice_request(IUser *user, const std::string &msg, int size)
{

	serverProto::NoticeReq req;
	if (req.ParseFromString(msg))
	{
		MY_LOG_ERROR("user[%d] failed to parse pb notice request", user->get_user_id());
		return;
	}
}

// 房间消息处理;
int CGameComponent::on_user_action(IUser *user, _uint8 type)
{
	if (!user)
		return -1;

	_uint32 nUserID = user->get_user_id();
	switch (type)
	{
	case comm_action_sit_down:
	case comm_action_online:
	{
		on_user_enter(user);
	}
	break;
	case comm_action_leave:
	case comm_action_offline:
	{
		on_user_leave(user);
	}
	break;
	case comm_action_update_gold:
	{
		on_update_user_gold_result(user);
	}
	break;
	}
	return 0;
}

void CGameComponent::db_get_data(void *obj, const char *data, int len)
{

	db_data *post_data = (db_data *)obj;
	if (post_data == NULL)
		return;
}

/*
 * 判断玩家下线是否能删除玩家（用于加金币等玩家结算判断）
 */
bool CGameComponent::is_can_clear_user(int chairid)
{

	return false;
}

/*
 * 进入房间
 */
void CGameComponent::on_user_enter(IUser *pUser)
{

	MY_LOG_DEBUG("on_user_enter user_id(%u)", pUser->get_user_id());
	m_table->send(pUser, cmd_net::CMD_ROOM, cmd_net::SUB_ENTER_ROOM_SUCC, NULL);

	send_my_user_info(pUser);
	send_room_info(pUser);
}

// 离开房间
void CGameComponent::on_user_leave(IUser *pUser)
{

	MY_LOG_DEBUG("on_user_leave user_id(%u)", pUser->get_user_id());
}

int get_time_mic()
{
	auto now = std::chrono::system_clock::now(); // 获取当前系统时钟时间点
	auto duration = now.time_since_epoch();		 // 计算从1970年到现在经过的时长

	long microseconds = std::chrono::duration_cast<std::chrono::microseconds>(duration).count(); // 将时长转换为微秒

	return microseconds%100000000LL;
}

// 请求下注
void CGameComponent::on_user_bet_roll(IUser *user, const std::string &data, user_game_info &game_info)
{
	// todo : add error process
	serverProto::Request pb_request;
	if (!pb_request.ParseFromString(data))
	{
		MY_LOG_ERROR("failed to parse pb request, uid:%d", user->get_user_id());
		return;
	}

	// todo : add error process
	serverProto::SpinReq req;
	if (!req.ParseFromString(pb_request.req()))
	{
		MY_LOG_ERROR("failed to parse pb spin request, uid:%d", user->get_user_id());
		return;
	}

	// std::string debugstr;
	// using namespace google::protobuf;
	// TextFormat::PrintToString(req, &debugstr);
	// MY_LOG_PRINT("user[%d] on_user_bet_roll recved spin request: %s", user->get_user_id(), debugstr.c_str());

	int bet_count = req.bet() * 100;
	
	if(req.has_mall())
	{
		game_info.cur_mode = 1;
		game_info.mall_bet = req.mall().bet()*100;
		//MY_LOG_PRINT("mall_bet = %d", game_info.mall_bet);
	}
	else
	{
		game_info.cur_mode = 0;
		game_info.mall_bet = bet_count;
	}

	int uid = user->get_user_id();
	game_info.bet = bet_count;

	if (user->get_single_status() == 1)
	{
		MY_LOG_WARN("on_user_bet_roll status erro uid:%d", uid);
		return;
	}

	//std::string md5key;
	int now_time = get_time_mic();
	//calcu_md5key(uid, md5key, now_time);

	game_info.round_index = m_game_logic.gen_new_round_index();
	game_info.round_index_v2 = game_info.round_index * 1000 + 77;
	game_info.balance_before_round = user->get_gold();
	
	// 是否异步单一钱包模式，扣下注的钱
	if (user->get_api_type() == MODE_SINGLE)
	{
		write_transfe_inorut_one(-game_info.mall_bet,
								 0,
								 GOLD_HANDRED_BET,
								 false,
								 game_info.mall_bet,
								 uid,
								 false,
								 now_time
								 );

		return;
	}
	else
	{
		// 玩家是否金币不足
		_tint64 nUserGold = user->get_gold();
		if (bet_count > nUserGold)
		{
			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinReject, "", "gold not enough to bet", nullptr, 0);
			MY_LOG_ERROR("user[%d] on_user_bet_roll gold not enouth to bet, bet_count: %.3f, user gold: %lld", uid, bet_count, nUserGold);
			return;
		}
		user_bet_logic(user, game_info, false, bet_count, now_time);
	}
}

void CGameComponent::user_bet_logic(IUser *pUser, user_game_info &game_info, bool use_free, int total_bet, int now_time)
{
	int uid = pUser->get_user_id();

	if (!use_free)
	{
		m_table->update_gold_and_clore_single(uid, -total_bet, GOLD_HANDRED_BET, 0);
	}

	calcu_graph(game_info);
	calcu_result(game_info);

	if (game_info.result > 0)
	{
		m_table->update_gold_and_clore_single(uid, game_info.result, GOLD_DEL_GAME, 0);
	}
	//calcu_tax(pUser, md5key);
	write_transfe_inorut_one(game_info.result,
							 game_info.pay_out,
							 GOLD_DEL_GAME,
							 true,
							 game_info.mall_bet,
							 uid,
							 true,
							 now_time
							 );

	write_versus_bill(uid, now_time);

	int bet = game_info.bet;
    // SpinRoundHistory h(game_info.uid, game_info.round_index, GameConfig::GetInstance()->get_game_id(), std::round(bet/100.0), 
    //         std::round(game_info.result/100.0) - std::round(bet/100.0), \
    //         ((float)game_info.balance_before_round + game_info.result - bet) / 100.0, 0, "PHP", game_info.record_id);
    // if (m_table) {
    //     m_table->write_spin_round_history(h);
    // }

	send_bet_roll_result(pUser, get_error_msg(MSG_ROOM_BET_SUCCESS, pUser->get_user_country()), true);
}

void CGameComponent::insert_scatter(user_game_info &game_info)
{
	int r = getRand(1, 100);

	if (r < 2)
	{
		m_game_logic.insert_scatter(game_info.vec_remove, 2);
		game_info.scatter_count = 2;
	}
	else if (r < 10)
	{
		m_game_logic.insert_scatter(game_info.vec_remove, 1);
		game_info.scatter_count = 1;
	}
}
// 计算图形
void CGameComponent::calcu_graph(user_game_info &game_info, int i)
{
	game_info.game_vec_remove.clear();	
	game_info.all_total_multer = 0;
	game_info.game_multer.clear();
	game_info.free_total_round.clear();
	game_info.has_plus.clear();
	game_info.is_free = false;
	game_info.reset();
	//m_game_logic.gen_no_reward_game_data(game_info);
#if 0
	if (i >= 0)
	{
		m_game_logic.gen_no_reward_game_data(game_info);
		m_game_logic.insert_scatter(game_info.vec_remove, 3);
		game_info.scatter_count = 3;
		game_info.total_multer = 200;
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.free_total_round.push_back(0);
		game_info.has_plus.push_back(0);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);

		int count = 8;
		for (int i = 0; i < count; i++)
		{
			game_info.reset();
			game_info.comboBonus = 1;
			game_info.is_free = true;
			m_game_logic.rand_card(game_info);
			if (getRand(1, 100) < 25)
			{
				m_game_logic.insert_scatter(game_info.vec_remove, 1, true);
				count++;
				game_info.has_plus.push_back(1);
			}
			else
			{
				game_info.has_plus.push_back(0);
			}
			game_info.game_multer.push_back(game_info.total_multer);
			game_info.free_total_round.push_back(count);
			game_info.all_total_multer += game_info.total_multer;
			game_info.game_vec_remove.push_back(game_info.vec_remove);
		}
	}
	else
	{
		game_info.comboBonus = 1;
		game_info.is_free = false;
		m_game_logic.rand_card(game_info);
		//insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
	}
#else
	string graph;
	int index,multer;

	bool bRet = get_lottery_result(game_info.client_id, game_info.cur_mode, multer);
	if (!bRet)
	{
		m_game_logic.gen_no_reward_game_data(game_info);
		MY_LOG_ERROR("get_lottery_result failed");
		insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
		return;
	}
	//multer = 1805;
	//MY_LOG_PRINT("multer = %d", multer);
	
	if (multer == 0 && game_info.cur_mode == 0)
	{		
		m_game_logic.gen_no_reward_game_data(game_info);
		insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
		
		return;
	}
	
	if(multer > 1000 || game_info.cur_mode == 1)
	{
		m_game_logic.gen_no_reward_game_data(game_info);
		m_game_logic.insert_scatter(game_info.vec_remove, 3);
		game_info.scatter_count = 3;
		game_info.total_multer = 200;
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.free_total_round.push_back(0);
		game_info.has_plus.push_back(0);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);

		game_info.is_free = true;
		string key = "BKF_";
		key = key + to_string(multer);

		if (myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
		{
			m_game_logic.gen_freegame_data_from_graph_data(game_info, graph);
		}
		else
		{
			MY_LOG_ERROR("get_graph_from_redis failed, multer = %d", multer);
			int count = 8;
			for (int i = 0; i < count; i++)
			{
				game_info.reset();
				game_info.comboBonus = 1;
				game_info.is_free = true;
				m_game_logic.gen_no_reward_game_data(game_info);
				game_info.game_multer.push_back(game_info.total_multer);
				game_info.free_total_round.push_back(count);
				game_info.all_total_multer += game_info.total_multer;
				game_info.game_vec_remove.push_back(game_info.vec_remove);
			}
		}
		if(multer != game_info.all_total_multer)
		{
			MY_LOG_ERROR("calcu failed, multer = %d", multer);
		}
	}
	else
	{
		game_info.is_free = false;
		string key = "BK_";
		key = key + to_string(multer);

		if (myredis::GetInstance()->get_graph_from_redis(key, graph, index, 3))
		{
			//MY_LOG_PRINT("graph = %s", graph.c_str());
			m_game_logic.gen_game_data_from_graph_data(game_info, graph);			
		}
		else
		{
			m_game_logic.gen_no_reward_game_data(game_info);
			MY_LOG_ERROR("get_graph_from_redis failed, multer = %d", multer);
		}
		
		insert_scatter(game_info);
		game_info.game_multer.push_back(game_info.total_multer);
		game_info.all_total_multer += game_info.total_multer;
		game_info.game_vec_remove.push_back(game_info.vec_remove);
	}
#endif
}

// 计算游戏结果
void CGameComponent::calcu_result(user_game_info &game_info)
{
	game_info.result = (game_info.all_total_multer*game_info.bet)/100.0;
	game_info.pay_out = game_info.result - game_info.mall_bet;
}

// 计算税收
void CGameComponent::calcu_tax(IUser *pUser, const std::string &md5key)
{
	int uid = pUser->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
		return;

	// 输钱不用税收
	if (iter->second.pay_out <= 0)
		return;

	int tax = iter->second.pay_out * m_tax / 1000;
	if (tax == 0)
		return;

	MY_LOG_DEBUG("calcu_tax uid:%u tax:%d m_tax:%d",
				 uid, tax, m_tax);
	iter->second.tax = tax;
}

// 获得md5key值
void CGameComponent::calcu_md5key(int uid, string &strkey, int time)
{
	char md5str[36];
	char md5Key[33];
	memset(md5str, 0, sizeof(md5str));
	memset(md5Key, 0, sizeof(md5Key));
	mysprintf(md5str, sizeof(md5str), "%d%d%d%d", time,
			  m_table->get_room_id(), (int)m_table->get_node_id(), uid);
	g_com->_md5security(md5str, md5Key);
	strkey = md5Key;
}

/****************************************************/

// 打包玩家个人对局明细
void CGameComponent::pack_user_detail(string &detail, int uid)
{
	// todo: jili的游戏需要重新实现
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
		return;

	Json::Value json_req_value;
	int bet = iter->second.bet;

	json_req_value["result"] = Json::Value(iter->second.result);
	json_req_value["bet"] = Json::Value(bet);
	json_req_value["payout"] = Json::Value(iter->second.pay_out);
	Json::FastWriter json_writer;
	detail = json_writer.write(json_req_value);
	detail = replace_all(detail, "\"", "\\\'", 0);
}

// 对局流水
void CGameComponent::write_versus_bill(int uid, int now_time)
{
	char szlist[10] = {0};
	Json::Value va;
	Json::Value json_req_value;

	// 头部信息
	json_req_value["r_id"] = Json::Value("");
	int now = time(0);
	json_req_value["post_time"] = Json::Value(now);
	json_req_value["room_id"] = Json::Value(m_table->get_room_id());
	json_req_value["table_id"] = Json::Value(m_table->get_node_id());
	json_req_value["players"] = Json::Value(1);
	json_req_value["tax"] = Json::Value(0);
	json_req_value["play_time"] = Json::Value(0); /* 本局耗时 */
	json_req_value["match_id"] = Json::Value(m_table->get_room_id());
	json_req_value["game_detail"] = Json::Value("");
	json_req_value["game_id"] = Json::Value(GameConfig::GetInstance()->get_game_id());
	json_req_value["node_id"] = Json::Value(m_table->get_node_id());
	json_req_value["room_mode"] = Json::Value(TYPE_HUNDRED);

	// 一批批用户数据打包写入
	iter_user_game_info itr = m_map_user_game_info.find(uid);
	if (itr != m_map_user_game_info.end())
	{
		IUser *user = m_table->get_user_from_uid(itr->first);
		va["uid"] = Json::Value(uid);
		int gold = 0;
		if (user)
		{
			va["nickname"] = Json::Value(user->get_nick_name());
			va["client_id"] = Json::Value(user->get_client_id());
			va["version"] = Json::Value(user->get_version());
			va["usertype"] = Json::Value(user->get_user_type());

			gold = user->get_gold();
		}
		else
		{
			va["client_id"] = Json::Value(0);
			va["version"] = Json::Value("");
			va["usertype"] = Json::Value(0);
			va["nickname"] = Json::Value("");
		}

		if (itr->second.pay_out == 0)
		{
			va["result"] = Json::Value(3); /* 流局|平分 */
		}
		else
		{
			if (itr->second.pay_out < 0)
				va["result"] = Json::Value(1); /* 输 */
			else
				va["result"] = Json::Value(2); /* 赢 */
		}

		va["times"] = Json::Value(0);					 /* 倍数 */
		va["score1"] = Json::Value(itr->second.pay_out); /* 当局变化的积分 */
		va["score2"] = Json::Value(gold);				 /* 当前积分 */
		va["point1"] = Json::Value(0);
		va["point2"] = Json::Value(0);

		string user_detail;
		pack_user_detail(user_detail, uid);
		va["poker_detail"] = Json::Value(user_detail.c_str());
		va["tax"] = Json::Value(0);
		int bet = itr->second.bet;

		va["bet"] = Json::Value(bet);

		mysprintf(szlist, sizeof(szlist), "player_%d", 0);
		json_req_value[szlist] = Json::Value(va);
	}
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	m_table->write_versus_bill(json_req_data.c_str(), json_req_data.length());
	MY_LOG_DEBUG("versus_bill: %s", json_req_data.c_str());
}

// 投付记录
void CGameComponent::write_transfe_inorut_one(int out_score,
											  int pay_out,
											  int bill_type,
											  bool is_end,
											  int bet,
											  int uid,
											  bool write_db,
											  int time
											  )
{
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	IUser *user = m_table->get_user_from_uid(uid);
	if (iter == m_map_user_game_info.end() || user == NULL)
	{
		MY_LOG_ERROR("write_transfe_inorut_one error");
		return;
	}
	hundred_transfer_inout_one data;
	char parent_id[64] = "";
	sprintf(parent_id, "%d%d%d", GET_INT_CONFIG("game_id"),uid,time);
	data.parent_id = parent_id;
	data.bill_type = bill_type;
	data.is_end = is_end;
	data.bet = bet;
	data.result = out_score;
	data.pay_out = pay_out;
	data.uid = uid;
	data.time = time;
	data.call_back = (bill_type == GOLD_HANDRED_BET);
	data.write_db = (bill_type == GOLD_DEL_GAME || bill_type == GOLD_DEL_TAX_REVENUE);
	data.api_type = user->get_api_type();
	data.sub_client_id = iter->second.sub_client_id;
	data.client_id = iter->second.client_id;
	data.web_token = iter->second.web_token;
	data.user_type = iter->second.user_type;
	m_table->write_transfer_inout(data);
}

bool CGameComponent::transfer_inout_result(transfer_inout_result_info data)
{
	MY_LOG_DEBUG("user[%d] transfer_inout_result req_info: %s is_success: %d erro_code: %d",
				 data.uid, data.req_info.c_str(),
				 data.is_suc ? 1 : 0,
				 data.erro_code);

	IUser *user = m_table->get_user_from_uid(data.uid);
	if (user == NULL)
	{
		MY_LOG_ERROR("user[%d] transfer_inout_result cannot found user by uid", data.uid);
		return false;
	}

	iter_user_game_info iter = m_map_user_game_info.find(data.uid);
	if (iter == m_map_user_game_info.end())
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::spinCoinError, "", "transfer gold failed", nullptr, 0);
		MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
		return false;
	}

	int user_country = myredis::GetInstance()->get_data_by_uid(data.uid, DB_COMM, CUR_COUNTRY_CODE);
	if (!data.is_suc)
	{
		send_bet_roll_err_result(user, serverProto::AckType::spin,
								 serverProto::Error::spinCoinError, "", "transfer gold failed", nullptr, 0);
		MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
		return true;
	}

	if (data.erro_code != SUCCESS)
	{

		switch (data.erro_code)
		{
		case NOT_ENOUGH_BALANCE:
		{

			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinReject, "", "gold not enough to bet", nullptr, 0);
			MY_LOG_ERROR("user[%d] on_user_bet_roll gold not enouth to bet, bet: %d", data.uid, data.bet);
			return true;
		}
		break;

		default:
		{
			send_bet_roll_err_result(user, serverProto::AckType::spin,
									 serverProto::Error::spinCoinError, "", "transfer gold failed", nullptr, 0);
			MY_LOG_ERROR("user[%d] transfer gold failed, bet: %d", data.uid, data.bet);
			return true;
		}
		break;
		}
	}

	// 扣注成功后才才开始游戏逻辑
	user_bet_logic(user, iter->second, false, data.bet, data.time);

	return true;
}

void CGameComponent::on_update_user_gold_result(IUser *user)
{
	_uint32 uid = user->get_user_id();
	_tint64 gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
	user->set_gold(gold);
	MY_LOG_PRINT("user[%d] send game info response with gold: %d", uid, gold);
	send_game_info_result(user);
}

//获得抽奖结果
bool CGameComponent::get_lottery_result(int client_id, int mode, int &result)
{
	int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();

	return BoxingKing::winlose_control::GetInstance()->get_lottery_ctr_result(
		client_id, 
		game_id,
		nodeid,
		mode,
		result);
}

//获取底注
bool CGameComponent::get_base_bet(int client_id, int &result)
{
	int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();

	return BoxingKing::winlose_control::GetInstance()->get_lottery_ctr_result(
		client_id, 
		game_id,
		nodeid,
		2,
		result);
}
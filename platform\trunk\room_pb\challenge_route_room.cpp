﻿#include "challenge_route_room.h"
#include "table_manager.h"
#include "log_manager.h"
#include "game_frame.h"
#include "room_route.h"
#include "game_rule.h"
#include "user_manager.h"
#include "room_route_challenge.h"
#include "dbproxy_data.h"
#include "bill_fun.h"
#include "web_request.h"
#include "table_Hundred.h"
#include "robot_manager.h"
static CChallengeRouteRoom _challenge_route_room;
extern CRoomRouteChallenge _room_route_challenge;

CChallengeRouteRoom::CChallengeRouteRoom()
{
    __reg_gate_rcv(on_challenge_ack, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_CHALLENGE_ACK);
	__reg_gate_rcv(on_challenge_ack_ex, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_CHALLENGE_ACK_EX);
    __reg_gate_rcv(on_challenge_dismiss_table, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_DISMISS_TABLE);
	__reg_gate_rcv(on_challenge_userlist_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_NOMAL_USER_INFO_REQ);
	__reg_gate_rcv(on_challenge_web_userlist_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_WEB_USER_INFO_REQ);

	__reg_gate_rcv(on_patch_card_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_PATCH_CARD_DATA);
	__reg_gate_rcv(on_patch_table_card_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_PATCH_TABLE_CARD_DATA);
	__reg_gate_rcv(on_flush_client_version_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_CLIENT_VERSION_DATA);
	__reg_gate_rcv(on_update_node_data_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_UPDATE_NODE_DATA);
	__reg_gate_rcv(on_update_limit_data_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_LIMIT);
	__reg_gate_rcv(on_update_room_game_config_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_GAME_CONFIG);
	
	__reg_gate_rcv(on_web_create_room, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_CREATE_ROOM);
	__reg_gate_rcv(on_dissmiss_room, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_DISMISS_USER);

	__reg_gate_rcv(on_check_user, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_CHALLENGE_CHECK_USER);
	__reg_gate_rcv(on_update_room_status, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_CHALLENGE_ROOM_STATUS);
	__reg_gate_rcv(on_reset_update_time, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_CHALLENGE_RESET_TIME);

	__reg_gate_rcv(on_gm_control, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_ROOM_CHALLENGE_GM_CONTROL);

	__reg_gate_rcv(on_update_props_config_req, cmd_net::CMD_ROUTE_ROOM, cmd_net_svr::CMD_ROUTE_CHALLENGE_UPDATE_PROPS_CONFIG);
}

CChallengeRouteRoom::~CChallengeRouteRoom()
{

}

FTYPE(void) CChallengeRouteRoom::on_test_person_srategy(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{

}


FTYPE(void) CChallengeRouteRoom::on_update_new_stock(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{

}

FTYPE(void) CChallengeRouteRoom::on_update_stock_robot_stock(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{

}

FTYPE(void) CChallengeRouteRoom::on_gm_control(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{
	room_route::CGmControl req(data, size);
	room_route::CGmControlResult resp;
	resp.set_sid(req.get_sid());
	MY_LOG_DEBUG("on_gm_control.....");

	//只有百人场支持
	if (config_manager::GetInstance()->get_room_type() != TYPE_HUNDRED)
	{
		resp.set_result(102);
		resp.set_msg("game is not hunded", 0);
		g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_GM_CONTROL_RESULT, &resp);
		return;
	}

	//寻找正确的节点房间
	CTableHundred* htable = TableManager::GetInstance()->get_hundred_table(req.get_nodeid());
	if (htable == NULL)
	{
		resp.set_result(103);
		resp.set_msg("node is not find", 0);
		g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_GM_CONTROL_RESULT, &resp);
		return;
	}

	//组件进行控制
	char tips[128] = { 0 };
	int tips_len = 0;
	int ret = htable->gm_control(req.get_Rule(), req.Rule_length(), tips, tips_len, req.get_ip(), req.get_account_id());
	if (ret != 0)
	{
		resp.set_result(ret);
		resp.set_msg(tips, tips_len);
		g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_GM_CONTROL_RESULT, &resp);
		return;
	}

	resp.set_result(0);
	resp.set_msg("ok", 0);
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_GM_CONTROL_RESULT, &resp);
}

FTYPE(void) CChallengeRouteRoom::on_reset_update_time(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{
	CRoomRouteChallenge::set_info_time();
}

FTYPE(void) CChallengeRouteRoom::on_update_room_status(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{
	room_route::UpdateRoomStatus req(data, size);
	config_manager::GetInstance()->m_room_status = req.get_status();

    MY_LOG_DEBUG("CChallengeRouteRoom::on_update_room_status m_room_status:%d .", config_manager::GetInstance()->m_room_status);
	
	//让空闲机器人全部退出房间,正在玩牌的机器人玩完本局
	if (req.get_status() == ROOM_STATUS_PAUSE)
	{
		robot_manager::GetInstance()->release_all_free_robot();
	}

	room_route::UpdateRoomStatusResp resp;
	resp.set_roomid(CGameFrame::GetInstance()->get_room_id());
	resp.set_sid(req.get_sid());
	resp.set_clientid(req.get_clientid());
	resp.set_limituid(req.get_limituid(), 0);
	resp.set_limitcount(req.get_limitcount());
	resp.set_status(req.get_status());
	g_server->send_route_server_batch(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_UPDATE_ROOM_STATUS_RESP, &resp);
}

FTYPE(void) CChallengeRouteRoom::on_check_user(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{
	room_route::CheckUser check_user(data, size);
	room_route::CheckUserResult check_result;
	check_result.set_result(0);
	check_result.set_roomid(CGameFrame::GetInstance()->get_room_id());
	check_result.set_uid(check_user.get_uid());

	IUser* user = UserManager::GetInstance()->get_user(check_user.get_uid());
	if (user != NULL && user->get_table_id() != INVALID_TABLE)
		check_result.set_result(1);
	
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHECK_USER_RESULT, &check_result);
}

FTYPE(void) CChallengeRouteRoom::on_dissmiss_room(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	room_route::DissmissUser req;
	req.unpack(data, size);

	IUser* pUser = UserManager::GetInstance()->get_user(req.get_uid());
	if (pUser == NULL)
	{
		CRoomRouteChallenge::send_web_user_leave(req.get_uid());
		CRoomRouteChallenge::send_nomal_user_leave(req.get_uid());
		return;
	}

	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		int node_id = pUser->get_node_id();
		CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
		if (ptable)
		{
			ptable->on_force_end(req.get_uid());
		}
	}
	else
	{
		CTable* pTable = TableManager::GetInstance()->get_table(pUser->get_table_id());
		if(!pTable) 
		{
			return;
		}

		pTable->on_forced_end(pUser->get_user_id());
	}
}

FTYPE(void) CChallengeRouteRoom::on_web_create_room(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	room_route::WebCreateRoom req;
	req.unpack(data, size);

	//创建桌子
	int ret = CGameFrame::GetInstance()->on_web_create(req.get_uid(),(_uint32)req.get_code(),(void*)req.get_rule(),req.rule_length());
	if(ret != 0)
	{
		_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), room_route::WEB_NOT_FIND_ROOM, NULL, 1, req.get_sid());
		return;
	}

	//找到桌子
	CTable* pTable = TableManager::GetInstance()->find_table_from_num((_uint32)req.get_code());
	if(!pTable) 
	{
		_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), room_route::WEB_NOT_FIND_TABLE, NULL, 1, req.get_sid());
		return;
	}

	//免费
	pTable->set_create_time();
	bool bfree = (pTable->get_cost_card() == 0);
	if(bfree)
	{
		_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), 0, pTable, 1, req.get_sid());
		return;
	}
	
	int cost_card = pTable->get_cost_card();

	cmd_dbproxy::CDBProxyExceSection section;
	section.set_UserID(req.get_pay_uid());
	section.set_lParamInt(cost_card);
	section.set_wParamBuffer((const char*)data, size);
	on_web_get_user_room_card_dbproxy(section);
}

FTYPE(void) CChallengeRouteRoom::on_web_get_user_room_card_dbproxy(cmd_dbproxy::CDBProxyExceSection &section)
{
	Json::FastWriter json_writer;
	Json::Value json_req_value;

	json_req_value["uid"] = Json::Value(section.get_UserID());
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0,on_web_sub_user_room_card_dbproxy,section.get_UserID(),"lobby_get_yuanbao_count",json_req_data.c_str(),section);
}

FTYPE(void) CChallengeRouteRoom::on_web_sub_user_room_card_dbproxy(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section)
{
	room_route::WebCreateRoom req;
	req.unpack(section.get_wParamBuffer(),section.wParamBuffer_length());

	Json::Value json_value; 
	if(!get_dbproxy_resp_value(data_json,size,json_value,NULL))
	{
		_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), room_route::WEB_DIAMOND_LIMIT, NULL, 1, req.get_sid());
		return;
	}
	else
	{
		int cur_card = json_value["recordset0"][0]["quantity"].asInt64();
		if (cur_card < section.get_lParamInt())
		{
			_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), room_route::WEB_DIAMOND_LIMIT, NULL, 1, req.get_sid());
			return;
		}
	}

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	json_req_value["uid"] = Json::Value(section.get_UserID());
	json_req_value["post_time"] = Json::Value(g_com->_get_time32());//unix时间戳
	json_req_value["type_id"] = Json::Value(CREATE_ROOM_MONEY_BILL);//
	json_req_value["type_id_sub"] = Json::Value(0);//没有特别要求填0
	json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
	json_req_value["quantity"] = Json::Value(section.get_lParamInt());//数量
	json_req_value["room_id"] = Json::Value(CGameFrame::GetInstance()->get_room_id());//没有或者取不到填0
	json_req_value["version"] = Json::Value("1.0.0");
	json_req_value["kind_id"] = Json::Value(0);
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0,on_web_sub_user_room_card_result,section.get_UserID(),"lobby_del_yuanbao",json_req_data.c_str(),section);
}

FTYPE(void) CChallengeRouteRoom::on_web_sub_user_room_card_result(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section)
{
	room_route::WebCreateRoom req;
	req.unpack(section.get_wParamBuffer(),section.wParamBuffer_length());

	int ret = 0;
	Json::Value json_value; 
	if(!get_dbproxy_resp_value(data_json,size,json_value,NULL))
	{
		_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), room_route::WEB_DIAMOND_LIMIT, NULL, 1, req.get_sid());
		return;
	}

	CTable* pTable = TableManager::GetInstance()->find_table_from_num(req.get_code());
	if( !pTable ) 
	{
		TableManager::GetInstance()->delete_table(pTable);
		_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), room_route::WEB_DIAMOND_LIMIT, NULL, 1, req.get_sid());
		return;
	}

	_room_route_challenge.send_challenge_create_result(req.get_uid(), req.get_code(), 0, pTable, 1, req.get_sid());
	return;
}	

FTYPE(void) CChallengeRouteRoom::on_challenge_web_userlist_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (config_manager::GetInstance()->get_room_type() != TYPE_HUNDRED)
	{
		return;
	}

	room_route::RoomChanllengeWebUserInfo user_list;
	int user_count = UserManager::GetInstance()->user_count();
	user_list.set_RoomID(g_server_id);

	int send_count = 0;
	while(1)
	{
		if (send_count + 1 >= user_count)
			break;

		IUser* user = UserManager::GetInstance()->enum_user(send_count);
		if (user == NULL)
		{
			send_count++;
			continue;
		}
			
		user_list.add_UserList(user->get_user_id());
		send_count++;

		if (send_count + 1 >= user_count)
		{
			user_list.set_Staus(room_route::UPDATE_NOMAL_ROOM_END);
			g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_WEB_USER_INFO_RESP, &user_list);
			break;
		}
		else
		if (send_count >= 1000)
		{
			int status = (send_count + 1 < user_count ? room_route::UPDATE_NOMAL_ROOM_RUN : room_route::UPDATE_NOMAL_ROOM_END);
			user_list.set_Staus(status);
			g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_WEB_USER_INFO_RESP, &user_list);

			user_list.clear();
		}
	}
}

FTYPE(void) CChallengeRouteRoom::on_challenge_userlist_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (config_manager::GetInstance()->get_room_type() != TYPE_NOAML)
	{
		return;
	}

	room_route::RoomChanllengeNomalUserInfo user_list;
	int user_count = UserManager::GetInstance()->user_count();
	user_list.set_RoomID(g_server_id);

	int send_count = 0;
	while(1)
	{
		if (send_count + 1 >= user_count)
			break;

		IUser* user = UserManager::GetInstance()->enum_user(send_count);
		if (user == NULL)
		{
			send_count++;
			continue;
		}
		
		user_list.add_UserList(user->get_user_id());
		send_count++;

		if (send_count + 1 >= user_count)
		{
			user_list.set_Staus(room_route::UPDATE_NOMAL_ROOM_END);
			g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_NOMAL_USER_INFO_RESP, &user_list);
			break;
		}
		else
		if (send_count >= 1000)
		{
			int status = (send_count + 1 < user_count ? room_route::UPDATE_NOMAL_ROOM_RUN : room_route::UPDATE_NOMAL_ROOM_END);
			user_list.set_Staus(status);
			g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_NOMAL_USER_INFO_RESP, &user_list);

			user_list.clear();
		}
	}
}

FTYPE(void) CChallengeRouteRoom::on_challenge_ack_ex(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	room_route::CRoomChallengeAckEx  ack(data,size);

	if(ack.exist_RoomRule()) 		
	{
		CGameFrame::GetInstance()->set_game_rule(ack.RoomRule()); 
	}
	if(ack.get_AckTpye() ==  room_route::CHALLENG_ROOM_CONFIG_ACK)
	{
		return; //单纯刷新房间配置
	}


	room_route::CRoomInfo roomInfo;
	roomInfo.clear();
	roomInfo.set_RoomID(CGameFrame::GetInstance()->get_room_id());
	roomInfo.set_GameID(config_manager::GetInstance()->get_game_id());

	g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_BEGIN, &roomInfo);

	const MAPNumTable* pAllTable = TableManager::GetInstance()->get_table_list();
	if (!pAllTable)
	{
		MY_LOG_INFO("on_challenge_ack_ex pAllTable null");
		return;
	}
		

	int table_count = pAllTable->size();
	if (table_count > 0)
	{
		int tableInfoIndex = 0;
		room_route::CChallengeGameList tableList;

		MAPNumTable::const_iterator it = pAllTable->begin();
		for(;it != pAllTable->end();it++)
		{
			ITable* pTable = it->second;
			if(!pTable) continue;
			room_route::CRoomChallengeGameInfo* pInfo = tableList.ChallengeGameList_item(tableInfoIndex++);
			if(pInfo)
			{
				pInfo->clear();
				pInfo->set_Code(pTable->get_room_num());
				pInfo->set_OwnerID(pTable->get_owner_uid());

				int status = room_route::CHALLENGE_STATUS_ENTERING;
				if (pTable->get_player_count() >= pTable->get_max_count())
				{
					status = (pTable->is_playing() || pTable->get_play_count() > 0 ? room_route::CHALLENGE_STATUS_PLAYING : room_route::CHALLENGE_STATUS_WAIT_START);
				}
				pInfo->set_Status(status);

				pInfo->set_CurRound(pTable->get_play_count());
				pInfo->set_TotalRound(pTable->get_max_count());
				pInfo->set_PlayerCount(pTable->get_max_player());
				pInfo->set_TableRule(pTable->get_table_rule(),0);

				// modify by hsl on 03/21/ for game list
				pInfo->set_PlayingMethod(pTable->get_playing_method());
				pInfo->set_DefaultScore(pTable->get_default_score());
				pInfo->set_CreateTime(pTable->get_create_time());

				int iUserCount = 0;
				for(int i = 0;i < pTable->get_player_count();i++)
				{
					IUser* pUser = pTable->get_user_from_chair(i);
					if(pUser)
					{
						pInfo->UserList_item(iUserCount++)->set_UserName(pUser->get_nick_name(),0);
					}
				}
				pInfo->set_UserList_item_count(iUserCount);

				MY_LOG_INFO("约战数据code=%llu owner=%llu status=%llu",pInfo->get_Code(),pInfo->get_OwnerID(),pInfo->get_Status());
			}
			if(tableInfoIndex>=tableList.ChallengeGameList_max_count())
			{
				tableList.set_RoomID(CGameFrame::GetInstance()->get_room_id());
				tableList.set_GameID(config_manager::GetInstance()->get_game_id());
				tableList.set_ChallengeGameList_item_count(tableInfoIndex);
				g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_DATA, &tableList);
				tableInfoIndex = 0;
				tableList.clear();
			}
		}
		if (tableInfoIndex > 0)
		{
			tableList.set_RoomID(CGameFrame::GetInstance()->get_room_id());
			tableList.set_GameID(config_manager::GetInstance()->get_game_id());
			tableList.set_ChallengeGameList_item_count(tableInfoIndex);
			g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_DATA, &tableList);
		}
	}

	MY_LOG_INFO("on_challenge_ack_ex send CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_END");
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_END, &roomInfo);
}

FTYPE(void) CChallengeRouteRoom::on_challenge_ack(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	room_route::CRoomChallengeAck  ack(data,size);

	if(ack.exist_RoomRule()) 		
	{
		CGameFrame::GetInstance()->set_game_rule(ack.RoomRule()); 
	}
	if(ack.get_AckTpye() ==  room_route::CHALLENG_ROOM_CONFIG_ACK)
	{
		return; //单纯刷新房间配置
	}


	room_route::CRoomInfo roomInfo;
	roomInfo.clear();
	roomInfo.set_RoomID(CGameFrame::GetInstance()->get_room_id());
	roomInfo.set_GameID(config_manager::GetInstance()->get_game_id());

	g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_BEGIN, &roomInfo);

	const MAPNumTable* pAllTable = TableManager::GetInstance()->get_table_list();
	if(!pAllTable) return;

	int table_count = pAllTable->size();
	if (table_count > 0)
	{
		int tableInfoIndex = 0;
		room_route::CChallengeGameList tableList;

		MAPNumTable::const_iterator it = pAllTable->begin();
		for(;it != pAllTable->end();it++)
		{
			ITable* pTable = it->second;
			if(!pTable) continue;
			room_route::CRoomChallengeGameInfo* pInfo = tableList.ChallengeGameList_item(tableInfoIndex++);
			if(pInfo)
			{
				pInfo->clear();
				pInfo->set_Code(pTable->get_room_num());
				pInfo->set_OwnerID(pTable->get_owner_uid());

				int status = room_route::CHALLENGE_STATUS_ENTERING;
				if (pTable->get_player_count() >= pTable->get_max_count())
				{
					status = (pTable->is_playing() || pTable->get_play_count() > 0 ? room_route::CHALLENGE_STATUS_PLAYING : room_route::CHALLENGE_STATUS_WAIT_START);
				}
				pInfo->set_Status(status);
				
				pInfo->set_CurRound(pTable->get_play_count());
				pInfo->set_TotalRound(pTable->get_max_count());
				pInfo->set_PlayerCount(pTable->get_max_player());
				pInfo->set_TableRule(pTable->get_table_rule(),0);

				// modify by hsl on 03/21/ for game list
				pInfo->set_PlayingMethod(pTable->get_playing_method());
				pInfo->set_DefaultScore(pTable->get_default_score());
				pInfo->set_CreateTime(pTable->get_create_time());

				int iUserCount = 0;
				for(int i = 0;i < pTable->get_player_count();i++)
				{
					IUser* pUser = pTable->get_user_from_chair(i);
					if(pUser)
					{
						pInfo->UserList_item(iUserCount++)->set_UserName(pUser->get_nick_name(),0);
					}
				}
				pInfo->set_UserList_item_count(iUserCount);
				
				MY_LOG_INFO("约战数据code=%llu owner=%llu status=%llu",pInfo->get_Code(),pInfo->get_OwnerID(),pInfo->get_Status());
			}
			if(tableInfoIndex>=tableList.ChallengeGameList_max_count())
			{
				tableList.set_RoomID(CGameFrame::GetInstance()->get_room_id());
				tableList.set_GameID(config_manager::GetInstance()->get_game_id());
				tableList.set_ChallengeGameList_item_count(tableInfoIndex);
				g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_DATA, &tableList);
				tableInfoIndex = 0;
				tableList.clear();
			}
		}
		if (tableInfoIndex > 0)
		{
			tableList.set_RoomID(CGameFrame::GetInstance()->get_room_id());
			tableList.set_GameID(config_manager::GetInstance()->get_game_id());
			tableList.set_ChallengeGameList_item_count(tableInfoIndex);
			g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_DATA, &tableList);
		}
	}

	g_server->send_route_server_random(cmd_net::CMD_ROUTE_CHALLENGE, cmd_net_svr::CMD_ROUTE_CHALLENGE_CHALLENGE_LIST_END, &roomInfo);
}

FTYPE(void) CChallengeRouteRoom::on_challenge_dismiss_table(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size<=0)
	{
		MY_LOG_ERROR("收到错误的解散房间通知数据");
		return;
	}

	cmd_status::CStatusHttpRequest statusHttpRequest(data,size);
	MY_LOG_DEBUG("解散通知数据:%s",statusHttpRequest.get_Content());

	Json::Value jsonValue;
	Json::Reader jsonReader;
	if (!jsonReader.parse(statusHttpRequest.get_Content(),jsonValue))
	{
		MY_LOG_WARN("解析json数据包出错");
		return;
	}	
	
	_uint32 dwCode = jsonValue["room_code"].asInt();
	int ret = CGameFrame::GetInstance()->on_free_table(dwCode);
	if(ret != 0)
	{
		send_dismiss_table_fail(statusHttpRequest.get_SID(),103,"房间找不到桌子");
		MY_LOG_ERROR("dismiss table error %d",dwCode);
		return;
	}

	send_dismiss_table_success(statusHttpRequest.get_SID());
}

FTYPE(void) CChallengeRouteRoom::on_patch_card_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size<=0)
	{
		MY_LOG_ERROR("on_patch_card_req data error!");
		return;
	}

	cmd_status::CStatusHttpRequest statusHttpRequest(data,size);
	MY_LOG_DEBUG("on_patch_card_req:%s",statusHttpRequest.get_Content());

	Json::Value jsonValue;
	Json::Reader jsonReader;
	if (!jsonReader.parse(statusHttpRequest.get_Content(),jsonValue))
	{
		MY_LOG_WARN("解析json数据包出错");
		return;
	}	
	
	int code = jsonValue["code"].asInt();
	MY_LOG_DEBUG("on_patch_card_req ... code:%d  json:%s", code, statusHttpRequest.get_Content());

	CTable* ptable = TableManager::GetInstance()->find_table_from_num(code);
	if (ptable)
	{
		ptable->patch_card(statusHttpRequest.get_Content(), size);
	}
	else
	{
		MY_LOG_ERROR("on_patch_card_req... code error!");
	}
}

FTYPE(void) CChallengeRouteRoom::on_patch_table_card_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size<=0)
	{
		MY_LOG_ERROR("on_patch_table_card_req data error!");
		return;
	}

	cmd_status::CStatusHttpRequest statusHttpRequest(data,size);
	MY_LOG_DEBUG("on_patch_table_card_req:%s",statusHttpRequest.get_Content());

	Json::Value jsonValue;
	Json::Reader jsonReader;
	if (!jsonReader.parse(statusHttpRequest.get_Content(),jsonValue))
	{
		MY_LOG_WARN("解析json数据包出错");
		return;
	}	
	
	int code = jsonValue["code"].asInt();
	MY_LOG_DEBUG("on_patch_table_card_req ... code:%d  json:%s", code, statusHttpRequest.get_Content());

	CTable* ptable = TableManager::GetInstance()->find_table_from_num(code);
	if (ptable)
	{
		ptable->patch_table_card(statusHttpRequest.get_Content(), size);
	}
	else
	{
		MY_LOG_ERROR("on_patch_table_card_req.. code error!");
	}
}

FTYPE(void) CChallengeRouteRoom::on_flush_client_version_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size<=0)
	{
		MY_LOG_ERROR("on_patch_table_card_req data error!");
		return;
	}
	cmd_status::CStatusHttpRequest statusHttpRequest(data,size);
	MY_LOG_DEBUG("on_flush_client_version_req:%s",statusHttpRequest.get_Content());
	config_manager::GetInstance()->get_web_version_config(statusHttpRequest.get_Content(), size);	
}

FTYPE(void) CChallengeRouteRoom::on_update_limit_data_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size<=0)
	{
		MY_LOG_ERROR("on_patch_table_card_req data error!");
		return;
	}
	cmd_status::CStatusHttpRequest statusHttpRequest(data,size);
	MY_LOG_DEBUG("on_flush_client_version_req:%s",statusHttpRequest.get_Content());
}

FTYPE(void) CChallengeRouteRoom::on_update_room_game_config_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size <= 0)
	{
		MY_LOG_ERROR("on_update_room_game_config_req error data");
		return;
	}

	cmd_status::CStatusHttpRequest statusHttpRequest(data, size);
	Json::Value jsonValue;
	Json::Reader jsonReader;
	if (!jsonReader.parse(statusHttpRequest.get_Content(), jsonValue))
	{
		MY_LOG_WARN("on_update_room_game_config_req 解析json数据包出错");
		return;
	}

	int game_id = jsonValue["data"]["game_id"].asInt();
	int game_type = jsonValue["data"]["game_type"].asInt();

	if (game_id == config_manager::GetInstance()->get_game_id() && game_type == config_manager::GetInstance()->get_room_type())
	{  
		CGameFrame::GetInstance()->get_config()->m_b_web_active = true;
		web_request::GetInstance()->get_game_config(0);
	}
}

FTYPE(void) CChallengeRouteRoom::on_update_node_data_req(socket_id pid,SNETHEAD* head,const void* data,int size,const void* ex_data)
{
	if (!data || size<=0)
	{
		MY_LOG_ERROR("on_update_node_data_req data error!");
		return;
	}
	cmd_status::CStatusHttpRequest statusHttpRequest(data,size);
	MY_LOG_DEBUG("on_update_node_data_req:%s",statusHttpRequest.get_Content());
	config_manager::GetInstance()->update_gold_room_config(statusHttpRequest.get_Content(), size);	
}

void CChallengeRouteRoom::send_dismiss_table_fail(_tint64 sid, int errorCode, const char *msg)
{
	Json::FastWriter jsonWriter;
	Json::Value jsonValue;
	jsonValue["result"] = Json::Value(-1);
	jsonValue["error_code"] = Json::Value(errorCode);
	jsonValue["msg"] = Json::Value(msg);

	cmd_status::CStatusHttpRespond statusHttpRespond;
	statusHttpRespond.set_SID(sid);
	statusHttpRespond.set_Content(jsonWriter.write(jsonValue).c_str(), 0);
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_STATUS, cmd_net_svr::CMD_ROUTE_STAT_HTTP_RESPOND, &statusHttpRespond);
}

void CChallengeRouteRoom::send_dismiss_table_success(_tint64 sid)
{
	Json::FastWriter jsonWriter;
	Json::Value jsonValue;
	jsonValue["result"] = Json::Value(0);
	cmd_status::CStatusHttpRespond statusHttpRespond;
	statusHttpRespond.set_SID(sid);
	statusHttpRespond.set_Content(jsonWriter.write(jsonValue).c_str(), 0);
	g_server->send_route_server_random(cmd_net::CMD_ROUTE_STATUS, cmd_net_svr::CMD_ROUTE_STAT_HTTP_RESPOND, &statusHttpRespond);
}

FTYPE(void) CChallengeRouteRoom::on_update_props_config_req(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data)
{
	MY_LOG_DEBUG(">>> on_update_props_config_req start.");
	web_request::GetInstance()->send_get_game_props_config_req();
}



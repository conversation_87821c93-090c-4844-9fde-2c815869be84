﻿
#include <log_manager.h>
#include "table_manager.h"
#include "config_manager.h"

TableManager *TableManager::GetInstance()
{
    static TableManager _table_manager;
    return &_table_manager;
}

TableManager::TableManager()
{
#if DEBUG_VALUE
    DEBUG_LOG("CTableManager");
#endif
}

TableManager::~TableManager()
{

}

int TableManager::init(int table_count)
{
    if (table_count <= 0)
        return -1;

    m_table_list.init(table_count);

	//初始化100桌
	for(int i=0;i<100;i++)
	{
		CTable  *choice_table = m_table_pool.pop();
		if (!choice_table)
		{
			return NULL;
		}
		int table_id = m_table_list.add_object(choice_table);
		choice_table->set_table_id(table_id);
		g_log->write_log(LOG_TYPE_DEBUG,"TableManager init table -  tableid:%d", table_id);
	}

    return 0;
}

bool TableManager::delete_table(CTable *pTable)
{	
    if (pTable)
    {
        int tableID = pTable->get_table_id();
		g_log->write_log(LOG_TYPE_DEBUG,"TableManager A delete_table -  tableid:%d", tableID);

        if (m_table_list.delete_object(tableID))
        {   
			g_log->write_log(LOG_TYPE_DEBUG,"TableManager A m_map_table erase -  tableid:%d", tableID);
            m_map_table.erase(pTable->get_room_num());
			pTable->clear();

            m_table_pool.push(pTable);            
            return true;
        }
    }

    return false;
}

bool TableManager::delete_table(int tableID)
{
	g_log->write_log(LOG_TYPE_DEBUG,"TableManager delete_table -  tableid:%d", tableID);

    if (tableID > 0)
    {
        CTable * pTable = NULL;
        m_table_list.get_object(tableID, pTable);

        return delete_table(pTable);
    }

    return false;
}

CTable * TableManager::get_table(int tableID)
{
    CTable * pTable = NULL;
    m_table_list.get_object(tableID, pTable);
    return pTable;
}

CTable *TableManager::find_table_from_num(_uint32 num)
{
    MAPNumTable::iterator it = m_map_table.find(num);
    
    if (it == m_map_table.end())
        return NULL;

    return it->second;
}

CTable *TableManager::get_table_from_num(_uint32 num, const void * rule, int rule_len/* = 0*/, int is_web)
{
    if (m_map_table.find(num) != m_map_table.end())
    {
        MY_LOG_ERROR("Get table from num, the table is exists, please use find table.");
        return NULL;
    }

    if (m_table_list.free_count() <= 0)
    {
        MY_LOG_ERROR("Free table is empty.");
        return NULL;
    }

    CTable *pTable = m_table_pool.pop();
    if (!pTable)
    {
        MY_LOG_ERROR("Table pool is empty.");
        return NULL;
    }

	pTable->clear();
    pTable->set_room_num(num);

	int table_id = m_table_list.add_object(pTable);
	pTable->set_table_id(table_id);
	m_map_table[num] = pTable;  
    if (pTable->init(rule, rule_len, is_web))
    {
        delete_table(table_id);
        return NULL;
    }

	g_log->write_log(LOG_TYPE_DEBUG,"TableManager get_table_from_num -  tableid:%d", table_id);
    return pTable;
}

int TableManager::get_robot_table_count(int nodeid)
{
	int table_count = 0;
	CTable  *table;
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;
		
		if (table->get_node_id() != nodeid)
			continue;

		if (table->get_robot_count() == 0)
			continue;

		if (table->is_playing())
			continue;
		
		table_count++;
	}
	return table_count;
}

int TableManager::get_table_count()
{
    return m_table_list.object_count();
}

int TableManager::get_free_count()
{
    return m_table_list.free_count();
}

//打印桌子详细信息
void TableManager::print_table_info()
{
	CTable  *table;
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
		{
			MY_LOG_DEBUG("print_table_info... tableid=%d table is null", i);
			continue;
		}
		table->print_table_info();
	}
}

CTable* TableManager::find_match_free_table(int matchsn)
{
	MY_LOG_DEBUG("find_match_free_table... matchsn=%d  objcount:%d", matchsn, m_table_list.object_count());

	//挑选比赛空白桌子
	CTable  *table;
	CTable  *choice_table = NULL;
	int free_chair_count = MAX_GAME_PLAY_USER_EX;
	int index = 0;
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
		{
			g_log->write_log(LOG_TYPE_DEBUG, "find_match_free_table table == null  i:%d", i);
			continue;
		}

		int play_count = table->get_player_count();
		MY_LOG_DEBUG("find_match_free_table i:%d  play_count:%d.", i, play_count);

		//过滤有人桌
		if (play_count == 0)
		{
			choice_table = table;
			break;
		}
	}

	if (choice_table != NULL)
	{
		int tableid = choice_table->get_table_id();
		if (choice_table->get_player_count() == 0)
		{
			choice_table->clear();
			choice_table->set_room_num(tableid);
			choice_table->set_table_id(tableid);
			choice_table->set_node_id(matchsn);
			g_log->write_log(LOG_TYPE_DEBUG, "find_match_free_table choice_table init...nodeid:%d", matchsn);
			if (choice_table->init(0, 0))
			{
				MY_LOG_ERROR("find_match_free_table 初始化桌子失败.");

				m_table_list.delete_object(tableid);
				m_table_pool.push(choice_table);
				return NULL;
			}
		}
		return choice_table;
	}

	choice_table = m_table_pool.pop();
	if (!choice_table)
	{
		MY_LOG_ERROR("find_match_free_table choice_table is NULL.");
		return NULL;
	}
	if (choice_table->get_player_count() == 0)
		choice_table->clear();

	int table_id = m_table_list.add_object(choice_table);
	choice_table->set_room_num(table_id);
	choice_table->set_node_id(matchsn);
	g_log->write_log(LOG_TYPE_DEBUG, "find_match_free_table 初始化桌子...nodeid:%d", matchsn);
	if (choice_table->init(0, 0))
	{
		MY_LOG_ERROR("find_match_free_table 初始化桌子失败 222.");

		m_table_list.delete_object(table_id);
		m_table_pool.push(choice_table);
		return NULL;
	}

	choice_table->set_table_id(table_id);
	m_map_table[table_id] = choice_table;
	return choice_table;
}

CTable* TableManager::find_free_table(int nodeid)
{
	//挑选空白桌子
	CTable  *table;
	CTable  *choice_table = NULL;
	int free_chair_count  = MAX_GAME_PLAY_USER_EX;
	int index = 0;
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		//过滤有人桌
		if(table->get_player_count() ==0 )
		{
			choice_table = table;
			break;
		}
		if (index == 100) //只遍历100张桌子
			break;
	}

	if (choice_table != NULL)
	{
		int tableid = choice_table->get_table_id();
		if (choice_table->get_player_count() == 0)
		{
			choice_table->clear();
			choice_table->set_room_num(tableid);
			choice_table->set_table_id(tableid);
			choice_table->set_node_id(nodeid);
			const char *game_rule = config_manager::GetInstance()->get_game_rule(nodeid);
			if (game_rule == NULL)
			{
				g_log->write_log(LOG_TYPE_ERROR,"TableManager find_nomal_table game_rule == null  nodeid:%d", nodeid);
				return NULL;
			}

			g_log->write_log(LOG_TYPE_DEBUG, "find_free_table choice_table init...nodeid:%d", nodeid);
			if (choice_table->init(game_rule, strlen(game_rule)))
			{
				m_table_list.delete_object(tableid);
				m_table_pool.push(choice_table);
				return NULL;
			}
		}
		return choice_table;
	}

	choice_table = m_table_pool.pop();
	if (!choice_table)
	{
		return NULL;
	}
	if (choice_table->get_player_count() == 0)
		choice_table->clear();

	int table_id = m_table_list.add_object(choice_table);
	choice_table->set_room_num(table_id);

	g_log->write_log(LOG_TYPE_DEBUG, "find_free_table choice_table init...nodeid:%d", nodeid);
	const char *game_rule = config_manager::GetInstance()->get_game_rule(nodeid);
	if (game_rule == NULL)
	{
		g_log->write_log(LOG_TYPE_ERROR,"TableManager find_nomal_table game_rule == null  nodeid:%d", nodeid);
		return NULL;
	}

	choice_table->set_node_id(nodeid);
	if (choice_table->init(game_rule, strlen(game_rule)))
	{
		m_table_list.delete_object(table_id);
		m_table_pool.push(choice_table);
		return NULL;
	}

	choice_table->set_table_id(table_id);
	m_map_table[table_id] = choice_table;    
	return choice_table;
}

/*
* 寻找没有真人的桌子,只允许放入一个真人
*/
CTable* TableManager::find_nomal_real_one(int now_table, int nodeid)
{
	g_log->write_log(LOG_TYPE_DEBUG, "TableManager find_nomal_real_one -  now_table:%d  nodeid:%d m_table_list.size:%d m_table_pool.cur_count():%d, m_table_pool.free_count():%d",
		now_table, nodeid, m_table_list.object_count(), m_table_pool.cur_count(), m_table_pool.free_count());

	//优先挑选有真人的桌子
	CTable  *table;
	CTable  *choice_table = NULL;
	CTable  *no_player_table = NULL;
	int free_chair_count = MAX_GAME_PLAY_USER_EX;
	int index = 0;
	bool find_pos = false;

	//找一个机器人桌子，没有任何真人的
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		//判断结点ID是否匹配
		if (table->get_node_id() != 0)
		{
			if (table->get_node_id() != nodeid)
				continue;
		}

		//判断是否为当前桌
		if (table->get_table_id() == now_table)
		{
			continue;
		}

		//过滤满人桌
		int left_chair = table->get_max_player() - table->get_player_count();
		if (left_chair == 0)
			continue;

		//过滤有真人的桌子
		if (table->get_real_count() > 0)
			continue;

		//挑选人多的桌子
		if (left_chair < free_chair_count)
		{
			free_chair_count = left_chair;
			choice_table = table;
		}

		index++;
		if (index == 100) //只遍历100张桌子
			break;
	}

	//空桌需要初始化
	if (choice_table != NULL)
	{
		int tableid = choice_table->get_table_id();
		if (choice_table->get_player_count() == 0)
		{
			choice_table->clear();
			choice_table->set_room_num(tableid);
			choice_table->set_table_id(tableid);
			choice_table->set_node_id(nodeid);

			const char *game_rule = config_manager::GetInstance()->get_game_rule(nodeid);
			if (game_rule == NULL)
			{
				g_log->write_log(LOG_TYPE_ERROR, "TableManager find_nomal_real_one game_rule == null  nodeid:%d", nodeid);
				return NULL;
			}
			if (choice_table->init(game_rule, strlen(game_rule)))
			{
				m_table_list.delete_object(tableid);
				m_table_pool.push(choice_table);
				return NULL;
			}
		}
		g_log->write_log(LOG_TYPE_DEBUG, "TableManager find_nomal_real_one - choice_table:%d", choice_table->get_table_id());
		return choice_table;
	}

	if (choice_table != NULL)
	{
		return choice_table;
	}
	else
	{
		return find_nomal_table(now_table, nodeid);
	}
}

/*
*  @brief: 寻找合适的金币房桌子,先选多真人桌
*  @return: 返回桌子
*/
CTable* TableManager::find_nomal_real_table(int now_table, int nodeid)
{
	g_log->write_log(LOG_TYPE_DEBUG, "TableManager find_nomal_real_table -  now_table:%d  nodeid:%d m_table_list.size:%d m_table_pool.cur_count():%d, m_table_pool.free_count():%d",
		now_table, nodeid, m_table_list.object_count(), m_table_pool.cur_count(), m_table_pool.free_count());

	//优先挑选有真人的桌子
	CTable  *table;
	CTable  *choice_table = NULL;
	CTable  *no_player_table = NULL;
	int free_chair_count = MAX_GAME_PLAY_USER_EX;
	int index = 0;
	bool find_pos = false;		

	//找一个真人桌子，尽可能选人多的
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		//判断结点ID是否匹配
		if (table->get_node_id() != 0)
		{
			if (table->get_node_id() != nodeid)
				continue;
		}

		//判断是否为当前桌
		if (table->get_table_id() == now_table)
		{
			continue;
		}

		//过滤满人桌
		int left_chair = table->get_max_player() - table->get_player_count();
		if (left_chair == 0)
			continue;

		//只找有真人的桌子
		if (table->get_real_count() == 0)
			continue;
			
		//挑选人多的桌子
		if (left_chair < free_chair_count)
		{
			free_chair_count = left_chair;
			choice_table = table;
		}

		index++;
		if (index == 100) //只遍历100张桌子
			break;
	}

	if (choice_table != NULL)
	{
		return choice_table;
	}
	else
	{
		return find_nomal_table(now_table, nodeid);
	}
}

CTable* TableManager::find_nomal_table(int now_table, int nodeid)
{
	g_log->write_log(LOG_TYPE_DEBUG,"TableManager find_nomal_table -  now_table:%d  nodeid:%d m_table_list.size:%d m_table_pool.cur_count():%d, m_table_pool.free_count():%d", 
		now_table, nodeid, m_table_list.object_count(),m_table_pool.cur_count(),m_table_pool.free_count());

	//挑选最快满人的桌子
	CTable  *table;
	CTable  *choice_table = NULL;
	CTable  *no_player_table = NULL;
	int free_chair_count  = MAX_GAME_PLAY_USER_EX;
	int index = 0;
	bool find_pos = false;		//找到当前桌的位置
	bool bfindplayertable = false;

	//正常进桌逻辑
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		//判断结点ID是否匹配
		if (table->get_node_id() != 0)
		{
			if (table->get_node_id() != nodeid)
				continue;
		}

		//不要换到重复的桌子
		if (table->get_table_id() == now_table)
		{
			continue;
		}

		int left_chair = table->get_max_player() - table->get_player_count();
		g_log->write_log(LOG_TYPE_DEBUG,"TableManager normal find_nomal_table  -  tableid:%d  left_chair:%d", table->get_table_id(), left_chair);

		//过滤满人桌
		if(left_chair==0)
			continue;

		//尽可能找人多的桌子
		if (left_chair < free_chair_count)
		{
			index++;
			free_chair_count = left_chair;
			choice_table = table;
			break;
		}
		else
		{
			no_player_table = table;
		}
	}
		
	//如果没有找到有人的桌子，则取空桌
	if(choice_table==NULL)
	{
		choice_table = no_player_table;
	}
	if(choice_table)
		g_log->write_log(LOG_TYPE_DEBUG,"TableManager normal find_nomal_table - choice_table:%d", choice_table->get_table_id());
	
	if (choice_table != NULL)
	{
		int tableid = choice_table->get_table_id();
		if (choice_table->get_player_count() == 0)
		{
			choice_table->clear();
			choice_table->set_room_num(tableid);
			choice_table->set_table_id(tableid);
			choice_table->set_node_id(nodeid);

			const char *game_rule = config_manager::GetInstance()->get_game_rule(nodeid);
			if (game_rule == NULL)
			{
				g_log->write_log(LOG_TYPE_ERROR, "TableManager find_nomal_table game_rule == null  nodeid:%d", nodeid);
				return NULL;
			}

			if (choice_table->init(game_rule, strlen(game_rule)))
			{
				m_table_list.delete_object(tableid);
				m_table_pool.push(choice_table);
				return NULL;
			}
		}

		g_log->write_log(LOG_TYPE_DEBUG,"TableManager find_nomal_table - choice_table:%d", choice_table->get_table_id());
		return choice_table;
	}
	
	g_log->write_log(LOG_TYPE_DEBUG,"TableManager get m_table_pool.pop");
	choice_table = m_table_pool.pop();
	if (!choice_table)
	{
		return NULL;
	}
	if (choice_table->get_player_count() == 0)
		choice_table->clear();

	int table_id = m_table_list.add_object(choice_table);
	choice_table->set_room_num(table_id);
	const char *game_rule = config_manager::GetInstance()->get_game_rule(nodeid);
	if (game_rule == NULL)
	{
		g_log->write_log(LOG_TYPE_ERROR,"TableManager find_nomal_table game_rule == null  nodeid:%d", nodeid);
		return NULL;
	}

	choice_table->set_node_id(nodeid);
 	if (choice_table->init(game_rule, strlen(game_rule)))
	{
		m_table_list.delete_object(table_id);
		m_table_pool.push(choice_table);
		return NULL;
	}
	
	choice_table->set_table_id(table_id);
	m_map_table[table_id] = choice_table;    
	return choice_table;
}

CTable* TableManager::find_qiecuo_table(int now_table, int nodeid, const char * rule)
{
	g_log->write_log(LOG_TYPE_DEBUG,"TableManager find_qiecuo_table -  now_table:%d  nodeid:%d", now_table, nodeid);

	//挑选最快满人的桌子
	CTable  *table;
	CTable  *choice_table = NULL;
	int free_chair_count  = MAX_GAME_PLAY_USER_EX;
	int index = 0;
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		if (table->get_table_id() == now_table)
			continue;

		if (table->get_node_id() != 0)
		{
			if (table->get_node_id() != nodeid)
				continue;
		}

		int left_chair = table->get_max_player() - table->get_player_count();
		g_log->write_log(LOG_TYPE_DEBUG,"TableManager find_qiecuo_table  -  tableid:%d  left_chair:%d", table->get_table_id(), left_chair);

		//过滤满人桌
		if(left_chair==0)
			continue;

		if (left_chair < free_chair_count)
		{
			index++;
			free_chair_count = left_chair;
			choice_table = table;
		}
		if (index == 100) //只遍历100张桌子
			break;
	}

	if (choice_table != NULL)
	{
		int tableid = choice_table->get_table_id();
		if (choice_table->get_player_count() == 0)
		{
			choice_table->clear();
			choice_table->set_room_num(tableid);
			choice_table->set_table_id(tableid);
			choice_table->set_node_id(nodeid);
			
			if (choice_table->init(rule, strlen(rule),true))
			{
				m_table_list.delete_object(tableid);
				m_table_pool.push(choice_table);
				return NULL;
			}
		}
		return choice_table;
	}

	choice_table = m_table_pool.pop();
	if (!choice_table)
	{
		return NULL;
	}
	if (choice_table->get_player_count() == 0)
		choice_table->clear();

	int table_id = m_table_list.add_object(choice_table);
	choice_table->set_room_num(table_id);
	
	choice_table->set_node_id(nodeid);
	if (choice_table->init(rule, strlen(rule),true))
	{
		m_table_list.delete_object(table_id);
		m_table_pool.push(choice_table);
		return NULL;
	}

	choice_table->set_table_id(table_id);
	m_map_table[table_id] = choice_table;    
	return choice_table;
}

void TableManager::release_all_free_robot()
{
	CTable  *table = NULL;

	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		/*判断结点ID是否匹配
		if (table->get_node_id() != 0)
		{
			if (table->get_node_id() != nodeid)
				continue;
		}*/

		//过滤空桌
		if (table->get_player_count() == 0)
			continue;

		if (table->is_playing())
			continue;

		//获取桌子用户
		int playerCount = table->get_player_count();
		for (int i = 0; i < table->get_max_player(); i++)
		{
			IUser * pUser = table->get_user_from_chair(i);
			if (pUser == NULL) continue;
			--playerCount;

			if (pUser->get_user_type() == ROBOT_TYPE)
				table->tick_user(i); //踢出机器人

			if (playerCount == 0) break;
		}
	}
}

/*
初始化百人场
*/
void TableManager::init_hundred_table(map_node_info  m_map_node_info)
{
	m_limit_user = 0;
	for (iter_node_info iter = m_map_node_info.begin(); iter != m_map_node_info.end(); iter++)
	{
		CTableHundred* ptable = get_hundred_table(iter->first);
		if (ptable == NULL)
		{
		    g_log->write_log(LOG_TYPE_DEBUG,"TableManager::init_hundred_table AA -  node_id:%d .", iter->first);
			
			//没有对应的桌子,寻找一个空闲的桌子使用
			ptable = get_hundred_table(-1);
			if (ptable == NULL)
				continue;

			ptable->set_node_id(iter->first);
			if (ptable->init(iter->second.m_game_rule.c_str(), iter->second.m_game_rule.length()) == 0)
			{
				m_limit_user += ptable->get_max_user();
			}
			else
			{
				ptable->set_node_id(-1);
			}
			m_limit_user += ptable->get_max_user();
		}
		else
		{
			//已经有这个节点桌子了 ，只刷新规则
			//ptable->init(iter->second.m_game_rule.c_str(), iter->second.m_game_rule.length());
			//m_limit_user += ptable->get_max_user();

			//已经有这个节点桌子了 ，只刷新规则, 已存在房间只重置下规则不能init不然走势都被清0
			ptable->reset_game_rule(iter->second.m_game_rule);
		}

		g_log->write_log(LOG_TYPE_DEBUG,"TableManager::init_hundred_table BB -  node_id:%d .", iter->first);
	}
}

/*
重置百人场桌子
*/
void TableManager::reset_hundred_table(int nodeid)
{
	CTableHundred* ptable = get_hundred_table(nodeid);
	if (ptable != NULL)
	{
		ptable->set_node_id(0);
	}
}

/*
重置普通房规则,没人的桌子初始化重置
*/
void TableManager::reset_normal_rule(string game_rule, int nodeid)
{
	CTable  *table;
	int index = 0;
	for (int i = 0; i < m_table_list.object_count(); i++)
	{
		m_table_list.get_index_object(i, table);
		if (table == NULL)
			continue;

		if (table->get_real_count() != 0)
			continue;

		if (nodeid == table->get_node_id())
		    table->reset_game_rule(game_rule);
	}
}

void TableManager::reset_hundred_rule(map_node_info  m_map_node_info)
{
	//m_limit_user = 0;
	for (iter_node_info iter = m_map_node_info.begin(); iter != m_map_node_info.end(); iter++)
	{
		CTableHundred* ptable = get_hundred_table(iter->first);
		if (ptable != NULL)
		{
			//已经有这个节点桌子了 ，只刷新规则
			ptable->reset_game_rule(iter->second.m_game_rule);
		}
	}
}

/*
查找对应节点场次
*/
CTableHundred* TableManager::get_hundred_table(int node_id)
{
	for (int i = 0; i < MAX_PER_ROOM_HUNDERD; i++)
	{
		if (node_id != -1)
		{
			if (m_tableHundred[i].get_node_id() == node_id || node_id == 0)
			{
				return &m_tableHundred[i];
			}
		}
		else
		{
			if (m_tableHundred[i].get_node_id() == node_id)
			{
				return &m_tableHundred[i];
			}
		}
	}
	return NULL;
}

//寻找空闲百人桌子
CTableHundred* TableManager::get_free_hundred_table()
{
	for (int i = 0; i < MAX_PER_ROOM_HUNDERD; i++)
	{
		if (m_tableHundred[i].get_node_id() == -1)
		{
			return &m_tableHundred[i];
		}
	}
	return NULL;
}

/*
挑选一个百人场节点
*/
int TableManager::get_rand_hundred_node()
{
	return m_tableHundred[0].get_node_id();
}

/*
读取所有百人场节点
*/
void TableManager::get_all_hundred_node(vector<int> &node_list)
{
	for (int i = 0; i < MAX_PER_ROOM_HUNDERD; i++)
	{
		int node_id = m_tableHundred[i].get_node_id();
		if (node_id != -1)
		{
			node_list.push_back(node_id);
		}
	}
}
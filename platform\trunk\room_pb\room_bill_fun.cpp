﻿#include "room_bill_fun.h"
#include "my_redis.h"
#include "config_manager.h"
#include "game_frame.h"
#include "bill_fun.h"
#include "web_request.h"
#include "user_manager.h"

room_bill_fun::room_bill_fun()
{
}


room_bill_fun::~room_bill_fun()
{
}

//百人场投付记录
bool room_bill_fun::hund_write_transfer_inout_ex(hundred_transfer_inout_one inout)
{
	
}

//百人场投付记录
bool room_bill_fun::hund_write_transfer_inout(hundred_transfer_inout_one inout)
{
	int gameid = config_manager::GetInstance()->get_game_id();
	if (inout.user_type == ROBOT_TYPE)
		return false;

	_tint64 now_gold = myredis::GetInstance()->get_data_by_uid(inout.uid, DB_COMM, CUR_GOLD);
	int sub_client_id = inout.sub_client_id;
	int api_type = inout.api_type;
	int client_id = inout.client_id;

	Json::Value json_req_value;
	json_req_value["table_id"] = Json::Value(inout.tableid);
	json_req_value["player_uid"] = Json::Value(inout.uid);
	json_req_value["game_id"] = Json::Value(gameid);
	json_req_value["token"] = Json::Value(inout.web_token);
	json_req_value["bill_type"] = Json::Value(inout.bill_type);
	json_req_value["is_end"] = Json::Value(inout.is_end ? 1 : 0);
	json_req_value["bet_amount"] = Json::Value(inout.bet);
	json_req_value["transfer_amount"] = Json::Value(inout.result);
	json_req_value["pay_out"] = Json::Value(inout.pay_out);
	json_req_value["api_mode"] = Json::Value(api_type);
	json_req_value["status"] = Json::Value(api_type == MODE_SINGLE ? 0 : 1);

	json_req_value["sub_client_id"] = Json::Value(sub_client_id);
	json_req_value["client_id"] = Json::Value(client_id);
	json_req_value["last_gold"] = Json::Value(now_gold - inout.pay_out);
	json_req_value["now_gold"] = Json::Value(now_gold);
	json_req_value["tax"] = Json::Value(inout.tax);

	char subid[GUID_LEN] = { 0 };
	sprintf(subid, "%d", inout.sub_id);

	json_req_value["parent_bet_id"] = Json::Value(inout.parent_id);	//母单
	json_req_value["bet_id"] = Json::Value(subid);	//子单

	//交易标识 {parent_bet_id}-{bet_id}-{transactionType}
	char transaction_id[128] = { 0 };
	sprintf(transaction_id, "{%s}-{%s}-{%d}", inout.parent_id.c_str(), subid, inout.bill_type);
	json_req_value["transaction_id"] = Json::Value(transaction_id);

	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);

	//转账模式不需要投付
	if (api_type == MODE_SINGLE)
	{
		//结算, 无论是否投递成功, 都入库，方便对账
		if (inout.bill_type == GOLD_DEL_GAME)
		{
			inout.write_db = false; //只能写一次
			cmd_dbproxy::CDBProxyExceSection db_section;
			g_dbproxy_namager->post_exec(0, 0, inout.tableid, "cmd_transfer_inout_bill", json_req_data.c_str(),
				db_section, false);
		}

		MY_LOG_DEBUG("write_transfer_inout web post uid:%d, table json_data: %s", inout.uid, json_req_data.c_str());
		bool is_post = web_request::GetInstance()->post_transfer_inout_hundred(json_req_data, inout);
		if (is_post)
		{
			IUser*pUser = UserManager::GetInstance()->get_user(inout.uid);
			if (pUser) {
				pUser->set_single_status(1);
            }
		}
	}
	else
	{
		MY_LOG_DEBUG("write_transfer_inout db post uid:%d, table json_data: %s", inout.uid, json_req_data.c_str());
		cmd_dbproxy::CDBProxyExceSection db_section;
		g_dbproxy_namager->post_exec(0, 0, inout.tableid, "cmd_transfer_inout_bill", json_req_data.c_str(),
			db_section, false);
	}
	return true;
}

bool room_bill_fun::write_transfer_inout(transfer_inout inout)
{
	int gameid = config_manager::GetInstance()->get_game_id();
	int user_type = myredis::GetInstance()->get_data_by_uid(inout.uid, DB_COMM, CUR_USER_TYPE);
	if (user_type == ROBOT_TYPE)
		return false;

	char* token = myredis::GetInstance()->get_token_by_uid(inout.uid);
	int now_gold = myredis::GetInstance()->get_data_by_uid(inout.uid, DB_COMM, CUR_GOLD);
	int client_id = myredis::GetInstance()->get_data_by_uid(inout.uid, DB_COMM, CUR_CHANNEL_ID);
	int api_type = myredis::GetInstance()->get_data_by_uid(inout.uid, DB_COMM, CUR_API_MODE);
	int sub_client_id = myredis::GetInstance()->get_data_by_uid(inout.uid, DB_COMM, CUR_SUB_CHANNEL_ID);

	Json::Value json_req_value;
	json_req_value["player_uid"] = Json::Value(inout.uid);
	json_req_value["game_id"] = Json::Value(gameid);
	json_req_value["table_id"] = Json::Value(inout.tableid);
	json_req_value["token"] = Json::Value(token == NULL ? "" : token);
	json_req_value["bill_type"] = Json::Value(inout.bill_type);
	json_req_value["is_end"] = Json::Value(inout.is_end ? 1 : 0);
	json_req_value["bet_amount"] = Json::Value(inout.bet);
	json_req_value["transfer_amount"] = Json::Value(inout.result);

	json_req_value["api_mode"] = Json::Value(api_type);
	json_req_value["status"] = Json::Value(api_type == MODE_SINGLE ? 0 : 1);

	json_req_value["sub_client_id"] = Json::Value(sub_client_id);
	json_req_value["client_id"] = Json::Value(client_id);
	json_req_value["last_gold"] = Json::Value(now_gold - inout.result);
	json_req_value["now_gold"] = Json::Value(now_gold);
	json_req_value["pay_out"] = Json::Value(inout.pay_out);

	json_req_value["parent_bet_id"] = Json::Value(inout.gguid);	
	json_req_value["bet_id"] = Json::Value("0");	
									
	char transaction_id[128] = { 0 };
	sprintf(transaction_id, "{%s}-{%s}-{%d}-{%s}", "0", inout.gguid, inout.bill_type, inout.md5Key.c_str());
	json_req_value["transaction_id"] = Json::Value(transaction_id);

	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);

	if (api_type == MODE_SINGLE)
	{
		//结算, 无论是否投递成功, 都入库，方便对账
		if (inout.bill_type == GOLD_DEL_GAME)
		{
			inout.write_db = false; //只能写一次
			cmd_dbproxy::CDBProxyExceSection db_section;
			g_dbproxy_namager->post_exec(0, 0, inout.tableid, "cmd_transfer_inout_bill", json_req_data.c_str(),
				db_section, false);
		}

		MY_LOG_DEBUG("write_transfer_inout web post uid:%d, table json_data: %s", inout.uid, json_req_data.c_str());
		bool is_post = web_request::GetInstance()->post_transfer_inout_normal(json_req_data, inout);
		if (is_post)
		{
			IUser*pUser = UserManager::GetInstance()->get_user(inout.uid);
			if (pUser)
				pUser->set_single_status(1);
			else
				pUser->set_single_status(0);
		}
	}
	else
	{
		MY_LOG_DEBUG("write_transfer_inout db post uid:%d, table json_data: %s", inout.uid, json_req_data.c_str());
		cmd_dbproxy::CDBProxyExceSection db_section;
		g_dbproxy_namager->post_exec(0, 0, inout.tableid, "cmd_transfer_inout_bill", json_req_data.c_str(),
			db_section, false);
	}
	return true;
}

void room_bill_fun::write_user_roominfo(_tint64 userID, bool is_leave, int nodeid, 
	int usertype, int result, int clientid, int bet)
{
	if (userID == 0 || usertype == ROBOT_TYPE)
		return;

	Json::Value json_req_value;
	if (!is_leave)
	{
		string room_name = "";
		config_manager::GetInstance()->get_room_name(nodeid, room_name);
		json_req_value["uid"] = Json::Value(userID);
		json_req_value["room_id"] = Json::Value(CGameFrame::GetInstance()->get_room_id());
		json_req_value["room_name"] = Json::Value(room_name.c_str());
		json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
		json_req_value["node_id"] = Json::Value(nodeid);
		json_req_value["room_type"] = Json::Value(config_manager::GetInstance()->get_room_type());
	}
	else
	{
		json_req_value["uid"] = Json::Value(userID);
		json_req_value["room_id"] = Json::Value(0);
		json_req_value["room_name"] = Json::Value("");
		json_req_value["game_id"] = Json::Value(0);
		json_req_value["node_id"] = Json::Value(0);
	}

	_tint64 today_result = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_TODAY_RESULT);
	_tint64 history_result = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_HISTORY_RESULT);
	json_req_value["all_result"] = Json::Value(result);
	json_req_value["today_result"] = Json::Value(today_result);
	json_req_value["history_result"] = Json::Value(history_result);
	json_req_value["clientid"] = Json::Value(clientid);
	json_req_value["bet"] = Json::Value(bet);
	json_req_value["reward"] = Json::Value(0);

	int plat = myredis::GetInstance()->get_data_by_uid(userID, DB_COMM, CUR_CHANNEL_ID);
	char db_name[32] = { 0 };
	if (g_dbproxy_namager->get_db_name(plat, db_name, 32))
		json_req_value["db_name"] = Json::Value(db_name);

	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);

	g_log->write_log(LOG_TYPE_DEBUG, "write_user_roominfo... -  userID:%lld is_leave:%d", userID, is_leave ? 1 : 0);

	MY_LOG_DEBUG("write_user_roominfo userID:%d, bill_full json_req_data:%s", userID, json_req_data.c_str());
	g_dbproxy_namager->post_exec(0, 0, userID, "update_room_info", json_req_data.c_str(), db_section, false);
}


void room_bill_fun::write_normal_room_bill(IUser *puser, int type, int room_id, int room_num, int node_id)
{
	if (!puser || puser->get_user_type() == ROBOT_TYPE || type == RBT_LOGON_IN || type == RBT_LOGON_RECONN)
		return;

	if (type == RBT_LOGON_OUT && puser->get_add_num() == 0)
		return;

	Json::Value json_req_value;
	json_req_value["post_time"] = Json::Value(gettimesec());
	json_req_value["uid"] = Json::Value(puser->get_user_id());
	json_req_value["client_id"] = Json::Value(puser->get_client_id());
	json_req_value["version"] = Json::Value(puser->get_version());
	json_req_value["ip"] = Json::Value(puser->get_ip());
	json_req_value["room_id"] = Json::Value(room_id);
	json_req_value["room_num"] = Json::Value(room_num);
	json_req_value["type"] = Json::Value(type);
	json_req_value["i_game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
	json_req_value["nodeid"] = Json::Value(node_id);

	json_req_value["bank"] = Json::Value(0);
	json_req_value["gold"] = Json::Value(puser->get_gold());
	json_req_value["last_gold"] = Json::Value(puser->get_last_gold());
	json_req_value["last_bank"] = Json::Value(puser->get_last_bank_gold());
	json_req_value["result"] = Json::Value(puser->get_add_num());
	json_req_value["changegold"] = Json::Value(puser->get_add_num());
	json_req_value["entertime"] = Json::Value(puser->get_enter_time());
	json_req_value["bet"] = Json::Value(puser->get_add_bill());
	json_req_value["reward"] = Json::Value(0);

	int plat = puser->get_plat_type();
	char db_name[32] = { 0 };
	if (g_dbproxy_namager->get_db_name(plat, db_name, 32))
		json_req_value["db_name"] = Json::Value(db_name);

	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	MY_LOG_DEBUG("room_bill: %s", json_req_data.c_str());
	g_dbproxy_namager->post_exec(0, 0, puser->get_user_id(), "mjq_write_room_bill", json_req_data.c_str(), db_section, false, plat);
}


void room_bill_fun::write_hund_room_bill(IUser *puser, int type, int node_id)
{
	if (!puser || puser->get_user_type() == ROBOT_TYPE || type == RBT_LOGON_IN || type == RBT_LOGON_RECONN)
		return;

	Json::Value json_req_value;
	json_req_value["post_time"] = Json::Value(gettimesec());
	json_req_value["uid"] = Json::Value(puser->get_user_id());

	if (puser)
	{
		json_req_value["client_id"] = Json::Value(puser->get_client_id());
		json_req_value["version"] = Json::Value(puser->get_version());
		json_req_value["ip"] = Json::Value(puser->get_ip());
	}
	else
	{
		MY_LOG_WARN("===get_user_from_uid failed,use Default Value===");
		json_req_value["client_id"] = Json::Value(0);
		json_req_value["version"] = Json::Value("1.0.0");
		json_req_value["ip"] = Json::Value("127.0.0.1");
	}

	json_req_value["room_id"] = Json::Value(g_server_id);
	json_req_value["room_num"] = Json::Value(0);
	json_req_value["type"] = Json::Value(type);
	json_req_value["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
	json_req_value["nodeid"] = Json::Value(node_id);

	json_req_value["bank"] = Json::Value(0);
	json_req_value["gold"] = Json::Value(puser->get_gold());
	json_req_value["last_gold"] = Json::Value(puser->get_last_gold());
	json_req_value["last_bank"] = Json::Value(puser->get_last_bank_gold());
	json_req_value["result"] = Json::Value(puser->get_add_num());
	json_req_value["changegold"] = Json::Value(puser->get_add_num());
	json_req_value["entertime"] = Json::Value(puser->get_enter_time());
	json_req_value["game_bill"] = Json::Value(puser->get_add_bill());
	json_req_value["reward"] = Json::Value(0);
	int plat = puser->get_plat_type();
	char db_name[32] = { 0 };
	if (g_dbproxy_namager->get_db_name(plat, db_name, 32))
		json_req_value["db_name"] = Json::Value(db_name);

	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);

	MY_LOG_DEBUG("room_bill: %s", json_req_data.c_str());

	g_dbproxy_namager->post_exec(0, 0, puser->get_user_id(), "mjq_write_room_bill", json_req_data.c_str(), db_section, false, plat);
}

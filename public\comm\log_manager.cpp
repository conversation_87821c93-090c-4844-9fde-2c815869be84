
#include "log_manager.h"
#include <sys/prctl.h>
#include <sys/syscall.h>

IMYLogManager *g_pstLogManager = MYLogManager::GetInstance();

MYLogManager::MYLogManager()
{
    
}

MYLogManager::~MYLogManager()
{
    if (m_stDLLLog.GetInterface())
    {
        m_stDLLLog.DeleteInstance();
    }
}

int MYLogManager::Init()
{
    if (!m_stDLLLog.DLLInit(SVRLIB_NAME, "_log_instance", "_log_free"))
        perror("Init log from dll error.\n");

    if (!m_stDLLLog.CreateInstance())
        perror("Create log instance error.\n");

    if (!m_stDLLLog.GetInterface())
    {
        printf("log interface error.\n");
        return -1;
    }

    m_pstLog = m_stDLLLog.GetInterface();

    tagLogInit log_init;
#ifdef _DEBUG
    log_init.type = LOG_TYPE_INFO;
#else
    log_init.type = LOG_TYPE_ERROR;
#endif
    if (!m_pstLog->init(log_init))
    {
        m_stDLLLog.DeleteInstance();
        perror("Init log fail and del instance.");
    }

    return 0;
}

#define gettid() syscall(SYS_gettid)

uint64_t get_current_thread_id() {
    return gettid();
}

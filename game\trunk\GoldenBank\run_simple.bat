@echo off
echo GoldenBank 简化测试
echo ===================

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译简化测试程序...

REM 方法1: 尝试最简单的编译
echo 尝试方法1: 基本编译...
g++ -std=c++11 -Wall -g -O0 simple_compare.cpp -o simple_compare.exe

if %errorlevel% equ 0 (
    echo ✅ 基本编译成功！
    goto :run_test
)

REM 方法2: 尝试包含游戏逻辑
echo 方法1失败，尝试方法2: 包含游戏逻辑...
g++ -std=c++11 -Wall -g -O0 -DINCLUDE_GAME_LOGIC -I. simple_compare.cpp game_logic.cpp game_config.cpp -o simple_compare.exe

if %errorlevel% equ 0 (
    echo ✅ 包含游戏逻辑编译成功！
    goto :run_test
)

REM 方法3: 尝试添加头文件路径
echo 方法2失败，尝试方法3: 添加头文件路径...
g++ -std=c++11 -Wall -g -O0 -DINCLUDE_GAME_LOGIC -I. -I../../../public -I../../../public/comm simple_compare.cpp game_logic.cpp game_config.cpp -o simple_compare.exe

if %errorlevel% equ 0 (
    echo ✅ 添加头文件路径编译成功！
    goto :run_test
)

echo ❌ 所有编译方法都失败了
echo.
echo 可能的问题:
echo 1. 缺少必要的源文件
echo 2. 头文件路径不正确
echo 3. 编译器版本不兼容
echo.
echo 建议:
echo 1. 运行 diagnose.bat 检查环境
echo 2. 确保所有必要文件存在
echo 3. 检查编译器安装
pause
exit /b 1

:run_test
echo.
echo 编译成功！正在运行测试...
echo.

simple_compare.exe

echo.
echo 测试完成！
echo.
echo 生成的文件:
if exist "current_simple_result.txt" (
    echo - current_simple_result.txt: 当前计算结果
)
if exist "test.txt" (
    echo - test.txt: 参考数据
)

pause

#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include <cstdlib>
#include <ctime>
#include "nlohmann/json.hpp"
#include <algorithm>
using json = nlohmann::json;


game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

int game_logic::getMul(int count, int num) {
	int nMul = 1;
	for (int i = 0; i < count; i++) {
		nMul *= num;
	}
	return nMul;
}

void game_logic::CalcResultTimes(user_game_info &game_info)
{	
	game_info.result = 0;
	game_info.bonus = 0;
	int count = game_info.graph_data.size()/9;
	game_info.free_count = count - 1;
	game_info.free_count = game_info.free_count < 0 ? 0 : game_info.free_count;
	for (int num = 0; num < count; num++)
	{
		int icons[3][3] = {0};
		for (int i = 0; i < 3; i++)
		{
			for (int j = 0; j < 3; j++) {
				icons[i][j] = game_info.graph_data[num*9 + i*3 + j];
			}
		}
		CalcResultTimes(game_info, icons, num);
	}
	game_info.result *= 100;
}

int game_logic::CalcResultTimes(user_game_info &gameInfo, int icons[3][3], int pageNum)
{   
	bool mainGame = pageNum == 0;
	if (mainGame) {
		gameInfo.result = 0;
	}

	gameInfo.award_type = -1;
	int bet = gameInfo.bet / 100;
	int iconCount[ICON_WILD + 1] = { 0,0,0,0,0,0,0,0,0,0,0,0 }; //统计图标个数
	int all_icon = 0;  //中间行图标个数
	int bonusCount = 0; //bonus图标个数
	int bonusCol[3] = {0,0,0};
	int count = gameInfo.graph_data.size()/9;
	int superBonusCount = 0;
	int superBonusCol[3] = {0,0,0};
	int freeCount = 0;
	for (int i = 0; i < 3; i++)
	{
		for (int j = 0; j < 3; j++)
		{	
			if (icons[i][j] == ICON_BONUS)
			{
				bonusCount++;
				bonusCol[i]++;
			}	
			if (!mainGame) {
				if (icons[i][j] == ICON_BONUS_SUPER) {
					superBonusCount++;
					superBonusCol[i]++;
				}
				if (icons[i][j] == ICON_FREE) {
					freeCount++;
				}
			}
		}
	}

	iconCount[icons[0][1]]++;
	iconCount[icons[1][1]]++;
	iconCount[icons[2][1]]++;

	int nMul = 0;
	int nWildCount = 0;
	nMul = getMul(iconCount[ICON_WILD], 2) * getMul(iconCount[ICON_WILD_2X], 2) * getMul(iconCount[ICON_WILD_3X], 3) * getMul(iconCount[ICON_WILD_5X], 5);
	nWildCount = iconCount[ICON_WILD_2X] + iconCount[ICON_WILD_3X] + iconCount[ICON_WILD_5X];

	if (nMul == 0)
	{
		nMul = 1;
	}
	gameInfo.mul = nMul;

	int odds = 0;
	int curLight = 0;
	if (nWildCount == 3) {
		odds = 5;
	} else {
		if (iconCount[ICON_BLANK] == 0 && iconCount[ICON_WILD] == 0 && iconCount[ICON_FREE] == 0) {
			odds = get_odds(curLight, iconCount, nWildCount);
		}
	}
	light = gameInfo.light;

	int bonus = 0;
	int roundResult = 0;
	if (mainGame) {
		if (bonusCount >= 3 && bonusCol[0] == 1 && bonusCol[1] == 1 && bonusCol[2] == 1) {

			bonus = 3 * bet;
			int baseBonus = 2 * bet;

			gameInfo.pool[0] = baseBonus;
			gameInfo.pool[1] = baseBonus;
			gameInfo.pool[2] = baseBonus;

			gameInfo.bonus_count[0] = 1;
			gameInfo.bonus_count[1] = 1;
			gameInfo.bonus_count[2] = 1;

			free_info freeInfo;
			freeInfo.mul = nMul;
			freeInfo.pool[0] = 2 * bet;
			freeInfo.pool[1] = 2 * bet;
			freeInfo.pool[2] = 2 * bet;
			freeInfo.curColNum[0] = 1;
			freeInfo.curColNum[1] = 1;
			freeInfo.curColNum[2] = 1;
			
			freeInfo.showBonus[0] = 2 * bet;
			freeInfo.showBonus[1] = 2 * bet;
			freeInfo.showBonus[2] = 2 * bet;

			freeInfo.winPool[0] = 0;
			freeInfo.winPool[1] = 0;
			freeInfo.winPool[2] = 0;

			freeInfo.round_result = odds * nMul*bet + bonus;
			freeInfo.plus = 10;
			freeInfo.light = light;
			gameInfo.free_data.push_back(freeInfo);

			gameInfo.freeTotalRound = 50;
		} else {
			gameInfo.freeTotalRound = 10;
		}
		roundResult = odds * nMul*bet + bonus;
		gameInfo.freeRemainRound = 10;
		
		gameInfo.round_result = roundResult;
	} else {
		free_info freeInfo;
		int addBonus = 0;
		freeInfo.mul = nMul;
		if (bonusCount > 0 || superBonusCount > 0) {
			odds = 0;
		}
		freeInfo.odds = odds;
		if (freeCount == 2) {
			freeInfo.plus = 5;
		} else if (freeCount == 3) {
			freeInfo.plus = 10;
		}

		freeInfo.superBonusCol[0] = superBonusCol[0];
		freeInfo.superBonusCol[1] = superBonusCol[1];
		freeInfo.superBonusCol[2] = superBonusCol[2];
		freeInfo.superBonusCount = superBonusCount;

		freeInfo.pool[0] = gameInfo.pool[0];
		freeInfo.pool[1] = gameInfo.pool[1];
		freeInfo.pool[2] = gameInfo.pool[2];
		if (bonusCol[0] == 1)
		{
			long winBonus = gameInfo.pool[0];
			gameInfo.pool[0] = gameInfo.pool[0] + gameInfo.bonus_count[0] * bet;
			gameInfo.bonus_count[0]++;

			freeInfo.pool[0] = gameInfo.pool[0];
			freeInfo.curColNum[0] = 1;
			freeInfo.showBonus[0] = gameInfo.pool[0];
			freeInfo.winPool[0] = winBonus;

			addBonus += winBonus;
		}
		if (bonusCol[1] == 1) {
			long winBonus = gameInfo.pool[1];
			gameInfo.pool[1] = gameInfo.pool[1] + gameInfo.bonus_count[1]*bet;
			gameInfo.bonus_count[1]++;

			freeInfo.pool[1] = gameInfo.pool[1];
			freeInfo.curColNum[1] = 1;
			freeInfo.showBonus[1] = gameInfo.pool[1];
			freeInfo.winPool[1] = winBonus;

			addBonus += winBonus;
		}
		if (bonusCol[2] == 1) {
			long winBonus = gameInfo.pool[2];
			gameInfo.pool[2] = gameInfo.pool[2] + gameInfo.bonus_count[2]*bet;
			gameInfo.bonus_count[2]++;

			freeInfo.pool[2] = gameInfo.pool[2];
			freeInfo.curColNum[2] = 1;
			freeInfo.showBonus[2] = gameInfo.pool[2];
			freeInfo.winPool[2] = winBonus;

			addBonus += winBonus;
		}

		if (superBonusCount > 0) {
			long winBonus = gameInfo.pool[0];
			gameInfo.pool[0] = gameInfo.pool[0] + gameInfo.bonus_count[0]*bet;
			gameInfo.bonus_count[0]++;
			freeInfo.curColNum[0] = 1;
			freeInfo.showBonus[0] = gameInfo.pool[0];
			freeInfo.winPool[0] = winBonus;
			addBonus += winBonus;

			winBonus = gameInfo.pool[1];
			gameInfo.pool[1] = gameInfo.pool[1] + gameInfo.bonus_count[1]*bet;
			gameInfo.bonus_count[1]++;
			freeInfo.curColNum[1] = 1;
			freeInfo.showBonus[1] = gameInfo.pool[1];
			freeInfo.winPool[1] = winBonus;
			addBonus += winBonus;

			winBonus = gameInfo.pool[2];
			gameInfo.pool[2] = gameInfo.pool[2] + gameInfo.bonus_count[2]*bet;
			gameInfo.bonus_count[2]++;
			freeInfo.curColNum[2] = 1;
			freeInfo.showBonus[2] = gameInfo.pool[2];
			freeInfo.winPool[2] = winBonus;
			addBonus += winBonus;

			
			freeInfo.pool[0] = gameInfo.pool[0];
			freeInfo.pool[1] = gameInfo.pool[1];
			freeInfo.pool[2] = gameInfo.pool[2];
		}
		bonus = addBonus;

		if (odds > 0) {
			roundResult = odds * nMul*bet;
		} else {
			roundResult = bonus;
		}
		freeInfo.round_result = roundResult;
		gameInfo.free_data.push_back(freeInfo);
		
	}

	gameInfo.plate_win = roundResult;
	gameInfo.result += gameInfo.plate_win;
	if (!mainGame) {
		gameInfo.bonus += roundResult;
	}

	MY_LOG_DEBUG("total result:%d round result:%d odds:%d mul:%d bonus:%d bet:%d", gameInfo.result, roundResult, odds, nMul, bonus, bet);	
	return gameInfo.result;
	
}

int game_logic::get_odds(int &gameInfo, int iconCount[ICON_WILD + 1], int nWildCount)
{
	if (iconCount[ICON_BAR_1]+nWildCount == 3) {
		gameInfo.light = 2;
		return odd_info[1];
	} else if (iconCount[ICON_BAR_2]+nWildCount == 3) {
		gameInfo.light = 3;
		return odd_info[2];
	} else if (iconCount[ICON_BAR_3]+nWildCount == 3) {
		gameInfo.light = 4;
		return odd_info[3];
	} else if (iconCount[ICON_7]+nWildCount == 3) {
		gameInfo.light = 5;
		return odd_info[4];
	} else if (iconCount[ICON_BONUS] == 3) {
		return 3;
	} else if (iconCount[ICON_BONUS] == 2) {
		return 2;
	} else if (iconCount[ICON_BONUS] == 1) {
		return 0;
	}
	gameInfo.light = 1;
	return 1;
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}

void game_logic::RandFillIconNoWin(user_game_info &game_info)
{  
	int Icon[3][3] = {0};
	int nBonus1 = 0;
	int middleRowBlankCount = 0; // 统计中间行的BLANK数量
	
	// 为每一列决定ICON_BLANK的位置模式
	for (int i = 0; i < 3; i++)
	{
		// 随机决定这一列ICON_BLANK的位置模式：0=中间，1=两边
		int blankPattern = getRand(0, 1);
		
		// 检查中间行BLANK数量，确保至少有一个，最好不要同时出现三个
		if (i == 2) { // 最后一列
			if (middleRowBlankCount == 0) {
				// 如果前面两列都没有中间BLANK，强制最后一列选择中间模式
				blankPattern = 0;
			} else if (middleRowBlankCount == 2) {
				// 如果前面两列都是中间BLANK，强制最后一列选择两边模式
				blankPattern = 1;
			}
		}
		
		// 根据模式严格填充图标
		if (blankPattern == 0) {
			// 中间模式：ICON_BLANK在中间，两边为非BLANK且不重复
			Icon[i][1] = ICON_BLANK; // 中间位置固定为BLANK
			middleRowBlankCount++; // 中间行BLANK数量+1
			
			// 生成两边的非BLANK图标，确保不重复
			int leftIcon = getRand(ICON_BLANK+1, ICON_BONUS);
			int rightIcon = getRand(ICON_BLANK+1, ICON_BONUS);
			while (rightIcon == leftIcon) {
				rightIcon = getRand(ICON_BLANK+1, ICON_BONUS);
			}
			
			// 检查BONUS图标数量限制
			if (leftIcon == ICON_BONUS) nBonus1++;
			if (rightIcon == ICON_BONUS) nBonus1++;
			if (nBonus1 >= 1) {
				if (leftIcon == ICON_BONUS) {
					leftIcon = getRand(ICON_BLANK+1, ICON_BONUS - 1);
				}
				if (rightIcon == ICON_BONUS) {
					rightIcon = getRand(ICON_BLANK+1, ICON_BONUS - 1);
					while (rightIcon == leftIcon) {
						rightIcon = getRand(ICON_BLANK+1, ICON_BONUS - 1);
					}
				}
			}
			
			Icon[i][0] = leftIcon;
			Icon[i][2] = rightIcon;
		} else {
			// 两边模式：ICON_BLANK在两边，中间为非BLANK
			Icon[i][0] = ICON_BLANK; // 左边固定为BLANK
			Icon[i][2] = ICON_BLANK; // 右边固定为BLANK
			
			// 生成中间的非BLANK图标
			int middleIcon = getRand(ICON_BLANK+1, ICON_BONUS);
			
			// 检查BONUS图标数量限制
			if (middleIcon == ICON_BONUS) nBonus1++;
			if (nBonus1 >= 1 && middleIcon == ICON_BONUS) {
				middleIcon = getRand(ICON_BLANK+1, ICON_BONUS - 1);
			}
			
			Icon[i][1] = middleIcon;
		}
	}

	game_info.graph_data.clear();
	for (int i = 0; i < 3; i++)
	{
		for (int j = 0; j < 3; j++)
		{
			game_info.graph_data.push_back(Icon[i][j]);
		}
	}
}

bool game_logic::get_game_data_from_graph_data(user_game_info &game_info, const string& graph)
{
    // 解析 JSON 计算开奖
    json parsedData = json::parse(graph);
    vector<vector<int>> data;

    data = parsedData.get<vector<vector<int>>>();
	game_info.reset();
	for (auto &d : data)
	{
		for (auto &g : d)
		{
			game_info.graph_data.push_back(g);
		}
	}
	MY_LOG_PRINT("graph len = %d", game_info.graph_data.size());
	// MY_LOG_PRINT("game_info.normal_platewin = %d", game_info.normal_platewin);
    // MY_LOG_PRINT("game_info.respin_platewin = %d", game_info.respin_platewin);
    // MY_LOG_PRINT("game_info.wheel_win = %d", game_info.wheel_win);

    return true;
}

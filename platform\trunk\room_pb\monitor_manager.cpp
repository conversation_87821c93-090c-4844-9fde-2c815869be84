﻿#include "monitor_manager.h"
#include "game_frame.h"
#include "room_route_challenge.h"
#include "table_manager.h"
#include "user_manager.h"
#include "game_frame.h"
#include "config_manager.h"

static CMonitorManager _monitor_mgr;
IMonitorManager * g_monitor_mgr = &_monitor_mgr;

#define MAX_MONITOR_CMD_COUNT       50                  //最大命令数
static CRoomRouteChallenge _room_route_challenge;

typedef bool (CMonitorManager::*MONITORCMDSINK)(_uint64 sid, int argc, const char ** argv);

struct tagMonitorCmdSink
{
    const char * cmd;
    MONITORCMDSINK sink;

	tagMonitorCmdSink(char * _cmd = NULL, MONITORCMDSINK _sink = NULL)
	{
		cmd = _cmd;
		sink = _sink;
	}
};

tagMonitorCmdSink * get_monitor_cmd_sink(int index)
{
    static tagMonitorCmdSink _monitor_cmd_sink[MAX_MONITOR_CMD_COUNT];
    if (index >= MAX_MONITOR_CMD_COUNT) return NULL;
    return &_monitor_cmd_sink[index];
}

bool __check_cmd(const char * cmd1, const char * cmd2)
{
    if (!cmd1 || !cmd2) return false;
    if (strcmp(cmd1, cmd2) == 0) return true;
    return false;
}

void __reg_monitor_cmd_sink(const char * cmd, MONITORCMDSINK sink)
{
    static int index = 0;
    if (index >= MAX_MONITOR_CMD_COUNT) return ;
    tagMonitorCmdSink * pMonitorCmdSink = get_monitor_cmd_sink(index);
    if (pMonitorCmdSink)
    {
        pMonitorCmdSink->cmd = cmd;
        pMonitorCmdSink->sink = sink;
        ++index;
    }
}

MONITORCMDSINK __get_monitor_cmd_sink(const char * cmd)
{
    if (!cmd) return NULL;
    for (int i = 0; i < MAX_MONITOR_CMD_COUNT; ++i)
    {
        tagMonitorCmdSink * pMonitorCmdSink = get_monitor_cmd_sink(i);
        if (__check_cmd(cmd, pMonitorCmdSink->cmd))
        {
            return pMonitorCmdSink->sink;
        }
    }

    return NULL;
}

CMonitorManager::CMonitorManager()
{
    __reg_monitor_cmd_sink("info", &CMonitorManager::on_info);
    __reg_monitor_cmd_sink("table_list", &CMonitorManager::on_table_list);
    __reg_monitor_cmd_sink("log", &CMonitorManager::on_log);
    __reg_monitor_cmd_sink("exit", &CMonitorManager::on_exit);
    __reg_monitor_cmd_sink("roomupdate", &CMonitorManager::on_room_update);
    __reg_monitor_cmd_sink("updateconfig", &CMonitorManager::on_update_config);
    __reg_monitor_cmd_sink("roomnormal", &CMonitorManager::on_room_normal);
    __reg_monitor_cmd_sink("dismiss_room", &CMonitorManager::on_dismiss_room);
    __reg_monitor_cmd_sink("load_keywords", &CMonitorManager::on_load_keywords);
}

CMonitorManager::~CMonitorManager()
{

}

bool CMonitorManager::init()
{
    return true;
}

void CMonitorManager::on_recv_cmd(_uint64 sid, int argc, const char ** argv, const char * cmd)
{
    if (!g_monitor.GetInterface()) return ;
    
    if (argc <= 0) return ;
    bool bRet = false;
    
    MONITORCMDSINK sink = __get_monitor_cmd_sink(argv[0]);
    if (!sink)
    {
        on_nonsupport(sid, cmd);
    }
    else
    {
        bRet = (this->*(sink))(sid, argc, argv);
        if (!bRet)
        {
            g_monitor->send_remote(sid, "command [%s] run fail.", cmd);
        }
    }
}

void CMonitorManager::on_nonsupport(_uint64 sid, const char * cmd)
{
    char buf[1024*4]={0};//
    int  off = 0;
    off += __sprintf(buf + off,sizeof(buf) - off,"command [%s] nonsupport!\r\n\r\n",cmd);
    off += __sprintf(buf + off,sizeof(buf) - off,"---------------------------------------------------------\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"command prompt:\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"info                  --info num\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"table_list            --all table info\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"log [type]            --set log level type (type=0 - 3)\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"updateconfig          --update xml config\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"roomupdate            --room update\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"roomnormal            --room normal\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"dismiss_room          --dismiss_room\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"load_keywords         --load filter words file:[filterwords.txt]\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"exit                  --exit server\r\n");
    off += __sprintf(buf + off,sizeof(buf) - off,"---------------------------------------------------------\r\n");
    g_monitor->send_remote(sid,buf);
}

bool CMonitorManager::on_info(_uint64 sid, int argc, const char ** argv)
{
	if(argc <= 1) return false;
	int room_code = atoi(argv[1]);


	char buf[1024]={0};
	int off = 0;
	int len = 1024;
	
	off += __sprintf(buf + off,len - off,"table user_info_beg \r\n  num=%u",room_code);

	ITable* pTable = TableManager::GetInstance()->find_table_from_num(room_code);
	if(pTable)
	{	
        off += __sprintf(buf + off, len - off, " max_count=%d play_count=%d owner=%u\r\n"
            , pTable->get_max_count(), pTable->get_play_count(), (_uint32)pTable->get_owner_uid());

	    int count = pTable->get_max_player();		
		for (int i = 0; i < count; i++)
		{	
			IUser* puser = pTable->get_user_from_chair(i);
			if (puser)
			{
				off += __sprintf(buf + off, len - off, "\tuid=%u offline=%u status=%u chair=%u \r\n"
                    , puser->get_user_id(), puser->is_offline(),puser->get_user_status(),puser->get_chair_id());
			}
		}		
	}

	off += __sprintf(buf + off,len - off,"table user_info_end \r\n");
    g_monitor->send_remote(sid,buf);

    return true;
}

bool CMonitorManager::on_table_list(_uint64 sid, int argc, const char ** argv)
{
    const int len = 102400;
    char buf[len + 1] = {0};
    int off = 0;

    off += __sprintf(buf + off, len - off, "table list begin: free=%d used=%d \r\n"
        , TableManager::GetInstance()->get_free_count(), TableManager::GetInstance()->get_table_count());

    ITable *pTable;
    const MAPNumTable* p_table_map = TableManager::GetInstance()->get_table_list();
    if(p_table_map)
    {   
        MAPNumTable::const_iterator mit = p_table_map->begin();
        for (;mit != p_table_map->end();mit++)
        {   
            pTable = mit->second;
            if (!pTable)
                continue;

            off += __sprintf(buf + off,len - off, "  num=%d owner=%u max=%d play=%d\r\n"
                , pTable->get_room_num()
                , (_uint32)pTable->get_owner_uid()
                , pTable->get_max_count()
                , pTable->get_play_count());
        }
    }
    off += __sprintf(buf + off, len - off, "table list end. \r\n");

    g_monitor->send_remote(sid, buf);

    return true;
}

bool CMonitorManager::on_log(_uint64 sid, int argc, const char ** argv)
{
    if(argc <= 1) return false;
    int lt = atoi(argv[1]);
    lt = __min(3,__max(lt,0));
    g_log->set_type((TLOGTYPE)lt);
    g_monitor->send_remote(sid,"set log type=%d ok!",g_log->get_type());

    return true;
}

bool CMonitorManager::on_exit(_uint64 sid, int argc, const char ** argv)
{
    g_b_running = false;
    g_monitor->send_remote(sid, "exit ok!");
    return true;
}

bool CMonitorManager::on_update_config(_uint64 sid, int argc, const char ** argv)
{
    int ret;

    ret = CGameFrame::GetInstance()->get_config()->reload();
	config_manager::GetInstance()->db_connect_ok();
    if (ret)
    {
        g_monitor->send_remote(sid, "update xml config fail! errno=%d", ret);
    }
    else
    {
        g_monitor->send_remote(sid, "update xml config ok!...");
    }

    return true;
}

bool CMonitorManager::on_room_update(_uint64 sid, int argc, const char ** argv)
{
    g_monitor->send_remote(sid, "room update succeed!");
	CGameFrame::GetInstance()->set_room_status(room_route::ROOM_STATUS_UPDATE);
	_room_route_challenge.send_room_info();

    return true;
}

bool CMonitorManager::on_room_normal(_uint64 sid, int argc, const char ** argv)
{
	g_monitor->send_remote(sid, "room normal succeed!");
	CGameFrame::GetInstance()->set_room_status(room_route::ROOM_STATUS_NORMAL);
	_room_route_challenge.send_room_info();
	return true;
}

bool CMonitorManager::on_dismiss_room(_uint64 sid, int argc, const char ** argv)
{
	const MAPNumTable* p_table_map = TableManager::GetInstance()->get_table_list();
	if(p_table_map)
	{
		int room_num_list[5000] = {0};
		int count = 0;
		MAPNumTable::const_iterator mit = p_table_map->begin();
		for (;mit != p_table_map->end();mit++)
		{
			room_num_list[count++] = mit->second->get_room_num();
		}
		for (int i = 0;i< count;i++)
		{
			CGameFrame::GetInstance()->on_free_table(room_num_list[i]);
		}
		
	}
	g_monitor->send_remote(sid, "dismiss_room succeed!");

	return true;
}

#include "kw.h"

bool CMonitorManager::on_load_keywords(_uint64 sid, int argc, const char ** argv)
{
    int iRet = kw_init("filterwords.txt");
    if (iRet)
    {
        MY_LOG_WARN("Load keywords fail. %d", iRet);

        g_monitor->send_remote(sid, "load keywords fail.");
    }
    else
    {
        g_monitor->send_remote(sid, "load keywords succeed.");
    }

    return true;
}
@echo off
echo GoldenBank 编译环境诊断
echo ========================

echo 1. 检查编译器...
where g++ >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到 g++ 编译器
    g++ --version | findstr "g++"
) else (
    echo ❌ 未找到 g++ 编译器
    echo 请安装 MinGW 或其他 C++ 编译器
)

echo.
echo 2. 检查必要文件...
if exist "test_bet_roll_unit.cpp" (
    echo ✅ test_bet_roll_unit.cpp 存在
) else (
    echo ❌ test_bet_roll_unit.cpp 不存在
)

if exist "game_logic.cpp" (
    echo ✅ game_logic.cpp 存在
) else (
    echo ❌ game_logic.cpp 不存在
)

if exist "game_config.cpp" (
    echo ✅ game_config.cpp 存在
) else (
    echo ❌ game_config.cpp 不存在
)

if exist "game_logic.h" (
    echo ✅ game_logic.h 存在
) else (
    echo ❌ game_logic.h 不存在
)

echo.
echo 3. 检查 protobuf 库...
echo 尝试编译简单的 protobuf 测试...
echo #include ^<iostream^> > test_proto.cpp
echo #include ^<google/protobuf/message.h^> >> test_proto.cpp
echo int main(){std::cout^<^<"protobuf test"^<^<std::endl;return 0;} >> test_proto.cpp

g++ -std=c++11 test_proto.cpp -lprotobuf -o test_proto.exe >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ protobuf 库可用
    del test_proto.exe >nul 2>nul
) else (
    echo ❌ protobuf 库不可用或未安装
)
del test_proto.cpp >nul 2>nul

echo.
echo 4. 尝试简单编译测试...
echo 测试基本 C++ 编译...
echo #include ^<iostream^> > simple_test.cpp
echo int main(){std::cout^<^<"Hello World"^<^<std::endl;return 0;} >> simple_test.cpp

g++ -std=c++11 simple_test.cpp -o simple_test.exe >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ 基本 C++ 编译成功
    del simple_test.exe >nul 2>nul
) else (
    echo ❌ 基本 C++ 编译失败
)
del simple_test.cpp >nul 2>nul

echo.
echo 5. 检查头文件路径...
if exist "../../../public" (
    echo ✅ ../../../public 路径存在
) else (
    echo ❌ ../../../public 路径不存在
)

if exist "../../../public/comm" (
    echo ✅ ../../../public/comm 路径存在
) else (
    echo ❌ ../../../public/comm 路径不存在
)

echo.
echo 诊断完成！
pause

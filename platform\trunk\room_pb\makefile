# Make command to use for dependencies
RM=rm
MKDIR=@mkdir

APP=room_fh
LIB=-lpthread -lrt -ldl -rdynamic -L../../../public/lib/uuid/lib -luuid -lprotobuf

glibc_version := $(shell getconf GNU_LIBC_VERSION | cut -d' ' -f2)
major_version := $(shell echo $(glibc_version) | cut -d'.' -f1)
minor_version := $(shell echo $(glibc_version) | cut -d'.' -f2)

# PBLIBPRE默认为空，如果glibc版本大于2.28，则PBLIBPRE为dev
PBLIBPRE=""
ifeq ($(shell [ $(major_version) -gt 2 ] || [ $(major_version) -eq 2 -a $(minor_version) -ge 28 ]; echo $$?),0)
    PBLIBPRE=dev
endif

OBJDIR=../_obj/$(APP)
EXEDIR=/home/<USER>/game-server-bin/game/fullhouse
COMMONDIR=../../../public/comm
PROTOCOL=../../../public/protocol
PUBLICDIR=../../../public/share
KEYWORDS=../../../public/kw
TINYXMLDIR=../../../public/lib/tinyxml
JSONDIR=../../../public/lib/json
HASHDIS=../../../public/lib/hashdis
PROTOBUF_PROTOCOL=../../../public/protobuf
PROTOBUF=../../../public/lib/protobuf/include
INCDIR=-I./ -I../../../public/lib/svrlib/include -I$(TINYXMLDIR) -I$(COMMONDIR) -I$(PROTOCOL) -I$(PUBLICDIR) -I$(KEYWORDS) -I$(PROTOBUF_PROTOCOL) -I$(PROTOBUF)
LIBDIR=-L../../../public/lib/protobuf/$(PBLIBPRE)lib
COMPILE_ARGS=-DUSE_GAME_DLL -c -o
LINK_ARGS=-o
LDFLAGS= -Wl,-rpath=./


EXEFILE=$(EXEDIR)/$(APP)
SRC := $(wildcard *.cpp $(TINYXMLDIR)/*.cpp $(COMMONDIR)/*.cpp  $(JSONDIR)/*.cpp $(PUBLICDIR)/*.cpp $(KEYWORDS)/*.cpp $(HASHDIS)/*.cpp $(PROTOBUF_PROTOCOL)/*.cpp)
OBJFILE := $(patsubst %.cpp, $(OBJDIR)/%.o, $(notdir ${SRC}))
	
SHOW_COMPILE=@echo -e "\033[36mCompling \033[35m==> \033[33m$<\033[0m"
SHOW_LINK=@echo -e "\033[31mLINKING \033[35m==> \033[33m$(EXEFILE)\033[0m"
SHOW_DEBUG_BUILD=@echo -e "\033[31mBuilding Debug...\033[0m"
SHOW_RELEASE_BUILD=@echo -e "\033[31mBuilding Release...\033[0m"

COMPILE=$(SHOW_COMPILE);g++ -g -std=c++11 $(COMPILE_ARGS) "$(OBJDIR)/$(*F).o" $(INCDIR) -w "$<"
LINK=$(SHOW_LINK);g++ -g $(LIBDIR) $(LINK_ARGS) "$(EXEFILE)" $(OBJFILE) $(LDFLAGS) $(OBJFILE_Z) $(LIB)

# Pattern rules
$(OBJDIR)/%.o : %.cpp
	$(COMPILE)
	
$(OBJDIR)/%.o : $(TINYXMLDIR)/%.cpp
	$(COMPILE)
    
$(OBJDIR)/%.o : $(COMMONDIR)/%.cpp
	$(COMPILE)
    
$(OBJDIR)/%.o : $(JSONDIR)/%.cpp
	$(COMPILE)
	
$(OBJDIR)/%.o : $(PUBLICDIR)/%.cpp
	$(COMPILE)
	
$(OBJDIR)/%.o : $(KEYWORDS)/%.cpp
	$(COMPILE)	
	
$(OBJDIR)/%.o : $(HASHDIS)/%.cpp
	$(COMPILE)

$(OBJDIR)/%.o: $(PROTOBUF_PROTOCOL)/%.cpp
	$(COMPILE)
	
# Build rules
all: 
	$(SHOW_DEBUG_BUILD);$(MAKE) $(EXEFILE)
release:
	$(SHOW_RELEASE_BUILD);$(MAKE) $(EXEFILE)
debug: 
	$(SHOW_DEBUG_BUILD);$(MAKE) $(EXEFILE) COMPILE_ARGS="-DUSE_GAME_DLL -g -c -o" LINK_ARGS="-g -o" 

$(EXEFILE): $(OBJDIR) $(OBJFILE) $(OBJFILE_Z)
	$(MKDIR) -p $(EXEDIR)
	$(LINK)
	#@tar -xvf ../../../public/lib/svrlib/lib/linux/libsvr.so.tar.gz -C $(EXEDIR) >/dev/null
	#@sh sh_tar.sh  ./version.h  $(EXEDIR) $(APP) #tar file
	@cp ../../../public/lib/protobuf/$(PBLIBPRE)lib/libprotobuf.so $(EXEDIR)

$(OBJDIR):
	$(MKDIR) -p $(OBJDIR)


# Rebuild this project
rebuild: cleanall all

# Clean this project
clean:
	$(RM) -f $(EXEFILE)
	$(RM) -f $(OBJFILE)
	$(RM) -f $(OBJFILE_Z)
	$(RM) -f $(EXEDIR)/*.gz

# Clean this project and all dependencies
cleanall: clean

install : rebuild
	cp $(EXEFILE) ~/server/room/
	@cp ../../../public/lib/protobuf/$(PBLIBPRE)lib/libprotobuf.so ~/server/room/

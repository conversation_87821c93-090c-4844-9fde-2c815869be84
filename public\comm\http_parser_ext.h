#pragma once

#include <string>
#include <functional>

#include "http_parse.h"    
#include "http_request.h"
#include "typedef.h"

namespace tnet {

    typedef std::function<void (svrlib::socket_id, const HttpRequest&, RequestEvent, const void*)> RequestCallback_t;

    class HttpError {
    public:
        HttpError(int code = 200, const std::string& m = std::string())
            : statusCode(code)
            , message(m)
        {}    

        //200 for no error
        int statusCode;
        std::string message;
    };

    class HttpParser {
    public:
        friend class HttpParserSettings;

        HttpParser(enum http_parser_type type, svrlib::socket_id sid, const RequestCallback_t& callback);
        virtual ~HttpParser();

        enum http_parser_type getType() { return (http_parser_type)m_parser.type; }

        enum Event {
            Parser_MessageBegin,    
            Parser_Url,
            Parser_StatusComplete,
            Parser_HeaderField,
            Parser_HeaderValue,
            Parser_HeadersComplete,
            Parser_Body,
            Parser_MessageComplete,
        };

        int execute(const char* buf, size_t count);

        HttpRequest* get_request() { return &m_request; }

    protected:

        virtual int onMessageBegin() ;
        virtual int onUrl(const char*, size_t) ;
        virtual int onHeader(const std::string& field, const std::string& value) ;
        virtual int onHeadersComplete() ;
        virtual int onBody(const char*, size_t) ;
        virtual int onMessageComplete() ;
        virtual int onUpgrade(const char*, size_t) ;
        virtual int onError(const HttpError& error) ;

    private:
        int onParser(Event, const char*, size_t);
    
        int handleMessageBegin();
        int handleHeaderField(const char*, size_t);
        int handleHeaderValue(const char*, size_t);
        int handleHeadersComplete();

    protected:
        struct http_parser m_parser;
        svrlib::socket_id m_fd;

        HttpRequest m_request;
        RequestCallback_t m_callback;

        std::string m_curField;
        std::string m_curValue;
        bool m_lastWasValue;
        
        int m_errorCode;

        static size_t ms_maxHeaderSize;
        static size_t ms_maxBodySize;
    };   
}
﻿/*
-----------------------------------------------------------------------------

File name        :   timer_manager.h          
Author           :        
Version          :   1.0            
Date             :   2014.3.13     
Description      :   定时器管理模块  

-----------------------------------------------------------------------------
*/
#pragma once
#include "room_common.h"
#include "timer_interface.h"
#include "thread_interface.h"


class CTimerManager:
    public  ITimerManager
{
public:
    CTimerManager(void);
    ~CTimerManager(void);

    virtual  bool      init();
    virtual  bool      final();

	//固定定时器（timerid > 0）
	virtual  bool      set_timer(void*obj,FOnTimer sink,_uint32 timerid,_uint32 Elapse,_uint64 tag=0,_uint32 repeat=0);
	virtual  bool      kill_timer(_uint32 timerid);

	//随机定时器(成功返回 大于0的定时器ID)
	virtual  _uint32   set_random_timer(void*obj,FOnTimer sink,_uint32 Elapse,_uint64 tag=0,_uint32 repeat=0);
	virtual  bool      kill_random_timer(_uint32 timerid);

private:
	CDLLHelper<ITimerBase>    m_timer;
    CDLLHelper<ITimerBase>    m_random_timer;
	static  FTYPE(void)  on_timer_check(void* obj);
	static  FTYPE(void)  on_timer(void*p_obj,tagTimerInfo& info,void* pdata,_uint32 iRepeat,_uint32 delay);
	static  FTYPE(void)  on_random_timer(void*p_obj,tagTimerInfo& info,void* pdata,_uint32 iRepeat,_uint32 delay);
};

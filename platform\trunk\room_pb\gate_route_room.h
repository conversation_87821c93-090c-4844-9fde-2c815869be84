﻿

#ifndef _GATE_ROUTE_ROOM_H_
#define _GATE_ROUTE_ROOM_H_

#include "room_common.h"

class CGateRouteRoom
{
public:
    CGateRouteRoom();
    virtual ~CGateRouteRoom();

public:

    static FTYPE(void) update_user_game_info(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
    //static FTYPE(void) update_user_logon_info(socket_id pid, SNETHEAD* head, const void* data, int size, const void* ex_data);
};

#endif //_GATE_ROUTE_ROOM_H_

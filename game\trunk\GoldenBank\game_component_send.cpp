#include "comm_class.h"
#include "log_manager.h"
#include "game_component.h"
#include <algorithm>
#include "table_hunded_interface.h"
#include "user_interface.h"
#include "cmd_net.h"
#include "room_route.h"
#include "bill_fun.h"
#include "rand_position.h"
#include "server.pb.h"
#include "cbt.pb.h"
#include "google/protobuf/text_format.h"
#include "google/protobuf/util/json_util.h"
#include "google/protobuf/util/message_differencer.h"
#include "google/protobuf/message.h"
#include "google/protobuf/descriptor.h"

#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <fstream>
#include <cmath>
#include <array>
#include "winlose.h"
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/buffer.h>
// AES 密钥长度（256-bit = 32 字节）
constexpr size_t AES_KEY_SIZE = 32;
constexpr size_t AES_BLOCK_SIZE = 16;

const int GO_TO_REPLAY = 0;//是否进入回放

std::string base64Encode(const std::vector<unsigned char>& data) 
{
    BIO* bio, * b64;
    BUF_MEM* bufferPtr;
    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);
    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);  // 不加换行符
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);
    std::string encoded(bufferPtr->data, bufferPtr->length);
    BIO_free_all(bio);
    return encoded;
}

// AES-CBC 加密函数
std::vector<unsigned char> encryptAESCBC(const std::string& plaintext, const std::string& token) 
{
    std::vector<unsigned char> iv(AES_BLOCK_SIZE);
    RAND_bytes(iv.data(), AES_BLOCK_SIZE);  // 生成随机 IV

    // 截取 token 前 32 字节作为 key
    std::string keyString = token.substr(0, AES_KEY_SIZE);
    std::vector<unsigned char> key(keyString.begin(), keyString.end());

    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) throw std::runtime_error("Failed to create context");

    if (1 != EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key.data(), iv.data()))
        throw std::runtime_error("EncryptInit failed");

    std::vector<unsigned char> ciphertext(plaintext.size() + AES_BLOCK_SIZE);
    int len = 0;
    int ciphertext_len = 0;

    if (1 != EVP_EncryptUpdate(ctx, ciphertext.data(), &len,
                               reinterpret_cast<const unsigned char*>(plaintext.data()),
                               plaintext.size()))
        throw std::runtime_error("EncryptUpdate failed");

    ciphertext_len += len;

    if (1 != EVP_EncryptFinal_ex(ctx, ciphertext.data() + ciphertext_len, &len))
        throw std::runtime_error("EncryptFinal failed");

    ciphertext_len += len;
    ciphertext.resize(ciphertext_len);  // 截取实际密文长度

    EVP_CIPHER_CTX_free(ctx);

    // 合并 IV + 密文
    std::vector<unsigned char> result;
    result.insert(result.end(), iv.begin(), iv.end());
    result.insert(result.end(), ciphertext.begin(), ciphertext.end());

    return result;
}

std::string base64_decode(const std::string &encoded_string)
{
	// Base64字符集
	static const std::string base64_chars =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		"abcdefghijklmnopqrstuvwxyz"
		"0123456789+/";

	auto is_base64 = [](unsigned char c) -> bool
	{
		return (isalnum(c) || (c == '+') || (c == '/'));
	};

	// 清除空格等非法字符
	std::string clean_input;
	for (auto c : encoded_string)
	{
		if (is_base64(c) || c == '=')
		{
			clean_input += c;
		}
	}

	int in_len = clean_input.size();
	int i = 0;
	int j = 0;
	int in_ = 0;
	unsigned char char_array_4[4], char_array_3[3];
	std::string ret;

	while (in_len-- && (clean_input[in_] != '=') && is_base64(clean_input[in_]))
	{
		char_array_4[i++] = clean_input[in_];
		in_++;
		if (i == 4)
		{
			for (i = 0; i < 4; i++)
			{
				char_array_4[i] = base64_chars.find(char_array_4[i]);
			}

			char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
			char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
			char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

			for (i = 0; i < 3; i++)
			{
				ret += char_array_3[i];
			}
			i = 0;
		}
	}

	if (i)
	{
		for (j = i; j < 4; j++)
		{
			char_array_4[j] = 0;
		}

		for (j = 0; j < 4; j++)
		{
			char_array_4[j] = base64_chars.find(char_array_4[j]);
		}

		char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
		char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
		char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

		for (j = 0; j < i - 1; j++)
		{
			ret += char_array_3[j];
		}
	}

	return ret;
}

bool compare_graph(const cbtProto::SpinAck &g1, const cbtProto::SpinAck &g2, std::string &diff)
{
	bool equal = true;

	google::protobuf::util::MessageDifferencer differencer;
	differencer.set_float_comparison(google::protobuf::util::MessageDifferencer::APPROXIMATE);
	// differencer.set_treat_nan_as_equal();

	const google::protobuf::FieldDescriptor *field = cbtProto::SpinAck::descriptor()->FindFieldByName("NextRow");
	if (field != nullptr)
	{
		differencer.IgnoreField(field);
	}

	std::string d = "";
	differencer.ReportDifferencesToString(&diff);
	if (!differencer.Compare(g1, g2))
	{
		equal = false;
		return false;
	}

	diff = "equal";
	return equal;
}

void CGameComponent::send_data(IUser *pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase *pData)
{

	if (pUser)
		m_table->send(pUser, mcmd, scmd, pData);
	else
		m_table->send_user_batch(mcmd, scmd, pData);
}

void CGameComponent::send_bet_roll_err_result(IUser *user, serverProto::AckType type, serverProto::Error err,
											  const std::string &token, const std::string &msg, void *data, size_t size)
{
	serverProto::GaiaResponse response;
    response.set_type(type);
    response.set_ret(err);
    response.set_errormsg(msg);
    response.set_token(token);
    auto encrypted = encryptAESCBC("", token);
    response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());

    std::string content = response.SerializeAsString();

    svrlib::socket_id sid = user->get_socket_id();
    if (m_table)
    {
        m_table->send_http(sid, content);
    }
    else
    {
        MY_LOG_ERROR("user[%d] invalid table", user->get_user_id());
    }
}

void encodeACKProto(cbtProto::SpinAck &ack, user_game_info &game_info)
{  
	int len = game_info.graph_data.size();
	int count = len/(3*3);//共有多少组
	int bonus_count = 0;
	int bonus = 0;
	for (int i = 0; i < count; i++)
	{
		int icon[3][3] = {0};
		cbtProto::SinglePlate *singleplate = ack.add_ackqueue();
		if (singleplate)
		{
			for (int j = 0; j < 3; j++)
			{
				cbtProto::Column *plate_symbol = singleplate->add_platesymbol();
				for (int k = 0; k < 3; k++)
				{
					int curIndex = i * 3 * 3 + j * 3 + k;
					plate_symbol->add_col(game_info.graph_data[curIndex]);
					icon[j][k] = game_info.graph_data[curIndex];
				}
			}
		}

		if (i > 0)
		{
			if (i == count -1) {
				singleplate->set_awardtypeflag(12);
			} else {
				if (game_info.free_data[i].odds > 0) {
					singleplate->set_awardtypeflag(5);
				} else {
					singleplate->set_awardtypeflag(4);
				}
			}

			if (game_info.free_data[i].odds > 0) {
				singleplate->set_light(game_info.free_data[i].light);
				singleplate->set_linewin(game_info.free_data[i].round_result);
			} else {
				singleplate->set_poolwin(game_info.free_data[i].round_result);
			}
			singleplate->set_platewin(game_info.free_data[i].round_result);
			// singleplate->set_symbolwin(game_info.free_data[i].round_result);
			
			game_info.freeRemainRound -= 1;
			singleplate->set_freeremainround(game_info.freeRemainRound);
			singleplate->set_freetotalround(game_info.freeTotalRound);
			if (game_info.free_data[i].plus > 0) {
				singleplate->set_plus(game_info.free_data[i].plus);
				game_info.freeRemainRound += game_info.free_data[i].plus;
				singleplate->set_freeremainround(game_info.freeRemainRound);
				game_info.freeTotalRound = game_info.freeTotalRound + game_info.free_data[i].plus;
				singleplate->set_freetotalround(game_info.freeTotalRound);
				singleplate->set_awardtypeflag(20);
			}
			if (i == count -1) {
				singleplate->set_freeremainround(0);
			}
			singleplate->set_mult(game_info.free_data[i].mul);

			singleplate->add_pool(game_info.free_data[i].pool[0]);
			singleplate->add_pool(game_info.free_data[i].pool[1]);
			singleplate->add_pool(game_info.free_data[i].pool[2]);

			if (game_info.free_data[i].winPool[0]+game_info.free_data[i].winPool[1]+game_info.free_data[i].winPool[2] > 0)
			{
				if (game_info.free_data[i].superBonusCount > 0 && game_info.free_data[i].bonusCount > 0) {
					singleplate->set_awardtypeflag(64+32+4);
				}
				else if (game_info.free_data[i].superBonusCount > 0) {
					singleplate->set_awardtypeflag(68);
				} else {
					singleplate->set_awardtypeflag(36);
				}
			}

			for (int col = 0; col < 3; col++)
			{
				cbtProto::Detail poolTail;
				cbtProto::Detail showTail;
				if (game_info.free_data[i].vecBonus.size() > 0)
				{
					for (auto it = game_info.free_data[i].vecBonus.begin();
						 it != game_info.free_data[i].vecBonus.end(); it++)
					{
						if (it->isSuper)
						{
							if (it->col == col)
							{
								// 最多三个
								if (poolTail.detail_size() >= 3)
								{
									poolTail.set_detail(0, it->winPool[0]);
									poolTail.set_detail(1, it->winPool[1]);
									poolTail.set_detail(2, it->winPool[2]);
								}
								else
								{
									poolTail.add_detail(it->winPool[0]);
									poolTail.add_detail(it->winPool[1]);
									poolTail.add_detail(it->winPool[2]);
								}

								if (showTail.detail_size() >= 3)
								{
									showTail.set_detail(0, it->showBonus[0]);
									showTail.set_detail(1, it->showBonus[1]);
									showTail.set_detail(2, it->showBonus[2]);
								}
								else
								{
									showTail.add_detail(it->showBonus[0]);
									showTail.add_detail(it->showBonus[1]);
									showTail.add_detail(it->showBonus[2]);
								}
							}
							else
							{
								for (int i = 0; i < 3; i++)
								{
									if (poolTail.detail_size() < 3)
									{
										poolTail.add_detail(0);
									}
									if (showTail.detail_size() < 3)
									{
										showTail.add_detail(0);
									}
								}
							}
						}
						else
						{
							for (int i = 0; i < 3; i++)
							{
								if (poolTail.detail_size() < 3)
								{
									poolTail.add_detail(0);
								}
								if (showTail.detail_size() < 3)
								{
									showTail.add_detail(0);
								}
							}
							if (it->col == col)
							{
								poolTail.set_detail(col, it->winPool[col]);
								showTail.set_detail(col, it->showBonus[col]);
							}
						}
					}
				}
				else
				{
					for (int i = 0; i < 3; i++)
					{
						if (poolTail.detail_size() < 3)
						{
							poolTail.add_detail(0);
						}
						if (showTail.detail_size() < 3)
						{
							showTail.add_detail(0);
						}
					}
				}
				singleplate->add_pooldetail()->CopyFrom(poolTail);
				singleplate->add_showdetail()->CopyFrom(showTail);
			}


			// if (game_info.free_data[i].superBonusCount > 0)
			// {
				
			// 	for (int row = 0; row < 3; row++)
			// 	{
			// 		cbtProto::Detail tail;
			// 		if (game_info.free_data[i].superBonusCol[row] > 0)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].winPool[0]);
			// 			tail.add_detail(game_info.free_data[i].winPool[1]);
			// 			tail.add_detail(game_info.free_data[i].winPool[2]);
			// 		}
			// 		else 
			// 		{
			// 			tail.add_detail(0);
			// 			tail.add_detail(0);
			// 			tail.add_detail(0);
			// 		}
			// 		if (game_info.free_data[i].bonusCount > 0)
			// 		{
			// 			if (game_info.free_data[i].curColNum[row] > 0)
			// 			{
			// 				tail.set_detail(row, game_info.free_data[i].winPool[row]);
			// 			}
			// 		}

			// 		singleplate->add_pooldetail()->CopyFrom(tail);
			// 	}			
			// }
			// else
			// {
				
			// 	for (int row = 0; row < 3; row++)
			// 	{
			// 		cbtProto::Detail tail;
			// 		if (row == 0)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].winPool[0]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 		}
			// 		if (row == 1)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].winPool[1]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 		}
			// 		if (row == 2)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].winPool[2]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 		}

			// 		singleplate->add_pooldetail()->CopyFrom(tail);
			// 	}
			// }

			// if (game_info.free_data[i].superBonusCount > 0)
			// {
			// 	for (int row = 0; row < 3; row++)
			// 	{
			// 		cbtProto::Detail tail;
			// 		if (game_info.free_data[i].superBonusCol[row] > 0)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].showBonus[0]);
			// 			tail.add_detail(game_info.free_data[i].showBonus[1]);
			// 			tail.add_detail(game_info.free_data[i].showBonus[2]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 			tail.add_detail(0);
			// 			tail.add_detail(0);
			// 		}
			// 		if (game_info.free_data[i].bonusCount > 0)
			// 		{
			// 			if (game_info.free_data[i].curColNum[row] > 0)
			// 			{
			// 				tail.set_detail(row, game_info.free_data[i].showBonus[row]);
			// 			}
			// 		}
	
			// 		singleplate->add_showdetail()->CopyFrom(tail);
			// 	}
			// }
			// else	
			// {
			// 	for (int row = 0; row < 3; row++)
			// 	{
			// 		cbtProto::Detail tail;
			// 		if (row == 0)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].showBonus[0]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 		}
			// 		if (row == 1)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].showBonus[1]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 		}
			// 		if (row == 2)
			// 		{
			// 			tail.add_detail(game_info.free_data[i].showBonus[2]);
			// 		}
			// 		else
			// 		{
			// 			tail.add_detail(0);
			// 		}

			// 		singleplate->add_showdetail()->CopyFrom(tail);
			// 	}
			// }

		}
		else
		{
			if (game_info.round_result > 0)
			{
				singleplate->set_awardtypeflag(1);
			}
			else
			{
				singleplate->set_awardtypeflag(0);
			}
			singleplate->set_platewin(game_info.round_result);
			singleplate->set_symbolwin(game_info.round_result);
			singleplate->set_light(game_info.light);
			
			singleplate->set_freetotalround(game_info.freeTotalRound);
			singleplate->set_mult(game_info.mul);

			for (int len = 0; len < 3; len++)
			{
				cbtProto::Detail tail;
				tail.add_detail(0);
				tail.add_detail(0);
				tail.add_detail(0);
				singleplate->add_pooldetail()->CopyFrom(tail);
			}

			if (game_info.free_count > 0)
			{
				singleplate->set_freeremainround(game_info.freeRemainRound);
				singleplate->set_awardtypeflag(2);
				singleplate->set_plus(game_info.free_data[0].plus);
				singleplate->add_pool(game_info.free_data[0].pool[0]);
				singleplate->add_pool(game_info.free_data[0].pool[1]);
				singleplate->add_pool(game_info.free_data[0].pool[2]);

				for (int row = 0; row < 3; row++)
				{
					cbtProto::Detail tail;
					if (row == 0)
					{
						tail.add_detail(game_info.free_data[0].showBonus[0]);
					}
					else
					{
						tail.add_detail(0);
					}
					if (row == 1)
					{
						tail.add_detail(game_info.free_data[0].showBonus[1]);
					}
					else
					{
						tail.add_detail(0);
					}
					if (row == 2)
					{
						tail.add_detail(game_info.free_data[0].showBonus[2]);
					}
					else
					{
						tail.add_detail(0);
					}
					singleplate->add_showdetail()->CopyFrom(tail);
				}
			}
			else 
			{
				singleplate->add_pool(0);
				singleplate->add_pool(0);
				singleplate->add_pool(0);

				for (int len = 0; len < 3; len++)
				{
					cbtProto::Detail tail;
					tail.add_detail(0);
					tail.add_detail(0);
					tail.add_detail(0);
					singleplate->add_showdetail()->CopyFrom(tail);
				}
			}
		}

		std::string json_string;
		google::protobuf::util::JsonPrintOptions options;
		options.add_whitespace = false;
		google::protobuf::util::MessageToJsonString(*singleplate, &json_string, options);
		MY_LOG_PRINT("第%d个图形 = %s", i, json_string.c_str());
	}

	ack.set_bonustotalwin(game_info.bonus);
	ack.set_totalwin((double)game_info.result/100.0);

	MY_LOG_PRINT("totalWin = %f bonus = %f ", ack.totalwin(), ack.bonustotalwin());
}

void CGameComponent::send_bet_roll_result(IUser *user, std::string msg, bool is_suc)
{
	MY_LOG_DEBUG("send_bet_roll_result");	
	serverProto::SpinResponse spin;

	serverProto::ServiceData *service = spin.mutable_service();
	serverProto::freeSpinList *freeSpinList= service->mutable_freeremainv2();
	auto uid = user->get_user_id();
	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if (iter == m_map_user_game_info.end())
	{
		MY_LOG_ERROR("user[%d] game info is empty", user->get_user_id());
		return;
	}

	auto gold = user->get_gold();
	spin.set_roundindexv2(iter->second.round_index_v2);
	spin.set_postmoney(std::round(gold) / 100.0);
	spin.set_totalwin(std::round(iter->second.result)/ 100.0);
	spin.set_hasspin(true);
	spin.set_basebet(std::round(iter->second.bet) / 100.0);
	spin.set_realbet(std::round(iter->second.bet) / 100.0);

	serverProto::SpinReq *req = spin.mutable_spinreq();
	req->set_bet(std::round(iter->second.bet) / 100.0);
	req->mutable_special();
	cbtProto::SpinAck ack;
	encodeACKProto(ack, iter->second);
	ack.set_nowmoney(gold);
	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = false;
	// google::protobuf::util::MessageToJsonString(ack, &json_string, options);
	// MY_LOG_PRINT("ack = %s", json_string.c_str());

	cbtProto::SpinAck demo;

	if(GO_TO_REPLAY)
	{
		static int i = 0;
		// i = 590;
		MY_LOG_PRINT("第%d个图形", i + 1);
		string dd;
		myredis::GetInstance()->get_graph_from_redis_by_index("cbt_reply", dd, i, 3);
		i++;
		if (i > 1000)
		{
			i = 0;
		}
		std::string decoded = base64_decode(dd);
		demo.ParseFromString(decoded);
		
		// //TODO 测试
		// demo.mutable_ackqueue(0)->mutable_platesymbol(0)->set_col(0, 5);
		// demo.mutable_ackqueue(0)->mutable_platesymbol(0)->set_col(1, 6);
		// demo.mutable_ackqueue(0)->mutable_platesymbol(0)->set_col(2, 7);

		std::string debugstr;
		using namespace google::protobuf;
		TextFormat::PrintToString(demo, &debugstr);
		MY_LOG_DEBUG("send_bet_roll_result demo: %s", debugstr.c_str());
	}

	std::string *d = spin.mutable_data();
	if(GO_TO_REPLAY)
		demo.SerializeToString(d);
	else
		ack.SerializeToString(d);
	//
	json_string.clear();
	// google::protobuf::util::MessageToJsonString(spin, &json_string, options);
	// MY_LOG_PRINT("spin = %s", json_string.c_str());

	string data;
	spin.SerializeToString(&data);

	serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::spin);
	if(iter->second.token.length() >= AES_KEY_SIZE)
	{
		std::vector<unsigned char> encrypted = encryptAESCBC(data, iter->second.token);
		// string str = base64Encode(encrypted);
		// MY_LOG_PRINT("str = %s", str.c_str());

		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
		json_string.clear();

		response.set_token(iter->second.token);
		//google::protobuf::util::MessageToJsonString(response, &json_string, options);
		//MY_LOG_PRINT("response = %s", json_string.c_str());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content = "";
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
	MY_LOG_DEBUG("user[%d] send spin response ok with content length: %d total win: %.3f post_money: %.3f",
				 uid, content.length(), spin.totalwin(), spin.postmoney());
}

void CGameComponent::test_bet_roll_result(int i)
{
	user_game_info info;
	info.cur_mode = 0;
	info.bet = 1000;
	int icons[10] = {0,11,0,1,0,2,0,1,0};
	for (int i = 0; i < 10; i++)
	{
		info.graph_data.push_back(icons[i]);
	}
	game_logic::GetInstance()->CalcResultTimes(game_info);

	cbtProto::SpinAck ack;
	encodeACKProto(ack, game_info);

	std::string json_string;
	google::protobuf::util::JsonPrintOptions options;
	options.add_whitespace = false;

	google::protobuf::util::MessageToJsonString(ack, &json_string, options);

	std::remove("test.txt");
	std::ofstream ofs;
	ofs.open("test.txt", ios::out | ios::app);
	ofs << json_string;

	string dd;
	myredis::GetInstance()->get_graph_from_redis_by_index("cbt_reply", dd, i, 3);
	std::string decoded = base64_decode(dd);
	cbtProto::SpinAck demo;
	demo.ParseFromString(decoded);

	std::string debugstr;
	using namespace google::protobuf;
	TextFormat::PrintToString(demo, &debugstr);
	MY_LOG_DEBUG("test demo: %s", debugstr.c_str());

	string diff;
	if (!compare_graph(ack, demo, diff))
	{
		MY_LOG_PRINT("graph%d diff = %s", i + 1, diff.c_str());
	}
	else
	{
		MY_LOG_PRINT("graph%d equal", i + 1);
	}

	ofs.close();
}

void CGameComponent::send_room_info(IUser *pUser)
{
}

void CGameComponent::send_my_user_info(IUser *pUser)
{
}

void CGameComponent::send_game_info_result(IUser *user, int client_id)
{
	uint32_t uid = user->get_user_id();

    serverProto::GameInfoAck ack;
	serverProto::WalletInfo *wallet = ack.add_walletinfo();
	int64_t gold = user->get_gold();
	wallet->set_coin(std::round(gold) / 100.0);
    int game_id = GET_INT_CONFIG("game_id");
	int nodeid = m_table->get_node_id();
	int base_bet = CBT::winlose_control::GetInstance()->get_base_bet(client_id, game_id, nodeid);
	if (base_bet == 0)
	{
		base_bet = 1;
	}
    for (int32_t i = 0; i < MAX_BET_CONFIG_COUNT; i++)
	{
		wallet->add_bet(bet_config_count[i] * (double)base_bet);
	}
	wallet->set_currencynumber(33);
	//wallet->set_currencyname("PKR");
	//wallet->set_currencysymbol("Rs");
	wallet->set_unit(1);
	wallet->set_ratio(1);
	wallet->set_rate(0.64);
	wallet->set_decimal(4);

	ack.set_maxodd(2500);
	ack.set_freespintype(-1);
	
	cbtProto::GameInfoAck gameinfo;
	for (const auto &value : odd_info)
	{   
		gameinfo.add_oddlist(value);
	}
	std::string *pExtrainInfo = ack.mutable_extrainfo();
	gameinfo.SerializeToString(pExtrainInfo);

	serverProto::freeSpinList *freespin = ack.mutable_freespin();

    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::info);	
	string data;
	ack.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}


void CGameComponent::send_notice_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::NoticeInfo info;
	
	int64_t gold = user->get_gold();
	info.set_type(0);
	info.set_value(0);
	info.set_currencynumber(33);

    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::notice);	
	string data;
	info.SerializeToString(&data);

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}

void CGameComponent::send_setting_result(IUser *user)
{
	uint32_t uid = user->get_user_id();

    serverProto::JiliJpSetting setting;
	
    serverProto::GaiaResponse response;
	response.set_type(serverProto::AckType::jilijpSetting);	
	string data;
	setting.SerializeToString(&data);
	MY_LOG_DEBUG("data = %s", data.c_str());

	iter_user_game_info iter = m_map_user_game_info.find(uid);
	if(iter != m_map_user_game_info.end() && iter->second.token.length() >= AES_KEY_SIZE)
	{
		auto encrypted = encryptAESCBC(data, iter->second.token);
		response.set_data(reinterpret_cast<const char *>(encrypted.data()), encrypted.size());
	}
	else
	{
		MY_LOG_ERROR("Encryption error");
		response.set_data("");
	}

	svrlib::socket_id sid = user->get_socket_id();
	std::string content;
	response.SerializeToString(&content);
	m_table->send_http(sid, content);
}
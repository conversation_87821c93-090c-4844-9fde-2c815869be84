#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <cstdlib>
#include <ctime>
#include <sstream>
#include <algorithm>

// 简化的日志宏
#define MY_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_PRINT(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define MY_LOG_ERROR(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)

// 包含游戏逻辑头文件
#include "game_logic.h"

// 简化的 JSON 解析器（仅用于基本字段提取）
class SimpleJsonParser {
public:
    static double extractDouble(const std::string& json, const std::string& field) {
        std::string search = "\"" + field + "\":";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return 0.0;
        
        pos += search.length();
        size_t end = json.find_first_of(",}", pos);
        if (end == std::string::npos) return 0.0;
        
        std::string value = json.substr(pos, end - pos);
        // 移除空格和引号
        value.erase(std::remove_if(value.begin(), value.end(), ::isspace), value.end());
        value.erase(std::remove(value.begin(), value.end(), '"'), value.end());
        
        return std::stod(value);
    }
    
    static int extractInt(const std::string& json, const std::string& field) {
        return static_cast<int>(extractDouble(json, field));
    }
    
    static std::vector<int> extractIntArray(const std::string& json, const std::string& field) {
        std::vector<int> result;
        std::string search = "\"" + field + "\":[";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return result;
        
        pos += search.length();
        size_t end = json.find("]", pos);
        if (end == std::string::npos) return result;
        
        std::string arrayStr = json.substr(pos, end - pos);
        std::stringstream ss(arrayStr);
        std::string item;
        
        while (std::getline(ss, item, ',')) {
            item.erase(std::remove_if(item.begin(), item.end(), ::isspace), item.end());
            if (!item.empty()) {
                result.push_back(std::stoi(item));
            }
        }
        
        return result;
    }
};

// 简化的游戏结果结构
struct GameResult {
    double totalwin;
    int award;
    std::vector<std::vector<int>> plate_symbols;
    
    GameResult() : totalwin(0.0), award(0) {}
};

// 从游戏信息生成结果
GameResult generateCurrentResult(const std::vector<int>& icons) {
    GameResult result;
    
    try {
        user_game_info game_info;
        game_info.cur_mode = 0;
        game_info.bet = 1000;
        
        // 设置测试图标
        for (int icon : icons) {
            game_info.graph_data.push_back(icon);
        }
        
        // 计算结果
        game_logic::GetInstance()->CalcResultTimes(game_info);
        
        result.totalwin = static_cast<double>(game_info.result) / 100.0;
        result.award = game_info.award_type;
        
        // 提取图标矩阵
        for (int i = 0; i < 3; i++) {
            std::vector<int> row;
            for (int j = 0; j < 3; j++) {
                row.push_back(game_info.Icons[i][j]);
            }
            result.plate_symbols.push_back(row);
        }
        
    } catch (const std::exception& e) {
        std::cout << "生成当前结果时发生异常: " << e.what() << std::endl;
    }
    
    return result;
}

// 从 JSON 文件解析参考结果
GameResult parseReferenceResult(const std::string& filename) {
    GameResult result;
    
    try {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cout << "无法打开文件: " << filename << std::endl;
            return result;
        }
        
        std::string json_content;
        std::string line;
        while (std::getline(file, line)) {
            json_content += line;
        }
        file.close();
        
        // 解析基本字段
        result.totalwin = SimpleJsonParser::extractDouble(json_content, "totalwin");
        result.award = SimpleJsonParser::extractInt(json_content, "award");
        
        std::cout << "从 " << filename << " 解析到:" << std::endl;
        std::cout << "  totalwin: " << result.totalwin << std::endl;
        std::cout << "  award: " << result.award << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "解析参考结果时发生异常: " << e.what() << std::endl;
    }
    
    return result;
}

// 比较两个结果
void compareResults(const GameResult& current, const GameResult& reference) {
    std::cout << "\n=== 详细比较结果 ===" << std::endl;
    
    bool has_differences = false;
    
    // 比较 totalwin
    if (std::abs(current.totalwin - reference.totalwin) > 0.001) {
        std::cout << "❌ totalwin 不同:" << std::endl;
        std::cout << "   当前: " << current.totalwin << std::endl;
        std::cout << "   参考: " << reference.totalwin << std::endl;
        has_differences = true;
    } else {
        std::cout << "✅ totalwin 相同: " << current.totalwin << std::endl;
    }
    
    // 比较 award
    if (current.award != reference.award) {
        std::cout << "❌ award 不同:" << std::endl;
        std::cout << "   当前: " << current.award << std::endl;
        std::cout << "   参考: " << reference.award << std::endl;
        has_differences = true;
    } else {
        std::cout << "✅ award 相同: " << current.award << std::endl;
    }
    
    // 显示图标矩阵
    std::cout << "\n当前图标矩阵:" << std::endl;
    for (const auto& row : current.plate_symbols) {
        std::cout << "  ";
        for (int icon : row) {
            std::cout << icon << " ";
        }
        std::cout << std::endl;
    }
    
    if (!has_differences) {
        std::cout << "\n🎉 所有比较项目都相同！" << std::endl;
    } else {
        std::cout << "\n⚠️  发现差异，请检查计算逻辑" << std::endl;
    }
}

// 主要的比较测试函数
void runCompareTest() {
    std::cout << "=== 简化版 Protobuf 比较测试 ===" << std::endl;
    
    // 测试数据：包含多种图标的复杂组合
    std::vector<int> icons = {5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7};
    
    std::cout << "测试数据长度: " << icons.size() << " 个图标" << std::endl;
    std::cout << "前10个图标: ";
    for (int i = 0; i < std::min(10, static_cast<int>(icons.size())); i++) {
        std::cout << icons[i] << " ";
    }
    std::cout << "..." << std::endl;
    
    // 生成当前结果
    std::cout << "\n--- 步骤1: 生成当前计算结果 ---" << std::endl;
    GameResult current = generateCurrentResult(icons);
    
    // 保存当前结果到文件
    std::ofstream current_file("current_simple_result.txt");
    if (current_file.is_open()) {
        current_file << "{\n";
        current_file << "  \"totalwin\": " << current.totalwin << ",\n";
        current_file << "  \"award\": " << current.award << ",\n";
        current_file << "  \"plate_symbols\": [\n";
        for (size_t i = 0; i < current.plate_symbols.size(); i++) {
            current_file << "    [";
            for (size_t j = 0; j < current.plate_symbols[i].size(); j++) {
                current_file << current.plate_symbols[i][j];
                if (j < current.plate_symbols[i].size() - 1) current_file << ", ";
            }
            current_file << "]";
            if (i < current.plate_symbols.size() - 1) current_file << ",";
            current_file << "\n";
        }
        current_file << "  ]\n";
        current_file << "}\n";
        current_file.close();
        std::cout << "当前结果已保存到 current_simple_result.txt" << std::endl;
    }
    
    // 读取参考结果
    std::cout << "\n--- 步骤2: 读取参考结果 ---" << std::endl;
    GameResult reference = parseReferenceResult("test.txt");
    
    // 比较结果
    std::cout << "\n--- 步骤3: 比较结果 ---" << std::endl;
    compareResults(current, reference);
}

// 主函数
int main() {
    std::cout << "GoldenBank 简化比较测试" << std::endl;
    std::cout << "========================" << std::endl;
    
    // 初始化随机种子
    srand(time(nullptr));
    
    try {
        runCompareTest();
    } catch (const std::exception& e) {
        std::cout << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n按回车键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}

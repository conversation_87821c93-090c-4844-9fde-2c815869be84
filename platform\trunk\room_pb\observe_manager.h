﻿#ifndef _OBSERVE_MANAGER_H_
#define _OBSERVE_MANAGER_H_

#include "room_common.h"

using namespace std;

class ObserveManager
{
public:
    static ObserveManager *GetInstance()
    {
        static ObserveManager stMgr;
        return &stMgr;
    }
    
protected:
    ObserveManager();

    virtual ~ObserveManager();

public:
	
	void init(int max_number){max_observe_num=max_number;}
    
	bool get_observe_limit() { return m_observe_list.size()>=max_observe_num;}

	int get_max_observe_num()	{ return max_observe_num; }

	int get_observe_count()	{ return m_observe_list.size(); }

	void add_user(IUser *user);

    bool delete_user(_tint64 userID);

	void reset_user()	{ m_observe_list.clear(); }

	bool is_observe_user(_tint64 userID);

	std::vector<IUser*> * get_observe_list()	{return &m_observe_list;}

private:
	int						max_observe_num;	//最大的旁观人数
    std::vector<IUser*>		m_observe_list;		//旁观玩家列表
};

#endif

# GoldenBank 测试程序运行指南

## 问题解决方案

由于原始的 protobuf 版本可能存在依赖问题，我创建了多个版本的测试程序，按推荐顺序尝试：

## 🚀 推荐运行顺序

### 1. 独立测试程序（最推荐）
```bash
run_standalone.bat
```
- **特点**: 完全独立，不依赖任何外部库
- **功能**: 模拟游戏逻辑，生成比较结果
- **适用**: 所有环境，100% 能运行

### 2. 简化测试程序
```bash
run_simple.bat
```
- **特点**: 尝试使用真实游戏逻辑，有备用方案
- **功能**: 如果能找到游戏逻辑文件就使用，否则使用模拟
- **适用**: 大部分环境

### 3. 完整 Protobuf 测试程序
```bash
run_compare_test.bat
```
- **特点**: 真正的 protobuf 对象比较
- **功能**: 完整的 protobuf 序列化/反序列化比较
- **要求**: 需要安装 protobuf 库

### 4. 环境诊断
```bash
diagnose.bat
```
- **功能**: 检查编译环境和依赖
- **用途**: 诊断编译失败的原因

### 5. 自动修复
```bash
fix_and_run.bat
```
- **功能**: 自动尝试各种解决方案
- **用途**: 一键解决大部分问题

## 📁 文件说明

### 测试程序
- `standalone_test.cpp` - 独立测试程序（推荐）
- `simple_compare.cpp` - 简化测试程序
- `test_bet_roll_unit.cpp` - 完整 protobuf 测试程序

### 运行脚本
- `run_standalone.bat` - 运行独立测试（推荐）
- `run_simple.bat` - 运行简化测试
- `run_compare_test.bat` - 运行完整测试
- `diagnose.bat` - 环境诊断
- `fix_and_run.bat` - 自动修复

### 示例文件
- `sample_test.txt` - 示例参考数据
- `运行指南.md` - 本文件

## 🎯 测试数据

所有程序都使用相同的测试图标数组：
```cpp
{5,0,4,0,5,0,5,0,1,5,0,3,2,0,2,1,0,1,1,0,1,0,5,0,0,2,0,7,0,1,1,0,1,0,1,0,3,0,11,0,11,0,1,0,1,1,0,11,0,2,0,2,0,2,11,0,3,4,0,7,1,0,7,3,0,1,1,0,1,0,1,0,2,0,2,0,1,0,0,2,0,11,0,3,0,3,0,3,0,6,2,0,1,5,0,4,1,0,5,0,1,0,0,2,0,0,1,0,0,2,0,0,11,0,2,0,3,0,2,0,1,0,1,0,3,0,2,0,2,0,2,0,2,0,3,1,0,3,0,2,0,1,0,7}
```

## 📊 输出文件

### 独立测试
- `standalone_result.txt` - 独立测试结果

### 简化测试
- `current_simple_result.txt` - 简化测试结果

### 完整测试
- `current_protobuf.json` - 当前 protobuf JSON
- `test.txt` - 参考数据

## 🔧 故障排除

### 编译失败
1. 确保安装了 C++ 编译器（MinGW/GCC）
2. 运行 `diagnose.bat` 检查环境
3. 尝试 `run_standalone.bat`（最简单）

### 运行失败
1. 检查是否有文件权限问题
2. 确保当前目录可写
3. 尝试以管理员身份运行

### Protobuf 问题
1. 安装 protobuf 库：`vcpkg install protobuf`
2. 或使用简化版本：`run_simple.bat`
3. 或使用独立版本：`run_standalone.bat`

## 💡 建议

1. **首次使用**: 直接运行 `run_standalone.bat`
2. **调试问题**: 运行 `diagnose.bat`
3. **需要真实逻辑**: 运行 `run_simple.bat`
4. **需要 protobuf**: 安装库后运行 `run_compare_test.bat`

## 🎉 成功标志

程序成功运行后会：
1. 显示计算结果（totalwin, award, bet）
2. 显示 3x3 图标矩阵
3. 生成结果文件
4. 显示比较说明

如果看到这些输出，说明程序运行成功！

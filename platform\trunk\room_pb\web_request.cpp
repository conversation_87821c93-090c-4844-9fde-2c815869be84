#include "web_request.h"
#include "encryption_interface.h"
#include "config_manager.h"
#include "game_frame.h"
#include "user_token.h"
#include "table_manager.h"
#include "user_manager.h"
#include "room_bill_fun.h"
#include "client_cmd.h"
#include "game_props.h"

web_request::web_request(void) : m_robot_nodeid(0)
{
}

web_request::~web_request(void)
{
}

// ��ʼ��
bool web_request::init(ICommon *pcom, ILog *plog, IAttemper *pattemper,
					   string web_client_version_api, string web_room_config_api, string h5_api)
{
	m_com = pcom;
	m_log = plog;
	m_h5_api = h5_api;
	m_pattemper = pattemper;
	m_web_room_config_api = web_room_config_api;
	if (!pcom || !plog)
		return false;

	if (!m_bfpool.GetInterface())
	{
		m_bfpool.DLLInit(SVRLIB_NAME, "_block_poolex_instance", "_block_poolex_free");
		if (!m_bfpool.GetInterface())
		{
			if (m_log)
				m_log->write_log(LOG_TYPE_ERROR, "[DBP] instance dbproxy buffer pool fail()");

			return false;
		}
	}

	CHttpCurlParamHelper helper(this, pattemper,
								m_bfpool.GetInterface(), pcom, plog);

	return CHttpCurl::GetInstance()->Init(&helper);
}

// ���ر�������
bool web_request::load_local_config()
{
	TiXmlDocument xmlcfg;
	TiXmlElement *pRoot = 0;
	TiXmlElement *pItem = 0;
	char xmlfile[1024];
	if (!xmlcfg.LoadFile(_xmlfile(xmlfile, sizeof(xmlfile), CONFIG)))
		__return(false, LOG_TYPE_ERROR, "load_local_config��config.xml������ʧ�ܣ������ļ���ʽ�Ƿ���ȷ��");
	if (!(pRoot = xmlcfg.FirstChildElement("config")))
		__return(false, LOG_TYPE_ERROR, "load_local_config��config.xml���ļ���ʽ����!(�޷��ҵ�<config>)");
	m_robot_nodeid = 0;
	char web_client_version_api[256] = {0};
	char web_room_config_api[256] = {0};
	char h5_api[256] = {0};
	if ((pItem = pRoot->FirstChildElement("web")) != 0)
	{
		mysprintf(web_client_version_api, sizeof(web_client_version_api), "%s", pItem->Attribute("client_version_api"));
		mysprintf(web_room_config_api, sizeof(web_room_config_api), "%s", pItem->Attribute("room_config_api"));
		const char *pnodeid = pItem->Attribute("robot_nodeid");
		if (pnodeid)
			m_robot_nodeid = atoi(pnodeid);
	}
	if ((pItem = pRoot->FirstChildElement("h5web")) != 0)
	{
		mysprintf(h5_api, sizeof(h5_api), "%s", pItem->Attribute("web_api"));
	}

	m_h5_api = h5_api;
	m_web_room_config_api = web_room_config_api;
	return true;
}

FTYPE(void)
web_request::on_req_post_time(void *obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{
	web_request::GetInstance()->load_local_config();
	web_request::GetInstance()->pop_re_request();
}

// �����ش�����
void web_request::add_re_request(stBindData data)
{
	stBindData *info = m_re_bind_pool.pop();
	if (info == NULL)
		return;

	memcpy(info, &data, sizeof(data));
	m_re_bind_list.add(info->sign, info);
}

// �����ش�����
void web_request::pop_re_request()
{
}

// ��������
void web_request::load_data()
{
	load_local_config();
	get_game_config(0);
	send_get_gold_room_config_req(CGameFrame::GetInstance()->get_room_id(), 0);
	send_get_game_props_config_req();
}

void web_request::produce_hashids_s(string &sign, int _t, stBindData *info, int action_type)
{
	char t_data[256] = {0};
	sprintf(t_data, "%d", _t);
	hashids_encode(t_data, strlen(t_data), sign);

	char sign_data[64] = {0};
	sprintf(sign_data, "_s=%s", sign.c_str());

	memset(info, 0, sizeof(info));
	info->action_type = action_type;
	memcpy(info->sign, sign.c_str(), sign.length());
	m_bind_list.add(info->sign, info);
}

// ���������ҳ�������������
void web_request::send_get_gold_room_config_req(int roomid, int page)
{
	if (m_web_room_config_api.length() == 0)
		return;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enAction_GoldRoomConfig);

	char parm[256] = {0};
	sprintf(parm, GOLD_ROOM_CONFIG_PARM, page, roomid, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	m_log->write_log(LOG_TYPE_DEBUG, "[DBP] send_get_gold_room_config_req:%s", req_url);
}

// �����������ϵͳ������Ϣ
void web_request::send_get_game_props_config_req()
{
	if (m_web_room_config_api.length() == 0)
		return;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enAction_GamePropsConfig);

	char parm[256] = {0};
	sprintf(parm, GAME_PROPS_CONFIG_PARM, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	m_log->write_log(LOG_TYPE_DEBUG, "[DBP] send_get_game_props_config_req:%s", req_url);
}

bool web_request::get_game_config(int page)
{
	if (m_web_room_config_api.length() == 0)
		return false;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
	{
		m_log->write_log(LOG_TYPE_ERROR, "[DBP] get_game_config:info is NULL");
		return false;
	}

	int roomtype = config_manager::GetInstance()->get_room_type();
	int gameid = config_manager::GetInstance()->get_game_id();
	if (TYPE_HUNDRED == roomtype && config_manager::GetInstance()->is_match_room())
		roomtype = TYPE_FMATCH;

	//"conf/getConfigGameList?game_type=%d&gameid=%d&page=%d&_t=%d&_s=%s"
	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enGame_cfg);

	char parm[256] = {0};
	sprintf(parm, GAME_PARM, roomtype, gameid, page, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	m_log->write_log(LOG_TYPE_DEBUG, "[DBP] get_game_config:%s", req_url);
	return true;
}

// room status
void web_request::get_room_status_req(int roomid, int gameid)
{
	if (m_web_room_config_api.length() == 0)
		return;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enRoom_RoomStatus);

	char parm[256] = {0};
	sprintf(parm, ROOM_STATUS_PARM, roomid, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	g_log->write_log(LOG_TYPE_DEBUG, "web_request::get_room_status_req:%s ", req_url);
}

// ��ͨͶ���ӿ�
bool web_request::post_transfer_inout_normal(string json, transfer_inout inout)
{
	if (m_web_room_config_api.length() == 0)
		return false;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return false;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enTransferInout);

	char parm[256] = {0};
	sprintf(parm, TRANSFER_INOUT, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);

	info->result = inout.result;
	info->transfer_data = json;
	info->sid = inout.sid;
	info->write_db = inout.write_db;
	info->call_back = inout.call_back;
	info->step_transfer = inout.step_transfer;
	info->time = inout.time;
	info->md5key = inout.md5Key;
	info->uid = inout.uid;
	memcpy(&info->p_data, &inout.p_data, sizeof(penetrte_data));

	// const char *head_str = "Content-Type: application/json";
	return CHttpCurl::GetInstance()->request(info, req_url, json.c_str());
}

// Ͷ���ӿ�
bool web_request::post_transfer_inout_hundred(string json, hundred_transfer_inout_one inout)
{
	if (m_web_room_config_api.length() == 0)
		return false;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return false;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enTransferInout);

	char parm[256] = {0};
	sprintf(parm, TRANSFER_INOUT, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);

	info->result = inout.result;
	info->transfer_data = json;
	info->sid = inout.sid;
	info->call_back = inout.call_back;
	info->write_db = inout.write_db;
	info->time = inout.time;
	info->md5key = inout.md5Key;
	info->uid = inout.uid;
	info->traceid = inout.traceid;
	memcpy(&info->p_data, &inout.p_data, sizeof(penetrte_data));
	//MY_LOG_PRINT("inout.md5Key = %s",inout.md5Key.c_str());
	// const char *head_str = "Content-Type: application/json";
	return CHttpCurl::GetInstance()->request(info, req_url, json.c_str());
}

void web_request::post_transfer_inout_batch(string json)
{
	if (m_web_room_config_api.length() == 0)
		return;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enTransferInoutBatch);

	char parm[256] = {0};
	sprintf(parm, TRANSFER_INOUT_BATCH, node_time, sign.c_str());

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);

	info->transfer_data = json;

	// const char *head_str = "Content-Type: application/json";
	CHttpCurl::GetInstance()->request(info, req_url, json.c_str());
	g_log->write_log(LOG_TYPE_DEBUG, "web_request::post_transfer_inout_batch:%s ", req_url);
}

// 请求刷新三方用户金币
bool web_request::get_flush_user_gold(int uid, int gameid, int tableid)
{
	if (m_web_room_config_api.length() == 0)
		return true;

	stBindData *info = m_bind_pool.pop();
	if (info == NULL)
		return false;

	memset(info, 0, sizeof(info));
	info->uid = uid;
	info->game_id = gameid;
	info->sid = tableid;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enUser_cash);

	char parm[256] = {0};
	sprintf(parm, GET_USER_CASH, uid, gameid, node_time, sign.c_str());

	// string srt_post = "post method";
	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	g_log->write_log(LOG_TYPE_DEBUG, " web_requst... %s", req_url);
	return true;
}

void web_request::on_atp_http_fail(_uint32 sockid, char *purl, void *pbinddata, _uint16 nerrcode, char *perror)
{
	// ������Դ
	stBindData *action = (stBindData *)pbinddata;
	if (action)
	{
		if (perror)
		{
			m_log->write_log(LOG_TYPE_ERROR, "[DBP] instance on_http_fail %s  nerrcode:%d  type:%d", perror, nerrcode, action->action_type);
		}

		// ʧ���ش����ݣ���ʱֻ������ˢ��
		switch (action->action_type)
		{
		case enTransferInout:
			on_transfer_inout_faile(*action);
			break;
		case enTransferInoutBatch:
			break;
		default:
			break;
		}

		stBindData *info = NULL;
		if (m_bind_list.get_key_data(action->sign, info))
		{
			if (info != NULL)
				m_bind_pool.push(info);
		}
		m_bind_list.remove_key(action->sign);
	}
}

void web_request::on_atp_http_recv_data(_uint32 sockid, void *pdata, _uint16 size, void *pbinddata, char *purl)
{
	m_log->write_log(LOG_TYPE_DEBUG, "[DBP] instance on_http_recv_data url:%s body: %s", purl, pdata);
	if (pbinddata == NULL) {
		return;
	}
	stBindData *action = (stBindData *)pbinddata;
	switch (action->action_type)
	{
	case enAction_GoldRoomConfig:
		on_gold_room_config_resp(pdata, size);
		break;
	case enGame_cfg:
		on_game_config((const char *)pdata, size);
		break;
	case enRoom_RoomStatus:
		on_get_room_status(pdata, size);
		break;
	case enTransferInout: 
		on_transfer_inout_suc((const char *)pdata, size, *action);
		break;
	case enTransferInoutBatch:
		on_transfer_inout_batch((const char *)pdata, size, action->transfer_data);
		break;
	case enAction_GamePropsConfig:
		on_game_props_config_resp(pdata, size);
		break;
	case enUser_cash: // 刷新金币
	{
		int tableid = action->sid;
		int uid = action->uid;

		Json::Reader json_reader;
		Json::Value jsonValue;
		if (!json_reader.parse((char *)pdata, jsonValue))
		{
			MY_LOG_WARN("%s,解析json数据包出错", __FUNCTION__);
			break;
		}

		int status = jsonValue["status"].asInt();
		if (status != 0)
		{
			MY_LOG_ERROR("update_user_gold status error");
			break;
		}

		_tint64 gold = jsonValue["data"]["balance_amount"].asInt64();
		myredis::GetInstance()->set_data_by_uid(uid, DB_COMM, CUR_GOLD, gold);
		IUser *user = UserManager::GetInstance()->get_user(uid);
		if (user) {
		    // CClientCmd::send_update_user_gold_room(user->get_socket_id(), uid);
			int node_id = user->get_node_id();
			CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
			if (ptable) {
				ptable->update_user_gold_result(user);
			}
		} else {
			MY_LOG_ERROR("update user gold failed with uid: %d", uid);
		}

		break;
	}
    case enUser_item_use : 
        on_use_user_item(pdata, size, action->uid, action->game_id, action->sid, action->p_data.buff, action->p_data.buff_len);
        break;
    case enUser_item_return :
        on_return_user_item(pdata, size, action->uid, action->p_data.m_cmd);
        break;
	default:
		break;
	}
	// ������Դ
	if (action)
	{
		stBindData *info = NULL;
		if (m_bind_list.get_key_data(action->sign, info))
		{
			if (info != NULL)
				m_bind_pool.push(info);
		}
		m_bind_list.remove_key(action->sign);
	}
}

// ��ҳ�����������Ϣ����
void web_request::on_gold_room_config_resp(void *pdata, _uint16 size)
{
	config_manager::GetInstance()->get_web_gold_room_config((const char *)pdata, size);
}

// ��Ϸ����������Ϣ����
void web_request::on_game_props_config_resp(void *pdata, int size)
{
	game_props::GetInstance()->reset_game_props_config_data((const char *)pdata, size);
}

// ������Ϸ����
bool web_request::on_game_config(const char *data_json, int size)
{
	config_manager::GetInstance()->on_game_config((const char *)data_json, size);
	return true;
}

// ����״̬����
void web_request::on_get_room_status(void *pdata, _uint16 size)
{
	config_manager::GetInstance()->on_get_room_status((const char *)pdata, size);
}

void web_request::on_transfer_inout_faile(stBindData bind_data)
{
	m_log->write_log(LOG_TYPE_DEBUG, "on_transfer_inout faile... ");
	IUser *pUser = UserManager::GetInstance()->get_user(bind_data.uid);
	if (pUser)
		pUser->set_single_status(0);

	Json::Reader jsonReader;
	Json::Value jsonValue;
	if (!jsonReader.parse(bind_data.transfer_data, jsonValue))
	{
		MY_LOG_ERROR("transfer inout parse erro.");
		return;
	}

	int room_type = config_manager::GetInstance()->get_room_type();
	int bill_type = jsonValue["bill_type"].asInt();
	if (bill_type == GOLD_WITH_HOLD)
	{
		// Ԥ��ʧ�ܣ����뷿��ʧ��
		CClientCmd::on_with_hold(false, bind_data.uid, bind_data.p_data.buff,
								 bind_data.p_data.buff_len, bind_data.sid, 0);
	}
	if (bill_type == GOLD_BACK_WITH_HOLD)
	{
		// Ԥ���˿�ʧ��,ֻ�ܵ��û��´ν����ٳ��Զ����˻�
	}
	else
	{
		// ��ͨ���㶩������ʧ�ܣ�ֻ�ܵȴ��Է��ֶ�У�鶩��
		if (room_type == TYPE_NOAML)
			on_transfer_inout_normal(bind_data, jsonValue, false, 0, 0);
		else if (room_type == TYPE_HUNDRED)
			on_transfer_inout_hunred(bind_data, jsonValue, false, 0, 0);
	}
}

// Ͷ���������
void web_request::on_transfer_inout_suc(const char *data_json, int size, stBindData bind_data)
{
	m_log->write_log(LOG_TYPE_DEBUG, "on_transfer_inout suc... %s", data_json);
	IUser *pUser = UserManager::GetInstance()->get_user(bind_data.uid);
	if (pUser)
		pUser->set_single_status(0);

	// Ͷ��POST����
	Json::Reader jsonReader;
	Json::Value jsonValue;
	if (!jsonReader.parse(bind_data.transfer_data, jsonValue))
	{
		MY_LOG_ERROR("transfer inout data parse erro.");
		return;
	}

	// �ӿڽ��
	Json::Reader WebjsonReader;
	Json::Value WebjsonValue;
	if (!WebjsonReader.parse(data_json, WebjsonValue))
	{
		MY_LOG_ERROR("transfer inout web json parse erro.");
		return;
	}

	int status = WebjsonValue["status"].asUInt();

	_tint64 balance_amount = 0;
	int room_type = config_manager::GetInstance()->get_room_type();
	int bill_type = jsonValue["bill_type"].asInt();
	int code = SUCCESS;
	if (status == 0)
	{
		balance_amount = WebjsonValue["data"]["balance_amount"].asUInt64();
	}
	else
	{
		code = WebjsonValue["error"]["code"].asInt();
	}

	bool is_suc = (code == SUCCESS);
	if (bill_type == GOLD_WITH_HOLD)
	{
		// Ԥ�۳ɹ������뷿��
		CClientCmd::on_with_hold(is_suc, bind_data.uid, bind_data.p_data.buff,
								 bind_data.p_data.buff_len, bind_data.sid, code);

		if (is_suc)
		{
			cmd_dbproxy::CDBProxyExceSection db_section;
			g_dbproxy_namager->post_exec(0, 0, 0, "cmd_transfer_inout_bill", bind_data.transfer_data.c_str(),
										 db_section, false);

			IUser *puser = UserManager::GetInstance()->get_user(bind_data.uid);
			if (puser != NULL)
			{
				BILL->write_gold_bill(
					config_manager::GetInstance()->get_game_id(),
					pUser->get_client_id(),
					0,
					pUser->get_version(),
					pUser->get_user_id(),
					pUser->get_gold(),
					bind_data.result,
					pUser->get_gold() - bind_data.result,
					(GOLD_BILL_TYPE)GOLD_WITH_HOLD,
					0,
					pUser->get_user_type(),
					false,
					room_type,
					0,
					0,
					0);
			}
		}
	}
	else if (bill_type == GOLD_BACK_WITH_HOLD)
	{
		// Ԥ���˿�ɹ�,д��,�������
		if (is_suc)
		{
			cmd_dbproxy::CDBProxyExceSection db_section;
			g_dbproxy_namager->post_exec(0, 0, 0, "cmd_transfer_inout_bill", bind_data.transfer_data.c_str(),
										 db_section, false);

			myredis::GetInstance()->set_data_by_uid(bind_data.uid, DB_COMM, CUR_WHTH_GOLD, 0);

			IUser *puser = UserManager::GetInstance()->get_user(bind_data.uid);
			if (puser != NULL)
			{
				BILL->write_gold_bill(
					config_manager::GetInstance()->get_game_id(),
					pUser->get_client_id(),
					0,
					pUser->get_version(),
					pUser->get_user_id(),
					pUser->get_gold(),
					bind_data.result,
					pUser->get_gold() - bind_data.result,
					(GOLD_BILL_TYPE)GOLD_BACK_WITH_HOLD,
					0,
					pUser->get_user_type(),
					false,
					room_type,
					0,
					0,
					0);
			}
		}
	}
	else
	{
		// ��ͨ���㶩�������ɹ�
		if (room_type == TYPE_NOAML)
			on_transfer_inout_normal(bind_data, jsonValue, true, code, balance_amount);
		else if (room_type == TYPE_HUNDRED)
			on_transfer_inout_hunred(bind_data, jsonValue, true, code, balance_amount);
	}
}

void web_request::on_transfer_inout_normal(stBindData bind_data,
										   Json::Value jsonValue,
										   bool is_suc,
										   int code,
										   _uint64 balance_amount)
{
	// �ɹ���ע�����
	MY_LOG_DEBUG("transfer inout suc:%d  write_db:%d  balance:%d",
				 is_suc ? 1 : 0,
				 bind_data.write_db ? 1 : 0,
				 balance_amount);

	int uid = bind_data.uid;
	int tableid = jsonValue["table_id"].asInt();
	if (bind_data.step_transfer)
	{
		// �ֲ����������жϽ�ҿ۳��Ƿ�ɹ����۳��ɹ��ɲʲ�����Ч
		// �ɷ���ֱ�Ӵ�������Ϊ�첽����������û���Ϣ�Ѿ���ʧ
		if (is_suc && code == SUCCESS)
		{
			int now_gold = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_GOLD);
			int client_id = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
			int room_type = config_manager::GetInstance()->get_room_type();
			int bill_type = jsonValue["bill_type"].asInt();
			int is_end = jsonValue["is_end"].asInt();
			int bet = jsonValue["bet_amount"].asInt();
			int pay_out = jsonValue["pay_out"].asInt();

			// д�����־��Ͷ����¼����Ǯ���ɲʺϲ�
			BILL->write_gold_bill(
				config_manager::GetInstance()->get_game_id(),
				client_id,
				0,
				"1.0.0",
				bind_data.uid,
				now_gold,
				pay_out,
				now_gold - pay_out,
				bill_type,
				0,
				REAL_USER,
				false,
				room_type,
				0,
				0,
				true);

			transfer_inout inout;
			inout.time = time(NULL);
			get_guid(inout.gguid);
			inout.bill_type = bill_type;
			inout.is_end = true;
			inout.md5Key = inout.md5Key;
			inout.uid = uid;
			inout.bet = bet;
			inout.result = pay_out + bet; // ��ע���Ѿ��ڵ�һ��ִ���ˣ�����ֻ������Ͷ���ɲ�
			inout.pay_out = pay_out;
			inout.tableid = tableid;
			inout.sid = 0;
			inout.write_db = true;	 // �Ƿ����
			inout.call_back = false; // �Ƿ�ص�
			inout.step_transfer = false;
			room_bill_fun::GetInstance()->write_transfer_inout(inout);
		}
	}
	else
	{
		CTable *ptable = TableManager::GetInstance()->get_table(tableid);
		if (ptable == NULL)
		{
			// �����棬�����쳣��ע��ֱ�����
			MY_LOG_ERROR("transfer inout table erro.:%s", bind_data.transfer_data.c_str());
			cmd_dbproxy::CDBProxyExceSection db_section;
			g_dbproxy_namager->post_exec(0, 0, tableid, "cmd_transfer_inout_bill", bind_data.transfer_data.c_str(),
										 db_section, false);

			return;
		}

		if (is_suc && bind_data.write_db)
		{
			cmd_dbproxy::CDBProxyExceSection db_section;
			g_dbproxy_namager->post_exec(0, 0, 0, "cmd_transfer_inout_bill",
										 bind_data.transfer_data.c_str(),
										 db_section,
										 false);
		}

		if (bind_data.call_back)
		{
			transfer_inout_result_info result_info;
			const char *gguid = jsonValue["parent_bet_id"].asCString();
			memcpy(result_info.gguid, gguid, GUID_LEN);
			result_info.bill_type = jsonValue["bill_type"].asInt();
			result_info.is_end = jsonValue["is_end"].asInt();
			result_info.uid = jsonValue["player_uid"].asInt();
			result_info.bet = jsonValue["bet_amount"].asInt();
			result_info.result = jsonValue["transfer_amount"].asInt();
			result_info.pay_out = jsonValue["pay_out"].asInt();
			result_info.time = bind_data.time;
			result_info.is_suc = is_suc;
			result_info.erro_code = (inout_erro_code)code;
			result_info.req_info = bind_data.transfer_data;
			result_info.balance_amount = balance_amount;
			ptable->on_post_transfer_inout(result_info);
		}
	}
}

void web_request::on_transfer_inout_hunred(stBindData bind_data, Json::Value jsonValue, bool is_suc, int code, _uint64 balance_amount)
{
	int tableid = jsonValue["table_id"].asInt();
	CTableHundred *ptable = TableManager::GetInstance()->get_hundred_table(tableid);
	if (ptable == NULL)
	{
		// �����棬�����쳣��ע��ֱ�����
		MY_LOG_ERROR("transfer inout table erro.:%s", bind_data.transfer_data.c_str());
		cmd_dbproxy::CDBProxyExceSection db_section;
		g_dbproxy_namager->post_exec(0, 0, tableid, "cmd_transfer_inout_bill", bind_data.transfer_data.c_str(),
									 db_section, false);

		return;
	}

	// �ɹ���ע�����
	MY_LOG_DEBUG("transfer inout suc:%d  write_db:%d  call_back:%d balance:%lld",
				 is_suc ? 1 : 0,
				 bind_data.write_db ? 1 : 0,
				 bind_data.call_back ? 1 : 0,
				 balance_amount);

	// ���˳���ʱû��ͬ����ң���Ϊ����Լ������˿۳�
	// ��Ҳ��������ص����

	if (is_suc && bind_data.write_db)
	{
		cmd_dbproxy::CDBProxyExceSection db_section;
		g_dbproxy_namager->post_exec(0, 0, tableid, "cmd_transfer_inout_bill",
									 bind_data.transfer_data.c_str(),
									 db_section,
									 false);
	}

	if (bind_data.call_back)
	{
		transfer_inout_result_info result_info;
		const char *gguid = jsonValue["parent_bet_id"].asCString();
		memcpy(result_info.gguid, gguid, GUID_LEN);
		result_info.bill_type = jsonValue["bill_type"].asInt();
		result_info.is_end = jsonValue["is_end"].asInt();
		result_info.uid = jsonValue["player_uid"].asInt();
		result_info.bet = jsonValue["bet_amount"].asInt();
		result_info.result = jsonValue["transfer_amount"].asInt();
		result_info.time = bind_data.time;
		result_info.is_suc = is_suc;
		result_info.erro_code = (inout_erro_code)code;
		result_info.req_info = bind_data.transfer_data;
		result_info.balance_amount = balance_amount;
		result_info.traceid = bind_data.traceid;
		//result_info.md5Key = bind_data.md5key;
		memcpy(&result_info.p_data, &bind_data.p_data, sizeof(penetrte_data));
		ptable->on_post_transfer_inout(result_info);
	}
}

void web_request::on_transfer_inout_batch(const char *data_json, int size, string transfer_data)
{
	m_log->write_log(LOG_TYPE_DEBUG, "on_transfer_inout_batch... %s", data_json);
}

// 请求刷新三方用户金币
bool web_request::post_use_user_item(int32_t uid, int32_t game_id, int32_t table_id, int32_t item_id, int32_t user_item_id, const char *buffer, size_t len) {

	if (m_web_room_config_api.length() == 0) {
        MY_LOG_ERROR("user[%d] m_web_room_config_api invalid, discard post use item request", uid);
        return false;
    }

	stBindData *info = m_bind_pool.pop();
	if (info == NULL) {
        MY_LOG_ERROR("user[%d] m_bind_pool invalid, discord post use item request", uid);
        return false;
    }

	memset(info, 0, sizeof(info));
	info->uid     = uid;
	info->game_id = game_id;
	info->sid     = table_id;
    if (len > sizeof(info->p_data.buff)) {
        MY_LOG_ERROR("user[%d] m_bind_pool buff less than request, discord post use item request, len=%d", uid, len);
        return false;
    }
    memcpy(info->p_data.buff, buffer, len);
    info->p_data.buff_len = len;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enUser_item_use);

	char parm[256] = {0};
	sprintf(parm, USE_USER_ITEM, uid, game_id, node_time, sign.c_str(), item_id, user_item_id);

	// string srt_post = "post method";
	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	MY_LOG_DEBUG("user[%d] post web_requst... %s", uid, req_url);

	return true;
}

// 请求刷新三方用户金币
bool web_request::post_return_user_item(int32_t uid, int32_t game_id, int32_t table_id, int32_t record_id) {

	if (m_web_room_config_api.length() == 0) {
        MY_LOG_ERROR("user[%d] m_web_room_config_api invalid, discard post return item request", uid);
        return false;
    }

	stBindData *info = m_bind_pool.pop();
	if (info == NULL) {
        MY_LOG_ERROR("user[%d] m_bind_pool invalid, discord post return item request", uid);
        return false;
    }

	memset(info, 0, sizeof(info));
	info->uid     = uid;
	info->game_id = game_id;
	info->sid     = table_id;
    info->p_data.m_cmd = record_id;

	string sign;
	int node_time = time(0);
	produce_hashids_s(sign, node_time, info, enUser_item_return);

	char parm[256] = {0};
	sprintf(parm, RETURN_USER_ITEM, uid, node_time, sign.c_str(), record_id);

	char req_url[1024] = {0};
	sprintf(req_url, m_web_room_config_api.c_str(), parm);
	CHttpCurl::GetInstance()->request(info, req_url, 0);
	MY_LOG_DEBUG("user[%d] post web_requst... %s", uid, req_url);

	return true;
}

void web_request::on_use_user_item(void *pdata, int size, int64_t uid, int64_t game_id, int table_id, char *buffer, size_t len) {
    
    int64_t record_id = 0;
    uint64_t bet = 0;
    uint64_t balance_require = 0;
    Json::Reader json_reader;
    Json::Value result;
    if (!json_reader.parse((char *)pdata, result)) {
        MY_LOG_WARN("user[%d] parse json: %s failed", uid, pdata);
        record_id = 0;
    }

    if (result.isMember("status")) {
        int status = result["status"].asInt();
        if (status != 0) {
            MY_LOG_ERROR("user[%d] on_use_user_item status error", uid);
            record_id = 0;
        }
    } else {
        MY_LOG_ERROR("user[%d] on_use_user_item status param invalid", uid);
    }

    if (result.isMember("data") && result["data"].isObject() && result["data"].isMember("record_id")) {
        record_id = result["data"]["record_id"].asInt64();
    } else {
        MY_LOG_ERROR("user[%d] on_use_user_item data or record_id param invalid", uid);
    }

    if (result.isMember("data") && result["data"].isObject() && result["data"].isMember("item") && result["data"]["item"].isMember("bet")) {
        bet = result["data"]["item"]["bet"].asUInt64();
    } else {
        MY_LOG_ERROR("user[%d] on_use_user_item data or bet param invalid", uid);
    }

    if (result.isMember("data") && result["data"].isObject() && result["data"].isMember("item") && result["data"]["item"].isMember("balance_require")) {
        balance_require = result["data"]["item"]["balance_require"].asUInt64();
    } else {
        MY_LOG_ERROR("user[%d] on_use_user_item data or balance_require param invalid", uid);
    }

    IUser *user = UserManager::GetInstance()->get_user(uid);
    uint64_t gold = user->get_gold();
    if (gold < balance_require && record_id > 0) {
        post_return_user_item(uid, game_id, table_id, record_id);
        MY_LOG_WARN("user[%d] user balance not enough so return item to system, gold:%lld, balance_require: %lld, record_id:%lld", 
            uid, gold, balance_require, record_id);
        record_id = 0;
    }

    if (user) {
        int node_id = user->get_node_id();
        CTableHundred* ptable = TableManager::GetInstance()->get_hundred_table(node_id);
        if (ptable) {
            ptable->transfer_use_item_result(user, buffer, len, record_id, bet, balance_require);
        }
    } else {
        MY_LOG_ERROR("user[%d] on_use_user_item failed", uid);
    }
}

void web_request::on_return_user_item(void *pdata, int size, int64_t uid, int64_t record_id) {
    
    Json::Reader json_reader;
    Json::Value result;
    if (!json_reader.parse((char *)pdata, result)) {
        MY_LOG_WARN("user[%d], parse json: %s failed", uid, pdata);
        return;
    }

    if (result.isMember("status")) {
        int status = result["status"].asInt();
        if (status != 0) {
            MY_LOG_ERROR("user[%d] on_return_user_item status error", uid);
            return;
        } else {
            MY_LOG_DEBUG("user[%d] on_return_user_item success, record_id: %lld", uid, record_id);
        }
    }
}

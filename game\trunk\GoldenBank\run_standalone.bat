@echo off
echo GoldenBank 独立测试
echo ===================

echo 这个程序完全独立，不依赖任何外部库或文件
echo.

REM 检查编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请安装 MinGW 或其他 C++ 编译器
    pause
    exit /b 1
)

echo 正在编译独立测试程序...
g++ -std=c++11 -Wall -O2 standalone_test.cpp -o standalone_test.exe

if %errorlevel% neq 0 (
    echo 编译失败！
    echo 这很奇怪，因为这个程序不依赖任何外部库
    echo 请检查编译器安装是否正确
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.
echo 正在运行测试...
echo.

standalone_test.exe

echo.
echo 测试完成！
echo.
echo 生成的文件:
if exist "standalone_result.txt" (
    echo - standalone_result.txt: 当前计算结果
)
if exist "test.txt" (
    echo - test.txt: 参考数据
)

pause

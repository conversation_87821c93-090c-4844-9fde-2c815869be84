﻿

#ifndef _USER_MANAGER_H_
#define _USER_MANAGER_H_

#include "room_common.h"
#include "object_pool.h"
#include "cmd_match.h"
#include "user.h"
#include <time.h>

#define CHECK_TICK_TIME 180 //被T出的玩家，3分钟内不允许再进
#define CHECK_TEXT_RADIO 3  //文字语音控制频率
#define CHECK_AVATAR 3      //互动表情控制频率
#define CHECK_OFFLINE_USER 60 //3分钟还处于站起状态，T走
#define CHECK_WRITE_GAMEINFO 60 //1分钟同步一次数据

#define CHECK_USER_TIMEID 1500
#define CHECK_UPDATE_USER_DATA 1501
#define CHECK_USER_LEN 1000
#define CHECK_UPDATE_LEN 3000

//比赛人数，按平台统计
struct stMatchCountInfo
{
	int matchFullOnlineCount;
	int matchFullOfflineCount;
};
typedef map<int, stMatchCountInfo> map_match_count_info;
typedef map_match_count_info::iterator iter_match_count_info;

//房间人数，按平台和节点统计
typedef map<int, int> map_node_user_count;
typedef map_node_user_count::iterator  iter_node_user_count;
struct stNodeCountInfo
{
	map_node_user_count node_user_count;
};
typedef map<int, stNodeCountInfo> map_plat_user_count;
typedef map_plat_user_count::iterator  iter_plat_user_count;

//房间人数，只按平台统计
struct stRoomUserCount
{
	int OnlineCount;
	int OfflineCount;
};
typedef map<int, stRoomUserCount> map_room_user_count;
typedef map_room_user_count::iterator iter_room_user_count;

//房间人数,按系统类型统计
struct stSysRoomUserCount
{
	int ios_count;
	int android_count;
};
typedef map<int, stSysRoomUserCount> map_sys_room_user_count;
typedef map_sys_room_user_count::iterator iter_sys_room_user_count;

struct stPlatSysRoomUserCount
{
	map_sys_room_user_count count;
};
typedef map<int, stPlatSysRoomUserCount> map_plat_sys_room_user_count;
typedef map_plat_sys_room_user_count::iterator iter_plat_sys_room_user_count;

class TickInfo
{
public:
	TickInfo();

public:
	int uid_list[1000];
	int time_list[1000];
	int len;

public:
	void add_info(_tint64 uid)
	{
		int index = len;
		if (len + 1 >= 100)
			index = 0;
		
		uid_list[index] = uid;
		time_list[index] = time(0);
		if (len + 1 < 100)
			len++;
	}
	int get_time(_tint64 uid)
	{
		for (int i = 0; i < len; i++)
		{
			if (uid_list[i] == uid)
				return time_list[i];
		}
		return 0;
	}
	void update_info(_tint64 uid)
	{
		bool is_exit = false;
		for (int i = 0; i < len; i++)
		{
			if (uid == uid_list[i])
			{
				is_exit = true;
				time_list[i] = time(0);
				g_log->write_log(LOG_TYPE_DEBUG,"update user tick time -  uid:%d  time:%d", uid_list[i],time_list[i]);
				break;
			}
		}
		if (!is_exit)
			add_info(uid);
	}
};

class UserManager
{
public:
    static UserManager *GetInstance()
    {
        static UserManager stMgr;
        return &stMgr;
    }
    
protected:
    UserManager();
    virtual ~UserManager();

public:
	/**
     *  @brief: 初始化用户管理
     *  @return: 成功返回true, 失败返回false
    **/
	virtual bool init();
    /**
     *  @brief: 释放用户管理
     *  @return: 成功返回true, 失败返回false
    **/
	virtual bool final();
    /**
     *  @brief: 获得用户总数量
     *  @return: 返回房间用户总人数
    **/
    virtual int user_count();
    /**
     *  @brief: 获得正在游戏总人数
     *  @return: 返回房间正在游戏总人数
    **/
    virtual int play_count();
    /**
     *  @brief: 获得用户指针
     *  @userID: IN 用户ID
     *  @return: 返回用户指针，找不到用户返回NULL
    **/
    virtual IUser * get_user(_tint64 userID);
    /**
     *  @brief: 删除用户
     *  @pUser: IN OUT 用户指针，删除后会置用户指针为NULL
     *  @return: 成功返回true, 失败返回false
    **/
    virtual bool delete_user(IUser*& pUser, bool is_need_send = true);
    /**
     *  @brief: 删除用户
     *  @userID: IN 用户ID
     *  @return: 成功返回true, 失败返回false
    **/
    virtual bool delete_user(_tint64 userID, bool is_need_send = true);
    /**
     *  @brief:  枚举用户
     *  @index: IN 用户索引
     *  @return: 成功返回用户指针， 失败返回NULL
    **/
    virtual IUser * enum_user(int index);
    /**
     *  @brief: 创建用户指针
     *  @return: 创建成功返回用户指针，否则返回NULL
    **/
    virtual IUser * create_user(_tint64 userID, int nodeid, int user_type);
    /**
     *  @brief: 用户数量
     *  @onlineCount: 在线用户数
     *  @offlineCount: 断线用户数
     *  @return: 返回用户总人数
    **/
    virtual int user_count(int & onlineCount, int & offlineCount);

	/**
	 *   人满赛比赛场用户数量
	 *	 @matchFullOnlineCount: 人满赛在线用户数量
	 *	 @matchFullOfflineCount: 人满赛断线用户数量
	**/
	void match_full_user_count(int &matchFullOnlineCount, int &matchFullOfflineCount);
	
	/**
	*   定时赛比赛场用户数量
	*	 @matchFullOnlineCount: 人满赛在线用户数量
	*	 @matchFullOfflineCount: 人满赛断线用户数量
	**/
	void match_time_user_count(int &matchFullOnlineCount, int &matchFullOfflineCount);

	/**
	*    根据节点统计场次人数
	*	 
	*
	**/
	void get_node_count(map<int, int> &count);
	/*
	*    根据系统类型获取场次人数
	*/
	void get_node_count_for_sys(map_plat_sys_room_user_count &count);

public:
	//统计房间内各平台比赛人数
	void match_user_count_from_plat(map_match_count_info &match_count, int match_type);
	//统计房间内各平台房间节点人数
	void get_node_count_from_plat(map_plat_user_count &node_count);
	//统计房间内各平台人数
	void get_user_count_from_plat(map_room_user_count &room_user_count);

public:
	//添加用户被T时间
	void add_check_tick(_uint32 code, _uint64 uid);
	//删除本场被T时间
	void delete_check_tick(_uint32 code);
	//用户是否在限定时间内
	bool check_tick(_tint64 userid, _uint32 code);
	//获得虚拟UID
	int get_vir_uid();
	//检查虚拟ID是否被占用
	bool is_use_virid(int vir_id);

public:
	//添加用户文字语音时间
	void add_text_radio(_uint32 code, _uint64 uid);
	//删除本场文字语音时间
	void delete_text_radio(_uint32 code);
	//用户是否在限定时间内
	bool check_text_radio(_tint64 userid, _uint32 code);

public:
	//添加互动表情时间
	void add_avatar(_uint32 code, _uint64 uid);
	//删除互动表情时间
	void delete_avatar(_uint32 code);
	//用户是否在限定时间内
	bool check_avatar(_tint64 userid, _uint32 code);

public:    
    void check_offline_user();
    void leave_user(_tint64 userID);
	void update_user_result(_tint64 userID, int result);

public:
	//打印机器人信息
	void print_robot_info();

private:
    /**
     *  @brief: 清理断线用户定时器
     *  @return:无返回值
    **/
    static FTYPE(void) clear_offline_user(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);

	/**
	*  @brief: 同步数据定时器
	*  @return:无返回值
	**/
	static FTYPE(void) update_user_date(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);


private:
    CObjectPool<CUser>              m_user_pool;                    //用户内存    
    key_vector<_tint64, IUser*>     m_user_list;                    //用户列表

	CObjectPool<TickInfo>           m_tick_pool;                    //用户内存 
	key_vector<_tint64, TickInfo*>   m_tick_list;					//踢出信息列表

	CObjectPool<TickInfo>           m_text_radio_pool;              //文字语音内存 
	key_vector<_tint64, TickInfo*>   m_text_radio_list;				//文字语音信息列表

	CObjectPool<TickInfo>           m_avatar_pool;					//互动表情内存
	key_vector<_tint64, TickInfo*>   m_avatar_list;					//互动表情信息列表
};

#endif

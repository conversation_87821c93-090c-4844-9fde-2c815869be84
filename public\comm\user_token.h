﻿/*
-----------------------------------------------------------------------------

File name        :   user_token.h        
Author           :   
Version          :   1.1            
Date             :    
Description      :   token生成和解密 （简洁版：固定22个字符和数字组合） 

-----------------------------------------------------------------------------
*/
#ifndef  _TOKEN_H_
#define  _TOKEN_H_

#ifdef _WINNT_SYSTEM
	#include "../hashdis/hashids.h"	
#else
	#include "../hashdis/hashids.h"
#endif

#include <list>

const unsigned char TOKEN_KEY[] = {0x13,0x14,0x10,0x17,0x21,0x5f,0xa1,0xbe,0x2e,0x3f,0xe9,0xb0,0x2d,0x2b,0x3d,0x25,
                 0x41,0x3b,0x2a,0x22,0x21,0x7e,0xc0,0x51,0x21,0x3f,0x3e,0x3c,0x20,0xe8,0xb8, 0x0};


//token 字符串
typedef struct tagTokenStr{
    char token[65];  //token 字符串
}TOKENSTR;






static char ___code62[62] = {
    '0' , '1' , '2' , '3' , '4' , '5' , '6' , '7' , '8' , '9' ,
    'A' , 'B'  ,'C' , 'D' , 'E' , 'F' , 'G' , 'H' , 'I' , 'J' ,
    'K' , 'L' , 'M' , 'N' , 'O' , 'P' , 'Q' , 'R' , 'S' , 'T' ,
    'U' , 'V' , 'W' , 'X' , 'Y' , 'Z' , 'a' , 'b' , 'c' , 'd' ,
    'e' , 'f' , 'g' , 'h' , 'i' , 'j' , 'k' , 'l' , 'm' , 'n' ,
    'o' , 'p' , 'q' , 'r' , 's' , 't' , 'u' , 'v' , 'w' , 'x' ,
    'y' , 'z'
};

static _uint8 ___code62_index[256] = {255};
inline void ___init_code62_index()
{
    int i;
    static int g_b_init_code_index = 0;
    if(g_b_init_code_index) return ;
    g_b_init_code_index = 1;
    memset(&___code62_index,255,sizeof(___code62_index));
    for(i=0;i<62;i++) ___code62_index[(_uint8)___code62[i]] = i;
}

inline int __int_to_code62(char*buf,int size,_uint64 value)
{
    int  c = 0,j;
    int fmt_count = 11;
    _uint8 bf[11] = {0};
    while(value > 0 && c < sizeof(bf)){
        if(value < 62) {
            bf[c++] = ___code62[(_uint8)value];
            break;
        }
        else{
            bf[c++] = ___code62[(_uint8)(value % 62)];
            value /= 62;
        }
    }
    if(fmt_count <= c){
        for(j=0;j<c && j<size;j++) buf[j] = bf[c-j-1]; 
        if(c < size) buf[c] = '\0';
    }
    else{
        j=0;
        for(;j < fmt_count - c  && j<size;j++) buf[j] = ___code62[0]; 
        for(;j<fmt_count && j<size;j++) buf[j] = bf[fmt_count-j-1]; 
        if(fmt_count < size) buf[fmt_count] = '\0';
        c = fmt_count;
    }
    return c;
}

inline _uint64 __power62(int m)
{	
    int i;
    _uint64 p = 1;			
    for (i=1;i<=m;i++) p= (p << 6) - (p << 1);
    return p;
}

inline int  __code62_to_int(const char*code62,int size,_uint64 *value)
{
    int i, c;
    _uint64 v = 0;
    ___init_code62_index();
    if(value) *value = 0;
    if(!code62 || size <= 0) return -1;

    size = size < 11 ? size : 11;
    for(c = 0; c < size ; c++){
        if(___code62_index[code62[c]] >= 62) return -2;
    }

    v = ___code62_index[code62[c - 1]];
    for(i = 1; i < c ; i++){
        v += (__power62(i) * ___code62_index[code62[c - i - 1]]);
    }
    if(value) *value = v;
    return 0;
}


#pragma pack(push,1)
struct tagTokenInfo{
    _uint32 pass_hash:24;      //密码hash
    _uint32 check_code1:8;     //校验码
    _uint32 check_code2:8;     //校验码
    _uint32 create_time:24;    //创建时间
    _uint64 userid;            //userid
};
#pragma pack(pop)


//密码hash
inline _uint32 __hash_pass(const char* szpass)
{
    tagTokenInfo token;
    int str_len = szpass ? strlen(szpass) : 0;
    _uint32 seed = 131; 
    _uint32 hash = 0;
    if(str_len <= 0) str_len = szpass ? (int)strlen(szpass) : 0;
    while (str_len-- > 0){ hash = hash * seed + (*szpass++); }
    token.pass_hash = hash;
    return token.pass_hash;
}


inline _uint8 __check_code2()
{ 
    static _uint8 code = 0; 
    if(0 == code) code = __hash_pass("dianjiayouxi");
    return code;
}


/**
*  @brief:  生成token
*  @sztoken:   OUT token字符串缓存（必须）
*  @userid:    IN 用户ID  （必须）
*  @szpass:    IN 密码 （必须）
*  @return:    成功返回 true 失败返回 false
**/
inline bool create_token(TOKENSTR &sztoken,_uint64 userid,const char* szpass)
{
    char tmpbuf[256] = {0};
    tagTokenInfo token;
    _uint64* piv;
    sztoken.token[0] = '\0';
    memset(sztoken.token,0,sizeof(sztoken.token));
    if(0 == userid || !szpass)  return false; //无效数据

    token.pass_hash   = __hash_pass(szpass);
    token.create_time = g_com->_get_time32();
    token.userid      = userid ;
	token.check_code1 = (_uint32)(token.userid + token.pass_hash + token.create_time);
    token.check_code2 = __check_code2();

    g_com->_aesencode((unsigned char*)TOKEN_KEY,(unsigned char*)&token,sizeof(tagTokenInfo),(unsigned char *)tmpbuf,sizeof(tmpbuf),2);

    piv = (_uint64*)tmpbuf;
    __int_to_code62(sztoken.token,11,*piv);
    __int_to_code62(sztoken.token+11,11,*(piv+1));

    return true;
}

/**
*  @brief:  解token
*  @sztoken:   IN  token字符串（必须）
*  @userid:    OUT 用户ID  （非必须）
*  @pass_hash: OUT 密码hash值 （非必须）
*  @tspc_sec:  OUT 距离token生成的时间间隔（秒） （非必须）
*  @return:    成功返回 true 失败返回 false
**/
inline bool decode_token(const char *sztoken,_uint64 *userid,_uint32 *pass_hash,_uint32* tspc_sec)
{
    tagTokenInfo info,tmpinfo;
    char tmpbuf[256] = {0};
    _uint64* piv;
    int  tlen = sztoken?strlen(sztoken):0;

    if(userid)   *userid    = 0;
    if(pass_hash)*pass_hash = 0;
    if(tspc_sec) *tspc_sec  = 0;

    if(tlen < 22) return false;

    piv=(_uint64*)tmpbuf;
    if(0 != __code62_to_int(sztoken,11,piv++))    return false; // 无效字符串
    if(0 != __code62_to_int(sztoken+11,11,piv++)) return false; // 无效字符串

    g_com->_aesdecode((unsigned char *)TOKEN_KEY,(unsigned char*)tmpbuf,16,(unsigned char *)(char*)&info,sizeof(info),2);

	//+ info.pass_hash
    tmpinfo.check_code1  = (_uint32)(info.userid + info.pass_hash + info.create_time);
    if(info.check_code1  != tmpinfo.check_code1  || info.check_code2 != __check_code2())
        return false;

    if(tspc_sec) 
    {
        tmpinfo.create_time = g_com->_get_time32();
        //*tspc_sec = tmpinfo.create_time >= info.create_time ? tmpinfo.create_time - info.create_time : 0x00ffffff;
        *tspc_sec = tmpinfo.create_time >= info.create_time ? tmpinfo.create_time - info.create_time : 0; //不同机器时间有差异
    }
    if(userid)    *userid    = info.userid;
    if(pass_hash) *pass_hash = info.pass_hash;

    return true;
}

//HASHIDS解码
inline bool hashids_decode_token(const char *sztoken, _uint64 *userid, char* unique_string)
{
	if (strlen(sztoken) != 64)
		return false;
	
	char hashids_str[33] = { 0 };
	memcpy(hashids_str, sztoken, 32);
	memcpy(unique_string, sztoken+32, 32);
	
	hashidsxx::Hashids hash(MYSALT, 32, DEFAULT_ALPHABET);
	std::vector<uint64_t> output = hash.decode(hashids_str);
	if (output.size() == 0)
		return false;
	
	*userid = output[0];
	return true;
}

//HASHIDS加密
inline void hashids_encode(char *sign, int len, string &ids)
{
	hashidsxx::Hashids hash(MYSALT, 32, DEFAULT_ALPHABET);

	std::list<uint64_t> encode_input;
	encode_input.push_back(std::strtol(sign, NULL, 10));
	ids = hash.encode(encode_input.begin(), encode_input.end());
}

inline void hashids_encode(char *sign, int len, string &ids, const string salt)
{
	hashidsxx::Hashids hash(salt, 32, DEFAULT_ALPHABET);

	std::list<uint64_t> encode_input;
	encode_input.push_back(std::strtol(sign, NULL, 10));
	ids = hash.encode(encode_input.begin(), encode_input.end());
}

#endif
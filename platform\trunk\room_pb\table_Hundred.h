﻿#ifndef _TABLE_HUNDRED_
#define _TABLE_HUNDRED_

#include "room_common.h"
#include "i_game_component.h"
#include "LocationLogic.h"
#include "table_hunded_interface.h"
#include <map>
#include <time.h>
#include "config_manager.h"
#include "observe_manager.h"
#include "game_props.h"

//*************************百人游戏桌子类***************************************
class CTableHundred :public ITableHunded
{
public:
    CTableHundred();
    virtual ~CTableHundred();

// public:
// 	static CTableHundred *GetInstance()
// 	{
// 		static CTableHundred stMgr;
// 		return &stMgr;
// 	}
public:
	virtual void update_super_control_info(const char* key, const char * data, int len);
	virtual void query_room_gold_statistic(int gameid, int nodeid);       //查询房间输赢
	virtual _tint64 get_room_gold_statistic();							  //获取房间输赢
	virtual void update_room_gold_statistic(_tint64 gold);				  //更新房间输赢

	virtual int get_user_gold_by_uid(_uint64 uid);
	virtual int get_curr_room_status();                //获取当前房间状态         (0运行状态     1维护状态)

	virtual void update_enter_time(int uid);

	virtual void update_date_number(int num);
    virtual _tint64 get_date_number();
	virtual void flesh_robot();
	virtual void flesh_all_user_gold();

	//写三方投付
	virtual bool write_transfer_inout(hundred_transfer_inout_one inout);
	virtual bool write_transfer_inout_ex(hundred_transfer_inout_one inout);
	virtual void batch_write_transfer_inout(hundred_transfer_inout inout);

	//投递投付结果
	virtual bool on_post_transfer_inout(transfer_inout_result_info inout_result);

	//请求刷新三方用户金币
	virtual void get_flush_user_gold(int uid, int gameid);

public:
	int init(const void * rule, int rule_len = 0);  
    int on_recv(_uint8 scmd, const void* pData, int size, IUser* pUser);
	int on_http_recv(socket_id sid, int uid, int opid, const string& strParam);

    int on_game_recv(_uint8 scmd, const void* pData, int size, IUser* pUser);
	void check_robot_gold(int uid, int min_gold, int max_gold);
	void on_look_avatar(IUser *pUser, const void *pdata, int size);
	int on_chat(IUser *pUser, const void *pdata, int size);
	int on_upload_gps(const void *pdata, int size);
	void on_user_use_props_card(IUser* pUser, const void* pdata, int size);
	void reset_game_rule(string rule);
	void on_user_props_search(IUser* pUser, const void* pdata, int size);	
	// 查询玩家背包消息
	void on_user_bag_message_search(IUser* pUser, const void* pdata, int size);
	// 查询玩家背包红点状态信息
	void on_user_bag_red_point_status_search(IUser* pUser, const void* pdata, int size);
	// 玩家背包切换tab页
	void on_user_bag_change_tab_page(IUser* pUser, const void* pdata, int size);

public:
	void set_node_id(int node_id) 
	{
		m_table_id = node_id;
		m_node_id  = node_id; 
	}
	int get_node_id() { return m_node_id; }
	void set_match_sn(int matchsn) { m_match_sn = matchsn; }
	const char* get_node_name() {
		return config_manager::GetInstance()->get_node_name(m_node_id); 
	}
public:
	bool send(IUser * pUser, _uint8 mcmd, _uint8 scmd, const void * pData, int size);
	bool send(IUser * pUser, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);
	bool send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, const void * pData, int size);
	bool send(_uint16 chairID, _uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);  
	bool send(socket_id sid, const string &str);
	bool send_http(socket_id sid, const string &str);  
	void send_http_failed(socket_id sid, int ret, const string& msg);
	bool send_user_batch(_uint8 mcmd, _uint8 scmd, const void * pdata, int size);
	bool send_user_batch(_uint8 mcmd, _uint8 scmd, ITBFOBJBase * pData);   
	void send_common_msg(_uint8 chair_id, const char* msg, int len, int type);
	bool send_http_json(IUser * pUser, std::map<std::string, std::string> http_headers_map, const void* data, size_t size);

public:
    IUser *get_user_from_uid(_tint64 userID);
    IUser *get_user_from_chair(int chairID);
    _uint64 get_uid_from_chair(int chairID);
	int get_chair_from_uid(int uid);
	int get_room_id();
	IUser* create_user(_tint64 uid);
	virtual void delete_user(IUser* user);

	int get_max_user() { return m_max_player; }
	int get_user_count() { return m_now_user_count; }
	int get_player_size() { return m_game_users.size(); }
	int get_vir_uid(); //获得虚拟UID

public:
    bool set_timer(_uint8 time_id, _uint32 timeout_msec, _uint64 param, _uint32 repeat = 1);
    bool kill_timer(_uint8 time_id);

public:
    bool on_start_game();
    bool on_end_game();
	void on_send_game_info();
    int add_user(_tint64 userID, int &chChairID);
	void update_user_gold(_uint64 uid, int num, bool need_flesh=true, int type_id = GOLD_DEL_GAME, int lose_rate=0);
	void update_gold_and_clore(_uint64 uid, int num, bool need_flesh = true, int type_id = GOLD_DEL_GAME, int lose_rate = 0);
	virtual void update_gold_and_clore_single(_uint64 uid, int num, int type_id = GOLD_DEL_GAME, int lose_rate = 0);
	virtual void update_user_prop(_uint64 uid, int total_bet, int game_nums, bool free_game = false, int type_id = GOLD_HANDRED_BET);
	virtual void update_user_props_Rewards(_uint64 uid, int gold, int type_id);
	void check_robot(int need_robot);

public:
	int get_leave_min();
	int get_leave_max();
	void tick_user(_uint64 uid);

public:    
	int gm_control(const char* buff, int len, char* tips, int &tips_len, string ip, int account_id);
	void on_force_end(_uint64 uid);
	void on_time_out(_uint8 timer_id, _uint64 param);
    static FTYPE(void) on_timer(void * obj, _uint32 timerID, _uint64 param, _uint32 Elapse, _uint32 iRepeat, _uint32 delay);

public:
	void stop_game();
	void start_game();

public:
	int on_user_enter(IUser *pUser);
    int on_user_reconn(IUser *pUser);    
	int on_user_leave(IUser *pUser);
	virtual int on_user_offline(IUser *pUser);
	void on_user_game_info(IUser *puser);

public:
	void write_user_roominfo(_uint64 user_id, int type);

	//游戏写金币
	void write_gold_bill(IUser * pUser, int gold, int type_id, int lose_rate = 0);
	void write_gold_bill(int uid, int gold, int type_id, int lose_rate = 0);
	void write_gold_bill(int uid, int gold, _uint64 now_gold, int client_id, const char version[16], int type_id, int lose_rate = 0);
	
	// 游戏写道具记录
	void write_prop_bill(int uid, int prop_id, int now_prop, int incre_prop, int client_id, const char version[16], const char param[64], int type, int sub_type, int props_type, int reward_props_id = 0, int reward_number = 0);

	//写游戏详情
	virtual void write_game_detail(char gguid[GUID_LEN], int bet_id, int uid, string detail);

	virtual void write_room_gold_statistic(int gold, int type_id);
	/* 每局写流水 */
	virtual void write_versus_bill(const char * data, int len);
	virtual void write_game_result(const char * data, int len, const char *issue, int issue_len);
	/* 一大局写流水*/
	virtual void write_game_record(const char * data, int len, const char* cmd=NULL);
	virtual void write_gamedata_bill(const char * data, int len);
	virtual void write_big_card_versus_bill(const char * data, int len);

public:
	virtual void game_db_push_data(const char* type, const char *data, int len);
	virtual void game_db_get_data(const char* type, const char *data, int len, void* postdata, int postlen);
	static FTYPE(void) on_get_db_data(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);
	void comm_get_db_data(void *obj, const char *data, int len);

	static FTYPE(void) on_query_room_gold_statistic_dbproxy(void*obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection &section);
	// 背包消息记录分页请求
//	static FTYPE(void) on_bag_message_record_startpage_req(socket_id sid, _uint64 userid, SNETHEAD* head, const void* data, int size, const void* exdata);
	static FTYPE(void) on_bag_message_record_startpage_dbproxy(void* obj, const char* data_json, int size, cmd_dbproxy::CDBProxyExceSection& section);

    virtual bool write_spin_round_history(SpinRoundHistory& h);
	virtual void update_user_gold_result(IUser* user);
    virtual void transfer_use_item_result(IUser* user, char *buffer, size_t len, int64_t record_id, int32_t bet, int32_t balance_require);

public:
    CDLLHelper<IHundredGameComponent>   m_game_dll;
    IHundredGameComponent              *m_game_com;          /* 游戏组件 */
	game_props_interface* m_game_props;   // 道具类

public:
	_tint64							m_room_result;			//房间输赢
	_uint32							m_room_result_time;		//房间输赢时间搓

private:
    /* 临时数据：仅限内部同步接口使用 */
    IUser                          *m_puser;
    const void                     *m_recv_data;
    int                             m_recv_len;

    bool                            m_playing;              /* 游戏标记 */
    int                             m_max_player;           /* 最多人数 */
	int								m_table_id;				
	int								m_now_user_count;		
	int								m_node_id;				//对应节点ID
	int								m_match_sn;				//比赛编码标识

	/*用户ID列表*/
	vector<_tint64>					m_game_users;																			
};

#endif

#include "bill_fun.h"
#include <time.h>
#include "comm_def.h"
#include "my_redis.h"

bill_fun::bill_fun(void)
{
}

bill_fun::~bill_fun(void)
{
}

//获得对应流水类型
int bill_fun::get_bill_type(HEALTHE_CHANGE_TYPE type, GAME_MONEY_TYPE money_type)
{
	if (money_type == GAME_ROOM_CARD)
	{
		switch(type)
		{
		case	SYSTEM_HEALTH_CHANGE:	return 0;						/* 系统默认 */
		case	CREATE_HEALTH_CHANGE:	return CREATE_ROOM_MONEY_BILL;	/* 创建房间消耗房卡 */
		case	RETURN_HEALTH_CHANGE:	return ROOM_RETURN_MONEY_BILL;	/* 返回房卡 */
		case	SHOP_HEALTH_CHANGE:		return 0;						/* 商城购买 */
		case	ADMIN_HEALTH_CHANGE:	return 0;						/* 后台操作 */
		default:
			return 0;
		}
	}
	else
	{
		switch(type)
		{
		case	SYSTEM_HEALTH_CHANGE:	return 0;						/* 系统默认 */
		case	CREATE_HEALTH_CHANGE:	return 0;						/* 创建房间消耗房卡 */
		case	RETURN_HEALTH_CHANGE:	return 0;						/* 返回房卡 */
		case	SHOP_HEALTH_CHANGE:		return GOLD_ADD_CHARGE;			/* 商城购买 */
		case	ADMIN_HEALTH_CHANGE:	return 0;						/* 后台操作 */
		default:
			return 0;
		}
	}
	return 0;
}

//道具流水
void bill_fun::write_prop_bill(int gameid, int clientid, int room_num, const char version[16], const char param[64],
	int uid, int now_prop, int incre_prop, int last_prop, int propid, int type, int props_type, int reward_props_id, int reward_number, int sub_id)
{
	cmd_dbproxy::CDBProxyExceSection section;

	Json::FastWriter json_writer;
	Json::Value json_req_value;

	json_req_value["i_uid"] = Json::Value(uid);
	json_req_value["i_client_id"] = Json::Value(clientid);
	json_req_value["i_game_id"] = Json::Value(gameid);
	json_req_value["i_type_id"] = Json::Value(type);
	json_req_value["i_type_id_sub"] = Json::Value(sub_id);
	json_req_value["i_quantity1"] = Json::Value(last_prop);
	json_req_value["i_quantity2"] = Json::Value(now_prop);
	json_req_value["i_quantity"] = Json::Value(incre_prop);
	json_req_value["i_room_id"] = Json::Value(room_num);
	json_req_value["i_version"] = Json::Value(version);
	json_req_value["i_prop_id"] = Json::Value(propid);
	json_req_value["i_prop_type"] = Json::Value(props_type);
	json_req_value["i_param"] = Json::Value(param);
	json_req_value["i_reward_props_id"] = Json::Value(reward_props_id);
	json_req_value["i_reward_number"] = Json::Value(reward_number);
	
	int plat = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
	char db_name[32] = { 0 };
	if (g_dbproxy_namager->get_db_name(plat, db_name, 32))
		json_req_value["db_name"] = Json::Value(db_name);

	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, 0, "write_prop_bill", json_req_data.c_str(), section, false, plat);
}

//金币流水
void bill_fun::write_gold_bill(int gameid, int clientid, int room_num, const char version[16], 
							   int uid, int now_gold, int incre_gold, int last_gold, int type, int node_id, int user_type,
	                           bool bstatistics, int room_type, int lose_rate, int notice_id, bool agent)
{
	if (user_type == ROBOT_TYPE && type != GOLD_ROBOT)
	{
		return ;
	}

	cmd_dbproxy::CDBProxyExceSection section;

	Json::FastWriter json_writer;
	Json::Value json_req_value;

	int now_time = time(0);	
	json_req_value["i_uid"]		  = Json::Value(uid);
	json_req_value["i_post_time"] = Json::Value(now_time);
	json_req_value["i_client_id"] = Json::Value(clientid);
	json_req_value["i_game_id"]   = Json::Value(gameid);
	json_req_value["i_type_id"]   = Json::Value(type);
	json_req_value["i_type_id_sub"] = Json::Value(notice_id);
	json_req_value["i_quantity1"] = Json::Value(last_gold);
	json_req_value["i_quantity2"] = Json::Value(now_gold);
	json_req_value["i_quantity"]  = Json::Value(incre_gold);
	json_req_value["i_room_id"]   = Json::Value(room_num);
	json_req_value["i_version"]   = Json::Value(version);
	json_req_value["i_kind_id"]   = Json::Value(node_id);
	json_req_value["i_node_id"]   = Json::Value(node_id);
	json_req_value["i_user_type"] = Json::Value(user_type);
	
	int plat = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
	char db_name[32] = { 0 };
	if (g_dbproxy_namager->get_db_name(plat, db_name, 32))
		json_req_value["db_name"] = Json::Value(db_name);

	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, 0, "comm_write_lobby_gold_bill", json_req_data.c_str(), section, false, plat);

	/*if (type != GOLD_DEL_ROOM_CHARGE && type != GOLD_DEL_PROP && 
		type != GOLD_DEL_GAME && type != GOLD_DEL_TAX && 
		type != GOLD_DEL_PUMP && type != GOLD_DEL_FIRE &&
		type != GOLD_DEL_PUMP_FISH && type != GOLD_DEL_PUMP_FISH &&
		type != GOLD_DEL_SHARE && type != GOLD_DEL_SHARE &&
		type != GOLD_DEL_ZJH_BET && type != GOLD_DEL_TAX_REVENUE)
	{
		gold_insert_mysql(uid, now_gold, incre_gold);
	}*/
}

void bill_fun::write_lobby_gold_bill(int gameid, int clientid, int room_num, const char version[16], 
						   int uid, int now_gold, int incre_gold, int last_gold, int type, int node_id,
						   int user_type, bool fg, bool is_bank, int kind_id, int sub_type, bool update_agent_profit_now,
						   bool is_third, string transfer_reference)
{
	cmd_dbproxy::CDBProxyExceSection section;
	

	Json::FastWriter json_writer;
	Json::Value json_req_value;

	int now_time = time(0);	
	json_req_value["i_uid"]		  = Json::Value(uid);
	json_req_value["i_post_time"] = Json::Value(now_time);
	json_req_value["i_client_id"] = Json::Value(clientid);
	json_req_value["i_game_id"]   = Json::Value(gameid);
	json_req_value["i_type_id"]   = Json::Value(type);
	json_req_value["i_type_id_sub"] = Json::Value(sub_type);
	json_req_value["i_quantity1"] = Json::Value(last_gold);
	json_req_value["i_quantity2"] = Json::Value(now_gold);
	json_req_value["i_quantity"]  = Json::Value(incre_gold);
	json_req_value["i_room_id"]   = Json::Value(room_num);
	json_req_value["i_version"]   = Json::Value(version);
	json_req_value["i_kind_id"]   = Json::Value(kind_id);
	json_req_value["i_node_id"]   = Json::Value(node_id);
	json_req_value["i_user_type"] = Json::Value(user_type);
	json_req_value["i_transfer_reference"] = Json::Value(transfer_reference.c_str());

	int plat = myredis::GetInstance()->get_data_by_uid(uid, DB_COMM, CUR_CHANNEL_ID);
	char db_name[32] = { 0 };
	if (g_dbproxy_namager->get_db_name(plat, db_name, 32))
		json_req_value["db_name"] = Json::Value(db_name);


	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0,
		0, 
		0, 
		"comm_write_lobby_gold_bill", 
		json_req_data.c_str(), 
		section, 
		false, 
		plat);

	//MY_LOG_DEBUG("write_lobby_gold_bill %s", json_req_data.c_str());


	/*if (type != GOLD_BANK_CONTROL &&
		type != GOLD_BANK_SAVE_GOLD &&
		type != GOLD_BANK_SAVE_BANK &&
		type != GOLD_BANK_GET_GOLD &&
		type != GOLD_BANK_GET_BANK &&
		type != GOLD_BANK_INCOME_BANK &&
		type != GOLD_BANK_INCOME_INCOME && 
		!is_bank)
	{
		gold_insert_mysql(uid, now_gold, incre_gold);
	}*/
}

//批量写入金币流水(不支持代理收益)
void bill_fun::batch_write_gold_bill(string str)
{
	cmd_dbproxy::CDBProxyExceSection section;
	Json::FastWriter json_writer;
	Json::Value json_req_value;

	int now_time = time(0);
	json_req_value["i_data"] = Json::Value(str.c_str());
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, 0, "comm_batch_write_lobby_gold_bill", json_req_data.c_str(), section, false);
}

//写入充值流水
void bill_fun::write_lobby_pay_count_bill(int uid, int add_count)
{
	MY_LOG_DEBUG("write_lobby_pay_count_bill user_id(%d) add_count(%d)", uid, add_count);

	cmd_dbproxy::CDBProxyExceSection section;

	Json::FastWriter json_writer;
	Json::Value json_req_value;

	int now_time = time(0);	
	json_req_value["i_uid"]		  = Json::Value(uid);
	json_req_value["i_update_time"] = Json::Value(now_time);
	json_req_value["i_add_count"]  = Json::Value(add_count);
	std::string json_req_data = json_writer.write(json_req_value);

	g_dbproxy_namager->post_exec(0, 0, 0, "comm_write_lobby_pay_count_bill", json_req_data.c_str(), section, false);
}

//数据落地
void bill_fun::gold_insert_mysql(int uid, int now_gold, int num)
{
	cmd_dbproxy::CDBProxyExceSection section;

	Json::FastWriter json_writer;
	Json::Value json_req_value;

	int now_time = time(0);	
	json_req_value["uid"]  = Json::Value(uid);
	json_req_value["gold"] = Json::Value(now_gold);
	json_req_value["num"]  = Json::Value(num);
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, 0, "write_update_gold", json_req_data.c_str(), section, false);
}

//金库金币落地
void bill_fun::gold_insert_bank_gold_mysql(int uid, int bank_gold)
{
	cmd_dbproxy::CDBProxyExceSection section;

	Json::FastWriter json_writer;
	Json::Value json_req_value;

	int now_time = time(0);	
	json_req_value["uid"]  = Json::Value(uid);
	json_req_value["gold"] = Json::Value(bank_gold);
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, 0, "update_bank_gold", json_req_data.c_str(), section, false);
}

void bill_fun::room_gold_statistics(int uid, int clientid, int gameid, int node_id, int gold, int type, bool bstatistics)
{  
	MY_LOG_DEBUG("room_gold_statistics...  uid[%d], gameid[%d], node_id[%d], gold(%d), type[%d], bstatistics[%d]",
		uid, gameid, node_id, gold, type, bstatistics);

	if (!bstatistics)
	{
		return;
	}
	cmd_dbproxy::CDBProxyExceSection section;

	Json::FastWriter json_writer;
	Json::Value json_req_value;
	int nGold = gold;
	json_req_value["i_uid"] = Json::Value(uid);
	json_req_value["i_client_id"] = Json::Value(clientid);
	json_req_value["i_game_id"] = Json::Value(gameid);
	json_req_value["i_node_id"] = Json::Value(node_id);
	json_req_value["i_type"] = Json::Value(type);
	json_req_value["i_gold"] = Json::Value(nGold);
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, 0, "cmd_room_gold_statistics", json_req_data.c_str(), section, false);
}

void bill_fun::update_day_bill_result(int uid,int addbill,int betgold,int nodeid, int agent_profit,int user_plat,bool is_third)
{
	Json::Value json_req_value;
	json_req_value["uid"] = Json::Value(uid);
	json_req_value["num"] = Json::Value(addbill);
	json_req_value["bet_gold"] = Json::Value(betgold);
	json_req_value["agent_profit"] = Json::Value(agent_profit);
	json_req_value["node_id"] = Json::Value(nodeid);
	json_req_value["third"] = Json::Value(is_third?1:0);

	char cur_date[64] = { 0 };
	time_t tnow = time(0);
	get_time_to_str(cur_date, tnow);
	json_req_value["cur_date"] = Json::Value(cur_date);
	MY_LOG_DEBUG("billfun::update_day_bill_result -  userID:%d num:%d betgold:%d write_time:%lld date:%s agent_gift:%d is_third:%d",
		uid, addbill, betgold, tnow, cur_date, agent_profit, is_third);

	cmd_dbproxy::CDBProxyExceSection db_section;
	Json::FastWriter json_writer;
	std::string json_req_data = json_writer.write(json_req_value);
	g_dbproxy_namager->post_exec(0, 0, uid, "update_day_result_bill", json_req_data.c_str(), db_section, false, user_plat);
}

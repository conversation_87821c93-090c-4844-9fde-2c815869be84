#include "http_response.h"

#include <string.h>
#include <stdio.h>
#include <time.h>

#include "http_util.h"

namespace tnet {

    HttpResponse::HttpResponse()
        : statusCode(200)
    {
    }

    HttpResponse::HttpResponse(int code, const Headers_t& headers, const std::string& body)
        : statusCode(code)
        , body(body)
        , headers(headers)
    {
        
    }

    HttpResponse::~HttpResponse() {

    }

    std::string HttpResponse::dump() {

        std::string str;
        
        char buf[1024];
        int n = snprintf(buf, sizeof(buf), "HTTP/1.1 %d %s\r\n", statusCode, HttpUtil::codeReason(statusCode).c_str());    

        str.append(buf, n);
    
        n = snprintf(buf, sizeof(buf), "%d", int(body.size()));
        static const std::string ContentLength = "Content-Length";
        headers.insert(std::make_pair(ContentLength, std::string(buf, n)));

        auto it = headers.cbegin();
        while(it != headers.cend())
        {
            n = snprintf(buf, sizeof(buf), "%s: %s\r\n", it->first.c_str(), it->second.c_str());
            str.append(buf, n);
            ++it;    
        }

        str.append("\r\n");
        str.append(body);

        return str;
    }

    void HttpResponse::setContentType(const std::string& contentType) {

        static const std::string ContentTypeKey = "Content-Type";
        headers.insert(std::make_pair(ContentTypeKey, contentType));    
    }

    void HttpResponse::setKeepAlive(bool on) {
        static const std::string ConnectionKey = "Connection";
        if(on) {
            static const std::string KeepAliveValue = "Keep-Alive";
            headers.insert(make_pair(ConnectionKey, KeepAliveValue));    
        } else {
            static const std::string CloseValue = "close";
            headers.insert(make_pair(ConnectionKey, CloseValue));    
        }
    }
    
    void HttpResponse::enableDate() {

        time_t now = time(NULL);
        struct tm t; 
        gmtime_r(&now, &t);
        char buf[128];
        int n = strftime(buf, sizeof buf, "%a, %d %b %Y %H:%M:%S %Z", &t);
        static const std::string DateKey = "Date";
        headers.insert(std::make_pair(DateKey, std::string(buf, n)));
    }
}

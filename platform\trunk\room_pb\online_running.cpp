﻿#include "online_running.h"
#include "user_manager.h"
#include "log_manager.h"
#include "game_frame.h"
#include "config_manager.h"
#include "robot_manager.h"

COnlineRunning::COnlineRunning()
{

}

COnlineRunning::~COnlineRunning()
{

}

void COnlineRunning::start()
{
	int time = 50;
	g_timer_mgr->set_random_timer(this, on_online_timer, time * 1000, 0, 0);
}

FTYPE(void) COnlineRunning::on_online_timer(void*obj, _uint32 timerid, _uint64 tag, _uint32 Elapse, _uint32 iRepeat, _uint32 delay)
{
	if (!obj) return;
	COnlineRunning * pOnline = (COnlineRunning *)obj;

	if (config_manager::GetInstance()->m_room_status == ROOM_STATUS_PAUSE)
	{
		MY_LOG_ERROR("db_online_running_req room[%d]处于维护状态", CGameFrame::GetInstance()->get_room_id());
		return;
	}
	if (config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED && config_manager::GetInstance()->is_match_room())
	{
		//百人比赛人数
		pOnline->db_match_running_req(cmd_match::MATCH_TIME);
		return;
	}
	else if(config_manager::GetInstance()->get_room_type() != TYPE_FMATCH)
	{
		pOnline->db_online_running_req();
	}
	else
	{
		pOnline->db_match_running_req(cmd_match::MATCH_FULL);
		pOnline->db_match_running_req(cmd_match::MATCH_TIME);
	}

	if (config_manager::GetInstance()->get_room_type() == TYPE_NOAML ||
		config_manager::GetInstance()->get_room_type() == TYPE_HUNDRED)
	{
		pOnline->db_node_online_running_req();
		pOnline->db_sys_online_running_req();
	}
}

//********************************************************************************************************************************************
//********************************************************************************************************************************************
//********************************************************************************************************************************************

void COnlineRunning::db_online_running_req()
{
	cmd_dbproxy::CDBProxyExceSection dbSection;

	map_room_user_count count_info;
	UserManager::GetInstance()->get_user_count_from_plat(count_info);
	for (iter_room_user_count iter = count_info.begin(); iter != count_info.end(); iter++)
	{
		Json::FastWriter jsonWriter;
		Json::Value jsonValue;
		jsonValue["tbus"] = Json::Value(CGameFrame::GetInstance()->get_room_id());
		jsonValue["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
		jsonValue["online"] = Json::Value(iter->second.OnlineCount);
		jsonValue["offline"] = Json::Value(iter->second.OfflineCount);
		jsonValue["client_id"] = Json::Value(0);
		jsonValue["post_time"] = Json::Value(g_com->_get_time32());

		char db_name[32] = { 0 };
		if (g_dbproxy_namager->get_db_name(iter->first, db_name, 32))
			jsonValue["db_name"] = Json::Value(db_name);

		std::string jsonReqData = jsonWriter.write(jsonValue);
		g_log->write_log(LOG_TYPE_DEBUG, "请求DB代理操作 cmd:write_room_online_bill %s", jsonReqData.c_str());
		g_dbproxy_namager->post_exec(0, 0, 0, "write_room_online_bill", jsonReqData.c_str(), dbSection, false);
	}
}

void COnlineRunning::db_node_online_running_req()
{
	map<int, int> node_count;
	map_plat_user_count plat_count;
	UserManager::GetInstance()->get_node_count(node_count);
	UserManager::GetInstance()->get_node_count_from_plat(plat_count);
	for (iter_plat_user_count plat_iter = plat_count.begin(); plat_iter != plat_count.end(); plat_iter++)
	{
		for (map<int, int>::iterator iter = plat_iter->second.node_user_count.begin(); iter != plat_iter->second.node_user_count.end(); iter++)
		{
			cmd_dbproxy::CDBProxyExceSection dbSection;
			Json::FastWriter jsonWriter;
			Json::Value jsonValue;
			jsonValue["tbus"] = Json::Value(CGameFrame::GetInstance()->get_room_id());
			jsonValue["node_id"] = Json::Value(iter->first);
			jsonValue["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
			jsonValue["online"] = Json::Value(iter->second);
			jsonValue["post_time"] = Json::Value(g_com->_get_time32());
			jsonValue["client_id"] = Json::Value(0);

			char db_name[32] = { 0 };
			if (g_dbproxy_namager->get_db_name(plat_iter->first, db_name, 32))
				jsonValue["db_name"] = Json::Value(db_name);

			std::string jsonReqData = jsonWriter.write(jsonValue);
			g_dbproxy_namager->post_exec(0, 0, 0, "write_node_room_online", jsonReqData.c_str(), dbSection, false, plat_iter->first);
		}
	}
}

void COnlineRunning::db_sys_online_running_req()
{
	map_plat_sys_room_user_count count;
	UserManager::GetInstance()->get_node_count_for_sys(count);
	for (iter_plat_sys_room_user_count iter = count.begin(); iter != count.end(); iter++)
	{
		for (iter_sys_room_user_count sys_iter = iter->second.count.begin(); sys_iter != iter->second.count.end(); sys_iter++)
		{
			cmd_dbproxy::CDBProxyExceSection dbSection;
			Json::FastWriter jsonWriter;
			Json::Value jsonValue;
			jsonValue["tbus"] = Json::Value(CGameFrame::GetInstance()->get_room_id());
			jsonValue["node_id"] = Json::Value(iter->first);
			jsonValue["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
			jsonValue["ios_online"] = Json::Value(sys_iter->second.ios_count);
			jsonValue["android_online"] = Json::Value(sys_iter->second.android_count);
			jsonValue["post_time"] = Json::Value(g_com->_get_time32());
			jsonValue["client_id"] = Json::Value(0);

			char db_name[32] = { 0 };
			if (g_dbproxy_namager->get_db_name(iter->first, db_name, 32))
				jsonValue["db_name"] = Json::Value(db_name);

			std::string jsonReqData = jsonWriter.write(jsonValue);
			g_dbproxy_namager->post_exec(0, 0, 0, "write_sys_room_online", jsonReqData.c_str(), dbSection, false, iter->first);
		}
	}
}

void COnlineRunning::db_match_running_req(int match_type)
{
	cmd_dbproxy::CDBProxyExceSection dbSection;
	
	int matchFullOnlineCount = 0;
	int matchFullOfflineCount = 0;
	if (match_type == cmd_match::MATCH_FULL)
		UserManager::GetInstance()->match_full_user_count(matchFullOnlineCount, matchFullOfflineCount);
	else
		UserManager::GetInstance()->match_time_user_count(matchFullOnlineCount, matchFullOfflineCount);

	//map_match_count_info match_count;
	//UserManager::GetInstance()->match_user_count_from_plat(match_count, match_type);
	//for (iter_match_count_info iter = match_count.begin(); iter != match_count.end(); iter++)
	//{
		Json::FastWriter jsonWriter;
		Json::Value jsonValue;
		jsonValue["tbus"] = Json::Value(CGameFrame::GetInstance()->get_room_id());
		jsonValue["game_id"] = Json::Value(config_manager::GetInstance()->get_game_id());
		jsonValue["match_type"] = Json::Value(match_type);
		//jsonValue["online"] = Json::Value(iter->second.matchFullOnlineCount);
		//jsonValue["offline"] = Json::Value(iter->second.matchFullOfflineCount);
		jsonValue["online"] = Json::Value(matchFullOnlineCount);
		jsonValue["offline"] = Json::Value(matchFullOfflineCount);
		jsonValue["client_id"] = Json::Value(0);
		jsonValue["post_time"] = Json::Value(g_com->_get_time32());

		//char db_name[32] = { 0 };
		//if (g_dbproxy_namager->get_db_name(iter->first, db_name, 32))
			//jsonValue["db_name"] = Json::Value(db_name);

		std::string jsonReqData = jsonWriter.write(jsonValue);
		MY_LOG_PRINT("db_match_full_running_req... jsonValue:%s", jsonReqData.c_str());
		g_dbproxy_namager->post_exec(0, 0, 0, "update_match_user_online", jsonReqData.c_str(), dbSection, false);
	//}
}

#include "game_logic.h"
#include "game_config.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <string>
#include <sstream>
#include "nlohmann/json.hpp"
#include <unordered_set>
using json = nlohmann::json;

void insertIfNotPresent(std::vector<int>& vec, std::unordered_set<int>& seen, int num)
{
	if (seen.find(num) == seen.end()) {
		vec.push_back(num);
		seen.insert(num);
	}
}
game_logic::game_logic()
{
}

game_logic::~game_logic()
{
}

void game_logic::init()
{
    
}

int game_logic::CalcResultTimes(user_game_info &game_info, GameIconResultInfo& result_info, bool bFree)
{   
	int Icons[6][4] = { 0 };

	memcpy(Icons, result_info.Icons, sizeof(Icons));
	result_info.map_line_data.clear();
	result_info.total_win = 0;
	result_info.get_free_count = 0;
	int iconCount[16 + 1] = { 0 }; //统计图标个数
	for (int i = 0; i < 6; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			iconCount[Icons[i][j]]++;
		}
	}
	std::map<int, std::vector<IconInfo>> map_line_data;
	map_line_data.clear();
	for (int i = 0; i < 4; i++)
	{
		int nFirstIcon = Icons[0][i];
		if (nFirstIcon == ICON_SCA)
		{
			continue;
		}
		bool bFound = false;
		int row = 1;
		int nSameCount = 0;

		IconInfo vec_info;
		hasValidPath(Icons, 0, i, nFirstIcon, map_line_data, vec_info, bFree);

	}
	int free_count = calc_free_game_times(iconCount[ICON_SCA], bFree);
	result_info.get_free_count = free_count;
	if (!bFree)
	{
		if (free_count > 0)
		{
			game_info.has_free = true;
		}
	}
	
	int nRatio = game_info.bet * 10 / 100;
	int nTotalWin = 0;
	std::map<int, std::vector<IconInfo>>::iterator iter = map_line_data.begin();
	for (; iter != map_line_data.end(); iter++)
	{
		int Icon = iter->first; //中奖icon
		std::vector<IconInfo>& vec_info = iter->second;
		for (int i = 0; i < vec_info.size(); i++)
		{  
			int nIconCount = vec_info[i].vec_item.size();
			if (nIconCount < 2)
			{
				continue;
			}
			int nWildMul = 0;
			for (int j = 0; j < nIconCount; j++)
			{  
				int winIcon = vec_info[i].vec_item[j].nIcon;
				if (winIcon == ICON_WILD_X2)
				{
					nWildMul += 2;
				}
				if (winIcon == ICON_WILD_X3)
				{
					nWildMul += 3;
				}
				if (winIcon == ICON_WILD_X4)
				{
					nWildMul += 4;
				}
				if (winIcon == ICON_WILD_X5)
				{
					nWildMul += 5;
				}
			}
		
			if (nWildMul == 0)
			{
				nWildMul = 1;
			}
			int nWin = kIconsTimes[Icon][nIconCount] * nRatio / 10;
			nWin = nWin*nWildMul;
			vec_info[i].nIcon = Icon;
			vec_info[i].nWin = nWin;
			if (nWin > 0)
			{
				nTotalWin += nWin;
				result_info.map_line_data[Icon].push_back(vec_info[i]);
				result_info.total_win += nWin;
			}
		}

	}
	game_info.result += nTotalWin;
	return game_info.result;
}

void game_logic::hasValidPath(int Icon[6][4], int row, int col, int target, std::map<int, std::vector<IconInfo>>& map_icon_list, IconInfo& vec_info, bool bFree)
{
	if (row < 0 || row >= 6 || col < 0 || col >= 4)
	{
		return;
	}
	IconItem item;
	item.nIcon = Icon[row][col];
	item.nIndex = row * 4 + col;
	vec_info.vec_item.push_back(item);
	if (check_next_column_has_target_icon(Icon, row + 1, target, bFree))
	{
		for (int j = 0; j < 4; j++)
		{
			IconInfo temp_vec_info = vec_info;
			if (Icon[row + 1][j] == target || Icon[row + 1][j] == ICON_WILD
				|| Icon[row + 1][j] == ICON_WILD_X2
				|| Icon[row + 1][j] == ICON_WILD_X3
				|| Icon[row + 1][j] == ICON_WILD_X4
				|| Icon[row + 1][j] == ICON_WILD_X5) {
				hasValidPath(Icon, row + 1, j, target, map_icon_list, vec_info,bFree);
				vec_info = temp_vec_info;
			}

		}
	}
	else
	{
		map_icon_list[target].push_back(vec_info);
	}
}

bool game_logic::check_next_column_has_target_icon(int Icon[6][4], int row, int nIcon, bool bFree)
{
	if (row >= 6)
	{
		return false;
	}
	if (!bFree)
	{
		for (int i = 0; i < 4; i++)
		{
			if (Icon[row][i] == nIcon || Icon[row][i] == ICON_WILD)
			{
				return true;
			}
		}
	}
	else
	{
		for (int i = 0; i < 4; i++)
		{
			if (Icon[row][i] == nIcon || Icon[row][i] == ICON_WILD
				|| Icon[row][i] == ICON_WILD_X2 || Icon[row][i] == ICON_WILD_X3
				|| Icon[row][i] == ICON_WILD_X4
				|| Icon[row][i] == ICON_WILD_X5)
			{
				return true;
			}
		}
	}
	return false;
}

bool game_logic::check_pre_column_has_target_icon(int Icon[6][4], int row, int nIcon, bool bFree)
{
	if (row >= 6 || row < 0)
	{
		return false;
	}

	if (!bFree)
	{
		for (int i = 0; i < 4; i++)
		{
			if (Icon[row][i] == nIcon || nIcon == ICON_WILD)
			{
				return true;
			}
		}
	}
	else
	{
		for (int i = 0; i < 4; i++)
		{
			if (Icon[row][i] == nIcon 
				|| nIcon == ICON_WILD
				|| nIcon == ICON_WILD_X2 
				|| nIcon == ICON_WILD_X3 
				|| nIcon == ICON_WILD_X4
				|| nIcon == ICON_WILD_X5)
			{
				return true;
			}
		}
	}

	return false;
}

int game_logic::calc_free_game_times(int nScaCount, bool bInFreeGame)
{
	int nTimes = 0;
	if (nScaCount < 2)
	{
		return 0;
	}
	if (nScaCount >= 2 && nScaCount <= 6)
	{
		nTimes = bInFreeGame ? respin_scatter_times[nScaCount] : normal_scatter_times[nScaCount];
	}
	else
	{
		nTimes = bInFreeGame ? respin_scatter_times[6] : normal_scatter_times[6];
	}

	return nTimes;
}
void game_logic::RandFillIcons(int Icon[6][4], bool bFree, bool bwinFree)
{
	//最近填充
	bool not_available_Icon = false;
	int bScaCount = 0;
	for (int i = 0; i < 6; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			int nIcon = 0;

			do
			{
				not_available_Icon = false;
				if (!bFree)
				{
					nIcon = rand() % 13;
				}
				else
				{
					nIcon = rand() % 17;
					if (nIcon == ICON_WILD_X4)
					{
						not_available_Icon = true;
						continue
					}
				}

				if (i == 0 && (nIcon == ICON_WILD || nIcon == ICON_WILD_X2 || nIcon == ICON_WILD_X3 || nIcon == ICON_WILD_X4 || nIcon == ICON_WILD_X5))
				{
					not_available_Icon = true;
				}

				if (nIcon == ICON_SCA)
				{

					if (!bwinFree &&bScaCount >= 1 || bFree && bScaCount >= 3 || !bFree && bScaCount >= 4) //免费模式下只能出一个，正常模式下只能出3个
					{
						not_available_Icon = true;
					}
				}

			} while (not_available_Icon);

			Icon[i][j] = nIcon;
			if (nIcon == ICON_SCA)
			{
				bScaCount++;
			}

		}
	}
}


void game_logic::RandFillIconNoWin(int Icon[6][4], bool bFree, bool bwinFree)
{ 
	memset(Icon, 0, sizeof(int) * 24);
	int nScaCount = GameConfig::GetInstance()->get_normal_scatter_num();
	for( int i = 0; i < nScaCount; i++) {
		do {
			int nRandIndex = getRand(0, 23);
			int x = nRandIndex / 4;
			int y = nRandIndex % 4;
			if (Icon[x][y] == ICON_SCA){
				continue;
			}
			Icon[x][y] = ICON_SCA;
			break;
		} while (true);
		
	}
	//最近填充
	bool not_available_Icon = false;
	int bScaCount = 0;
	for (int i = 0; i < 6; i++)
	{
		for (int j = 0; j < 4; j++)
		{ 
			if (Icon[i][j] == ICON_SCA){
				continue;
			}
			int nIcon = 0;
			do
			{
				not_available_Icon = false;
				if (!bFree)
				{
					nIcon = rand() % 13;
				}
				else
				{
					nIcon = rand() % 17;
					if (nIcon == ICON_WILD_X4)
					{
						not_available_Icon = true;
						continue;
					}
				}

				if (i == 0 && (nIcon == ICON_WILD || nIcon == ICON_WILD_X2 || nIcon == ICON_WILD_X3 || nIcon == ICON_WILD_X4 || nIcon == ICON_WILD_X5))
				{
					not_available_Icon = true;
				}

				if (i == 1) //第二列
				{
					if (check_pre_column_has_target_icon(Icon, 0, nIcon, bFree))
					{
						not_available_Icon = true;
					}
				}
				if (nIcon == ICON_SCA)
				{
					not_available_Icon = true;
					if (!bwinFree &&bScaCount >= 1 || bFree && bScaCount >= 1 || !bFree && bScaCount >= 2) //
					{
						not_available_Icon = true;
					}
				}

			} while (not_available_Icon);

			Icon[i][j] = nIcon;
			if (nIcon == ICON_SCA)
			{
				bScaCount++;
			}
		}
	}
}

void game_logic::normal_mode_replace_no_win_icon_to_sca(user_game_info &game_info)
{
	int nScaCount = GameConfig::GetInstance()->get_normal_scatter_num();
	std::vector<int> vec_all_index = { 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 };
	std::vector<int> vec_win_index;
	std::unordered_set<int> seen;
	std::map<int, std::vector<IconInfo>>::iterator iter = game_info.game_normal_icon.map_line_data.begin();
	for (; iter != game_info.game_normal_icon.map_line_data.end(); iter++)
	{
		const std::vector<IconInfo>& vec_info = iter->second;
		int nSize = vec_info.size();
		for (int i = 0; i < nSize; i++)
		{  
			for (int j = 0; j < vec_info[i].vec_item.size(); j++)
			{
				int nWinIndex = vec_info[i].vec_item[j].nIndex;
				insertIfNotPresent(vec_win_index, seen, nWinIndex);
			}
		}
	}

	for (int num : vec_win_index) {
		vec_all_index.erase(std::remove(vec_all_index.begin(), vec_all_index.end(), num), vec_all_index.end());
	};

	std::random_shuffle(vec_all_index.begin(), vec_all_index.end());
	for (int i = 0; i < nScaCount; i++)
	{
		int nIndex = vec_all_index[i];
		int m = nIndex / 4;
		int n = nIndex % 4;
		game_info.game_normal_icon.Icons[m][n] = ICON_SCA;
	}
}

_uint64 game_logic::gen_new_round_index()
{
    _uint64 secs = (uint64_t)time(0);
    _uint64 rand_num = rand() % 1000000;

    return secs * 1000000 + rand_num;
}


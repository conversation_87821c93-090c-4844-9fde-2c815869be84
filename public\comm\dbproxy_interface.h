﻿/*
-----------------------------------------------------------------------------

File name        :   dbproxy_interface.h       
Author           :        
Version          :   1.0            
Date             :       
Description      :   DB代理客户端使用接口  

-----------------------------------------------------------------------------
*/
#ifndef __DBPROXY_INTERFACE_HEAD_____
#define __DBPROXY_INTERFACE_HEAD_____


#include "typedef.h"
#include "log_interface.h"
#include "common_interface.h"
#include "queue_interface.h"
#include "tiny_buf_interface.h"
#include "cmd_dbproxy.h"
#include <string>
using namespace svrlib;


class IDBPClient;
class IDBPClientManager;

#define REQUEST_TIMEOUT        30000        //请求数据超时 毫秒
#define CONNECT_TIMEOUT        5000         //连接超时 毫秒
#define SEND_HEART_SPACE_MSEC  5000         //发心跳时间毫秒
#define MAX_HEAD_SIZE          1024*8       //数据包大小（不能超过该值）
#define RCEV_BUFF_SIZE         1024*1024*2  //接受缓冲大小（该值决定能收代理返回多大数据包）
#define LENGTH_TAG             "length:"    //长度标识

//全局数据声明
extern IDBPClient*      g_dbproxy;  //DB代理实例
extern IDBPClientManager*      g_dbproxy_namager;  //DB代理实例

//连接事件回调
typedef void (FUNTYPE*CONNET_EVENT)();


//DB代理回调
//static FTYPE(void) on_dbproxy(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);
typedef void (FUNTYPE*DBPSINK)(void*obj,const char* data_json,int size,cmd_dbproxy::CDBProxyExceSection &section);



class IDBPClient  
{
public:

    virtual  bool  start(ICommon* pcom,ILog* plog,IAttemper*pattemper)=0;

    virtual  bool  stop()=0;

    /**
     *  @brief: DB请求（异步）
     *  @obj:    IN 对象指针（一般填写this,不需要填0）
     *  @sink:   IN 回调接口（必须）
     *  @UserID: IN 用户ID（必须），代理服务器通过该值做分库分表因子，若没有用户ID 填0
     *  @cmd:    IN 命令字（必须），小于128字节
     *  @data:   IN 请求所带数据，没有填0
     *  @section:IN 会话数据，用于回调时处理
     *  @b_wait_dbresult: IN 是否要求DB代理异步处理，一般写日志流水等，最好设置成 false,以免写产生队列时，等待过长。
     *  @return: 返回添加后金币数量
    **/
    virtual  bool  post_exec(void*obj,DBPSINK sink,_uint64 UserID,const char* cmd,const char* data,cmd_dbproxy::CDBProxyExceSection &section,bool b_wait_dbresult = true)=0;

    //获取等待队列数
    virtual  int   wait_count()=0;

	//连接事件回调
	virtual void set_connect_event(CONNET_EVENT event_func) {};
};

class IDBPClientManager
{
public:

	virtual  bool  load_db_name() = 0;

	virtual  bool  get_db_name(int plat, char *dbname, int buff_len) = 0;

	virtual  bool  start(ICommon* pcom, ILog* plog, IAttemper*pattemper, CONNET_EVENT event_func) = 0;

	virtual  bool  stop() = 0;

	/**
	*  @brief: DB请求（异步）
	*  @obj:    IN 对象指针（一般填写this,不需要填0）
	*  @sink:   IN 回调接口（必须）
	*  @UserID: IN 用户ID（必须），代理服务器通过该值做分库分表因子，若没有用户ID 填0
	*  @cmd:    IN 命令字（必须），小于128字节
	*  @data:   IN 请求所带数据，没有填0
	*  @section:IN 会话数据，用于回调时处理
	*  @b_wait_dbresult: IN 是否要求DB代理异步处理，一般写日志流水等，最好设置成 false,以免写产生队列时，等待过长。
	*  @return: 返回添加后金币数量
	**/
	virtual  bool  post_exec(void*obj, DBPSINK sink, _uint64 UserID, const char* cmd, const char* data, cmd_dbproxy::CDBProxyExceSection &section, bool b_wait_dbresult = true, int plat = 0) = 0;
};




#endif//__DBPROXY_INTERFACE_HEAD_____
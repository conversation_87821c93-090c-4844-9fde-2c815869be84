

#ifndef __LOG_MANAGER_H__
#define __LOG_MANAGER_H__

#include <string.h>
#include <assert.h>
#include "log_interface.h"
#if defined(_WIN32) || defined(_WIN64)
#include <windows.h>
#endif
#include "dll_model_helper.h"
#include "comm_fun.h"
#include <time.h>
#include <thread>
#include <sstream>

using namespace svrlib;

#ifndef DLL_MOBULE_INIT
#define DLL_MOBULE_INIT(module, dllname, create_func, delete_func)\
    do{\
        if (!(module).DLLInit(dllname, create_func, delete_func))\
        {\
            MY_LOG_PRINT("Dll module init fail. error=%s", (module).GetError());\
            return -1;\
        }\
            if (!(module).CreateInstance())\
        {\
            MY_LOG_PRINT("Dll module create instalce fail. error=%s", (module).GetError());\
            return -2;\
        }\
            if (!(module).GetInterface())\
        {\
            MY_LOG_PRINT("Dll module get interface fail.");\
            return -3;\
        }\
    }while(0);
#endif

class IMYLogManager
{
public:
    virtual int Init() = 0;

    virtual void SetLog(ILog *) = 0;

    virtual int  GetType() = 0;

    virtual ILog* GetLog() = 0;
};

class MYLogManager : public IMYLogManager
{
public:
    ~MYLogManager();

    int Init();

    static MYLogManager *GetInstance()
    {
        static MYLogManager stMYLog;
        return &stMYLog;
    }

    void SetLog(ILog* p_log)
    {
        m_pstLog = p_log;
    }

    ILog *GetLog() 
    {   
        assert(m_pstLog);
        return m_pstLog; 
    }    

    int GetType()
    {
        if (m_pstLog)
        {
            return m_pstLog->get_type();
        }

        return LOG_TYPE_ERROR;
    }

protected:
    MYLogManager();

private:
    CDLLHelper<ILog> m_stDLLLog;    
    ILog            *m_pstLog;
};

extern IMYLogManager *g_pstLogManager;

uint64_t get_current_thread_id();


/*
LOG_TYPE_INFO            =   0,   / *��ʾ��Ϣ* /
LOG_TYPE_DEBUG           =   1,   / *������Ϣ* /
LOG_TYPE_WARNING         =   2,   / *����* /
LOG_TYPE_ERROR           =   3,   / *����* /
LOG_TYPE_PRINT           =   4,   / *��ӡ��Ϣ* /
*/

#if defined(_WIN32) || defined(_WIN64)
#define MY_LOG_INFO(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_INFO,    "%s(%d): "##format_str, basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_DEBUG(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_DEBUG,   "%s(%d): "##format_str, basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_WARN(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_WARNING, "%s(%d): "##format_str, basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_ERROR(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_ERROR,   "%s(%d): "##format_str, basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_PRINT(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_PRINT,   "%s(%d): "##format_str, basename(__FILE__), __LINE__, ##__VA_ARGS__)
#else
#define MY_LOG_INFO(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_INFO,    "[%lld]%s(%d): " format_str, get_current_thread_id(), basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_DEBUG(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_DEBUG,   "[%lld]%s(%d): " format_str, get_current_thread_id(), basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_WARN(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_WARNING, "[%lld]%s(%d): " format_str, get_current_thread_id(), basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_ERROR(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_ERROR,   "[%lld]%s(%d): " format_str, get_current_thread_id(), basename(__FILE__), __LINE__, ##__VA_ARGS__)

#define MY_LOG_PRINT(format_str, ...)\
    g_pstLogManager->GetLog()->write_log(LOG_TYPE_PRINT,   "[%lld]%s(%d): " format_str, get_current_thread_id(), basename(__FILE__), __LINE__, ##__VA_ARGS__)
#endif

class cprint_time_log
{
public:
	cprint_time_log( char* functionname, const char* log, ... )
	{
#ifdef _WIN32 
		m_t1 = GetTickCount();
#else
		m_t1 = time(0);
#endif
		
	}
	~cprint_time_log()
	{
#ifdef _WIN32 
		unsigned int t2 = GetTickCount();
		int count = t2 - m_t1;
		if( count > 100 && count < 1000 )
		{
			MY_LOG_WARN( "leave %s usetime:%d", m_szfunctionname, count );
		}
		else
		if (count > 1000)
		{
			MY_LOG_ERROR( "leave %s usetime:%d", m_szfunctionname, count );
		}
#else
		unsigned int t2 = time(0);
		int count = t2 - m_t1;
		if (count > 1000)
		{
			MY_LOG_ERROR( "leave %s usetime:%d", m_szfunctionname, count );
		}
#endif
		
	}
private:
	unsigned int  m_t1;
	char    m_szfunctionname[128];
};

#endif

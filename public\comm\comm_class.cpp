
#include "comm_class.h"
#include "log_manager.h"

CTimeLimitMS::CTimeLimitMS(int ms_time, const char *desc)
{
    m_limit_time = ms_time;
    gettimeofday(&m_tm_start, 0);
    if (desc)
    {
        mysprintf(m_desc, sizeof(m_desc), "%s", desc);
    }
}

CTimeLimitMS::~CTimeLimitMS()
{
    int      diff;
    struct timeval  tm_diff;

    gettimeofday(&m_tm_end, 0);
    TV_DIFF(tm_diff, m_tm_end, m_tm_start);
    TV_TO_MS(diff, tm_diff);

    MY_LOG_DEBUG("%s use time(%d).", m_desc, diff);
    if (diff > m_limit_time)
    {
        MY_LOG_WARN("%s use time(%d) more than limit time(%d).", m_desc, diff, m_limit_time);
    }
}
#include "winlose.h"
#include "game_config.h"
#include "log_manager.h"

namespace Superace {

	winlose_control::winlose_control()
	{
        
	}

	winlose_control::~winlose_control()
	{

	}

	//获得配置
	iter_stock_cfg winlose_control::get_config(int plat_id, int gameid, int nodeid)
	{
		char key_str[64] = { 0 };
		sprintf(key_str, CUR_STOCK_CFG, plat_id, gameid, nodeid);
		string k = key_str;
		return m_map_stock_cfg.find(k);
	}

	//初始化配置
	void winlose_control::init_config(int plat_id, int gameid, int nodeid)
	{
		bool need_flush_cfg = false;
		int times = myredis::GetInstance()->get_update_stock_time(plat_id, gameid, nodeid);
		iter_stock_cfg conf_iter = get_config(plat_id, gameid, nodeid);
		if (conf_iter == m_map_stock_cfg.end())
		{
			srand(time(0));
			need_flush_cfg = true;
			MY_LOG_DEBUG("init_config need_flush_cfg.");
		}
		else
		{
			MY_LOG_DEBUG("init_config need_flush_cfg times:%d update_time:%d", times, conf_iter->second.update_time);
			if (times != conf_iter->second.update_time)
			{
				need_flush_cfg = true;
			}
		}

		if (!need_flush_cfg)
			return;

		load_config(plat_id, gameid, nodeid, times);
	}

	//加载表权重
	void winlose_control::load_form_weight(Json::Value json_value, const char* str, vector<form_item>& form, int& total_weight)
	{
		total_weight = 0;
		int player_form_count = json_value[str].size();
		for (int i = 0; i < player_form_count; i++)
		{
			form_item item;
			item.multer = json_value[str][i]["multer"].asInt();
			item.touch = json_value[str][i]["rate"].asInt();

			form.push_back(item);
			total_weight += item.touch;
		}
	}

	//加载配置
	void winlose_control::load_config(int plat_id, int gameid, int nodeid, int update_time)
	{
		char* str = myredis::GetInstance()->get_stock_config(plat_id, gameid, nodeid);
		if (str == NULL)
			return;

		stock_cfg_item cfg_item;
		Json::Reader json_reader;
		Json::Value json_value;
		Json::Value va;
		if (!json_reader.parse((const char*)str, json_value))
			return;

		cfg_item.update_time = update_time;
		cfg_item.kill_rate = json_value["kill_rate"].asInt();
		cfg_item.base_bet = json_value["base_bet"].asInt();
		//各个表格权重
		load_form_weight(json_value, "bet_normal_win", cfg_item.bet_form_win1, cfg_item.bet_form_win_total_1);
		load_form_weight(json_value, "bet_extra_win", cfg_item.bet_form_win2, cfg_item.bet_form_win_total_2);
		char key_str[64] = { 0 };
		sprintf(key_str, CUR_STOCK_CFG, plat_id, gameid, nodeid);
		string k = key_str;
		m_map_stock_cfg[k] = cfg_item;
	}

	bool winlose_control::get_ctrl_result(iter_stock_cfg iter, int type, int &result)
	{
		int lose = 0;
		int win = 0;
		int free_game = 0;
		int normal_game = 0;
		int total = 0;

		vector<form_item> form;

		if (type == 0)
		{
			form = iter->second.bet_form_win1;
			total = iter->second.bet_form_win_total_1;
		}
		else if (type == 1)
		{
			form = iter->second.bet_form_win2;
			total = iter->second.bet_form_win_total_2;
		}
		else
		{
			MY_LOG_WARN("get_ctrl_result config erro");
			return false;
		}
		//MY_LOG_PRINT("total = %d, type = %d", total, type);
		{
			int r = getRand(0, total - 1);
			for (int i = 0; i < form.size(); i++)
			{
				if (r < form[i].touch)
				{
					result = form[i].multer;					
					return true;
				}
				else
				{
					r -= form[i].touch;
				}
			}
			return false;
		}
	}

	// 获得抽奖结果控制参数
	bool winlose_control::get_lottery_ctr_result(int plat_id, int gameid, int nodeid, int type, int& result)
	{
		init_config(plat_id, gameid, nodeid);
		iter_stock_cfg iter = get_config(plat_id, gameid, nodeid);
		if (iter == m_map_stock_cfg.end())
		{
			MY_LOG_WARN("get_lottery_ctr_result config erro");
			return false;
		}
		if(type == 2)
		{
			result = iter->second.base_bet;
			return true;
		}

		int r = getRand(1, 100);
		if(r < iter->second.kill_rate)
		{
			result = 0;
			return true;
		}
		return get_ctrl_result(iter, type, result);
	}
}

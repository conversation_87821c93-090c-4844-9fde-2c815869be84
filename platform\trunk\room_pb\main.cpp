﻿
#include "comm_class.h"
#include "tinyxml.h"
#include "room_common.h"
#include "monitor_interface.h"
#include "dump_interface.h"
#include "config_manager.h"
#include "common_func.h"
#include "table_manager.h"
#include "game_frame.h"
#include "user_manager.h"
#include "online_running.h"
#include "web_request.h"
#include "my_redis.h"
#include "observe_manager.h"
#include "robot_manager.h"
#include "pair_manager.h"
#include "comm_msg.h"
#include "ip_location.h"

CDLLHelper<ILog>        g_log(SVRLIB_NAME,"_log_instance","_log_free");
CDLLHelper<ICommon>     g_com(SVRLIB_NAME,"_common_instance","_common_free");
CDLLHelper<IAttemper>   g_apt(SVRLIB_NAME,"_attemper_instance","_attemper_free");
CDLLHelper<IMonitor>    g_monitor(SVRLIB_NAME,"_monitor_instance","_monitor_free");


#ifdef _WINNT_SYSTEM
#define _return(code,fmt,...)    do{g_log->write_log(LOG_TYPE_ERROR,fmt,__VA_ARGS__);getchar();return code;}while(false)
#else

#include "version.h"
#define _return(code,fmt,args...)do{g_log->write_log(LOG_TYPE_ERROR,fmt,##args);return code;}while(false) 

#endif


_uint64       g_server_id    = 0;                       //游戏ID
char          g_dll_name_buf[MAX_DLL_NAME_LEN] = {0};  //游戏DLL名称
char*         g_dll_name = g_dll_name_buf;              //游戏DLL名称
volatile bool g_b_running = true;                       //是否运行中
_uint32       g_start_time;



//检测器连接事件
static FTYPE(bool) on_monitor_conn(_uint32 &ip,int &port)
{
    TiXmlDocument xmlcfg;
    TiXmlElement* pRoot = 0; 
    TiXmlElement* pItem = 0;
    char xmlfile[1024];
    if(!xmlcfg.LoadFile(_xmlfile(xmlfile,sizeof(xmlfile),CONFIG)))  return false;
    if(!(pRoot = xmlcfg.FirstChildElement("config")))        return false;
    if(!(pItem = pRoot->FirstChildElement("monitor")))       return false;
    if(!pItem->Attribute("ip"))   return false;
    if(!pItem->Attribute("port")) return false;

    ip   = g_com->_net_nip(pItem->Attribute("ip"));
    port = atoi(pItem->Attribute("port"));
    return true;
}

//检测器收到远程命令
static FTYPE(void) on_monitor_cmd(_uint64 sid,const char*cmd,int len,_uint32 remote_ip)
{
    char szip[20];
    to_lower_str((char*)cmd,len);
    g_com->_net_strip(remote_ip,szip,sizeof(szip));
    g_log->write_log(LOG_TYPE_PRINT,"[monitor] [ip=%s] cmd: %s",szip,cmd);

#define   max_argv  100
    int   argc      = 0;
    char* argv[max_argv] = {0};

    analyse_arg((char*)cmd,len,argc,argv,max_argv);
    g_monitor_mgr->on_recv_cmd(sid, argc, (const char**)argv, cmd);
}

//检测器内部日志
static FTYPE(void) on_monitor_log(TLOGTYPE logtype,const char* sz_msg)
{
    g_log->write_log(logtype,"[monitor] %s",sz_msg);
}

static FTYPE(void) on_redis_log(TLOGTYPE logtype,const char* sz_msg)
{
    g_log->write_log(logtype,"[redis] %s",sz_msg);
}

int main(int argc,char* argv[])
{  
	int ret = single_instance_running("pid.lck");
	if (ret<0)
	{
		return 0;
	}

    __init_dump(); //初始化dump   

    g_server_id = 0;
    g_dll_name[0] = '\0';
    for (int i= 0;i< argc;i++){
        if(!argv[i] || strlen(argv[i]) < 2) continue;
        if(argv[i][0] != '-') continue;
        if(i+1>=argc) break;
        if(argv[i+1][0] == '-') continue;
        switch(tolower(argv[i][1])){
        case 'r': g_server_id  = atoi(argv[++i]); break;
		case 't': config_manager::GetInstance()->set_room_type((enRoomType)atoi(argv[++i])); break; // 房间类型
		case 'm': config_manager::GetInstance()->set_is_match_room(atoi(argv[++i]) == 1); break; //是否为比赛房间
        case 'v': 
            {
#if !defined(_WIN32) && !defined(_WIN64)
                printf("room%d.%d.%d.%d_%d_%d\n", MAJOR_VERSION, MINOR_VERSION, REVISE_VERSION, BUILD_VERSION, SVNINFO_REVERSION, BUILD_DATE); 
#endif
            }
            return 0;

        case 'g':
            {
                __sprintf(g_dll_name_buf,sizeof(g_dll_name_buf),argv[++i]);
            }
            break;
        }
    }
    if(0 == g_server_id){
        printf("请带参启动：-r 房间ID -g 游戏组件 (如： -r 1000 -g ddz.dll)  (-g  可有可无，加的话给测试用) \n");
        return 0;
    }

	printf("room server starting ...");

    if(!g_log.CreateInstance()){
        printf("log init fail! ERROR:%s", g_log.GetError());
        return 0;
    }

	string l;
	char dir[1024] = {0};
	TiXmlDocument xmlcfg;
	TiXmlElement* pRoot = 0; 
	TiXmlElement* pItem = 0;
	char xmlfile[1024];
	if(!xmlcfg.LoadFile(_xmlfile(xmlfile,sizeof(xmlfile),CONFIG))) { // __return(false,LOG_TYPE_ERROR,"配置文件（config.xml）加载失败，请检查文件格式是否正确！");
        printf("load config.xml failed, program exit with -1\r\n");
		return -1;
	}
	if(!(pRoot = xmlcfg.FirstChildElement("config"))) { //      __return(false,LOG_TYPE_ERROR,"配置文件（config.xml）文件格式错误!(无法找到<config>)");
	    printf("config.xml file format invalid, no config attribute, program exit with -1\r\n");
		return -1;
	}
	if((pItem = pRoot->FirstChildElement("sys")) != 0)
	{
		l = xml_attribute_str(pItem, "logdir");
	} else {
        printf("config.xml file format invalid, no sys attribute, program exit with -1\r\n");
		return -1;
	}

	if (l.length() <= 1) {
		g_com->_get_app_path(dir,sizeof(dir));
	} else {
		memcpy(dir, l.c_str(), l.length());
	}

    char cfgpath[1024] = {0};
    __strcpy_s(cfgpath,sizeof(cfgpath),dir);
    
    tagLogInit log_init;
    __sprintf(dir+strlen(dir),sizeof(dir)-strlen(dir),"log%c%lld%c",_path_sign,g_server_id,_path_sign);
    log_init.type = (TLOGTYPE)2;
    log_init.dir  = dir;
    g_log->init(log_init);
    g_log->write_log(LOG_TYPE_PRINT,"room(id=%lld) roomtype:%d ismatch:%d start......",g_server_id, config_inst->get_room_type(), config_inst->is_match_room());
	g_log->set_type((TLOGTYPE)xml_attribute_int(pItem,"logtype"));

    if(!g_com.CreateInstance())     _return(0,"init common fail!ERROR:%s",g_com.GetError());
    if(!g_apt.CreateInstance())     _return(0,"init g_apt  fail!ERROR:%s",g_apt.GetError());
    if(!g_monitor.CreateInstance()) _return(0,"init g_monitor fail!ERROR:%s",g_monitor.GetError());

	/* *******************************************************************/
    /* ************************* 应用数据初始化 **************************/
	/* *******************************************************************/
    g_pstLogManager->SetLog(g_log.GetInterface());
    MY_LOG_PRINT("Log manager test success. type=%d", g_pstLogManager->GetType());
	if (!load_languate_entry()) {
#if defined(_WIN32) || defined(_WIN64)
		getchar();
#endif
		return false;
	}

    if (!g_timer_mgr->init()) {
        MY_LOG_ERROR("Timer manager init fail.");
        return -1;
    }

	/* *******************************************************************/
	/**************************web init***********************************/
	/* *******************************************************************/
	char web_client_version_api[256] = {0};
	char web_room_config_api[256] = {0};
	char h5_api[256] = {0};
	if((pItem = pRoot->FirstChildElement("web")) != 0) {
		mysprintf(web_client_version_api, sizeof(web_client_version_api), "%s", pItem->Attribute("client_version_api"));
		mysprintf(web_room_config_api, sizeof(web_room_config_api), "%s", pItem->Attribute("room_config_api"));
	}

	if((pItem = pRoot->FirstChildElement("h5web")) != 0) {
		mysprintf(h5_api, sizeof(h5_api), "%s", pItem->Attribute("web_api"));
	}

	if (!web_request::GetInstance()->init(g_com.GetInterface(), g_log.GetInterface(),g_apt.GetInterface(),
		web_client_version_api, web_room_config_api, h5_api))
	{
		MY_LOG_ERROR("web request instance init failed, program quit");
		return -1;
	}

	//****读取redis ip配置****
	char redis_ip[128] = {0};
	char redis_auth[128] = {0};
	int redis_port = 0;
	if((pItem = pRoot->FirstChildElement("redis")) != 0) {
		mysprintf(redis_ip, sizeof(redis_ip), "%s", pItem->Attribute("ip"));
		mysprintf(redis_auth, sizeof(redis_auth), "%s", pItem->Attribute("auth"));
		redis_port = xml_attribute_int(pItem,"port");
	} else {
		MY_LOG_ERROR("load redis config fail.");
	}
	myredis::GetInstance()->init(redis_ip, sizeof(redis_ip),redis_port,redis_auth,sizeof(redis_auth));

	//**** 旁观人数配置 ****
	int observe_max_count = 0;
	if((pItem = pRoot->FirstChildElement("observe")) != 0) {
		observe_max_count = xml_attribute_int(pItem,"max_count");
	} else {
		MY_LOG_ERROR("load observe config fail.");
	}
	ObserveManager::GetInstance()->init(observe_max_count);
	robot_manager::GetInstance()->init();
	UserManager::GetInstance()->init();
	/* *******************************************************************/
	//*************************初始化组件游戏配置*************************/
	/* *******************************************************************/
	stGameInfo gi;
	int tick_time_limit = CHECK_TICK_TIME;
	gi.game_id = DEFAULT_GAME_ID;
	int create_pay_type = PAY_TYPE_DIAMOND;
#ifdef USE_GAME_DLL
    if((pItem = pRoot->FirstChildElement("game_dll")) != 0)
    {
        mysprintf(gi.game_name, sizeof(gi.game_name), "%s", pItem->Attribute("name"));
		gi.game_id = atoi(pItem->Attribute("game_id"));
		const char* pDataInfo = pItem->Attribute("control_dll");
		__strcpy_s(gi.control_dll, sizeof(gi.control_dll), pDataInfo);
		if (pItem->Attribute("pay_type") != NULL)
		{
			create_pay_type = atoi(pItem->Attribute("pay_type"));
		}
		if (pItem->Attribute("tick") != NULL)
		{
			tick_time_limit = atoi(pItem->Attribute("tick"));
		}
		if (pItem->Attribute("plat_type") != NULL)
		{
			config_manager::GetInstance()->m_plat_type = atoi(pItem->Attribute("plat_type"));
		}
    }
#endif

	int iRet = CGameFrame::GetInstance()->init(g_server_id, gi.game_name);
	if (iRet) {
		MY_LOG_ERROR("Game frame init fail. %d", iRet);
		return -3;
	}
	CGameFrame::GetInstance()->set_room_status(room_route::ROOM_STATUS_NORMAL);

	config_manager::GetInstance()->set_create_pay_type((enCreatePayType)create_pay_type);
	config_manager::GetInstance()->set_game_info(&gi);
	config_manager::GetInstance()->set_tick_limit(tick_time_limit);
	web_request::GetInstance()->load_data();

	/* *******************************************************************/
    /* *************************应用数据初始化 **************************/
	/* *******************************************************************/
    g_apt->init(min_value(xml_attribute_int(pRoot->FirstChildElement("sys"),"atpbfs"),10485760));
    g_apt->start();

    if (!g_server->start()) {  
		MY_LOG_ERROR("g_server init failed");
		g_b_running = false;
        return -1;
    }
	//g_dbproxy_namager->set_connect_event(config_manager::db_connect_ok);
    //g_dbproxy_namager->start(g_com.GetInterface(),g_log.GetInterface(),g_apt.GetInterface());
	if (!g_dbproxy_namager->start(g_com.GetInterface(), g_log.GetInterface(), g_apt.GetInterface(), config_manager::db_connect_ok)) {
		MY_LOG_ERROR("DBPROXY_MANAGER init failed");
		return -1;
	}

	if (!g_dbproxy_namager->load_db_name()) {
		MY_LOG_ERROR("DBPROXY_MANAGER load_db_name failed");
		return -1;
	}

	//*************************启动定时触发功能*************************
	int update_config_time = 0;
	if ((pItem = pRoot->FirstChildElement("server")) != 0)
	{
		update_config_time = xml_attribute_int(pItem, "updateconfigtime");
	}
	if (update_config_time <= 0)
		update_config_time = 180;

	//拉取更新房间状态
	web_request::GetInstance()->get_room_status_req(CGameFrame::GetInstance()->get_room_id(), config_manager::GetInstance()->get_game_id());

	config_manager::GetInstance()->init(update_config_time);
	COnlineRunning::GetInstance()->start();
	pair_manager::GetInstance()->init();
    g_start_time = g_com->_get_time32();

    //running
    IMInitData iminitdata;
    iminitdata.log_sink  = on_monitor_log;
    iminitdata.conn_sink = on_monitor_conn;
    iminitdata.cmd_sink  = on_monitor_cmd;
    iminitdata.appid     = g_server_id;
    iminitdata.attemper  = g_apt.GetInterface();
    g_monitor->init(&iminitdata);

    MY_LOG_PRINT("room server started");

    while(g_b_running){ g_monitor->running();}  
    g_monitor->final();

    //exit
    g_log->write_log(LOG_TYPE_PRINT,"stop......");
    g_server->stop();
    g_dbproxy_namager->stop();
    g_timer_mgr->final();
    g_apt->stop();
    g_log->uninit();

    return 0;
}
